package com.bimowu.resume.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 
 * 用户登录token表
 * 
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "用户登录token类")
@TableName("user_login_device")
public class UserLoginDevice {

  //loginType对应的值
  public static final String LOGINTYPE_PC_TYPE = "pc";
  public static final String LOGINTYPE_APP_TYPE = "app";


  /**主键ID**/
  @TableId(value = "ult_id", type = IdType.AUTO)
  private Long ultId;


  /**登录token**/

  private String token;


  /**用户id**/

  private Long userId;


  /**登录类型,pc/app**/

  private String loginType;


  /**状态，0正常，-1无效**/

  private Integer status;


  /**登陆用户名**/

  private String phone;


  /**设备号**/

  private String deviceNo;


  /**设备类型**/

  private String deviceType;


  /**创建时间**/
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;


  /**更新时间**/
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date modifyTime;


  /**失效时间**/
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date invalidTime;


}
