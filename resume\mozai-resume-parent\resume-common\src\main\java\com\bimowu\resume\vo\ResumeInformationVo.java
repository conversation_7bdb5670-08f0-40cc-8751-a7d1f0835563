package com.bimowu.resume.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
/**
 * 基本信息
 * */
@Data
public class ResumeInformationVo {
    private Long inforId;
    private Long resumeId;
    private String avatar;
    private String name;
    private String gender;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date birthDate;
    private String phone;
    private String email;
    private String hometown;
    private String nationality;
    private Integer age;
    private String jobObjective;
}