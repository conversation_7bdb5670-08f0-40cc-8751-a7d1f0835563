package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.common.service.CSSProcessor;
import com.bimowu.resume.dto.ExtractedStyles;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.w3c.css.sac.InputSource;
import org.w3c.dom.css.*;

import com.steadystate.css.parser.CSSOMParser;
import com.steadystate.css.parser.SACParserCSS3;

import java.io.StringReader;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * CSS处理器实现类
 */
@Slf4j
@Service
public class CSSProcessorImpl implements CSSProcessor {
    
    // CSS属性正则表达式
    private static final Pattern FONT_FAMILY_PATTERN = Pattern.compile("font-family\\s*:\\s*([^;]+)");
    private static final Pattern FONT_SIZE_PATTERN = Pattern.compile("font-size\\s*:\\s*([^;]+)");
    private static final Pattern COLOR_PATTERN = Pattern.compile("color\\s*:\\s*([^;]+)");
    private static final Pattern BACKGROUND_COLOR_PATTERN = Pattern.compile("background-color\\s*:\\s*([^;]+)");
    private static final Pattern MEDIA_QUERY_PATTERN = Pattern.compile("@media\\s*([^{]+)\\s*\\{([^}]+)\\}");
    private static final Pattern URL_PATTERN = Pattern.compile("url\\s*\\(\\s*['\"]?([^'\"\\)]+)['\"]?\\s*\\)");
    
    // 不支持的CSS属性列表
    private static final Set<String> UNSUPPORTED_PROPERTIES = new HashSet<>(Arrays.asList(
        "transform", "transition", "animation", "filter", "backdrop-filter",
        "box-shadow", "text-shadow", "gradient", "flex", "grid"
    ));
    
    // PDF特定的CSS规则
    private static final String PDF_SPECIFIC_CSS = 
        "@page { size: A4; margin: 2cm; }\n" +
        "body { -webkit-print-color-adjust: exact; }\n" +
        "* { box-sizing: border-box; }\n" +
        ".page-break { page-break-before: always; }\n" +
        ".no-break { page-break-inside: avoid; }\n";
    
    @Override
    public String processVueStyles(String vueCSS) {
        if (!StringUtils.hasText(vueCSS)) {
            return "";
        }
        
        try {
            // 移除Vue特定的样式语法
            String processedCSS = removeVueSpecificSyntax(vueCSS);
            
            // 处理scoped样式
            processedCSS = processScopedStyles(processedCSS);
            
            // 标准化CSS
            processedCSS = normalizeCSSStyles(processedCSS);
            
            return processedCSS;
        } catch (Exception e) {
            log.error("处理Vue样式失败", e);
            return vueCSS; // 返回原始CSS作为备用
        }
    }
    
    @Override
    public String optimizeForPDF(String css) {
        if (!StringUtils.hasText(css)) {
            return "";
        }
        
        try {
            // 移除不支持的属性
            String optimizedCSS = removeUnsupportedProperties(css);
            
            // 处理响应式CSS
            optimizedCSS = processResponsiveCSS(optimizedCSS, 800, 1200);
            
            // 添加PDF特定规则
            optimizedCSS = addPDFSpecificRules(optimizedCSS);
            
            // 优化字体设置
            optimizedCSS = optimizeFontSettings(optimizedCSS);
            
            // 优化颜色设置
            optimizedCSS = optimizeColorSettings(optimizedCSS);
            
            return optimizedCSS;
        } catch (Exception e) {
            log.error("优化PDF CSS失败", e);
            return css;
        }
    }
    
    @Override
    public String normalizeCSSStyles(String css) {
        if (!StringUtils.hasText(css)) {
            return "";
        }
        
        try {
            // 移除注释
            String normalizedCSS = removeComments(css);
            
            // 标准化空白字符
            normalizedCSS = normalizeWhitespace(normalizedCSS);
            
            // 标准化属性值
            normalizedCSS = normalizePropertyValues(normalizedCSS);
            
            // 排序CSS规则
            normalizedCSS = sortCSSRules(normalizedCSS);
            
            return normalizedCSS;
        } catch (Exception e) {
            log.error("标准化CSS失败", e);
            return css;
        }
    }
    
    @Override
    public String processResponsiveCSS(String css, int targetWidth, int targetHeight) {
        if (!StringUtils.hasText(css)) {
            return "";
        }
        
        try {
            StringBuilder processedCSS = new StringBuilder();
            
            // 处理媒体查询
            Matcher mediaMatcher = MEDIA_QUERY_PATTERN.matcher(css);
            String remainingCSS = css;
            
            while (mediaMatcher.find()) {
                String mediaCondition = mediaMatcher.group(1).trim();
                String mediaCSS = mediaMatcher.group(2);
                
                // 判断媒体查询是否适用于目标尺寸
                if (isMediaQueryApplicable(mediaCondition, targetWidth, targetHeight)) {
                    processedCSS.append(mediaCSS).append("\\n");
                }
                
                // 从剩余CSS中移除已处理的媒体查询
                remainingCSS = remainingCSS.replace(mediaMatcher.group(0), "");
            }
            
            // 添加非媒体查询的CSS
            processedCSS.append(remainingCSS);
            
            return processedCSS.toString();
        } catch (Exception e) {
            log.error("处理响应式CSS失败", e);
            return css;
        }
    }
    
    @Override
    public String mergeCSS(String... cssContents) {
        if (cssContents == null || cssContents.length == 0) {
            return "";
        }
        
        StringBuilder mergedCSS = new StringBuilder();
        
        for (String css : cssContents) {
            if (StringUtils.hasText(css)) {
                mergedCSS.append(css).append("\\n");
            }
        }
        
        return mergedCSS.toString();
    }
    
    @Override
    public boolean validateCSS(String css) {
        if (!StringUtils.hasText(css)) {
            return true;
        }
        
        try {
            CSSOMParser parser = new CSSOMParser(new SACParserCSS3());
            InputSource source = new InputSource(new StringReader(css));
            CSSStyleSheet stylesheet = parser.parseStyleSheet(source, null, null);
            return stylesheet != null;
        } catch (Exception e) {
            log.warn("CSS验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public ExtractedStyles.FontInfo[] extractFontInfo(String css) {
        List<ExtractedStyles.FontInfo> fonts = new ArrayList<>();
        Set<String> processedFonts = new HashSet<>();
        
        if (!StringUtils.hasText(css)) {
            return fonts.toArray(new ExtractedStyles.FontInfo[0]);
        }
        
        try {
            Matcher fontMatcher = FONT_FAMILY_PATTERN.matcher(css);
            while (fontMatcher.find()) {
                String fontFamily = fontMatcher.group(1).trim().replaceAll("['\"]", "");
                
                if (!processedFonts.contains(fontFamily)) {
                    ExtractedStyles.FontInfo fontInfo = new ExtractedStyles.FontInfo();
                    fontInfo.setFamily(fontFamily);
                    fontInfo.setStyle("normal");
                    fontInfo.setWeight(400);
                    fontInfo.setSize("14px");
                    fontInfo.setLineHeight("1.5");
                    
                    fonts.add(fontInfo);
                    processedFonts.add(fontFamily);
                }
            }
        } catch (Exception e) {
            log.error("提取字体信息失败", e);
        }
        
        return fonts.toArray(new ExtractedStyles.FontInfo[0]);
    }
    
    @Override
    public ExtractedStyles.ColorPalette extractColorPalette(String css) {
        ExtractedStyles.ColorPalette palette = new ExtractedStyles.ColorPalette();
        Set<String> colors = new HashSet<>();
        
        if (!StringUtils.hasText(css)) {
            return palette;
        }
        
        try {
            // 提取颜色值
            Matcher colorMatcher = COLOR_PATTERN.matcher(css);
            while (colorMatcher.find()) {
                colors.add(colorMatcher.group(1).trim());
            }
            
            Matcher bgColorMatcher = BACKGROUND_COLOR_PATTERN.matcher(css);
            while (bgColorMatcher.find()) {
                colors.add(bgColorMatcher.group(1).trim());
            }
            
            // 设置调色板
            List<String> colorList = new ArrayList<>(colors);
            if (!colorList.isEmpty()) {
                palette.setPrimary(colorList.get(0));
                if (colorList.size() > 1) {
                    palette.setSecondary(colorList.get(1));
                }
                if (colorList.size() > 2) {
                    palette.setAccent(colorList.get(2));
                }
            }
            
            // 设置默认值
            if (palette.getPrimary() == null) palette.setPrimary("#333333");
            if (palette.getBackground() == null) palette.setBackground("#ffffff");
            if (palette.getText() == null) palette.setText("#333333");
            if (palette.getBorder() == null) palette.setBorder("#cccccc");
            
        } catch (Exception e) {
            log.error("提取颜色调色板失败", e);
        }
        
        return palette;
    }
    
    @Override
    public String compressCSS(String css) {
        if (!StringUtils.hasText(css)) {
            return "";
        }
        
        try {
            // 移除注释
            String compressed = removeComments(css);
            
            // 移除多余空白
            compressed = compressed.replaceAll("\\s+", " ");
            compressed = compressed.replaceAll("\\s*{\\s*", "{");
            compressed = compressed.replaceAll("\\s*}\\s*", "}");
            compressed = compressed.replaceAll("\\s*;\\s*", ";");
            compressed = compressed.replaceAll("\\s*:\\s*", ":");
            compressed = compressed.replaceAll("\\s*,\\s*", ",");
            
            return compressed.trim();
        } catch (Exception e) {
            log.error("压缩CSS失败", e);
            return css;
        }
    }
    
    @Override
    public String addPDFSpecificRules(String css) {
        StringBuilder result = new StringBuilder();
        result.append(PDF_SPECIFIC_CSS);
        
        if (StringUtils.hasText(css)) {
            result.append("\\n").append(css);
        }
        
        return result.toString();
    }
    
    @Override
    public String resolveRelativePaths(String css, String basePath) {
        if (!StringUtils.hasText(css) || !StringUtils.hasText(basePath)) {
            return css;
        }
        
        try {
            Matcher urlMatcher = URL_PATTERN.matcher(css);
            StringBuffer result = new StringBuffer();
            
            while (urlMatcher.find()) {
                String url = urlMatcher.group(1);
                if (!url.startsWith("http") && !url.startsWith("/")) {
                    // 相对路径，需要解析
                    String resolvedUrl = basePath + "/" + url;
                    urlMatcher.appendReplacement(result, "url('" + resolvedUrl + "')");
                } else {
                    urlMatcher.appendReplacement(result, urlMatcher.group(0));
                }
            }
            urlMatcher.appendTail(result);
            
            return result.toString();
        } catch (Exception e) {
            log.error("解析相对路径失败", e);
            return css;
        }
    }
    
    @Override
    public String removeUnsupportedProperties(String css) {
        if (!StringUtils.hasText(css)) {
            return "";
        }
        
        try {
            String result = css;
            
            for (String property : UNSUPPORTED_PROPERTIES) {
                // 移除不支持的属性
                Pattern pattern = Pattern.compile(property + "\\s*:[^;]+;?", Pattern.CASE_INSENSITIVE);
                result = pattern.matcher(result).replaceAll("");
            }
            
            return result;
        } catch (Exception e) {
            log.error("移除不支持属性失败", e);
            return css;
        }
    }
    
    // 私有辅助方法
    
    private String removeVueSpecificSyntax(String css) {
        // 移除Vue特定的CSS语法，如 v-deep, ::v-deep 等
        String result = css;
        result = result.replaceAll("::v-deep\\s+", "");
        result = result.replaceAll("/deep/\\s+", "");
        result = result.replaceAll(">>>\\s+", "");
        return result;
    }
    
    private String processScopedStyles(String css) {
        // 处理Vue的scoped样式，移除data-v-xxx属性选择器
        return css.replaceAll("\\[data-v-[a-f0-9]+\\]", "");
    }
    
    private String removeComments(String css) {
        return css.replaceAll("/\\*[^*]*\\*+(?:[^/*][^*]*\\*+)*/", "");
    }
    
    private String normalizeWhitespace(String css) {
        return css.replaceAll("\\s+", " ").trim();
    }
    
    private String normalizePropertyValues(String css) {
        // 标准化属性值，如颜色值、单位等
        String result = css;
        
        // 标准化颜色值
        result = result.replaceAll("#([0-9a-fA-F])([0-9a-fA-F])([0-9a-fA-F])\\b", 
                                 "#$1$1$2$2$3$3");
        
        // 标准化单位
        result = result.replaceAll("\\b0px\\b", "0");
        result = result.replaceAll("\\b0em\\b", "0");
        result = result.replaceAll("\\b0%\\b", "0");
        
        return result;
    }
    
    private String sortCSSRules(String css) {
        // 简单的CSS规则排序，实际实现可能更复杂
        return css;
    }
    
    private boolean isMediaQueryApplicable(String mediaCondition, int targetWidth, int targetHeight) {
        // 简化的媒体查询判断逻辑
        if (mediaCondition.contains("max-width")) {
            Pattern pattern = Pattern.compile("max-width:\\s*(\\d+)px");
            Matcher matcher = pattern.matcher(mediaCondition);
            if (matcher.find()) {
                int maxWidth = Integer.parseInt(matcher.group(1));
                return targetWidth <= maxWidth;
            }
        }
        
        if (mediaCondition.contains("min-width")) {
            Pattern pattern = Pattern.compile("min-width:\\s*(\\d+)px");
            Matcher matcher = pattern.matcher(mediaCondition);
            if (matcher.find()) {
                int minWidth = Integer.parseInt(matcher.group(1));
                return targetWidth >= minWidth;
            }
        }
        
        return true; // 默认适用
    }
    
    private String optimizeFontSettings(String css) {
        // 优化字体设置，确保PDF中正确显示
        String result = css;
        
        // 添加字体回退
        result = result.replaceAll("font-family:\\s*([^;]+);", 
                                 "font-family: $1, SimSun, serif;");
        
        return result;
    }
    
    private String optimizeColorSettings(String css) {
        // 优化颜色设置，确保PDF中正确显示
        String result = css;
        
        // 确保颜色值完整
        result = result.replaceAll("color:\\s*rgb\\(([^)]+)\\)", "color: rgb($1)");
        
        return result;
    }
}