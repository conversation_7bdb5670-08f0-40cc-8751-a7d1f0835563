<template>
  <MainLayout>
    <div class="ai-resume-page">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">AI智能写简历</h1>
          <p class="page-subtitle">基于人工智能技术，快速制作专业简历</p>
        </div>
        
        <div class="ai-content">
          <div class="ai-input-section">
            <div class="section-header">
              <h2>告诉AI你的情况</h2>
              <p>输入你的经历和期望，AI会为你生成一份专业简历</p>
            </div>
            
            <el-form class="ai-form">
              <el-form-item label="目标职位">
                <el-input v-model="formData.targetPosition" placeholder="例如：Web前端开发工程师" />
              </el-form-item>
              
              <el-form-item label="工作经验">
                <el-select v-model="formData.experience" placeholder="选择工作经验">
                  <el-option label="应届毕业生" value="fresh" />
                  <el-option label="1-3年" value="1-3" />
                  <el-option label="3-5年" value="3-5" />
                  <el-option label="5-10年" value="5-10" />
                  <el-option label="10年以上" value="10+" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="教育背景">
                <el-input 
                  type="textarea" 
                  v-model="formData.education" 
                  placeholder="学校名称、专业、学历、毕业时间等，例如：北京大学，计算机科学，本科，2018-2022"
                  :rows="3"
                />
              </el-form-item>
              
              <el-form-item label="工作经历">
                <el-input 
                  type="textarea" 
                  v-model="formData.workExperience" 
                  placeholder="公司名称、职位、工作时间、职责与成果等"
                  :rows="5"
                />
              </el-form-item>
              
              <el-form-item label="项目经历">
                <el-input 
                  type="textarea" 
                  v-model="formData.projects" 
                  placeholder="项目名称、担任角色、项目描述、技术栈与成果等"
                  :rows="5"
                />
              </el-form-item>
              
              <el-form-item label="技能特长">
                <el-input 
                  type="textarea" 
                  v-model="formData.skills" 
                  placeholder="例如：精通JavaScript、Vue.js，熟悉React、Node.js等"
                  :rows="3"
                />
              </el-form-item>
              
              <el-form-item label="个人特点">
                <el-input 
                  type="textarea" 
                  v-model="formData.traits" 
                  placeholder="你的性格特点、工作风格等，例如：善于沟通、团队协作能力强、注重细节等"
                  :rows="3"
                />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="generateResume" :loading="loading">
                  <el-icon><MagicStick /></el-icon> 生成AI简历
                </el-button>
                <p class="tips">提示：信息越详细，生成的简历质量越高</p>
              </el-form-item>
            </el-form>
          </div>
          
          <div class="ai-output-section">
            <div class="section-header">
              <h2>AI生成简历预览</h2>
              <div class="preview-actions" v-if="resumeGenerated">
                <el-button type="primary" @click="useGeneratedResume">使用这份简历</el-button>
                <el-button @click="regenerateResume">重新生成</el-button>
              </div>
            </div>
            
            <div class="resume-preview" v-if="resumeGenerated">
              <div class="resume-content">
                <div v-for="(section, index) in generatedResume" :key="index" class="resume-section">
                  <h3>{{ section.title }}</h3>
                  <div v-html="formatContent(section.content)"></div>
                </div>
              </div>
            </div>
            
            <div class="empty-preview" v-else>
              <el-empty description="填写左侧表单，点击生成AI简历即可预览">
                <template #image>
                  <el-icon :size="60"><MagicStick /></el-icon>
                </template>
              </el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import MainLayout from '@/layouts/MainLayout.vue'
import { useResumeStore } from '@/stores/resume'

const router = useRouter()
const resumeStore = useResumeStore()

// 表单数据
const formData = reactive({
  targetPosition: '',
  experience: '',
  education: '',
  workExperience: '',
  projects: '',
  skills: '',
  traits: ''
})

// 生成状态
const loading = ref(false)
const resumeGenerated = ref(false)
const generatedResume = ref([])

// 生成简历
const generateResume = () => {
  // 验证基本表单
  if (!formData.targetPosition || !formData.education) {
    ElMessage.warning('请至少填写目标职位和教育背景')
    return
  }
  
  loading.value = true
  
  // 模拟AI生成过程
  setTimeout(() => {
    generatedResume.value = generateAIContent()
    resumeGenerated.value = true
    loading.value = false
    ElMessage.success('简历生成成功!')
  }, 2000)
}

// 根据用户输入生成简历内容
const generateAIContent = () => {
  // 实际应用中，这里应该调用AI API进行生成
  // 这里使用简单的模拟数据
  return [
    {
      title: '个人简介',
      content: `作为一名${formData.experience === 'fresh' ? '应届毕业生' : `拥有${formData.experience}年经验的${formData.targetPosition}`}，具备扎实的专业知识和实践经验。${formData.traits || '善于学习新技术，具有良好的团队合作能力和沟通能力，能够高效完成工作任务。'}`
    },
    {
      title: '教育背景',
      content: formatEducation(formData.education)
    },
    {
      title: '工作经历',
      content: formData.workExperience || '暂无工作经历'
    },
    {
      title: '项目经验',
      content: formData.projects || '暂无项目经验'
    },
    {
      title: '专业技能',
      content: formatSkills(formData.skills)
    }
  ]
}

// 格式化教育背景
const formatEducation = (education) => {
  if (!education) return '暂无教育背景'
  
  // 简单处理，实际应用中可以更复杂
  const parts = education.split('，')
  if (parts.length >= 3) {
    return `<strong>${parts[0]}</strong><br>${parts[1]}，${parts[2]}${parts[3] ? '，' + parts[3] : ''}`
  }
  return education
}

// 格式化技能
const formatSkills = (skills) => {
  if (!skills) return '暂无技能信息'
  
  // 将技能分条显示
  return skills.split('，').map(skill => `• ${skill.trim()}`).join('<br>')
}

// 格式化内容（将换行符转换为HTML）
const formatContent = (content) => {
  return content.replace(/\n/g, '<br>')
}

// 重新生成简历
const regenerateResume = () => {
  generateResume()
}

// 使用生成的简历
const useGeneratedResume = () => {
  // 创建一个新的简历对象
  const newResume = resumeStore.createEmptyResume()
  
  // 填充AI生成的内容
  newResume.name = `${formData.targetPosition}简历`
  
  // 基本信息
  newResume.modules.basic.name = '您的姓名' // 用户需要自行填写
  newResume.modules.basic.phone = '请填写您的电话'
  newResume.modules.basic.email = '请填写您的邮箱'
  
  // 自我评价
  const introduction = generatedResume.value.find(section => section.title === '个人简介')
  if (introduction) {
    newResume.modules.selfEvaluation = introduction.content
  }
  
  // 教育经历
  const education = generatedResume.value.find(section => section.title === '教育背景')
  if (education && education.content !== '暂无教育背景') {
    const eduParts = formData.education.split('，')
    newResume.modules.education.push({
      school: eduParts[0] || '',
      major: eduParts[1] || '',
      degree: eduParts[2] || '',
      startDate: eduParts[3]?.split('-')?.[0] || '',
      endDate: eduParts[3]?.split('-')?.[1] || '',
      description: ''
    })
  }
  
  // 工作经历
  if (formData.workExperience) {
    // 简单处理工作经历，实际项目中可以使用更复杂的解析
    const workExp = formData.workExperience.split('\n')
    
    if (workExp.length > 0) {
      newResume.modules.work.push({
        company: workExp[0] || '公司名称',
        position: formData.targetPosition || '职位名称',
        startDate: '2020',
        endDate: '至今',
        description: formData.workExperience
      })
    }
  }
  
  // 项目经历
  if (formData.projects) {
    // 简单处理项目经历，实际项目中可以使用更复杂的解析
    const projects = formData.projects.split('\n')
    
    if (projects.length > 0) {
      newResume.modules.projects.push({
        name: projects[0] || '项目名称',
        role: '开发者',
        startDate: '2020',
        endDate: '至今',
        description: formData.projects
      })
    }
  }
  
  // 技能特长
  if (formData.skills) {
    formData.skills.split('、').forEach((skill, index) => {
      newResume.modules.skills.push({
        name: skill.trim(),
        level: 4
      })
    })
    
    // 如果没有找到分隔符，使用逗号分隔
    if (newResume.modules.skills.length === 0) {
      formData.skills.split('，').forEach((skill, index) => {
        newResume.modules.skills.push({
          name: skill.trim(),
          level: 4
        })
      })
    }
  }
  
  // 保存到store
  resumeStore.setCurrentResume(newResume)
  
  // 跳转到编辑器
  router.push('/editor/new')
  ElMessage.success('简历已创建，请继续完善内容')
}
</script>

<style scoped>
.ai-resume-page {
  padding: 40px 0 60px;
  background-color: #f5f9fc;
  min-height: calc(100vh - 60px);
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 10px;
  color: #333;
}

.page-subtitle {
  font-size: 16px;
  color: #666;
}

.ai-content {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.ai-input-section {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
}

.ai-output-section {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  min-height: 600px;
  display: flex;
  flex-direction: column;
}

.section-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.section-header p {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.preview-actions {
  display: flex;
  gap: 10px;
}

.ai-form {
  padding: 10px;
}

.tips {
  margin-top: 10px;
  font-size: 12px;
  color: #999;
}

.empty-preview {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
}

.resume-preview {
  flex: 1;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 20px;
  background-color: #fcfcfc;
  overflow-y: auto;
}

.resume-content {
  max-width: 700px;
  margin: 0 auto;
}

.resume-section {
  margin-bottom: 20px;
}

.resume-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #3498db;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
}

@media (max-width: 992px) {
  .ai-content {
    flex-direction: column;
  }
  
  .ai-output-section {
    min-height: 400px;
  }
}
</style> 