package com.bimowu.resume.common.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.resume.entity.ResumeInterest;
import com.bimowu.resume.vo.ResumeInterestVo;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * <p>
 * 兴趣爱好表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface ResumeInterestMapper extends BaseMapper<ResumeInterest> {

    ResumeInterestVo selectByResumeId(@Param("resumeId") Long resumeId);
}
