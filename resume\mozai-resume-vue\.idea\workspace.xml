<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1a8fe484-9e69-4dd4-acd5-cc6c15d20322" name="Changes" comment="上产域名" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="Security Analysis" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2xflv4eRhwq9d8f2S34zAuecV1M" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/IdeaProjects/sso/resume/mozai-resume-vue&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\program files (x86)\\IntelliJ IDEA 2025.1\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-vue\public\images" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1a8fe484-9e69-4dd4-acd5-cc6c15d20322" name="Changes" comment="" />
      <created>1748337236494</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748337236494</updated>
      <workItem from="1748337237619" duration="118000" />
      <workItem from="1748344937467" duration="1879000" />
      <workItem from="1749890457855" duration="2489000" />
      <workItem from="1750405099399" duration="701000" />
      <workItem from="1750406466409" duration="3075000" />
      <workItem from="1750411967243" duration="977000" />
      <workItem from="1750417581565" duration="6841000" />
      <workItem from="1750493603254" duration="761000" />
      <workItem from="1750647036472" duration="4659000" />
      <workItem from="1750729568162" duration="1000" />
      <workItem from="1750843911461" duration="2759000" />
      <workItem from="1750891538861" duration="3057000" />
      <workItem from="1750936021106" duration="604000" />
      <workItem from="1750946020821" duration="14000" />
      <workItem from="1751281146566" duration="1821000" />
      <workItem from="1751334784061" duration="10762000" />
      <workItem from="1751421970292" duration="12942000" />
      <workItem from="1751508034696" duration="9683000" />
      <workItem from="1751528269334" duration="1897000" />
      <workItem from="1751594182178" duration="598000" />
      <workItem from="1751619654554" duration="913000" />
      <workItem from="1751958914550" duration="4170000" />
      <workItem from="1751965888337" duration="5598000" />
      <workItem from="1752026971098" duration="2735000" />
      <workItem from="1752225934558" duration="2558000" />
      <workItem from="1752281912781" duration="15533000" />
      <workItem from="1752461248325" duration="3897000" />
      <workItem from="1752492109605" duration="6985000" />
      <workItem from="1752571052278" duration="3770000" />
      <workItem from="1752732419141" duration="4964000" />
      <workItem from="1752811364300" duration="12612000" />
      <workItem from="1752978720701" duration="7438000" />
      <workItem from="1753152026233" duration="5774000" />
      <workItem from="1753237333822" duration="31862000" />
      <workItem from="1753669242760" duration="89000" />
      <workItem from="1753755477485" duration="1062000" />
      <workItem from="1753757478954" duration="11000" />
      <workItem from="1753771357117" duration="12065000" />
      <workItem from="1754273711117" duration="11261000" />
      <workItem from="1754449968536" duration="8738000" />
      <workItem from="1754910615132" duration="14264000" />
      <workItem from="1755136681437" duration="2316000" />
      <workItem from="1755483713993" duration="3503000" />
    </task>
    <task id="LOCAL-00140" summary="分页优化">
      <option name="closed" value="true" />
      <created>1753252681240</created>
      <option name="number" value="00140" />
      <option name="presentableId" value="LOCAL-00140" />
      <option name="project" value="LOCAL" />
      <updated>1753252681240</updated>
    </task>
    <task id="LOCAL-00141" summary="分页优化1">
      <option name="closed" value="true" />
      <created>1753253155503</created>
      <option name="number" value="00141" />
      <option name="presentableId" value="LOCAL-00141" />
      <option name="project" value="LOCAL" />
      <updated>1753253155503</updated>
    </task>
    <task id="LOCAL-00142" summary="分页优化2">
      <option name="closed" value="true" />
      <created>1753256077840</created>
      <option name="number" value="00142" />
      <option name="presentableId" value="LOCAL-00142" />
      <option name="project" value="LOCAL" />
      <updated>1753256077840</updated>
    </task>
    <task id="LOCAL-00143" summary="分页优化3">
      <option name="closed" value="true" />
      <created>1753256703023</created>
      <option name="number" value="00143" />
      <option name="presentableId" value="LOCAL-00143" />
      <option name="project" value="LOCAL" />
      <updated>1753256703023</updated>
    </task>
    <task id="LOCAL-00144" summary="分页优化3">
      <option name="closed" value="true" />
      <created>1753256935001</created>
      <option name="number" value="00144" />
      <option name="presentableId" value="LOCAL-00144" />
      <option name="project" value="LOCAL" />
      <updated>1753256935001</updated>
    </task>
    <task id="LOCAL-00145" summary="回滚到正常的代码">
      <option name="closed" value="true" />
      <created>1753257276935</created>
      <option name="number" value="00145" />
      <option name="presentableId" value="LOCAL-00145" />
      <option name="project" value="LOCAL" />
      <updated>1753257276935</updated>
    </task>
    <task id="LOCAL-00146" summary="错别字需改">
      <option name="closed" value="true" />
      <created>1753257554332</created>
      <option name="number" value="00146" />
      <option name="presentableId" value="LOCAL-00146" />
      <option name="project" value="LOCAL" />
      <updated>1753257554332</updated>
    </task>
    <task id="LOCAL-00147" summary="使用vue-pdf解决方案">
      <option name="closed" value="true" />
      <created>1753260747041</created>
      <option name="number" value="00147" />
      <option name="presentableId" value="LOCAL-00147" />
      <option name="project" value="LOCAL" />
      <updated>1753260747041</updated>
    </task>
    <task id="LOCAL-00148" summary="使用vue-pdf解决方案">
      <option name="closed" value="true" />
      <created>1753261294288</created>
      <option name="number" value="00148" />
      <option name="presentableId" value="LOCAL-00148" />
      <option name="project" value="LOCAL" />
      <updated>1753261294288</updated>
    </task>
    <task id="LOCAL-00149" summary="使用vue-pdf解决方案-优化，按行分页">
      <option name="closed" value="true" />
      <created>1753261659131</created>
      <option name="number" value="00149" />
      <option name="presentableId" value="LOCAL-00149" />
      <option name="project" value="LOCAL" />
      <updated>1753261659131</updated>
    </task>
    <task id="LOCAL-00150" summary="回滚方案">
      <option name="closed" value="true" />
      <created>1753263794243</created>
      <option name="number" value="00150" />
      <option name="presentableId" value="LOCAL-00150" />
      <option name="project" value="LOCAL" />
      <updated>1753263794243</updated>
    </task>
    <task id="LOCAL-00151" summary="优化截断方案">
      <option name="closed" value="true" />
      <created>1753264290698</created>
      <option name="number" value="00151" />
      <option name="presentableId" value="LOCAL-00151" />
      <option name="project" value="LOCAL" />
      <updated>1753264290698</updated>
    </task>
    <task id="LOCAL-00152" summary="优化-第二页空内容问题">
      <option name="closed" value="true" />
      <created>1753265315392</created>
      <option name="number" value="00152" />
      <option name="presentableId" value="LOCAL-00152" />
      <option name="project" value="LOCAL" />
      <updated>1753265315392</updated>
    </task>
    <task id="LOCAL-00153" summary="切换生成方案-vue-html2pdf">
      <option name="closed" value="true" />
      <created>1753268554829</created>
      <option name="number" value="00153" />
      <option name="presentableId" value="LOCAL-00153" />
      <option name="project" value="LOCAL" />
      <updated>1753268554829</updated>
    </task>
    <task id="LOCAL-00154" summary="切换生成方案-vue-html2pdf-优化">
      <option name="closed" value="true" />
      <created>1753323206658</created>
      <option name="number" value="00154" />
      <option name="presentableId" value="LOCAL-00154" />
      <option name="project" value="LOCAL" />
      <updated>1753323206658</updated>
    </task>
    <task id="LOCAL-00155" summary="切换html2pdf">
      <option name="closed" value="true" />
      <created>1753324373128</created>
      <option name="number" value="00155" />
      <option name="presentableId" value="LOCAL-00155" />
      <option name="project" value="LOCAL" />
      <updated>1753324373128</updated>
    </task>
    <task id="LOCAL-00156" summary="切换html2pdf--优化">
      <option name="closed" value="true" />
      <created>1753325407843</created>
      <option name="number" value="00156" />
      <option name="presentableId" value="LOCAL-00156" />
      <option name="project" value="LOCAL" />
      <updated>1753325407843</updated>
    </task>
    <task id="LOCAL-00157" summary="切换html2pdf--优化2">
      <option name="closed" value="true" />
      <created>1753326429818</created>
      <option name="number" value="00157" />
      <option name="presentableId" value="LOCAL-00157" />
      <option name="project" value="LOCAL" />
      <updated>1753326429818</updated>
    </task>
    <task id="LOCAL-00158" summary="切换html2pdf--优化3">
      <option name="closed" value="true" />
      <created>1753327500323</created>
      <option name="number" value="00158" />
      <option name="presentableId" value="LOCAL-00158" />
      <option name="project" value="LOCAL" />
      <updated>1753327500323</updated>
    </task>
    <task id="LOCAL-00159" summary="切换-后端下载">
      <option name="closed" value="true" />
      <created>1753331725605</created>
      <option name="number" value="00159" />
      <option name="presentableId" value="LOCAL-00159" />
      <option name="project" value="LOCAL" />
      <updated>1753331725605</updated>
    </task>
    <task id="LOCAL-00160" summary="切换-后端下载-优化">
      <option name="closed" value="true" />
      <created>1753340601510</created>
      <option name="number" value="00160" />
      <option name="presentableId" value="LOCAL-00160" />
      <option name="project" value="LOCAL" />
      <updated>1753340601510</updated>
    </task>
    <task id="LOCAL-00161" summary="切换-后端下载-优化">
      <option name="closed" value="true" />
      <created>1753347686441</created>
      <option name="number" value="00161" />
      <option name="presentableId" value="LOCAL-00161" />
      <option name="project" value="LOCAL" />
      <updated>1753347686441</updated>
    </task>
    <task id="LOCAL-00162" summary="切换-后端下载-kiro写的一大段代码">
      <option name="closed" value="true" />
      <created>1753773950698</created>
      <option name="number" value="00162" />
      <option name="presentableId" value="LOCAL-00162" />
      <option name="project" value="LOCAL" />
      <updated>1753773950698</updated>
    </task>
    <task id="LOCAL-00163" summary="切换-后端下载-kiro写的一大段代码">
      <option name="closed" value="true" />
      <created>1753774967220</created>
      <option name="number" value="00163" />
      <option name="presentableId" value="LOCAL-00163" />
      <option name="project" value="LOCAL" />
      <updated>1753774967221</updated>
    </task>
    <task id="LOCAL-00164" summary="切换-后端下载-kiro写的一大段代码">
      <option name="closed" value="true" />
      <created>1753775542698</created>
      <option name="number" value="00164" />
      <option name="presentableId" value="LOCAL-00164" />
      <option name="project" value="LOCAL" />
      <updated>1753775542698</updated>
    </task>
    <task id="LOCAL-00165" summary="切换-后端下载-kiro写的一大段代码">
      <option name="closed" value="true" />
      <created>1753787273278</created>
      <option name="number" value="00165" />
      <option name="presentableId" value="LOCAL-00165" />
      <option name="project" value="LOCAL" />
      <updated>1753787273279</updated>
    </task>
    <task id="LOCAL-00166" summary="删除没用的文档">
      <option name="closed" value="true" />
      <created>1753930551422</created>
      <option name="number" value="00166" />
      <option name="presentableId" value="LOCAL-00166" />
      <option name="project" value="LOCAL" />
      <updated>1753930551422</updated>
    </task>
    <task id="LOCAL-00167" summary="优化下载交互体验">
      <option name="closed" value="true" />
      <created>1754276256285</created>
      <option name="number" value="00167" />
      <option name="presentableId" value="LOCAL-00167" />
      <option name="project" value="LOCAL" />
      <updated>1754276256285</updated>
    </task>
    <task id="LOCAL-00168" summary="优化下载交互体验">
      <option name="closed" value="true" />
      <created>1754276855560</created>
      <option name="number" value="00168" />
      <option name="presentableId" value="LOCAL-00168" />
      <option name="project" value="LOCAL" />
      <updated>1754276855560</updated>
    </task>
    <task id="LOCAL-00169" summary="优化下载交互体验">
      <option name="closed" value="true" />
      <created>1754277088502</created>
      <option name="number" value="00169" />
      <option name="presentableId" value="LOCAL-00169" />
      <option name="project" value="LOCAL" />
      <updated>1754277088502</updated>
    </task>
    <task id="LOCAL-00170" summary="优化预览页面交互">
      <option name="closed" value="true" />
      <created>1754280322362</created>
      <option name="number" value="00170" />
      <option name="presentableId" value="LOCAL-00170" />
      <option name="project" value="LOCAL" />
      <updated>1754280322362</updated>
    </task>
    <task id="LOCAL-00171" summary="优化预览页面交互">
      <option name="closed" value="true" />
      <created>1754284194497</created>
      <option name="number" value="00171" />
      <option name="presentableId" value="LOCAL-00171" />
      <option name="project" value="LOCAL" />
      <updated>1754284194497</updated>
    </task>
    <task id="LOCAL-00172" summary="简历编辑页面优化">
      <option name="closed" value="true" />
      <created>1754285816206</created>
      <option name="number" value="00172" />
      <option name="presentableId" value="LOCAL-00172" />
      <option name="project" value="LOCAL" />
      <updated>1754285816206</updated>
    </task>
    <task id="LOCAL-00173" summary="优化">
      <option name="closed" value="true" />
      <created>1754380560653</created>
      <option name="number" value="00173" />
      <option name="presentableId" value="LOCAL-00173" />
      <option name="project" value="LOCAL" />
      <updated>1754380560653</updated>
    </task>
    <task id="LOCAL-00174" summary="兴趣爱好、证书编辑主键回传">
      <option name="closed" value="true" />
      <created>1754451819355</created>
      <option name="number" value="00174" />
      <option name="presentableId" value="LOCAL-00174" />
      <option name="project" value="LOCAL" />
      <updated>1754451819356</updated>
    </task>
    <task id="LOCAL-00175" summary="模板2-去掉头部灰色线条">
      <option name="closed" value="true" />
      <created>1754472342521</created>
      <option name="number" value="00175" />
      <option name="presentableId" value="LOCAL-00175" />
      <option name="project" value="LOCAL" />
      <updated>1754472342521</updated>
    </task>
    <task id="LOCAL-00176" summary="模板2-主修课程与内容不换行">
      <option name="closed" value="true" />
      <created>1754474658758</created>
      <option name="number" value="00176" />
      <option name="presentableId" value="LOCAL-00176" />
      <option name="project" value="LOCAL" />
      <updated>1754474658758</updated>
    </task>
    <task id="LOCAL-00177" summary="隐藏逻辑优化">
      <option name="closed" value="true" />
      <created>1754556675570</created>
      <option name="number" value="00177" />
      <option name="presentableId" value="LOCAL-00177" />
      <option name="project" value="LOCAL" />
      <updated>1754556675570</updated>
    </task>
    <task id="LOCAL-00178" summary="隐藏逻辑优化--清空id">
      <option name="closed" value="true" />
      <created>1754558038536</created>
      <option name="number" value="00178" />
      <option name="presentableId" value="LOCAL-00178" />
      <option name="project" value="LOCAL" />
      <updated>1754558038536</updated>
    </task>
    <task id="LOCAL-00179" summary="隐藏逻辑优化--清空id=---回滚">
      <option name="closed" value="true" />
      <created>1754558559966</created>
      <option name="number" value="00179" />
      <option name="presentableId" value="LOCAL-00179" />
      <option name="project" value="LOCAL" />
      <updated>1754558559966</updated>
    </task>
    <task id="LOCAL-00180" summary="个人评价回显">
      <option name="closed" value="true" />
      <created>1754562978032</created>
      <option name="number" value="00180" />
      <option name="presentableId" value="LOCAL-00180" />
      <option name="project" value="LOCAL" />
      <updated>1754562978032</updated>
    </task>
    <task id="LOCAL-00181" summary="模板4--校园经历、兴趣爱好">
      <option name="closed" value="true" />
      <created>1754910636532</created>
      <option name="number" value="00181" />
      <option name="presentableId" value="LOCAL-00181" />
      <option name="project" value="LOCAL" />
      <updated>1754910636532</updated>
    </task>
    <task id="LOCAL-00182" summary="模板5 主修课程展示优化">
      <option name="closed" value="true" />
      <created>1754985305620</created>
      <option name="number" value="00182" />
      <option name="presentableId" value="LOCAL-00182" />
      <option name="project" value="LOCAL" />
      <updated>1754985305620</updated>
    </task>
    <task id="LOCAL-00183" summary="模板5 主修课程展示优化">
      <option name="closed" value="true" />
      <created>1754990974461</created>
      <option name="number" value="00183" />
      <option name="presentableId" value="LOCAL-00183" />
      <option name="project" value="LOCAL" />
      <updated>1754990974461</updated>
    </task>
    <task id="LOCAL-00184" summary="练手项目地址">
      <option name="closed" value="true" />
      <created>1755054348160</created>
      <option name="number" value="00184" />
      <option name="presentableId" value="LOCAL-00184" />
      <option name="project" value="LOCAL" />
      <updated>1755054348160</updated>
    </task>
    <task id="LOCAL-00185" summary="删除不需要的模板">
      <option name="closed" value="true" />
      <created>1755065999194</created>
      <option name="number" value="00185" />
      <option name="presentableId" value="LOCAL-00185" />
      <option name="project" value="LOCAL" />
      <updated>1755065999195</updated>
    </task>
    <task id="LOCAL-00186" summary="替换缩略图">
      <option name="closed" value="true" />
      <created>1755066519916</created>
      <option name="number" value="00186" />
      <option name="presentableId" value="LOCAL-00186" />
      <option name="project" value="LOCAL" />
      <updated>1755066519916</updated>
    </task>
    <task id="LOCAL-00187" summary="暂时删除颜色可选择功能">
      <option name="closed" value="true" />
      <created>1755067591058</created>
      <option name="number" value="00187" />
      <option name="presentableId" value="LOCAL-00187" />
      <option name="project" value="LOCAL" />
      <updated>1755067591058</updated>
    </task>
    <task id="LOCAL-00188" summary="上产域名">
      <option name="closed" value="true" />
      <created>1755239368323</created>
      <option name="number" value="00188" />
      <option name="presentableId" value="LOCAL-00188" />
      <option name="project" value="LOCAL" />
      <updated>1755239368323</updated>
    </task>
    <option name="localTasksCounter" value="189" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="3fde9a9f-e306-4606-977f-ebb94885f0f7" value="TOOL_WINDOW" />
        <entry key="aad039dd-943c-4687-b466-4501bae5c741" value="TOOL_WINDOW" />
        <entry key="a1ba9d91-f6e1-4600-9298-4182c5caafb1" value="TOOL_WINDOW" />
        <entry key="483632fd-6d8c-44f7-b25b-f80e951ac85d" value="TOOL_WINDOW" />
        <entry key="a06d7ced-eab3-42bf-90b0-2b8103d1e28b" value="TOOL_WINDOW" />
        <entry key="2495baa7-b954-409a-9fdb-36b17272770e" value="TOOL_WINDOW" />
        <entry key="6cc9b71b-a26d-46a8-b2ed-c01b7049d7be" value="TOOL_WINDOW" />
        <entry key="6ace8c5a-e3f1-4e51-87c8-ef9ed374dcfa" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="2495baa7-b954-409a-9fdb-36b17272770e">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="3fde9a9f-e306-4606-977f-ebb94885f0f7">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="483632fd-6d8c-44f7-b25b-f80e951ac85d">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="6ace8c5a-e3f1-4e51-87c8-ef9ed374dcfa">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="6cc9b71b-a26d-46a8-b2ed-c01b7049d7be">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
        <entry key="a06d7ced-eab3-42bf-90b0-2b8103d1e28b">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="a1ba9d91-f6e1-4600-9298-4182c5caafb1">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="aad039dd-943c-4687-b466-4501bae5c741">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="切换html2pdf--优化" />
    <MESSAGE value="切换html2pdf--优化2" />
    <MESSAGE value="切换html2pdf--优化3" />
    <MESSAGE value="切换-后端下载" />
    <MESSAGE value="切换-后端下载-优化" />
    <MESSAGE value="切换-后端下载-kiro写的一大段代码" />
    <MESSAGE value="删除没用的文档" />
    <MESSAGE value="优化下载交互体验" />
    <MESSAGE value="优化预览页面交互" />
    <MESSAGE value="简历编辑页面优化" />
    <MESSAGE value="优化" />
    <MESSAGE value="兴趣爱好、证书编辑主键回传" />
    <MESSAGE value="模板2-去掉头部灰色线条" />
    <MESSAGE value="模板2-主修课程与内容不换行" />
    <MESSAGE value="隐藏逻辑优化" />
    <MESSAGE value="隐藏逻辑优化--清空id" />
    <MESSAGE value="隐藏逻辑优化--清空id=---回滚" />
    <MESSAGE value="个人评价回显" />
    <MESSAGE value="模板4--校园经历、兴趣爱好" />
    <MESSAGE value="模板5 主修课程展示优化" />
    <MESSAGE value="练手项目地址" />
    <MESSAGE value="删除不需要的模板" />
    <MESSAGE value="替换缩略图" />
    <MESSAGE value="暂时删除颜色可选择功能" />
    <MESSAGE value="上产域名" />
    <option name="LAST_COMMIT_MESSAGE" value="上产域名" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/vite.config.js</url>
          <line>16</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/api/index.js</url>
          <line>7</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/views/UserCenter.vue</url>
          <line>44</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>