package com.bimowu.interview.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bimowu.interview.model.Interview;
import com.bimowu.interview.model.InterviewTranscription;
import com.bimowu.interview.model.ResumeProjectQuestion;
import com.bimowu.interview.service.InterviewService;
import com.bimowu.interview.service.InterviewTranscriptionService;
import com.bimowu.interview.service.ResumeProjectQuestionService;
import com.bimowu.interview.service.impl.AiManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 面试分析定时任务
 * 支持分布式集群部署
 */
@Component
@Slf4j
public class InterviewAnalysisTask {

    @Autowired
    private InterviewService interviewService;

    @Autowired
    private InterviewTranscriptionService transcriptionService;

    @Autowired
    private ResumeProjectQuestionService resumeProjectQuestionService;

    @Autowired
    private AiManager aiManager;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 定时分析模拟面试结果
     * 每30分钟执行一次
     * 使用Redisson分布式锁确保在集群环境中只有一个实例执行任务
     */
    @Scheduled(fixedRate = 3 * 60 * 1000) // 每30分钟执行一次
    public void analyzeInterviews() {
        // 创建分布式锁
        RLock lock = redissonClient.getLock("interview:analysis:task:lock");
        
        try {
            // 尝试获取锁，等待5秒，锁过期时间10分钟
            boolean acquired = lock.tryLock(5, 10 * 60, TimeUnit.SECONDS);
            
            if (acquired) {
                try {
                    log.info("开始执行面试分析定时任务");
                    
                    // 查询所有状态为1（已上传视频）且类型为mock的面试记录
                    LambdaQueryWrapper<Interview> queryWrapper = new LambdaQueryWrapper<Interview>()
                            .eq(Interview::getStatus, 1)
                            .eq(Interview::getType, "mock");
                    
                    List<Interview> interviewList = interviewService.list(queryWrapper);
                    log.info("找到{}条待分析的模拟面试记录", interviewList.size());
                    
                    // 遍历处理每条记录
                    for (Interview interview : interviewList) {
                        try {
                            processInterview(interview);
                        } catch (Exception e) {
                            log.error("处理面试记录{}时发生异常", interview.getId(), e);
                        }
                    }
                    
                    log.info("面试分析定时任务执行完成");
                } finally {
                    // 释放锁
                    lock.unlock();
                }
            } else {
                log.info("未能获取到面试分析任务锁，可能其他服务实例正在执行");
            }
        } catch (InterruptedException e) {
            log.error("获取分布式锁时被中断", e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("面试分析定时任务执行异常", e);
        }
    }
    
    /**
     * 处理单个面试记录
     * 
     * @param interview 面试记录
     */
    private void processInterview(Interview interview) {
        String interviewId = interview.getId();
        log.info("开始处理面试{}的AI分析", interviewId);
        
        try {
            // 获取面试转写记录
            LambdaQueryWrapper<InterviewTranscription> wrapper = new LambdaQueryWrapper<InterviewTranscription>()
                    .eq(InterviewTranscription::getInterviewId, interviewId);
            List<InterviewTranscription> transcriptions = transcriptionService.list(wrapper);
            
            if (transcriptions == null || transcriptions.isEmpty()) {
                log.warn("面试{}没有转写记录，跳过分析", interviewId);
                return;
            }
            
            // 提取问题和转写内容
            List<String> questions = new ArrayList<>();
            List<String> transcription = new ArrayList<>();
            List<String> answer = new ArrayList<>();
            
            for (InterviewTranscription t : transcriptions) {
                questions.add(t.getQuestion());
                transcription.add(t.getTranscription());
                
                // 获取标准答案
                LambdaQueryWrapper<ResumeProjectQuestion> questionWrapper = new LambdaQueryWrapper<>();
                questionWrapper.eq(ResumeProjectQuestion::getQuestion, t.getQuestion());
                ResumeProjectQuestion questionEntity = resumeProjectQuestionService.getOne(questionWrapper);
                
                if (questionEntity != null) {
                    answer.add(questionEntity.getAnswer());
                } else {
                    answer.add(""); // 如果没有标准答案，添加空字符串
                }
            }
            
            // 调用AI进行分析
            String result = aiManager.doChat(questions, transcription, answer);
            log.info("面试{}的AI分析处理完成: {}", interviewId, result);
            
            // 解析AI分析结果
            String[] parts = result.split("\\|");
            
            // 更新面试记录
            if (parts.length >= 1 && StringUtils.isNotBlank(parts[0])) {
                String feedback = parts[0].split("：")[1];
                interview.setFeedback(feedback);
            }
            
            if (parts.length >= 2 && StringUtils.isNotBlank(parts[1])) {
                String overallScore = parts[1].split("：")[1].trim();
                interview.setOverallScore(Integer.parseInt(overallScore));
            }
            
            if (parts.length >= 3 && StringUtils.isNotBlank(parts[2])) {
                String strengths = parts[2].split("：")[1].trim();
                interview.setStrengths(strengths);
            }
            
            if (parts.length >= 4 && StringUtils.isNotBlank(parts[3])) {
                String improvements = parts[3].split("：")[1].trim();
                interview.setImprovements(improvements);
            }
            // 更新状态为已完成
            interview.setStatus(2);
            //如果为hr面试，则需要人工再次审核，所以状态还是等待结果
            interview.setStatus(1);
            interviewService.updateById(interview);
            log.info("面试{}的分析结果已更新", interviewId);
        } catch (Exception e) {
            log.error("分析面试{}时发生异常", interviewId, e);
        }
    }
} 