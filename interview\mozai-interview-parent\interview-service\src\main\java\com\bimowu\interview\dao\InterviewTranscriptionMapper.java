package com.bimowu.interview.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.interview.model.InterviewTranscription;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 面试转写记录Mapper接口
 */
@Mapper
public interface InterviewTranscriptionMapper extends BaseMapper<InterviewTranscription> {

    /**
     * 插入面试转写记录
     * 
     * @param transcription 转写记录
     * @return 影响的行数
     */
    int insert(InterviewTranscription transcription);
    
    /**
     * 批量插入面试转写记录
     * 
     * @param transcriptions 转写记录列表
     * @return 影响的行数
     */
    int batchInsert(List<InterviewTranscription> transcriptions);
    
    /**
     * 更新面试转写记录
     * 
     * @param transcription 转写记录
     * @return 影响的行数
     */
    int update(InterviewTranscription transcription);
    
    /**
     * 根据ID查询面试转写记录
     * 
     * @param id 记录ID
     * @return 转写记录
     */
    InterviewTranscription selectById(@Param("id") Long id);
    
    /**
     * 根据面试ID和问题索引查询面试转写记录
     * 
     * @param interviewId 面试ID
     * @param questionIndex 问题索引
     * @return 转写记录
     */
    InterviewTranscription selectByInterviewIdAndQuestionIndex(
            @Param("interviewId") String interviewId, 
            @Param("questionIndex") Integer questionIndex);
    
    /**
     * 根据面试ID查询所有面试转写记录
     * 
     * @param interviewId 面试ID
     * @return 转写记录列表
     */
    List<InterviewTranscription> selectByInterviewId(@Param("interviewId") String interviewId);
    
    /**
     * 根据面试ID查询转写记录，并按问题索引分组
     * 
     * @param interviewId 面试ID
     * @return 问题索引到转写记录的映射
     */
    List<InterviewTranscription> selectMapByInterviewId(@Param("interviewId") String interviewId);
    
    /**
     * 根据条件查询面试转写记录
     * 
     * @param params 查询条件
     * @return 转写记录列表
     */
    List<InterviewTranscription> selectByCondition(Map<String, Object> params);
    
    /**
     * 根据ID删除面试转写记录
     * 
     * @param id 记录ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据面试ID删除面试转写记录
     * 
     * @param interviewId 面试ID
     * @return 影响的行数
     */
    int deleteByInterviewId(@Param("interviewId") String interviewId);
} 