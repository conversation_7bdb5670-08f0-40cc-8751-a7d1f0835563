# 字体文件说明

## 中文字体支持

为了确保PDF中的中文字符能够正确显示，需要在此目录下放置中文字体文件。

### 推荐字体文件

1. **simsun.ttf** - 宋体（推荐）
   - 系统路径：C:\Windows\Fonts\simsun.ttc (Windows)
   - 系统路径：/System/Library/Fonts/STSong.ttc (macOS)
   - 系统路径：/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf (Linux)

2. **msyh.ttf** - 微软雅黑
   - 系统路径：C:\Windows\Fonts\msyh.ttc (Windows)

3. **simhei.ttf** - 黑体
   - 系统路径：C:\Windows\Fonts\simhei.ttf (Windows)

### 字体文件获取方法

#### Windows系统
```bash
# 复制宋体文件
copy "C:\Windows\Fonts\simsun.ttc" src/main/resources/fonts/simsun.ttf

# 复制微软雅黑
copy "C:\Windows\Fonts\msyh.ttc" src/main/resources/fonts/msyh.ttf
```

#### Linux系统
```bash
# 安装中文字体包
sudo apt-get install fonts-wqy-zenhei fonts-wqy-microhei

# 或者下载开源字体
wget https://github.com/adobe-fonts/source-han-sans/releases/download/2.004R/SourceHanSansCN.zip
```

#### macOS系统
```bash
# 复制系统字体
cp /System/Library/Fonts/STSong.ttc src/main/resources/fonts/simsun.ttf
```

### 注意事项

1. 字体文件较大，建议只包含必要的字体
2. 确保字体文件的版权合规
3. 可以使用开源字体如思源黑体、文泉驿等
4. 字体文件应该放在 `src/main/resources/fonts/` 目录下

### 开源字体推荐

- **思源黑体**: https://github.com/adobe-fonts/source-han-sans
- **思源宋体**: https://github.com/adobe-fonts/source-han-serif
- **文泉驿微米黑**: http://wenq.org/wqy2/index.cgi?MicroHei
- **Noto Sans CJK**: https://www.google.com/get/noto/