package com.bimowu.interview.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bimowu.interview.dao.ResumeCategoryRelationMapper;
import com.bimowu.interview.model.ResumeCategoryRelation;
import com.bimowu.interview.service.ResumeCategoryRelationService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【resume_category_relation(简历与职位类别关联表)】的数据库操作Service实现
* @createDate 2025-05-28 14:42:19
*/
@Service
public class ResumeCategoryRelationServiceImpl extends ServiceImpl<ResumeCategoryRelationMapper, ResumeCategoryRelation>
implements ResumeCategoryRelationService {

}
