package com.bimowu.resume.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.SetBucketCORSRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * OSS配置类，用于初始化OSS Bucket的CORS规则
 */
@Configuration
@Slf4j
public class OssConfig implements CommandLineRunner {

    @Value("${aliyun.oss.endpoint:}")
    private String endpoint;

    @Value("${aliyun.oss.accessKeyId:}")
    private String accessKeyId;

    @Value("${aliyun.oss.accessKeySecret:}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucketName:}")
    private String bucketName;

    @Override
    public void run(String... args) {
        // 应用启动时配置OSS Bucket的CORS规则
        configureBucketCORS();
    }

    /**
     * 配置OSS Bucket的CORS规则
     */
    private void configureBucketCORS() {
        log.info("开始配置OSS Bucket的CORS规则...");
        
        // 检查OSS配置
        if (endpoint == null || endpoint.isEmpty() || 
            accessKeyId == null || accessKeyId.isEmpty() || 
            accessKeySecret == null || accessKeySecret.isEmpty() || 
            bucketName == null || bucketName.isEmpty()) {
            
            log.error("OSS配置不完整，无法配置CORS规则");
            return;
        }
        
        OSS ossClient = null;
        try {
            // 创建OSSClient实例
            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            
            // 创建CORS规则
            SetBucketCORSRequest request = new SetBucketCORSRequest(bucketName);
            
            // 创建第一条规则 - 允许所有跨域请求
            SetBucketCORSRequest.CORSRule rule1 = new SetBucketCORSRequest.CORSRule();
            rule1.addAllowdOrigin("*");
            rule1.addAllowedMethod("GET"); // 允许的HTTP方法
            rule1.addAllowedHeader("*"); // 允许所有头信息
            rule1.setMaxAgeSeconds(3600); // 预检请求的有效期
            
            // 创建第二条规则 - 专门为html2canvas和PDF导出设置
            SetBucketCORSRequest.CORSRule rule2 = new SetBucketCORSRequest.CORSRule();
            // 允许前端应用的域名
            List<String> allowedOrigins = new ArrayList<>();
            allowedOrigins.add("https://tt.bimowo.com");
            allowedOrigins.add("http://tt.bimowo.com");
            allowedOrigins.add("https://resume.bimowo.com");
            allowedOrigins.add("http://resume.bimowo.com");
            allowedOrigins.add("http://localhost:5173"); // 本地开发环境
            allowedOrigins.add("http://localhost:8080"); // 本地开发环境备用
            rule2.setAllowedOrigins(allowedOrigins);
            // 允许的HTTP方法
            rule2.addAllowedMethod("GET");
            rule2.addAllowedMethod("HEAD");
            // 允许的头信息
            rule2.addAllowedHeader("*");
            // 允许的响应头
            rule2.addExposeHeader("Content-Length");
            rule2.addExposeHeader("Content-Type");
            rule2.addExposeHeader("ETag");
            rule2.setMaxAgeSeconds(3600);
            
            // 将规则添加到请求中
            request.addCorsRule(rule1);
            request.addCorsRule(rule2);
            
            // 设置CORS规则
            ossClient.setBucketCORS(request);
            
            log.info("OSS Bucket的CORS规则配置成功");
        } catch (Exception e) {
            log.error("配置OSS Bucket的CORS规则失败: {}", e.getMessage(), e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
} 