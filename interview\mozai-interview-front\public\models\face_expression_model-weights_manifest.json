[{"weights": [{"name": "dense0/conv0/filters", "shape": [3, 3, 3, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004614637101248847, "min": -0.5999028231623501}}, {"name": "dense0/conv0/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00399490292284526, "min": -0.6791335969837942}}, {"name": "dense0/conv1/depthwise_filter", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006853456353698231, "min": -0.8912493259807701}}, {"name": "dense0/conv1/pointwise_filter", "shape": [1, 1, 32, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010509579321917366, "min": -1.3662453118492577}}, {"name": "dense0/conv1/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.002832852725459068, "min": -0.4816849633280416}}, {"name": "dense0/conv2/depthwise_filter", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005672684837790097, "min": -0.7374490289127126}}, {"name": "dense0/conv2/pointwise_filter", "shape": [1, 1, 32, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00854733496904173, "min": -1.1111535459754251}}, {"name": "dense0/conv2/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0030300481062309416, "min": -0.5757091401838789}}, {"name": "dense1/conv0/depthwise_filter", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.003915555453767957, "min": -0.48944443172099455}}, {"name": "dense1/conv0/pointwise_filter", "shape": [1, 1, 32, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.009089225896138965, "min": -1.1270640111212318}}, {"name": "dense1/conv0/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.003096127475876291, "min": -0.4149610817674229}}, {"name": "dense1/conv1/depthwise_filter", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006593170586754294, "min": -0.8966312000000001}}, {"name": "dense1/conv1/pointwise_filter", "shape": [1, 1, 64, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.011632566358528886, "min": -1.5122336266087553}}, {"name": "dense1/conv1/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.002681326550261297, "min": -0.3378471453329034}}, {"name": "dense1/conv2/depthwise_filter", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00545282506632653, "min": -0.6816031332908162}}, {"name": "dense1/conv2/pointwise_filter", "shape": [1, 1, 64, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010311148253220047, "min": -1.3404492729186057}}, {"name": "dense1/conv2/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0030966952905688645, "min": -0.4211105595173655}}, {"name": "dense2/conv0/depthwise_filter", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0037769308077079903, "min": -0.4910010050020387}}, {"name": "dense2/conv0/pointwise_filter", "shape": [1, 1, 64, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01003597572175506, "min": -1.2545999652193824}}, {"name": "dense2/conv0/bias", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0030921888114143134, "min": -0.5195677203576047}}, {"name": "dense2/conv1/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00554193468654513, "min": -0.7150796746243718}}, {"name": "dense2/conv1/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01372127832901391, "min": -1.7851661827718084}}, {"name": "dense2/conv1/bias", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0031757816776398326, "min": -0.5335313219235521}}, {"name": "dense2/conv2/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005679475136531984, "min": -0.7383317677491579}}, {"name": "dense2/conv2/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.011226850271541277, "min": -1.4371968347572834}}, {"name": "dense2/conv2/bias", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0029380727048013726, "min": -0.5288530868642471}}, {"name": "dense3/conv0/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004317825708444834, "min": -0.5785486449316078}}, {"name": "dense3/conv0/pointwise_filter", "shape": [1, 1, 128, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010051267287322281, "min": -1.3569210837885082}}, {"name": "dense3/conv0/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0031214782786875965, "min": -0.4682217417969395}}, {"name": "dense3/conv1/depthwise_filter", "shape": [3, 3, 256, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00448304671049118, "min": -0.6276265394687652}}, {"name": "dense3/conv1/pointwise_filter", "shape": [1, 1, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01413825248912772, "min": -1.8096163186083483}}, {"name": "dense3/conv1/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.003077661010371961, "min": -0.47703745660765395}}, {"name": "dense3/conv2/depthwise_filter", "shape": [3, 3, 256, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00534420241662419, "min": -0.7215673262442657}}, {"name": "dense3/conv2/pointwise_filter", "shape": [1, 1, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01412463953993865, "min": -1.8079538211121473}}, {"name": "dense3/conv2/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0034643323982463162, "min": -0.5369715217281791}}, {"name": "fc/weights", "shape": [256, 7], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004834276554631252, "min": -0.7203072066000765}}, {"name": "fc/bias", "shape": [7], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0029505149069676737, "min": 0.07683877646923065}}], "paths": ["face_expression_model-shard1"]}]