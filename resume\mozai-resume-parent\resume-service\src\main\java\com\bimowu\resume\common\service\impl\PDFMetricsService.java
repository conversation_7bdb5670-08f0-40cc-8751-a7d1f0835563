package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.config.PDFConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * PDF生成监控指标服务
 */
@Service
@Slf4j
public class PDFMetricsService {
    
    @Autowired
    private PDFConfig pdfConfig;
    
    // 基础统计指标
    private final AtomicLong totalGenerations = new AtomicLong(0);
    private final AtomicLong successfulGenerations = new AtomicLong(0);
    private final AtomicLong failedGenerations = new AtomicLong(0);
    private final AtomicLong totalDuration = new AtomicLong(0);
    private final AtomicLong maxDuration = new AtomicLong(0);
    private final AtomicLong minDuration = new AtomicLong(Long.MAX_VALUE);
    
    // 当前状态指标
    private final AtomicInteger currentConcurrency = new AtomicInteger(0);
    private final AtomicInteger maxConcurrency = new AtomicInteger(0);
    
    // 按模板统计
    private final Map<String, TemplateMetrics> templateMetrics = new ConcurrentHashMap<>();
    
    // 错误统计
    private final Map<String, AtomicLong> errorCounts = new ConcurrentHashMap<>();
    
    // 数据完整性统计
    private final AtomicLong completeDataCount = new AtomicLong(0);
    private final AtomicLong incompleteDataCount = new AtomicLong(0);
    
    // 时间统计（最近重置时间）
    private volatile long lastResetTime = System.currentTimeMillis();
    
    /**
     * 模板指标内部类
     */
    private static class TemplateMetrics {
        private final AtomicLong requests = new AtomicLong(0);
        private final AtomicLong successes = new AtomicLong(0);
        private final AtomicLong totalDuration = new AtomicLong(0);
        private final AtomicLong maxDuration = new AtomicLong(0);
        private final AtomicLong minDuration = new AtomicLong(Long.MAX_VALUE);
        
        public void recordRequest(boolean success, long duration) {
            requests.incrementAndGet();
            if (success) {
                successes.incrementAndGet();
            }
            totalDuration.addAndGet(duration);
            maxDuration.updateAndGet(max -> Math.max(max, duration));
            minDuration.updateAndGet(min -> Math.min(min, duration));
        }
        
        public double getSuccessRate() {
            long total = requests.get();
            return total > 0 ? (double) successes.get() / total * 100 : 0;
        }
        
        public double getAverageDuration() {
            long total = requests.get();
            return total > 0 ? (double) totalDuration.get() / total : 0;
        }
    }
    
    /**
     * 开始PDF生成监控
     */
    public void startGeneration(String templateId) {
        totalGenerations.incrementAndGet();
        int current = currentConcurrency.incrementAndGet();
        maxConcurrency.updateAndGet(max -> Math.max(max, current));
        
        // 初始化模板指标
        templateMetrics.computeIfAbsent(templateId, k -> new TemplateMetrics());
    }
    
    /**
     * 结束PDF生成监控
     */
    public void endGeneration(String templateId, boolean success, long duration, String errorType) {
        currentConcurrency.decrementAndGet();
        
        if (success) {
            successfulGenerations.incrementAndGet();
        } else {
            failedGenerations.incrementAndGet();
            if (errorType != null) {
                errorCounts.computeIfAbsent(errorType, k -> new AtomicLong(0)).incrementAndGet();
            }
        }
        
        // 更新时间统计
        totalDuration.addAndGet(duration);
        maxDuration.updateAndGet(max -> Math.max(max, duration));
        minDuration.updateAndGet(min -> Math.min(min, duration));
        
        // 更新模板统计
        TemplateMetrics metrics = templateMetrics.get(templateId);
        if (metrics != null) {
            metrics.recordRequest(success, duration);
        }
    }
    
    /**
     * 记录数据完整性
     */
    public void recordDataCompleteness(boolean isComplete) {
        if (isComplete) {
            completeDataCount.incrementAndGet();
        } else {
            incompleteDataCount.incrementAndGet();
        }
    }
    
    /**
     * 获取PDF生成成功率
     */
    public double getSuccessRate() {
        long total = totalGenerations.get();
        if (total == 0) return 0.0;
        return (double) successfulGenerations.get() / total * 100;
    }
    
    /**
     * 获取平均生成时间
     */
    public double getAverageGenerationTime() {
        long total = totalGenerations.get();
        if (total == 0) return 0.0;
        return (double) totalDuration.get() / total;
    }
    
    /**
     * 获取数据完整性比率
     */
    public double getDataCompletenessRate() {
        long total = completeDataCount.get() + incompleteDataCount.get();
        if (total == 0) return 0.0;
        return (double) completeDataCount.get() / total * 100;
    }
    
    /**
     * 获取当前并发数
     */
    public int getCurrentConcurrency() {
        return currentConcurrency.get();
    }
    
    /**
     * 获取最大并发数
     */
    public int getMaxConcurrency() {
        return maxConcurrency.get();
    }
    
    /**
     * 获取详细的监控报告
     */
    public String getDetailedMetricsReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("=== PDF生成监控指标报告 ===\n");
        report.append(String.format("报告时间: %s\n", new java.util.Date()));
        report.append(String.format("统计周期: %d 分钟\n", (System.currentTimeMillis() - lastResetTime) / 60000));
        report.append("\n");
        
        // 基础指标
        report.append("【基础指标】\n");
        report.append(String.format("总请求数: %d\n", totalGenerations.get()));
        report.append(String.format("成功数: %d\n", successfulGenerations.get()));
        report.append(String.format("失败数: %d\n", failedGenerations.get()));
        report.append(String.format("成功率: %.2f%%\n", getSuccessRate()));
        report.append("\n");
        
        // 性能指标
        report.append("【性能指标】\n");
        report.append(String.format("平均耗时: %.2f ms\n", getAverageGenerationTime()));
        report.append(String.format("最大耗时: %d ms\n", maxDuration.get()));
        report.append(String.format("最小耗时: %d ms\n", minDuration.get() == Long.MAX_VALUE ? 0 : minDuration.get()));
        report.append("\n");
        
        // 并发指标
        report.append("【并发指标】\n");
        report.append(String.format("当前并发: %d\n", currentConcurrency.get()));
        report.append(String.format("最大并发: %d\n", maxConcurrency.get()));
        report.append(String.format("并发限制: %d\n", pdfConfig != null ? pdfConfig.getPerformance().getMaxConcurrentGenerations() : 0));
        report.append("\n");
        
        // 数据质量指标
        report.append("【数据质量指标】\n");
        report.append(String.format("完整数据: %d\n", completeDataCount.get()));
        report.append(String.format("不完整数据: %d\n", incompleteDataCount.get()));
        report.append(String.format("数据完整率: %.2f%%\n", getDataCompletenessRate()));
        report.append("\n");
        
        // 按模板统计
        if (!templateMetrics.isEmpty()) {
            report.append("【按模板统计】\n");
            templateMetrics.forEach((templateId, metrics) -> {
                report.append(String.format("模板 %s:\n", templateId));
                report.append(String.format("  请求数: %d\n", metrics.requests.get()));
                report.append(String.format("  成功数: %d\n", metrics.successes.get()));
                report.append(String.format("  成功率: %.2f%%\n", metrics.getSuccessRate()));
                report.append(String.format("  平均耗时: %.2f ms\n", metrics.getAverageDuration()));
                report.append(String.format("  最大耗时: %d ms\n", metrics.maxDuration.get()));
            });
            report.append("\n");
        }
        
        // 错误统计
        if (!errorCounts.isEmpty()) {
            report.append("【错误统计】\n");
            errorCounts.forEach((errorType, count) -> {
                report.append(String.format("%s: %d 次\n", errorType, count.get()));
            });
            report.append("\n");
        }
        
        return report.toString();
    }
    
    /**
     * 获取JSON格式的指标数据
     */
    public Map<String, Object> getMetricsAsJson() {
        Map<String, Object> metrics = new ConcurrentHashMap<>();
        
        // 基础指标
        metrics.put("totalGenerations", totalGenerations.get());
        metrics.put("successfulGenerations", successfulGenerations.get());
        metrics.put("failedGenerations", failedGenerations.get());
        metrics.put("successRate", getSuccessRate());
        
        // 性能指标
        metrics.put("averageGenerationTime", getAverageGenerationTime());
        metrics.put("maxDuration", maxDuration.get());
        metrics.put("minDuration", minDuration.get() == Long.MAX_VALUE ? 0 : minDuration.get());
        
        // 并发指标
        metrics.put("currentConcurrency", currentConcurrency.get());
        metrics.put("maxConcurrency", maxConcurrency.get());
        
        // 数据质量指标
        metrics.put("completeDataCount", completeDataCount.get());
        metrics.put("incompleteDataCount", incompleteDataCount.get());
        metrics.put("dataCompletenessRate", getDataCompletenessRate());
        
        // 模板统计
        Map<String, Object> templateStats = new ConcurrentHashMap<>();
        templateMetrics.forEach((templateId, templateMetric) -> {
            Map<String, Object> stats = new ConcurrentHashMap<>();
            stats.put("requests", templateMetric.requests.get());
            stats.put("successes", templateMetric.successes.get());
            stats.put("successRate", templateMetric.getSuccessRate());
            stats.put("averageDuration", templateMetric.getAverageDuration());
            stats.put("maxDuration", templateMetric.maxDuration.get());
            templateStats.put(templateId, stats);
        });
        metrics.put("templateStats", templateStats);
        
        // 错误统计
        Map<String, Long> errors = new ConcurrentHashMap<>();
        errorCounts.forEach((errorType, count) -> errors.put(errorType, count.get()));
        metrics.put("errorStats", errors);
        
        // 时间信息
        metrics.put("lastResetTime", lastResetTime);
        metrics.put("reportTime", System.currentTimeMillis());
        
        return metrics;
    }
    
    /**
     * 重置所有统计指标
     */
    public void resetMetrics() {
        totalGenerations.set(0);
        successfulGenerations.set(0);
        failedGenerations.set(0);
        totalDuration.set(0);
        maxDuration.set(0);
        minDuration.set(Long.MAX_VALUE);
        
        currentConcurrency.set(0);
        maxConcurrency.set(0);
        
        templateMetrics.clear();
        errorCounts.clear();
        
        completeDataCount.set(0);
        incompleteDataCount.set(0);
        
        lastResetTime = System.currentTimeMillis();
        
        log.info("PDF生成监控指标已重置");
    }
    
    /**
     * 检查是否需要自动重置统计
     */
    public void checkAutoReset() {
        if (pdfConfig != null && pdfConfig.getPerformance().isEnableStatistics()) {
            long resetInterval = pdfConfig.getPerformance().getStatisticsResetInterval() * 1000; // 转换为毫秒
            if (System.currentTimeMillis() - lastResetTime > resetInterval) {
                log.info("达到自动重置间隔，重置统计指标");
                resetMetrics();
            }
        }
    }
    
    /**
     * 获取健康状态
     */
    public boolean isHealthy() {
        // 检查成功率
        double successRate = getSuccessRate();
        if (successRate < 90.0 && totalGenerations.get() > 10) {
            return false;
        }
        
        // 检查平均响应时间
        double avgTime = getAverageGenerationTime();
        long slowThreshold = pdfConfig != null ? pdfConfig.getPerformance().getSlowQueryThresholdMs() : 5000;
        if (avgTime > slowThreshold && totalGenerations.get() > 5) {
            return false;
        }
        
        // 检查并发情况
        int maxConcurrent = pdfConfig != null ? pdfConfig.getPerformance().getMaxConcurrentGenerations() : 10;
        if (currentConcurrency.get() >= maxConcurrent) {
            return false;
        }
        
        return true;
    }
}