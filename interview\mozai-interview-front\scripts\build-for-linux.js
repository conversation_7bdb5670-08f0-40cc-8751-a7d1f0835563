/**
 * 用于Linux构建的脚本
 * 1. 设置环境变量跳过可选依赖
 * 2. 构建项目
 *
 * 使用方法: node scripts/build-for-linux.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 设置颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

console.log(`${colors.blue}===== 开始 Linux 环境构建 =====${colors.reset}`);

try {
  // 1. 检查是否存在package.json
  const packageJsonPath = path.resolve(__dirname, '../package.json');
  if (!fs.existsSync(packageJsonPath)) {
    throw new Error('找不到package.json文件');
  }

  // 2. 修改环境变量并构建
  console.log(`${colors.yellow}正在构建项目(跳过可选依赖)...${colors.reset}`);

  // 设置环境变量并执行构建
  execSync('npm run build', {
    env: {
      ...process.env,
      SKIP_OPTIONAL_DEPENDENCIES: 'true'
    },
    stdio: 'inherit'
  });

  console.log(`${colors.green}构建成功!${colors.reset}`);
  console.log(`${colors.blue}输出目录: ${path.resolve(__dirname, '../dist-interview-ai')}${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}构建失败: ${error.message}${colors.reset}`);
  process.exit(1);
} 