package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.common.dao.SysTodoMapper;
import com.bimowu.resume.common.service.SysTodoService;
import com.bimowu.resume.entity.SysTodo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 系统待办服务实现类
 */
@Service
@Slf4j
public class SysTodoServiceImpl implements SysTodoService {

    @Autowired
    private SysTodoMapper sysTodoMapper;

    @Override
    @Transactional
    public boolean createTodo(SysTodo sysTodo) {
        log.info("创建待办事项: userId={}, resumeId={}, title={}, todoType={}", 
                sysTodo.getUserId(), sysTodo.getResumeId(), sysTodo.getTitle(), sysTodo.getTodoType());
        
        // 设置默认值
        if (sysTodo.getStatus() == null) {
            sysTodo.setStatus(0); // 默认未完成
        }
        
        // 设置默认待办类型
        if (sysTodo.getTodoType() == null) {
            sysTodo.setTodoType(0); // 默认为创建简历类型
        }
        
        // 设置时间
        Date now = new Date();
        sysTodo.setCreateTime(now);
        sysTodo.setUpdateTime(now);
        
        try {
            int result = sysTodoMapper.insert(sysTodo);
            return result > 0;
        } catch (Exception e) {
            log.error("创建待办事项失败", e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean updateTodo(SysTodo sysTodo) {
        log.info("更新待办事项: id={}, title={}", sysTodo.getId(), sysTodo.getTitle());
        
        // 设置更新时间
        sysTodo.setUpdateTime(new Date());
        
        try {
            int result = sysTodoMapper.updateByPrimaryKeySelective(sysTodo);
            return result > 0;
        } catch (Exception e) {
            log.error("更新待办事项失败", e);
            throw e;
        }
    }

    @Override
    public SysTodo getTodoById(Long id) {
        return sysTodoMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<SysTodo> getUnfinishedTodosByUserId(Long userId) {
        return sysTodoMapper.selectUnfinishedByUserId(userId);
    }
    
    @Override
    public List<SysTodo> getUnfinishedTodosByUserIdAndResumeId(Long userId, Long resumeId) {
        return sysTodoMapper.selectUnfinishedByUserIdAndResumeId(userId, resumeId);
    }

    @Override
    public List<SysTodo> getTodosByUserId(Long userId) {
        return sysTodoMapper.selectByUserId(userId);
    }
    
    @Override
    public List<SysTodo> getTodosByUserIdAndResumeId(Long userId, Long resumeId) {
        return sysTodoMapper.selectByUserIdAndResumeId(userId, resumeId);
    }

    @Override
    @Transactional
    public boolean completeTodo(Long id) {
        log.info("完成待办事项: id={}", id);
        
        try {
            SysTodo todo = new SysTodo();
            todo.setId(id);
            todo.setStatus(1); // 设置为已完成
            todo.setUpdateTime(new Date());
            
            int result = sysTodoMapper.updateByPrimaryKeySelective(todo);
            return result > 0;
        } catch (Exception e) {
            log.error("完成待办事项失败", e);
            throw e;
        }
    }
    
    @Override
    @Transactional
    public boolean completeTodoByUserIdAndType(Long userId, Integer todoType) {
        log.info("根据用户ID和待办类型更新待办事项状态: userId={}, todoType={}", userId, todoType);
        
        try {
            // 获取用户未完成的待办事项
            List<SysTodo> unfinishedTodos = getUnfinishedTodosByUserId(userId);
            boolean updated = false;
            
            if (unfinishedTodos != null && !unfinishedTodos.isEmpty()) {
                for (SysTodo todo : unfinishedTodos) {
                    // 如果待办类型匹配，则标记为已完成
                    if (todo.getTodoType() != null && todo.getTodoType().equals(todoType)) {
                        log.info("将待办事项标记为已完成，todoId: {}, title: {}", todo.getId(), todo.getTitle());
                        todo.setStatus(1); // 设置为已完成
                        todo.setUpdateTime(new Date());
                        
                        sysTodoMapper.updateByPrimaryKeySelective(todo);
                        updated = true;
                    }
                }
            }
            
            return updated;
        } catch (Exception e) {
            log.error("根据用户ID和待办类型更新待办事项状态失败", e);
            throw e;
        }
    }
    
    @Override
    @Transactional
    public boolean completeTodoByUserIdResumeIdAndType(Long userId, Long resumeId, Integer todoType) {
        log.info("根据用户ID、简历ID和待办类型更新待办事项状态: userId={}, resumeId={}, todoType={}", userId, resumeId, todoType);
        
        try {
            // 获取用户指定简历未完成的待办事项
            List<SysTodo> unfinishedTodos = getUnfinishedTodosByUserIdAndResumeId(userId, resumeId);
            boolean updated = false;
            
            if (unfinishedTodos != null && !unfinishedTodos.isEmpty()) {
                for (SysTodo todo : unfinishedTodos) {
                    // 如果待办类型匹配，则标记为已完成
                    if (todo.getTodoType() != null && todo.getTodoType().equals(todoType)) {
                        log.info("将待办事项标记为已完成，todoId: {}, title: {}", todo.getId(), todo.getTitle());
                        todo.setStatus(1); // 设置为已完成
                        todo.setUpdateTime(new Date());
                        
                        sysTodoMapper.updateByPrimaryKeySelective(todo);
                        updated = true;
                    }
                }
            }
            
            return updated;
        } catch (Exception e) {
            log.error("根据用户ID、简历ID和待办类型更新待办事项状态失败", e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean deleteByUserIdAndResumeId(Long userId, Long resumeId) {
        log.info("批量删除待办事项: userId={}, resumeId={}", userId, resumeId);
        try {
            int result = sysTodoMapper.deleteByUserIdAndResumeId(userId, resumeId);
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除待办事项失败", e);
            throw e;
        }
    }
} 