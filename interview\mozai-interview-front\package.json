{"name": "quiz-ai-interview", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview", "download-models": "node scripts/download-models.js", "build:full": "npm run download-models && npm run build", "build:linux": "SKIP_OPTIONAL_DEPENDENCIES=true npm install --no-optional && npm run build", "build:safe": "node scripts/build-for-linux.js", "build:node16": "NODE_OPTIONS='--max-old-space-size=4096' npm run build"}, "dependencies": {"@mediapipe/camera_utils": "^0.3.1", "@mediapipe/face_detection": "^0.4.1", "@types/uuid": "^9.0.0", "axios": "^1.5.0", "element-plus": "2.3.12", "face-api.js": "^0.20.0", "pinia": "^2.1.6", "quiz-ai-interview": "file:", "recordrtc": "^5.6.2", "three": "^0.160.1", "uuid": "^9.0.0", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@types/node": "^16.18.0", "@types/recordrtc": "^5.6.14", "@types/three": "^0.160.0", "@vitejs/plugin-vue": "^4.4.0", "sass": "^1.66.1", "typescript": "^4.9.5", "vite": "^4.4.9"}, "optionalDependencies": {"@types/ali-oss": "^6.16.11", "ali-oss": "^6.23.0"}}