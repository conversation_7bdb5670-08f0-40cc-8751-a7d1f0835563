<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bimowu.interview.dao.ResumeProgressMapper">
    
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.bimowu.interview.model.ResumeProgress">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="current_stage" property="currentStage" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
    </resultMap>
    
    <!-- 所有列 -->
    <sql id="Base_Column_List">
        id, user_id, current_stage, create_time, update_time, is_deleted
    </sql>
    
    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.bimowu.interview.model.ResumeProgress" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO resume_progress (
            user_id, current_stage, create_time, update_time, is_deleted
        ) VALUES (
            #{userId,jdbcType=INTEGER},
            #{currentStage,jdbcType=VARCHAR},
            NOW(),
            NOW(),
            0
        )
    </insert>
    
    <!-- 更新记录 -->
    <update id="update" parameterType="com.bimowu.interview.model.ResumeProgress">
        UPDATE resume_progress
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="currentStage != null">
                current_stage = #{currentStage,jdbcType=VARCHAR},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT} AND is_deleted = 0
    </update>
    
    <!-- 更新进度阶段 -->
    <update id="updateStage">
        UPDATE resume_progress
        SET current_stage = #{currentStage,jdbcType=VARCHAR},
            update_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT} AND is_deleted = 0
    </update>
    
    <!-- 逻辑删除 -->
    <update id="deleteById">
        UPDATE resume_progress
        SET is_deleted = 1,
            update_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT} AND is_deleted = 0
    </update>
    
    <!-- 根据ID查询记录 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM resume_progress
        WHERE id = #{id,jdbcType=BIGINT} AND is_deleted = 0
    </select>
    
    <!-- 根据用户ID查询记录 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM resume_progress
        WHERE user_id = #{userId,jdbcType=INTEGER} AND is_deleted = 0
        LIMIT 1
    </select>
    
    <!-- 根据条件查询记录 -->
    <select id="selectByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM resume_progress
        <where>
            is_deleted = 0
            <if test="userId != null">
                AND user_id = #{userId,jdbcType=INTEGER}
            </if>
            <if test="currentStage != null">
                AND current_stage = #{currentStage,jdbcType=VARCHAR}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper> 