<template>
  <div class="resume-preview-wrapper">
    <div class="resume-preview-container">
      <component :is="currentTemplateComponent" :resume="resume" />
    </div>
    <div class="resume-actions">
      <el-tooltip v-if="resume.status !== 1" content="只有已通过审核的简历才能下载" placement="top">
        <el-button type="primary" :disabled="true">下载简历</el-button>
      </el-tooltip>
      <el-button v-else type="primary" @click="handleDownload">下载简历</el-button>

      <el-tooltip v-if="resume.status !== 1" content="只有已通过审核的简历才能下载" placement="top">
        <el-dropdown trigger="click">
          <el-button type="primary" :disabled="true">
            <span>选择格式</span>
            <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
        </el-dropdown>
      </el-tooltip>
      <el-dropdown v-else trigger="click" @command="handleDownloadFormat">
        <el-button type="primary">
          <span>选择格式</span>
          <el-icon class="el-icon--right"><arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="pdf">PDF格式</el-dropdown-item>
            <el-dropdown-item command="word">Word格式</el-dropdown-item>
            <el-dropdown-item command="image">图片格式</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { computed, defineProps, nextTick, createApp } from 'vue';
import { ArrowDown } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import Template1 from './Template1.vue';
import Template2 from './Template2.vue';
import Template3 from './Template3.vue';
import Template4 from './Template4.vue';
import Template5 from './Template5.vue';
import Avatar from '@/components/Avatar.vue';

const props = defineProps({
  resume: {
    type: Object,
    required: true
  },
  templateId: {
    type: Number,
    default: 1
  }
});

// 根据模板ID获取对应的模板组件
const currentTemplateComponent = computed(() => {
  switch (props.templateId) {
    case 1:
      return Template1;
    case 2:
      return Template2;
    case 3:
      return Template3;
    case 4:
      return Template4;
    case 5:
      return Template5;
    default:
      return Template1;
  }
});

// 处理下载
const handleDownload = () => {
  ElMessage.success('开始下载PDF格式简历...');
  handleDownloadFormat('pdf');
};

// 处理不同格式的下载
const handleDownloadFormat = async (format) => {
  if (format === 'pdf') {
    await downloadAsPDF();
  } else {
    const templateName = getTemplateName();
    ElMessage.success(`正在下载${format}格式的"${templateName}"简历...`);

    // 模拟下载延迟
    setTimeout(() => {
      ElMessage.success(`"${templateName}"简历已成功下载!`);
    }, 1500);
  }
};

// PDF下载功能
const downloadAsPDF = async () => {
  try {
    ElMessage.success('正在生成PDF...');

    // 创建临时预览容器
    const previewContainer = document.createElement('div');
    previewContainer.style.width = '794px'; // A4宽度，DPI=96
    previewContainer.style.minHeight = '1123px'; // A4高度，DPI=96
    previewContainer.style.maxWidth = '794px';
    previewContainer.style.backgroundColor = '#fff';
    previewContainer.style.position = 'fixed';
    previewContainer.style.left = '-9999px';
    previewContainer.style.top = '0';
    previewContainer.style.zIndex = '-1';
    previewContainer.style.transform = 'none'; // 禁止缩放
    previewContainer.style.paddingTop = '40px';
    previewContainer.style.paddingBottom = '40px';
    previewContainer.style.boxSizing = 'border-box';
    previewContainer.style.height = 'auto';
    previewContainer.style.maxHeight = 'none';
    document.body.appendChild(previewContainer);

    // 根据模板ID选择对应的模板组件
    const templateId = props.templateId;
    let TemplateComponent;

    switch (templateId) {
      case 1:
        TemplateComponent = Template1;
        break;
      case 2:
        TemplateComponent = Template2;
        break;
      case 3:
        TemplateComponent = Template3;
        break;
      case 4:
        TemplateComponent = Template4;
        break;
      case 5:
        TemplateComponent = Template5;
        break;
      default:
        TemplateComponent = Template1;
    }

    // 创建临时Vue应用来渲染模板
    const app = createApp(TemplateComponent, {
      resume: props.resume
    });
    app.use(ElementPlus);
    app.component('Avatar', Avatar);
    app.mount(previewContainer);

    // 等待 Vue 渲染完成
    await nextTick();

    // 等待所有图片加载完成
    const waitImagesLoaded = (container) => {
      return new Promise((resolve) => {
        const images = container.querySelectorAll('img');
        if (images.length === 0) {
          resolve();
          return;
        }

        let loadedCount = 0;
        const totalImages = images.length;

        const checkAllLoaded = () => {
          loadedCount++;
          if (loadedCount === totalImages) {
            resolve();
          }
        };

        Array.from(images).forEach(img => {
          img.setAttribute('crossorigin', 'anonymous');

          if (img.src.includes('.oss-cn-')) {
            const timestamp = new Date().getTime();
            const separator = img.src.includes('?') ? '&' : '?';
            img.src = `${img.src}${separator}_t=${timestamp}`;

            img.onerror = () => {
              console.error('图片加载失败:', img.src);
              img.src = '/images/default-avatar.svg';
              checkAllLoaded();
            };
          }

          if (img.complete) {
            checkAllLoaded();
          } else {
            img.onload = checkAllLoaded;
            img.onerror = checkAllLoaded;
          }
        });

        setTimeout(() => {
          if (loadedCount < totalImages) {
            console.warn(`图片加载超时，已加载${loadedCount}/${totalImages}张图片，继续处理`);
            resolve();
          }
        }, 5000);
      });
    };

    // 等待图片加载
    await waitImagesLoaded(previewContainer);

    // 给渲染更多时间
    await new Promise(resolve => setTimeout(resolve, 1000));

    try {
      // 使用 html2canvas 将内容转换为 canvas
      const canvas = await html2canvas(previewContainer, {
        scale: 2,
        useCORS: true,
        backgroundColor: '#fff',
        logging: false,
        onclone: (clonedDoc) => {
          const clonedContainer = clonedDoc.querySelector('div');
          if (clonedContainer) {
            clonedContainer.style.width = '794px';
            clonedContainer.style.minHeight = '1123px';
            clonedContainer.style.maxWidth = '794px';
            clonedContainer.style.backgroundColor = '#fff';
            clonedContainer.style.transform = 'none';
            clonedContainer.style.paddingTop = '40px';
            clonedContainer.style.paddingBottom = '40px';
            clonedContainer.style.boxSizing = 'border-box';
            clonedContainer.style.height = 'auto';
            clonedContainer.style.maxHeight = 'none';
            const images = clonedContainer.querySelectorAll('img');
            Array.from(images).forEach(img => {
              img.setAttribute('crossorigin', 'anonymous');
              if (img.src.includes('.oss-cn-')) {
                const timestamp = new Date().getTime();
                const separator = img.src.includes('?') ? '&' : '?';
                img.src = `${img.src}${separator}_t=${timestamp}`;
                if (img.src.includes('/avatars/') || img.className.includes('avatar')) {
                  img.onerror = function() {
                    this.src = '/images/default-avatar.svg';
                  };
                }
              }
            });
            // 给主内容区加margin-top，防止内容贴顶
            const mainContent = clonedContainer.querySelector('.resume-template, .resume-main, .resume-content, .resume-paper, .resume-preview-container');
            if (mainContent) {
              mainContent.style.marginTop = '20px';
              mainContent.style.overflow = 'visible';
              mainContent.style.maxHeight = 'none';
            }
          }
        }
      });

      // 检查canvas是否为空
      if (!canvas || canvas.width === 0 || canvas.height === 0) {
        throw new Error('Canvas生成失败');
      }

      // 创建 PDF 文档
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const imgData = canvas.toDataURL('image/jpeg', 1.0);
      const imgProps = pdf.getImageProperties(imgData);
      const pdfImgHeight = (imgProps.height * pageWidth) / imgProps.width;

      // 优化分页逻辑
      if (pdfImgHeight <= pageHeight) {
        pdf.addImage(imgData, 'JPEG', 0, 0, pageWidth, pdfImgHeight);
      } else {
        let position = 0;
        let remainingHeight = pdfImgHeight;
        while (remainingHeight > 0) {
          pdf.addImage(imgData, 'JPEG', 0, position, pageWidth, pdfImgHeight);
          remainingHeight -= pageHeight;
          if (remainingHeight > 0) {
            pdf.addPage();
            position -= pageHeight;
          }
        }
      }

      // 保存 PDF
      const fileName = `${props.resume.name || getTemplateName()}.pdf`;
      pdf.save(fileName);

      // 清理
      app.unmount();
      document.body.removeChild(previewContainer);

      ElMessage.success('下载成功');
    } catch (error) {
      throw new Error(`PDF生成失败: ${error.message}`);
    }
  } catch (error) {
    console.error('下载PDF失败:', error);
    ElMessage.error('下载PDF失败: ' + error.message);
  } finally {
    // 确保清理临时容器
    const container = document.querySelector('div[style*="left: -9999px"]');
    if (container) {
      document.body.removeChild(container);
    }
  }
};

// 获取当前模板名称
const getTemplateName = () => {
  switch (props.templateId) {
    case 1:
      return '线条风格简历模板';
    case 2:
      return '左右分栏蓝色模板';
    case 3:
      return '简洁浅色方格模板';
    case 4:
      return '现代简约模板';
    case 5:
      return '专业商务模板';
    case 6:
      return '创意设计模板';
    case 7:
      return '经典黑白模板';
    case 8:
      return '科技风格模板';
    case 9:
      return '艺术创意模板';
    case 10:
      return '极简主义模板';
    default:
      return '简历模板';
  }
};
</script>

<style scoped>
.resume-preview-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.resume-preview-container {
  width: 100%;
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.resume-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 10px 0;
}
</style> 