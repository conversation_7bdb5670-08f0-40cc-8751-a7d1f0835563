<template>
  <div class="preview-page">
    <div class="preview-header">
      <div class="container preview-header-container">
        <div class="preview-logo">
          <router-link to="/">
            <h1>墨崽简历</h1>
          </router-link>
        </div>

        <div class="preview-actions">
          <el-button @click="goBack">
            <el-icon><Back /></el-icon> 返回
          </el-button>
        </div>
      </div>
    </div>

    <div class="preview-content">
      <div class="resume-container">
        <div class="resume-paper">
          <!-- 使用动态组件渲染对应的模板 -->
          <component
              :is="currentTemplate"
              :resume="resume"
          />
        </div>
      </div>
    </div>
  </div>


</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back, Phone, Message, Location, Aim } from '@element-plus/icons-vue'
import { useResumeStore } from '@/stores/resume'
import { resumeApi } from '@/api'

// 导入所有模板组件
import Template1 from '@/components/templates/Template1.vue'
import Template2 from '@/components/templates/Template2.vue'
import Template3 from '@/components/templates/Template3.vue'
import Template4 from '@/components/templates/Template4.vue'
import Template5 from '@/components/templates/Template5.vue'

const route = useRoute()
const router = useRouter()
const resumeStore = useResumeStore()

// 简历数据
const resume = ref(resumeStore.currentResume || resumeStore.createEmptyResume())


// 根据templateId动态选择模板组件
const currentTemplate = computed(() => {
  const templateId = resume.value.templateId || 1
  const templateMap = {
    1: Template1,
    2: Template2,
    3: Template3,
    4: Template4,
    5: Template5
  }
  console.log(resume.value)
  return templateMap[templateId] || Template1
})

// 返回我的简历页面
const goBack = () => {
  router.push('/user')
}





// 尝试从API获取数据
const fetchResumeData = async () => {
  try {
    const response = await resumeApi.getResumeById(route.params.id)
    if (response && response.data) {
      const apiData = response.data
      resume.value = {
        resumeId: apiData.resumeVo?.resumeId || '',
        templateId: apiData.resumeVo?.templateId || 1,
        category: apiData.resumeVo?.category || 0,
        status: apiData.resumeVo?.status || 0,
        name: apiData.resumeVo?.title,
        modules: {
          basic: {
            id: apiData.information?.inforId || '',
            resumeId: apiData.information?.resumeId || '',
            name: apiData.information?.name || '',
            gender: apiData.information?.gender || '',
            birthday: apiData.information?.birthDate || '',
            phone: apiData.information?.phone || '',
            email: apiData.information?.email || '',
            address: apiData.information?.hometown || '',
            avatar: apiData.information?.avatar || '',
            age: apiData.information?.age || '',
            jobObjective: apiData.information?.jobObjective || ''
          },
          education: apiData.educationList?.map(edu => ({
            id: edu.eduId || '',
            resumeId: edu.resumeId || '',
            school: edu.school || '',
            major: edu.major || '',
            degree: edu.education || '',
            time: (edu.timePeriod || '').split('至').map(part => part.trim()).filter(Boolean) || [],
            startDate: edu.timePeriod?.split('至')[0] || '',
            endDate: edu.timePeriod?.split('至')[1] || '',
            courses: edu.mainCourses || ''
          })) || [],
          work: apiData.workList?.map(work => ({
            id: work.resId || '',
            resumeId: work.resumeId || '',
            company: work.company || '',
            position: work.position || '',
            startDate: work.timePeriod?.split('-')[0] || '',
            endDate: work.timePeriod?.split('-')[1] || '',
            time: (work.timePeriod || '').split('至').map(part => part.trim()).filter(Boolean) || [],
            description: work.workDescription || ''
          })) || [],
          projects: apiData.projectList?.map(project => ({
            id: project.expId || '',
            resumeId: project.resumeId || '',
            name: project.projectName || '',
            role: project.role || '',
            startDate: project.timePeriod?.split('-')[0] || '',
            endDate: project.timePeriod?.split('-')[1] || '',
            time: (project.timePeriod || '').split('至').map(part => part.trim()).filter(Boolean) || [],
            description: project.projectDescription || ''
          })) || [],
          practices: apiData.practiceList?.map(practice => ({
            id: practice.praId || '',
            resumeId: practice.resumeId || '',
            name: practice.projectName || '',
            startDate: practice.timePeriod?.split('-')[0] || '',
            endDate: practice.timePeriod?.split('-')[1] || '',
            time: (practice.timePeriod || '').split('至').map(part => part.trim()).filter(Boolean) || [],
            url: practice.projectUrl || '',
            role: practice.role || '',
            description: practice.projectDescription || ''
          })) || [],
          skills: apiData.talentList?.map(skill => ({
            id: skill.talId || '',
            resumeId: skill.resumeId || '',
            name: skill.skillName || '',
            level: skill.proficiency || 1,
            description: skill.skillDescription || ''
          })) || [],
          // certificates: apiData.certificateList?.map(cert => cert.certificateName).join('\n') || '',
          certificates: {
            id: apiData?.certificate?.cerId || '',
            resumeId: apiData?.certificate?.resumeId || '',
            certificateName: apiData?.certificate?.certificateName || ''
          },
          // campus: apiData.campusList?.map(campus => campus.campusExperience).join('\n') || '',
          campus: {
            id: apiData.campus?.camId || '',
            resumeId: apiData.campus?.resumeId || '',
            description: apiData.campus?.campusExperience || ''
          },
          //interests: apiData.interestList?.map(interest => interest.interestName).join('\n') || '',
          interests: {
            id: apiData?.interest?.intId || '',
            resumeId: apiData?.interest?.resumeId || '',
            description: apiData?.interest?.interest || ''
          },
          //selfEvaluation: apiData.evaluateList?.map(evaluation => evaluation.evaluateContent).join('\n') || ''
          selfEvaluation: {
            id: apiData?.evaluate?.evaId || '',
            resumeId: apiData?.evaluate?.resumeId || '',
            description: apiData?.evaluate?.selfEvaluation || ''
          },
        }
      }
      return
    }
  } catch (error) {
    console.error('从API获取数据失败:', error)
  }

}

// 获取简历数据
onMounted(async () => {
  const id = route.params.id

  // 如果是临时预览，使用store中的数据
  if (id === 'temp') {
    if (!resumeStore.currentResume) {
      ElMessage.warning('没有可预览的简历数据')
      router.push('/editor/new')
    }
    return
  }

  await fetchResumeData()
})
</script>

<style scoped>
.preview-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.preview-header {
  background-color: var(--white);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 10px 0;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
}

.preview-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-logo h1 {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color);
}

.preview-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.preview-content {
  flex: 1;
  margin-top: 60px;
  padding: 30px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
}

.resume-container {
  width: 100%;
  max-width: 210mm;
}

.resume-paper {
  background-color: var(--white);
  width: 210mm;
  min-height: 297mm;
  padding: 20mm;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
}

.resume-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid currentColor;
}

.resume-name {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 10px;
}

.basic-details {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.section-item {
  margin-bottom: 15px;
}

.item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.item-title {
  font-weight: 600;
}

.item-date {
  color: #666;
}

.item-subtitle {
  margin-bottom: 5px;
}

.item-description {
  font-size: 14px;
  line-height: 1.5;
}

.skills-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.skill-item {
  margin-bottom: 10px;
}

.skill-name {
  margin-bottom: 5px;
}

.skill-bar {
  height: 6px;
  background-color: #eee;
  border-radius: 3px;
}

.skill-progress {
  height: 100%;
  background-color: currentColor;
  border-radius: 3px;
}

.self-evaluation {
  line-height: 1.6;
}

@media print {
  .preview-header {
    display: none;
  }

  .preview-content {
    margin-top: 0;
    padding: 0;
    background-color: transparent;
  }

  .resume-paper {
    box-shadow: none;
    width: 100%;
    height: auto;
    padding: 0;
  }
}

@media (max-width: 768px) {
  .preview-content {
    padding: 15px;
  }

  .resume-paper {
    width: 100%;
    padding: 15px;
  }
}

</style>