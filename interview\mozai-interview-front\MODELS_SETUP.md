# 人脸检测模型配置说明

## 问题描述

前端应用需要加载人脸检测模型文件，但出现404错误：
```
404 Not Found: https://tt.bimowo.com/models/tiny_face_detector_model-weights_manifest.json
```

## 解决方案

我们已经修改了模型加载路径，并确保模型文件位于正确位置。

### 已完成的修改

1. 修改了`src/utils/faceDetection.ts`文件中的模型加载路径：
   ```typescript
   // 从
   await faceapi.nets.tinyFaceDetector.loadFromUri('/models')
   
   // 改为
   const MODEL_URL = '/interview-ai/models'
   await faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL)
   ```

2. 创建了必要的模型文件：
   - tiny_face_detector_model-weights_manifest.json
   - tiny_face_detector_model-shard1
   - face_landmark_68_model-weights_manifest.json
   - face_landmark_68_model-shard1
   - face_expression_model-weights_manifest.json
   - face_expression_model-shard1

### 部署步骤

1. 构建前端项目：
   ```bash
   npm run build
   ```

2. 确保模型文件位于正确位置：
   ```
   dist-interview-ai/models/tiny_face_detector_model-weights_manifest.json
   dist-interview-ai/models/tiny_face_detector_model-shard1
   dist-interview-ai/models/face_landmark_68_model-weights_manifest.json
   dist-interview-ai/models/face_landmark_68_model-shard1
   dist-interview-ai/models/face_expression_model-weights_manifest.json
   dist-interview-ai/models/face_expression_model-shard1
   ```

3. 将整个`dist-interview-ai`目录部署到服务器

### 验证

部署后，访问以下URL确认模型文件是否可以正常访问：
```
https://tt.bimowo.com/interview-ai/models/tiny_face_detector_model-weights_manifest.json
```

如果能够正常访问，则问题已解决。

### 重要说明

请确保服务器上的Nginx配置正确映射了`/interview-ai/`路径。如果您的前端应用部署在不同的路径下，请相应地调整`MODEL_URL`常量。 