package com.bimowu.interview.dao;

import com.bimowu.interview.model.SysTodo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * SysTodo数据库操作接口类
 */
@Repository
public interface SysTodoMapper {

    /**
     * 查询（根据主键ID查询）
     */
    SysTodo selectByPrimaryKey(@Param("id") Long id);

    /**
     * 根据用户ID查询未完成的待办事项
     */
    List<SysTodo> selectUnfinishedByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询所有待办事项
     */
    List<SysTodo> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据简历ID查询所有待办事项
     */
    List<SysTodo> selectByResumeId(@Param("resumeId") Long resumeId);

    /**
     * 根据简历ID查询未完成的待办事项
     */
    List<SysTodo> selectUnfinishedByResumeId(@Param("resumeId") Long resumeId);

    /**
     * 根据用户ID和待办类型查询待办事项
     */
    List<SysTodo> selectByUserIdAndTodoType(@Param("userId") Long userId, @Param("todoType") Integer todoType);

    /**
     * 根据用户ID和待办类型查询已完成的待办事项
     */
    List<SysTodo> selectCompletedByUserIdAndTodoType(@Param("userId") Long userId, @Param("todoType") Integer todoType);

    /**
     * 根据用户ID、待办类型和简历ID查询已完成的待办事项
     */
    List<SysTodo> selectCompletedByUserIdAndTodoTypeAndResumeId(@Param("userId") Long userId, @Param("todoType") Integer todoType, @Param("resumeId") Long resumeId);

    /**
     * 删除（根据主键ID删除）
     */
    int deleteByPrimaryKey(@Param("id") Long id);

    /**
     * 添加
     */
    int insert(SysTodo record);

    /**
     * 修改（匹配有值的字段）
     */
    int updateByPrimaryKeySelective(SysTodo record);
} 