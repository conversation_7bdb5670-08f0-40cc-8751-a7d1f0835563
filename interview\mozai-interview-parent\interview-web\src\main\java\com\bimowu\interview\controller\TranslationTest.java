package com.bimowu.interview.controller;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/translation")
@Slf4j
public class TranslationTest {
    /**
     * 章节段落
     * @param requestBody
     * @return 包含翻译+章节段落
     * 20:56:08.391 [http-nio-7021-exec-8] INFO  com.bimowu.interview.controller.TranslationTest - 转义响应：{"Code":"0","Data":{"TaskId":"4c2ce711f210489b982eedea8f8bb92c","TaskKey":"task1748091115435","TaskStatus":"COMPLETED","Result":{"Transcription":"https://prod-tingwu-paas-common-beijing.oss-cn-beijing.aliyuncs.com/tingwu/output/1588178996444530/4c2ce711f210489b982eedea8f8bb92c/4c2ce711f210489b982eedea8f8bb92c_Transcription_20250524205220.json?Expires=1750683158&OSSAccessKeyId=LTAI5tMzZ1D4o1drkJN1TfCr&Signature=1jW05iw9%2FIdXP6XqdNeKfGuux3s%3D","AutoChapters":"https://prod-tingwu-paas-common-beijing.oss-cn-beijing.aliyuncs.com/tingwu/output/1588178996444530/4c2ce711f210489b982eedea8f8bb92c/4c2ce711f210489b982eedea8f8bb92c_AutoChapters_20250524205235.json?Expires=1750683158&OSSAccessKeyId=LTAI5tMzZ1D4o1drkJN1TfCr&Signature=Dt1zX%2BFJ9fZYt%2FvrDMJDq4IAYTA%3D"}},"Message":"success","RequestId":"073F573B-CD8C-5578-9B6E-4760141631A7"}
     */
    @PostMapping("/testTranslate")
    public JSONObject testTranslate(@RequestBody JSONObject requestBody) {
        String videoUrl = requestBody.getString("videoUrl");
        CommonRequest request = createCommonRequest("tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS, MethodType.PUT, "/openapi/tingwu/v2/tasks");
        request.putQueryParameter("type", "offline");

        JSONObject root = new JSONObject();
        root.put("AppKey", "E7wWXUG2PeTfTpEC");

        JSONObject input = new JSONObject();
        input.fluentPut("FileUrl", videoUrl)
                .fluentPut("SourceLanguage", "cn")
                .fluentPut("TaskKey", "task" + System.currentTimeMillis());
        root.put("Input", input);

        JSONObject parameters = new JSONObject();
        parameters.put("AutoChaptersEnabled", true);
        root.put("Parameters", parameters);
        System.out.println(root.toJSONString());
        request.setHttpContent(root.toJSONString().getBytes(), "utf-8", FormatType.JSON);

        // TODO 请通过环境变量设置您的AccessKeyId、AccessKeySecret
        DefaultProfile profile = DefaultProfile.getProfile("cn-beijing", "LTAI5tQ2bXYzSNY9DK2nkVRo", "******************************");
        IAcsClient client = new DefaultAcsClient(profile);

        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);

            JSONObject result = new JSONObject();
            result.put("code", 0);
            result.put("message", "处理成功");
            result.put("data", JSONObject.parse(response.getData()));
            JSONObject responseData = JSONObject.parseObject(response.getData());
            JSONObject data =  responseData.getJSONObject("Data");
            String taskId =data.getString("TaskId");
            getTaskInfo(taskId);

        } catch (ClientException e) {
            throw new RuntimeException(e);
        }

        log.info("转义响应："+response.getData());
        JSONObject result = new JSONObject();
        result.put("code", 0);
        result.put("message", "处理成功");
        result.put("data", JSONObject.parse(response.getData()));
        return result;
    }

    public static CommonRequest createCommonRequest(String domain, String version, ProtocolType protocolType, MethodType method, String uri) {
        // 创建API请求并设置参数
        CommonRequest request = new CommonRequest();
        request.setSysDomain(domain);
        request.setSysVersion(version);
        request.setSysProtocol(protocolType);
        request.setSysMethod(method);
        request.setSysUriPattern(uri);
        request.setHttpContentType(FormatType.JSON);
        return request;
    }

    @GetMapping("/getTaskInfo")
    public void getTaskInfo(String taskId)  {
        String queryUrl = String.format("/openapi/tingwu/v2/tasks" + "/%s", taskId);

        CommonRequest request = createCommonRequest("tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS, MethodType.GET, queryUrl);
        DefaultProfile profile = DefaultProfile.getProfile("cn-beijing", "LTAI5tQ2bXYzSNY9DK2nkVRo", "******************************");
        IAcsClient client = new DefaultAcsClient(profile);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }
        log.info("转义响应1："+response.getData());
    }

}
