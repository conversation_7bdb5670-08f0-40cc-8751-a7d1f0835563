import type { ExtractPropTypes, StyleValue, VNode, VNodeChild } from 'vue';
import type { Arrayable } from 'element-plus/es/utils';
export declare const spaceProps: {
    readonly direction: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "horizontal" | "vertical", unknown, "horizontal", boolean>;
    readonly class: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => Arrayable<string | Record<string, boolean>> & {}) | (() => Arrayable<string | Record<string, boolean>>) | ((new (...args: any[]) => Arrayable<string | Record<string, boolean>> & {}) | (() => Arrayable<string | Record<string, boolean>>))[], unknown, unknown, "", boolean>;
    readonly style: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => StyleValue & {}) | (() => StyleValue) | ((new (...args: any[]) => StyleValue & {}) | (() => StyleValue))[], unknown, unknown, "", boolean>;
    readonly alignment: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string & {}) | (() => string) | ((new (...args: any[]) => string & {}) | (() => string))[], unknown, unknown, "center", boolean>;
    readonly prefixCls: {
        readonly type: import("vue").PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly spacer: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => VNodeChild & {}) | (() => VNodeChild) | ((new (...args: any[]) => VNodeChild & {}) | (() => VNodeChild))[], unknown, unknown, null, boolean>;
    readonly wrap: BooleanConstructor;
    readonly fill: BooleanConstructor;
    readonly fillRatio: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 100, boolean>;
    readonly size: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, ArrayConstructor, NumberConstructor], "" | "default" | "small" | "large", number | [number, number]>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
};
export declare type SpaceProps = ExtractPropTypes<typeof spaceProps>;
declare const Space: import("vue").DefineComponent<{
    readonly direction: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "horizontal" | "vertical", unknown, "horizontal", boolean>;
    readonly class: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => Arrayable<string | Record<string, boolean>> & {}) | (() => Arrayable<string | Record<string, boolean>>) | ((new (...args: any[]) => Arrayable<string | Record<string, boolean>> & {}) | (() => Arrayable<string | Record<string, boolean>>))[], unknown, unknown, "", boolean>;
    readonly style: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => StyleValue & {}) | (() => StyleValue) | ((new (...args: any[]) => StyleValue & {}) | (() => StyleValue))[], unknown, unknown, "", boolean>;
    readonly alignment: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string & {}) | (() => string) | ((new (...args: any[]) => string & {}) | (() => string))[], unknown, unknown, "center", boolean>;
    readonly prefixCls: {
        readonly type: import("vue").PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly spacer: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => VNodeChild & {}) | (() => VNodeChild) | ((new (...args: any[]) => VNodeChild & {}) | (() => VNodeChild))[], unknown, unknown, null, boolean>;
    readonly wrap: BooleanConstructor;
    readonly fill: BooleanConstructor;
    readonly fillRatio: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 100, boolean>;
    readonly size: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, ArrayConstructor, NumberConstructor], "" | "default" | "small" | "large", number | [number, number]>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}, () => string | VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}> | {
    [name: string]: unknown;
    $stable?: boolean | undefined;
} | null, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<ExtractPropTypes<{
    readonly direction: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "horizontal" | "vertical", unknown, "horizontal", boolean>;
    readonly class: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => Arrayable<string | Record<string, boolean>> & {}) | (() => Arrayable<string | Record<string, boolean>>) | ((new (...args: any[]) => Arrayable<string | Record<string, boolean>> & {}) | (() => Arrayable<string | Record<string, boolean>>))[], unknown, unknown, "", boolean>;
    readonly style: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => StyleValue & {}) | (() => StyleValue) | ((new (...args: any[]) => StyleValue & {}) | (() => StyleValue))[], unknown, unknown, "", boolean>;
    readonly alignment: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string & {}) | (() => string) | ((new (...args: any[]) => string & {}) | (() => string))[], unknown, unknown, "center", boolean>;
    readonly prefixCls: {
        readonly type: import("vue").PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly spacer: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => VNodeChild & {}) | (() => VNodeChild) | ((new (...args: any[]) => VNodeChild & {}) | (() => VNodeChild))[], unknown, unknown, null, boolean>;
    readonly wrap: BooleanConstructor;
    readonly fill: BooleanConstructor;
    readonly fillRatio: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 100, boolean>;
    readonly size: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, ArrayConstructor, NumberConstructor], "" | "default" | "small" | "large", number | [number, number]>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}>>, {
    readonly fill: boolean;
    readonly style: StyleValue;
    readonly class: Arrayable<string | Record<string, boolean>>;
    readonly wrap: boolean;
    readonly direction: import("element-plus/es/utils").EpPropMergeType<StringConstructor, "horizontal" | "vertical", unknown>;
    readonly alignment: string;
    readonly spacer: VNodeChild;
    readonly fillRatio: number;
}>;
export declare type SpaceInstance = InstanceType<typeof Space>;
export default Space;
