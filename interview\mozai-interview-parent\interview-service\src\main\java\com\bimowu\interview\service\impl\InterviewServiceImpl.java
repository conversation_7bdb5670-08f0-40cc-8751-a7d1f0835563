package com.bimowu.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bimowu.interview.dao.InterviewMapper;
import com.bimowu.interview.model.*;
import com.bimowu.interview.service.*;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import com.bimowu.interview.service.InterviewTranscriptionService;
import com.bimowu.interview.model.InterviewTranscription;

/**
 * 面试服务实现类
 */
@Service
@Slf4j
public class InterviewServiceImpl extends ServiceImpl<InterviewMapper, Interview>
        implements InterviewService {

    @Autowired
    private InterviewMapper interviewMapper;

    @Autowired
    private ResumeHrQuestionsService resumeHrQuestionsService;

    @Autowired
    private InterviewQuestionsService interviewQuestionsService;

    @Autowired
    private ResumeCategoryRelationService resumeCategoryRelationService;

    @Autowired
    private ResumeProjectExperienceService resumeProjectExperienceService;

    @Autowired
    private ResumeProjectContentService resumeProjectContentService;

    @Autowired
    private ResumeProjectQuestionService resumeProjectQuestionService;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private InterviewSpeechRecordService interviewSpeechRecordService;

    @Autowired
    private InterviewSpeechChapterService interviewSpeechChapterService;

    @Autowired
    private InterviewTranscriptionService interviewTranscriptionService;
    
    @Autowired
    private AiManager aiManager;

    @Value("${interview.maxQuestion}")
    private Integer maxQuestion;

    @Override
    @Transactional
    public String createInterview(Interview interview) {
        // 生成UUID作为面试ID
        String interviewId = UUID.randomUUID().toString().replace("-", "");
        try{
            log.info("创建面试信息: type={}, position={}, company={}",
                    interview.getType(), interview.getPosition(), interview.getCompany());

            if(interview.getType().equals("formal")){
                // 生成UUID作为面试ID
                interview.setId(interviewId);

                // 设置默认状态为进行中
                if (interview.getStatus() == null) {
                    interview.setStatus(1);
                }

                // 确保面试时间不为空
                if (interview.getInterviewTime() == null) {
                    interview.setInterviewTime(new Date());
                }

                // 设置时间
                Date now = new Date();
                interview.setCreateTime(now);
                interview.setUpdateTime(now);
                interview.setInterviewTime(now);
                // 先插入面试记录，确保interviewId已存在
                interviewMapper.insert(interview);
                return interviewId;
            }

            interview.setId(interviewId);
            // 设置默认状态为进行中
            if (interview.getStatus() == null) {
                interview.setStatus(0);
            }
            // 确保面试时间不为空
            if (interview.getInterviewTime() == null) {
                interview.setInterviewTime(new Date());
            }
            // 设置时间
            Date now = new Date();
            interview.setInterviewTime(now);
            interview.setCreateTime(now);
            interview.setUpdateTime(now);
            Gson gson = new GsonBuilder()
                    .setPrettyPrinting() // 格式化输出（可选）
                    .create();

            log.info("面试信息: {}", interview.toString());

            // 先插入面试记录，确保interviewId已存在
            interviewMapper.insert(interview);

            // 查询问题，存到interview_questions表
            if(Objects.equals(interview.getStage(), "hr")) {
                QueryWrapper<ResumeHrQuestions> queryWrapper = new QueryWrapper<>();
                queryWrapper.last("LIMIT 10");
                List<ResumeHrQuestions> resumeHrQuestions = resumeHrQuestionsService.list(queryWrapper);
                InterviewQuestions questions = new InterviewQuestions();
                questions.setInterviewId(interview.getId());
                questions.setQuestions(gson.toJson(resumeHrQuestions));
                questions.setCreateTime(now);
                questions.setUpdateTime(now);
                log.info("面试问题1: {}", questions.toString());
                interviewQuestionsService.save(questions);
            } else {
                Integer userId = interview.getUserId();
                Integer catId = Integer.valueOf(interview.getPosition());

                // 第一步：查询 resumeId
                LambdaQueryWrapper<ResumeCategoryRelation> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ResumeCategoryRelation::getUserId, userId)
                        .eq(ResumeCategoryRelation::getCatId, catId);

                ResumeCategoryRelation relation = resumeCategoryRelationService.getOne(wrapper);
                if (relation == null) {
                    // 直接抛出异常，由外层catch捕获，让事务回滚
                    log.error("未找到对应的 resumeId，userId={}, catId={}", userId, catId);
                    throw new RuntimeException("您还未创建简历，请先到简历系统创建对应简历吧！");
                }
                Long resumeId = relation.getResumeId();
                // 第二步：查询 projectId 列表（假设 resume_project_experience 表有 resumeId 字段）
                LambdaQueryWrapper<ResumeProjectExperience> projectWrapper = new LambdaQueryWrapper<>();
                projectWrapper.eq(ResumeProjectExperience::getResumeId, resumeId);

                List<ResumeProjectExperience> projectExperiences = resumeProjectExperienceService.list(projectWrapper);
                List<Long> projectIdList = projectExperiences.stream()
                        .map(ResumeProjectExperience::getProjectId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                List<ResumeProjectQuestion> questionList = new ArrayList<>();
                if (!projectIdList.isEmpty()) {
                    // 第三步：根据 projectId 批量查询 conId（使用 in 条件优化循环查询）
                    QueryWrapper<ResumeProjectContent> contentWrapper = new QueryWrapper<>();
                    contentWrapper.select("con_id")
                            .in("project_id", projectIdList); // 批量 in 条件

                    List<ResumeProjectContent> contentList = resumeProjectContentService.list(contentWrapper);
                    List<Long> conIdList = contentList.stream()
                            .map(ResumeProjectContent::getConId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    if (conIdList.isEmpty()) {
                        log.error("未找到对应的 conId，resumeId={}", resumeId);
                        throw new RuntimeException("您的简历未包含项目经验，请完善简历内容吧！");
                    }
                    // 第四步：根据 conId 批量查询问题（假设 resume_project_question 表主键为 conId）
                    LambdaQueryWrapper<ResumeProjectQuestion> questionWrapper = new LambdaQueryWrapper<>();
                    questionWrapper.in(ResumeProjectQuestion::getConId, conIdList);
                    questionList = resumeProjectQuestionService.list(questionWrapper);

                }
                //面试问题数量少于10，可能是用户自有项目导致，也可能是系统项目问题过少导致
                if(questionList.size()<10){
                    Integer needQuestion = maxQuestion-questionList.size();
                    //继续生成问题
                    StringBuffer resumeProjectExperiences = new StringBuffer();
                    for(ResumeProjectExperience resumeProjectExperience : projectExperiences){
                        resumeProjectExperiences.append(resumeProjectExperience.getProjectDescription());
                    }

                    //resumeProjectExperiences存储了用户简历的项目内容，请将resumeProjectExperiences扔个ai，让ai生成needQuestion个问题
                    log.info("项目问题数量不足，使用AI生成额外问题：需要生成{}个问题", needQuestion);
                    try {
                        // 获取面试岗位信息
                        String position = interview.getPosition();
                        // 调用AiManager生成面试问题
                        List<String> aiGeneratedQuestions = aiManager.generateInterviewQuestions(
                                resumeProjectExperiences.toString(),
                                position,
                                needQuestion
                        );

                        log.info("AI成功生成了{}个问题", aiGeneratedQuestions.size());

                        // 将AI生成的问题转换为ResumeProjectQuestion对象并添加到questionList
                        if (aiGeneratedQuestions != null && !aiGeneratedQuestions.isEmpty()) {
                            for (String question : aiGeneratedQuestions) {
                                ResumeProjectQuestion projectQuestion = new ResumeProjectQuestion();
                                projectQuestion.setConId(0L); // 使用特殊值标记AI生成的问题
                                projectQuestion.setQuestion(question);
                                projectQuestion.setAnswer(""); // AI生成的问题暂无标准答案
                                projectQuestion.setCreateTime(new Date());
                                projectQuestion.setUpdateTime(new Date());

                                questionList.add(projectQuestion);
                            }

                            log.info("成功添加AI生成的问题到面试问题列表，现有问题总数：{}", questionList.size());
                        } else {
                            log.warn("AI未能生成有效问题");
                        }
                    } catch (Exception e) {
                        log.error("使用AI生成面试问题失败", e);
                        // 异常情况下继续使用现有问题列表
                    }
                }
                InterviewQuestions questions = new InterviewQuestions();
                questions.setInterviewId(interview.getId());
                questions.setQuestions(gson.toJson(questionList));
                questions.setCreateTime(now);
                questions.setUpdateTime(now);
                log.info("面试问题2: {}", questions.toString());
                interviewQuestionsService.save(questions);
            }
        }catch (Exception e){
            log.error("创建面试失败，原因：", e);
            // 抛出异常，让事务回滚，确保interviewMapper.insert(interview)操作也回滚
            throw new RuntimeException("创建面试失败: " + e.getMessage(), e);
        }
        return interviewId;
    }

    @Override
    @Transactional
    public boolean updateInterview(Interview interview) {
        log.info("更新面试信息: id={}", interview.getId());
        interview.setUpdateTime(new Date());
        
        // 如果有问题列表，保存到questions表
//        if (interview.getQuestions() != null && !interview.getQuestions().isEmpty()) {
//            saveInterviewQuestions(interview.getId(), interview.getQuestions());
//        }
        
        int result = interviewMapper.update(interview);
        return result > 0;
    }

    @Override
    public Interview getInterviewById(String interviewId) {
        log.info("获取面试信息: id={}", interviewId);
        return interviewMapper.selectById(interviewId);
    }

    @Override
    public List<Interview> getInterviewsByCondition(Map<String, Object> params) {
        log.info("根据条件查询面试列表: params={}", params);
        return interviewMapper.selectByCondition(params);
    }

    @Override
    public List<Interview> getInterviewsByType(String type) {
        log.info("根据类型查询面试列表: type={}", type);
        return interviewMapper.selectByType(type);
    }

    @Override
    @Transactional
    public boolean updateInterviewStatus(String interviewId, Integer status) {
        log.info("更新面试状态: id={}, status={}", interviewId, status);
        int result = interviewMapper.updateStatus(interviewId, status);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean updateInterviewResult(String interviewId, Integer overallScore, 
                                        String feedback, String strengths, String improvements) {
        log.info("更新面试结果: id={}, score={}", interviewId, overallScore);
        
        Interview interview = new Interview();
        interview.setId(interviewId);
        interview.setOverallScore(overallScore);
        interview.setFeedback(feedback);
        interview.setStrengths(strengths);
        interview.setImprovements(improvements);
        interview.setStatus(1); // 已完成
        
        int result = interviewMapper.update(interview);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean updateInterviewPassResult(String interviewId, Integer result) {
        log.info("更新面试通过/未通过状态: id={}, result={}", interviewId, result);
        
        int updatedRows = interviewMapper.updateInterviewResult(interviewId, result);
        return updatedRows > 0;
    }

    @Override
    @Transactional
    public boolean updateInterviewVideoUrl(String interviewId, String videoUrl) {
        log.info("更新面试视频URL: id={}, url={}", interviewId, videoUrl);
        
        int result = interviewMapper.updateVideoUrl(interviewId, videoUrl);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteInterview(String interviewId) {
        log.info("删除面试: id={}", interviewId);
        try {
            // 删除相关的转写记录
            jdbcTemplate.update("DELETE FROM interview_transcription WHERE interview_id = ?", interviewId);
            
            // 删除相关的问题记录
            jdbcTemplate.update("DELETE FROM interview_questions WHERE interview_id = ?", interviewId);
            
            // 删除面试记录
            int result = interviewMapper.deleteById(interviewId);
            return result > 0;
        } catch (Exception e) {
            log.error("删除面试时发生异常", e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean saveInterviewQuestions(String interviewId, List<String> questions) {
        log.info("保存面试问题列表: interviewId={}, count={}", interviewId, questions.size());
        
        // 使用JSON格式保存问题列表
        String questionsJson = JSON.toJSONString(questions);
        
        // 检查是否已存在问题记录
        int count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM interview_questions WHERE interview_id = ?",
                Integer.class, interviewId);
        
        if (count > 0) {
            // 更新现有记录
            jdbcTemplate.update(
                    "UPDATE interview_questions SET questions = ?, update_time = NOW() WHERE interview_id = ?",
                    questionsJson, interviewId);
        } else {
            // 插入新记录
            jdbcTemplate.update(
                    "INSERT INTO interview_questions (interview_id, questions, create_time, update_time) VALUES (?, ?, NOW(), NOW())",
                    interviewId, questionsJson);
        }
        
        return true;
    }

    @Override
    public List<String> getInterviewQuestions(String interviewId) {
        log.info("获取面试问题列表: interviewId={}", interviewId);

        
        try {
            // 查询JSON格式的问题列表
            String questionsJson = jdbcTemplate.queryForObject(
                    "SELECT questions FROM interview_questions WHERE interview_id = ?",
                    String.class, interviewId);

            if (questionsJson != null) {

                return JSON.parseArray(questionsJson, String.class);
            }
        } catch (Exception e) {
            log.error("获取面试问题列表失败", e);
        }
        
        return null;
    }
    /**
     * 保存对话转写记录到数据库
     *
     * @param dialogList 对话记录列表
     * @param interviewId 面试ID
     * @return 是否保存成功
     */
    @Override
    @Transactional
    public boolean saveDialogRecords(List<Object> dialogList, String interviewId) {
        log.info("保存对话转写记录: interviewId={}, 记录数={}", interviewId, dialogList.size());

        try {
            // 先删除已有的记录，确保不会重复
            interviewSpeechRecordService.deleteRecordsByInterviewId(interviewId);

            // 将对象列表转换为InterviewSpeechRecord列表
            List<InterviewSpeechRecord> records = new ArrayList<>();
            for (Object obj : dialogList) {
                if (obj instanceof InterviewSpeechRecord) {
                    InterviewSpeechRecord record = (InterviewSpeechRecord) obj;
                    // 确保记录有面试ID
                    if (record.getInterviewId() == null) {
                        record.setInterviewId(interviewId);
                    }
                    records.add(record);
                }
            }

            // 批量保存记录
            if (!records.isEmpty()) {
                log.info("开始批量保存{}条对话记录", records.size());
                boolean result = interviewSpeechRecordService.batchSaveRecords(records);
                log.info("批量保存对话记录完成: {}", result ? "成功" : "失败");
                return result;
            } else {
                log.warn("没有有效的对话记录需要保存");
                return false;
            }
        } catch (Exception e) {
            log.error("保存对话转写记录失败", e);
            throw new RuntimeException("保存对话转写记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存章节转写记录到数据库
     *
     * @param chapterList 章节记录列表
     * @param interviewId 面试ID
     * @return 是否保存成功
     */
    @Override
    @Transactional
    public boolean saveChapterRecords(List<Object> chapterList, String interviewId) {
        log.info("保存章节转写记录: interviewId={}, 记录数={}", interviewId, chapterList.size());

        try {
            // 先删除已有的记录，确保不会重复
            interviewSpeechChapterService.deleteChaptersByInterviewId(interviewId);

            // 将对象列表转换为InterviewSpeechChapter列表
            List<InterviewSpeechChapter> chapters = new ArrayList<>();
            for (Object obj : chapterList) {
                if (obj instanceof InterviewSpeechChapter) {
                    InterviewSpeechChapter chapter = (InterviewSpeechChapter) obj;
                    // 确保记录有面试ID
                    if (chapter.getInterviewId() == null) {
                        chapter.setInterviewId(interviewId);
                    }
                    chapters.add(chapter);
                }
            }

            // 批量保存记录
            if (!chapters.isEmpty()) {
                log.info("开始批量保存{}条章节记录", chapters.size());
                boolean result = interviewSpeechChapterService.batchSaveChapters(chapters);
                log.info("批量保存章节记录完成: {}", result ? "成功" : "失败");
                return result;
            } else {
                log.warn("没有有效的章节记录需要保存");
                return false;
            }
        } catch (Exception e) {
            log.error("保存章节转写记录失败", e);
            throw new RuntimeException("保存章节转写记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存问答转写记录到数据库
     *
     * @param qaList 问答记录列表
     * @param interviewId 面试ID
     * @return 是否保存成功
     */
    @Override
    @Transactional
    public boolean saveTranscriptionRecords(List<Object> qaList, String interviewId) {
        log.info("保存问答转写记录: interviewId={}, 记录数={}", interviewId, qaList.size());

        try {
            // 先删除已有的记录，确保不会重复
            interviewTranscriptionService.deleteTranscriptionsByInterviewId(interviewId);

            // 将对象列表转换为InterviewTranscription列表
            List<InterviewTranscription> transcriptions = new ArrayList<>();
            for (Object obj : qaList) {
                if (obj instanceof InterviewTranscription) {
                    InterviewTranscription transcription = (InterviewTranscription) obj;
                    // 确保记录有面试ID
                    if (transcription.getInterviewId() == null) {
                        transcription.setInterviewId(interviewId);
                    }
                    // 设置创建和更新时间
                    Date now = new Date();
                    if (transcription.getCreateTime() == null) {
                        transcription.setCreateTime(now);
                    }
                    if (transcription.getUpdateTime() == null) {
                        transcription.setUpdateTime(now);
                    }
                    transcriptions.add(transcription);
                }
            }

            // 批量保存记录
            if (!transcriptions.isEmpty()) {
                log.info("开始批量保存{}条问答转写记录", transcriptions.size());
                boolean result = interviewTranscriptionService.batchSaveTranscriptions(transcriptions);
                log.info("批量保存问答转写记录完成: {}", result ? "成功" : "失败");
                return result;
            } else {
                log.warn("没有有效的问答转写记录需要保存");
                return false;
            }
        } catch (Exception e) {
            log.error("保存问答转写记录失败", e);
            throw new RuntimeException("保存问答转写记录失败: " + e.getMessage(), e);
        }
    }
}