import type { UploadFile } from './upload';
declare const _default: import("vue").DefineComponent<{
    readonly files: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadFiles) | (() => import("./upload").UploadFiles) | ((new (...args: any[]) => import("./upload").UploadFiles) | (() => import("./upload").UploadFiles))[], unknown, unknown, () => never[], boolean>;
    readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly handlePreview: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: UploadFile) => void) | (() => (uploadFile: UploadFile) => void) | {
        (): (uploadFile: UploadFile) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (uploadFile: UploadFile) => void) | (() => (uploadFile: UploadFile) => void) | {
        (): (uploadFile: UploadFile) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
}, {
    emit: (event: "remove", file: UploadFile) => void;
    t: import("element-plus/es/hooks").Translator;
    nsUpload: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    nsIcon: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    nsList: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    disabled: import("vue").ComputedRef<boolean>;
    focusing: import("vue").Ref<boolean>;
    handleRemove: (file: UploadFile) => void;
    ElIcon: import("../../../utils").SFCWithInstall<import("vue").DefineComponent<{
        readonly size: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly color: {
            readonly type: import("vue").PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        style: import("vue").ComputedRef<import("vue").CSSProperties>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly size: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly color: {
            readonly type: import("vue").PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }>>, {}>> & Record<string, any>;
    Check: import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>;
    CircleCheck: import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>;
    Close: import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>;
    Delete: import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>;
    Document: import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>;
    ZoomIn: import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>;
    ElProgress: import("../../../utils").SFCWithInstall<import("vue").DefineComponent<{
        readonly type: import("../../../utils").EpPropFinalized<StringConstructor, "circle" | "line" | "dashboard", unknown, "line", boolean>;
        readonly percentage: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
        readonly status: import("../../../utils").EpPropFinalized<StringConstructor, "" | "success" | "warning" | "exception", unknown, "", boolean>;
        readonly indeterminate: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly duration: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 3, boolean>;
        readonly strokeWidth: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 6, boolean>;
        readonly strokeLinecap: import("../../../utils").EpPropFinalized<(new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square") | ((new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square"))[], unknown, unknown, "round", boolean>;
        readonly textInside: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 126, boolean>;
        readonly showText: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly color: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]) & {}) | (() => string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]) | ((new (...args: any[]) => (string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]) & {}) | (() => string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]))[], unknown, unknown, "", boolean>;
        readonly striped: BooleanConstructor;
        readonly stripedFlow: BooleanConstructor;
        readonly format: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("element-plus/es/components/progress").ProgressFn) | (() => import("element-plus/es/components/progress").ProgressFn) | {
            (): import("element-plus/es/components/progress").ProgressFn;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => import("element-plus/es/components/progress").ProgressFn) | (() => import("element-plus/es/components/progress").ProgressFn) | {
            (): import("element-plus/es/components/progress").ProgressFn;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, (percentage: number) => string, boolean>;
    }, {
        STATUS_COLOR_MAP: Record<string, string>;
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly type: import("../../../utils").EpPropFinalized<StringConstructor, "circle" | "line" | "dashboard", unknown, "line", boolean>;
            readonly percentage: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
            readonly status: import("../../../utils").EpPropFinalized<StringConstructor, "" | "success" | "warning" | "exception", unknown, "", boolean>;
            readonly indeterminate: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly duration: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 3, boolean>;
            readonly strokeWidth: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 6, boolean>;
            readonly strokeLinecap: import("../../../utils").EpPropFinalized<(new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square") | ((new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square"))[], unknown, unknown, "round", boolean>;
            readonly textInside: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 126, boolean>;
            readonly showText: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly color: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]) & {}) | (() => string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]) | ((new (...args: any[]) => (string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]) & {}) | (() => string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]))[], unknown, unknown, "", boolean>;
            readonly striped: BooleanConstructor;
            readonly stripedFlow: BooleanConstructor;
            readonly format: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("element-plus/es/components/progress").ProgressFn) | (() => import("element-plus/es/components/progress").ProgressFn) | {
                (): import("element-plus/es/components/progress").ProgressFn;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => import("element-plus/es/components/progress").ProgressFn) | (() => import("element-plus/es/components/progress").ProgressFn) | {
                (): import("element-plus/es/components/progress").ProgressFn;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, (percentage: number) => string, boolean>;
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        barStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
        relativeStrokeWidth: import("vue").ComputedRef<string>;
        radius: import("vue").ComputedRef<number>;
        trackPath: import("vue").ComputedRef<string>;
        perimeter: import("vue").ComputedRef<number>;
        rate: import("vue").ComputedRef<1 | 0.75>;
        strokeDashoffset: import("vue").ComputedRef<string>;
        trailPathStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
        circlePathStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
        stroke: import("vue").ComputedRef<string>;
        statusIcon: import("vue").ComputedRef<import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>>;
        progressTextSize: import("vue").ComputedRef<number>;
        content: import("vue").ComputedRef<string>;
        getColors: (color: import("element-plus/es/components/progress").ProgressColor[]) => import("element-plus/es/components/progress").ProgressColor[];
        getCurrentColor: (percentage: number) => string;
        ElIcon: import("../../../utils").SFCWithInstall<import("vue").DefineComponent<{
            readonly size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }, {
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                readonly size: {
                    readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly color: {
                    readonly type: import("vue").PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
            }>> & {
                [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
            }>>;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            style: import("vue").ComputedRef<import("vue").CSSProperties>;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }>>, {}>> & Record<string, any>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly type: import("../../../utils").EpPropFinalized<StringConstructor, "circle" | "line" | "dashboard", unknown, "line", boolean>;
        readonly percentage: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
        readonly status: import("../../../utils").EpPropFinalized<StringConstructor, "" | "success" | "warning" | "exception", unknown, "", boolean>;
        readonly indeterminate: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly duration: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 3, boolean>;
        readonly strokeWidth: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 6, boolean>;
        readonly strokeLinecap: import("../../../utils").EpPropFinalized<(new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square") | ((new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square"))[], unknown, unknown, "round", boolean>;
        readonly textInside: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 126, boolean>;
        readonly showText: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly color: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]) & {}) | (() => string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]) | ((new (...args: any[]) => (string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]) & {}) | (() => string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]))[], unknown, unknown, "", boolean>;
        readonly striped: BooleanConstructor;
        readonly stripedFlow: BooleanConstructor;
        readonly format: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("element-plus/es/components/progress").ProgressFn) | (() => import("element-plus/es/components/progress").ProgressFn) | {
            (): import("element-plus/es/components/progress").ProgressFn;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => import("element-plus/es/components/progress").ProgressFn) | (() => import("element-plus/es/components/progress").ProgressFn) | {
            (): import("element-plus/es/components/progress").ProgressFn;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, (percentage: number) => string, boolean>;
    }>>, {
        readonly type: import("../../../utils").EpPropMergeType<StringConstructor, "circle" | "line" | "dashboard", unknown>;
        readonly width: number;
        readonly color: import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]) & {}) | (() => string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]) | ((new (...args: any[]) => (string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]) & {}) | (() => string | import("element-plus/es/components/progress").ProgressFn | import("element-plus/es/components/progress").ProgressColor[]))[], unknown, unknown>;
        readonly strokeLinecap: import("../../../utils").EpPropMergeType<(new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square") | ((new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square"))[], unknown, unknown>;
        readonly strokeWidth: number;
        readonly indeterminate: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly format: import("element-plus/es/components/progress").ProgressFn;
        readonly percentage: number;
        readonly status: import("../../../utils").EpPropMergeType<StringConstructor, "" | "success" | "warning" | "exception", unknown>;
        readonly duration: number;
        readonly textInside: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly showText: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly striped: boolean;
        readonly stripedFlow: boolean;
    }>> & Record<string, any>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    remove: (file: UploadFile) => boolean;
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly files: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadFiles) | (() => import("./upload").UploadFiles) | ((new (...args: any[]) => import("./upload").UploadFiles) | (() => import("./upload").UploadFiles))[], unknown, unknown, () => never[], boolean>;
    readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly handlePreview: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: UploadFile) => void) | (() => (uploadFile: UploadFile) => void) | {
        (): (uploadFile: UploadFile) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (uploadFile: UploadFile) => void) | (() => (uploadFile: UploadFile) => void) | {
        (): (uploadFile: UploadFile) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
}>> & {
    onRemove?: ((file: UploadFile) => any) | undefined;
}, {
    readonly disabled: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly listType: import("../../../utils").EpPropMergeType<StringConstructor, "text" | "picture" | "picture-card", unknown>;
    readonly files: import("./upload").UploadFiles;
    readonly handlePreview: (uploadFile: UploadFile) => void;
}>;
export default _default;
