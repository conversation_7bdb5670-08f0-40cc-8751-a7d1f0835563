<template>
  <div class="pdf-generator">
    <vue-html2pdf
      ref="pdfRef"
      :show-layout="false"
      :float-layout="true"
      :enable-download="true"
      :preview-modal="false"
      :paginate-elements-by-height="1400"
      :filename="filename"
      :pdf-quality="2"
      :manual-pagination="true"
      pdf-format="a4"
      pdf-orientation="portrait"
      pdf-content-width="800px"
      @progress="onProgress"
      @hasStartedGeneration="onGenerationStarted"
      @hasGenerated="onGenerationComplete"
      @error="onGenerationError"
    >
      <template #pdf-content>
        <div class="pdf-content">
          <slot></slot>
        </div>
      </template>
    </vue-html2pdf>
  </div>
</template>

<script setup>
import { ref, defineProps, defineExpose, defineEmits } from 'vue';

const props = defineProps({
  filename: {
    type: String,
    default: 'document.pdf'
  }
});

const emit = defineEmits(['progress', 'started', 'complete', 'error']);

const pdfRef = ref(null);

const onProgress = (progress) => {
  console.log('PDF生成进度:', progress);
  emit('progress', progress);
};

const onGenerationStarted = () => {
  console.log('PDF生成开始');
  emit('started');
};

const onGenerationComplete = () => {
  console.log('PDF生成完成');
  emit('complete');
};

const onGenerationError = (error) => {
  console.error('PDF生成错误:', error);
  emit('error', error);
};

// 暴露方法给父组件
const generatePdf = async () => {
  if (pdfRef.value) {
    try {
      await pdfRef.value.generatePdf();
      return true;
    } catch (error) {
      console.error('PDF生成失败:', error);
      emit('error', error);
      return false;
    }
  }
  return false;
};

defineExpose({
  generatePdf
});
</script>

<style scoped>
.pdf-generator {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.pdf-content {
  background-color: white;
  padding: 20px;
  width: 794px; /* A4宽度 */
  min-height: 1123px; /* A4高度 */
  margin: 0 auto;
}
</style> 