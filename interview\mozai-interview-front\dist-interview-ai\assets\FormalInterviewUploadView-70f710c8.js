import{d as T,r as V,m as h,T as B,e as c,o as C,c as x,b as a,w as s,g as N,X as q,a as g,f as b,_ as I,Y as z,u as P,E as _}from"./index-4df1abd4.js";import{f as R,I as H,c as O,u as Y}from"./interview-b37c71d4.js";const E={class:"custom-date-picker"},L={class:"date-picker-container"},W={class:"actions"},A=T({__name:"CustomDatePicker",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:"请选择日期时间"},disabled:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(S,{emit:D}){const y=S,v=D,f=V(!1),o=V(null),p=V("");h(()=>{if(y.modelValue)try{const t=new Date(y.modelValue);if(!isNaN(t.getTime()))o.value=t,p.value=d(t);else{const e=new Date;o.value=e,p.value=d(e),v("update:modelValue",d(e))}}catch(t){console.error("初始化日期时出错:",t);const e=new Date;o.value=e,p.value=d(e),v("update:modelValue",d(e))}else{const t=new Date;o.value=t,p.value=d(t),v("update:modelValue",d(t))}});const $=[{text:"今天",value:new Date},{text:"明天",value:()=>{const t=new Date;return t.setTime(t.getTime()+3600*1e3*24),t}},{text:"一周后",value:()=>{const t=new Date;return t.setTime(t.getTime()+3600*1e3*24*7),t}}];function d(t){const e=t.getFullYear(),l=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0"),u=String(t.getHours()).padStart(2,"0"),m=String(t.getMinutes()).padStart(2,"0"),i=String(t.getSeconds()).padStart(2,"0");return`${e}-${l}-${n} ${u}:${m}:${i}`}function k(t){t&&(o.value=t)}function F(){if(o.value){const t=d(o.value);p.value=t,v("update:modelValue",t),v("change",t)}f.value=!1}return B(()=>y.modelValue,t=>{if(t&&t!==p.value)try{if(t.includes("yyyy")||t.includes("Fr")){const e=new Date;o.value=e,p.value=d(e),v("update:modelValue",d(e))}else{const e=new Date(t);isNaN(e.getTime())||(o.value=e,p.value=d(e))}}catch(e){console.error("解析日期时出错:",e)}}),(t,e)=>{const l=c("el-icon"),n=c("el-input"),u=c("el-date-picker"),m=c("el-button"),i=c("el-popover");return C(),x("div",E,[a(n,{modelValue:p.value,"onUpdate:modelValue":e[0]||(e[0]=r=>p.value=r),placeholder:S.placeholder,disabled:S.disabled,onClick:e[1]||(e[1]=r=>f.value=!0),class:"date-input"},{prefix:s(()=>[a(l,null,{default:s(()=>[a(N(q))]),_:1})]),_:1},8,["modelValue","placeholder","disabled"]),a(i,{visible:f.value,"onUpdate:visible":e[4]||(e[4]=r=>f.value=r),trigger:"manual",placement:"bottom-start",width:320},{reference:s(()=>e[5]||(e[5]=[g("div",null,null,-1)])),default:s(()=>[g("div",L,[a(u,{ref:"datePicker",modelValue:o.value,"onUpdate:modelValue":e[2]||(e[2]=r=>o.value=r),type:"datetime",placeholder:S.placeholder,format:"yyyy-MM-dd HH:mm:ss",shortcuts:$,style:{width:"100%"},onChange:k},null,8,["modelValue","placeholder"]),g("div",W,[a(m,{size:"small",onClick:e[3]||(e[3]=r=>f.value=!1)},{default:s(()=>e[6]||(e[6]=[b("取消",-1)])),_:1,__:[6]}),a(m,{size:"small",type:"primary",onClick:F},{default:s(()=>e[7]||(e[7]=[b("确定",-1)])),_:1,__:[7]})])])]),_:1},8,["visible"])])}}});const X=I(A,[["__scopeId","data-v-1e8163b0"]]),j={class:"upload-container"},G={class:"upload-content"},J={class:"form-actions"},K=T({__name:"FormalInterviewUploadView",setup(S){const D=P(),y=V(),v=V(!1),f=V([]),o=z({company:"",position:"",interviewTime:"",mediaFile:null}),p={company:[{required:!0,message:"请输入面试公司",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],position:[{required:!0,message:"请选择面试岗位",trigger:"change"}],interviewTime:[{required:!0,message:"请选择面试时间",trigger:"change"}],mediaFile:[{required:!0,message:"请上传面试音视频文件",trigger:"change"}]};h(()=>{const e=new Date;o.interviewTime=R(e.toString())});const $=e=>{console.log("自定义日期选择器变更:",e),o.interviewTime=e},d=e=>{var n,u;if(!((n=e.raw)!=null&&n.type.startsWith("audio/"))&&!((u=e.raw)!=null&&u.type.startsWith("video/")))return _.error("请上传音频或视频文件"),f.value=[],!1;const l=500*1024*1024;return e.size&&e.size>l?(_.error("文件大小不能超过500MB"),f.value=[],!1):(o.mediaFile=e.raw||null,!0)},k=()=>{o.mediaFile=null,f.value=[]},F=async e=>{e&&await e.validate(async l=>{if(l){v.value=!0;try{if(!o.mediaFile){_.error("请上传面试音视频文件"),v.value=!1;return}let n=o.interviewTime;if(n&&(n.includes("yyyy")||n.includes("Fr"))){const i=new Date;n=`${i.getFullYear()}-${String(i.getMonth()+1).padStart(2,"0")}-${String(i.getDate()).padStart(2,"0")} ${String(i.getHours()).padStart(2,"0")}:${String(i.getMinutes()).padStart(2,"0")}:${String(i.getSeconds()).padStart(2,"0")}`,console.log("日期格式不正确，已修复为当前时间:",n)}const u={type:H.FORMAL,company:o.company,position:o.position,interviewTime:n,status:"pending",videoUrl:o.mediaFile.name};console.log("提交面试数据:",u);const m=await O(u);if(m){console.log("面试记录创建成功，ID:",m);let i=!1;try{i=await Y(m,o.mediaFile,r=>{console.log(`上传进度: ${r}%`)})}catch(r){console.error("OSS直传失败:",r),i=!1}i?(_.success("面试信息和视频提交成功"),D.push({path:"/interviews",query:{tab:"formal"}})):_.error("视频上传失败，请稍后重试")}else _.error("提交面试信息失败")}catch(n){console.error("提交面试信息失败:",n),_.error("提交失败，请稍后重试")}finally{v.value=!1}}else _.warning("请完善表单信息")})},t=()=>{D.back()};return(e,l)=>{const n=c("el-input"),u=c("el-form-item"),m=c("el-option"),i=c("el-select"),r=c("el-button"),M=c("el-upload"),U=c("el-form");return C(),x("div",j,[g("div",G,[l[8]||(l[8]=g("h1",{class:"title"},"上传正式面试信息",-1)),l[9]||(l[9]=g("p",{class:"description"},"请填写您的面试信息并上传面试音视频",-1)),a(U,{ref_key:"formRef",ref:y,model:o,rules:p,"label-position":"top",class:"upload-form"},{default:s(()=>[a(u,{label:"面试公司",prop:"company"},{default:s(()=>[a(n,{modelValue:o.company,"onUpdate:modelValue":l[0]||(l[0]=w=>o.company=w),placeholder:"请输入面试公司名称"},null,8,["modelValue"])]),_:1}),a(u,{label:"面试岗位",prop:"position"},{default:s(()=>[a(i,{modelValue:o.position,"onUpdate:modelValue":l[1]||(l[1]=w=>o.position=w),placeholder:"请选择面试岗位",style:{width:"100%"}},{default:s(()=>[a(m,{label:"开发",value:1}),a(m,{label:"技术支持",value:2}),a(m,{label:"测试",value:3})]),_:1},8,["modelValue"])]),_:1}),a(u,{label:"面试时间",prop:"interviewTime"},{default:s(()=>[a(X,{modelValue:o.interviewTime,"onUpdate:modelValue":l[2]||(l[2]=w=>o.interviewTime=w),placeholder:"请选择面试时间",onChange:$},null,8,["modelValue"])]),_:1}),a(u,{label:"面试音视频",prop:"mediaFile"},{default:s(()=>[a(M,{class:"media-uploader","auto-upload":!1,limit:1,"on-change":d,"on-remove":k,"file-list":f.value,accept:"audio/*,video/*"},{trigger:s(()=>[a(r,{type:"primary"},{default:s(()=>l[4]||(l[4]=[b("选择文件",-1)])),_:1,__:[4]})]),tip:s(()=>l[5]||(l[5]=[g("div",{class:"el-upload__tip"}," 支持上传音频或视频文件，大小不超过500MB ",-1)])),_:1},8,["file-list"])]),_:1}),g("div",J,[a(r,{onClick:t},{default:s(()=>l[6]||(l[6]=[b("取消",-1)])),_:1,__:[6]}),a(r,{type:"primary",loading:v.value,onClick:l[3]||(l[3]=w=>F(y.value))},{default:s(()=>l[7]||(l[7]=[b(" 提交 ",-1)])),_:1,__:[7]},8,["loading"])])]),_:1},8,["model"])])])}}});const ee=I(K,[["__scopeId","data-v-ec04dd60"]]);export{ee as default};
