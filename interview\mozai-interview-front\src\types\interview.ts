// 面试类型枚举
export enum InterviewType {
  MOCK = 'mock',      // 模拟面试
  FORMAL = 'formal'   // 正式面试
}

// 面试评价
export interface InterviewEvaluation {
  score: number
  feedback: string
  strengths: string
  improvements: string
}

// 基础面试数据
export interface BaseInterview {
  id: string
  type: InterviewType
  position: string        // 面试岗位
  interviewTime: string   // 面试时间
  status: 'in_progress' | 'waiting_result' | 'completed' | 'pending'
  result?: number         // 面试结果(0:未通过,1:通过,null:未评定)
  videoUrl?: string       // 面试视频文件URL
  evaluation?: InterviewEvaluation  // 面试评价
}

// 模拟面试数据
export interface MockInterview extends BaseInterview {
  type: InterviewType.MOCK
  stage: string         // 面试环节
}

// 正式面试数据
export interface FormalInterview extends BaseInterview {
  type: InterviewType.FORMAL
  company: string         // 面试公司
}

// 面试联合类型
export type Interview = MockInterview | FormalInterview

// 后端API返回的面试数据结构
export interface ApiInterview {
  id: string
  type: string
  candidateName?: string
  company?: string
  position: string
  stage?: string
  experience?: string
  status: number
  result?: number
  overallScore?: number
  feedback?: string
  strengths?: string
  improvements?: string
  videoUrl?: string
  interviewTime: string
  createTime: string
  updateTime: string
}

// API响应类型
export interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

// 将后端数据转换为前端数据模型
export function convertApiInterviewToModel(apiInterview: ApiInterview): Interview {
  // 处理position值，确保将数字字符串转换为数字再转回字符串，保持一致的类型
  let position = apiInterview.position;
  if (position && !isNaN(Number(position))) {
    // 转换为数字再转回字符串，保持类型兼容性的同时确保后续处理统一
    position = String(Number(position));
  }
  
  const base = {
    id: apiInterview.id,
    position: position,
    interviewTime: apiInterview.interviewTime,
    status: apiInterview.status === 0 ? 'in_progress' : 
           apiInterview.status === 1 ? 'waiting_result' : 
           apiInterview.status === 2 ? 'completed' : 'pending',
    videoUrl: apiInterview.videoUrl,
    result: apiInterview.result
  };
  
  // 如果有评价数据，则转换评价
  let evaluation: InterviewEvaluation | undefined = undefined;
  if (apiInterview.overallScore !== undefined && apiInterview.feedback) {
    evaluation = {
      score: apiInterview.overallScore,
      feedback: apiInterview.feedback,
      strengths: apiInterview.strengths || '',
      improvements: apiInterview.improvements || ''
    };
  }
  
  // 根据类型返回不同的面试数据结构
  if (apiInterview.type === InterviewType.FORMAL) {
    return {
      ...base,
      type: InterviewType.FORMAL,
      company: apiInterview.company || '未知公司',
      evaluation
    } as FormalInterview;
  } else {
    return {
      ...base,
      type: InterviewType.MOCK,
      stage: apiInterview.stage || '未知阶段',
      evaluation
    } as MockInterview;
  }
} 