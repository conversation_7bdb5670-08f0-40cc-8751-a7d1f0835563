declare const _default: import("vue").DefineComponent<{
    readonly data: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeData) | (() => import("./types").TreeData) | ((new (...args: any[]) => import("./types").TreeData) | (() => import("./types").TreeData))[], unknown, unknown, () => [], boolean>;
    readonly emptyText: {
        readonly type: import("vue").PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly height: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 200, boolean>;
    readonly props: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeOptionProps) | (() => import("./types").TreeOptionProps) | ((new (...args: any[]) => import("./types").TreeOptionProps) | (() => import("./types").TreeOptionProps))[], unknown, unknown, () => import("../../../utils").Mutable<{
        readonly children: import("./virtual-tree").TreeOptionsEnum.CHILDREN;
        readonly label: import("./virtual-tree").TreeOptionsEnum.LABEL;
        readonly disabled: import("./virtual-tree").TreeOptionsEnum.DISABLED;
        readonly value: import("./virtual-tree").TreeOptionsEnum.KEY;
    }>, boolean>;
    readonly highlightCurrent: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly showCheckbox: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly defaultCheckedKeys: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeKey[]) | (() => import("./types").TreeKey[]) | ((new (...args: any[]) => import("./types").TreeKey[]) | (() => import("./types").TreeKey[]))[], unknown, unknown, () => [], boolean>;
    readonly checkStrictly: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly defaultExpandedKeys: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeKey[]) | (() => import("./types").TreeKey[]) | ((new (...args: any[]) => import("./types").TreeKey[]) | (() => import("./types").TreeKey[]))[], unknown, unknown, () => [], boolean>;
    readonly indent: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 16, boolean>;
    readonly itemSize: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, number, boolean>;
    readonly icon: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) | ((new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly expandOnClickNode: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    readonly checkOnClickNode: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly currentNodeKey: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("./types").TreeKey & {}) | (() => import("./types").TreeKey) | ((new (...args: any[]) => import("./types").TreeKey & {}) | (() => import("./types").TreeKey))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly accordion: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly filterMethod: {
        readonly type: import("vue").PropType<import("./types").FilterMethod>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly perfMode: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
}, {
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly data: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeData) | (() => import("./types").TreeData) | ((new (...args: any[]) => import("./types").TreeData) | (() => import("./types").TreeData))[], unknown, unknown, () => [], boolean>;
        readonly emptyText: {
            readonly type: import("vue").PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly height: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 200, boolean>;
        readonly props: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeOptionProps) | (() => import("./types").TreeOptionProps) | ((new (...args: any[]) => import("./types").TreeOptionProps) | (() => import("./types").TreeOptionProps))[], unknown, unknown, () => import("../../../utils").Mutable<{
            readonly children: import("./virtual-tree").TreeOptionsEnum.CHILDREN;
            readonly label: import("./virtual-tree").TreeOptionsEnum.LABEL;
            readonly disabled: import("./virtual-tree").TreeOptionsEnum.DISABLED;
            readonly value: import("./virtual-tree").TreeOptionsEnum.KEY;
        }>, boolean>;
        readonly highlightCurrent: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly showCheckbox: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly defaultCheckedKeys: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeKey[]) | (() => import("./types").TreeKey[]) | ((new (...args: any[]) => import("./types").TreeKey[]) | (() => import("./types").TreeKey[]))[], unknown, unknown, () => [], boolean>;
        readonly checkStrictly: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly defaultExpandedKeys: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeKey[]) | (() => import("./types").TreeKey[]) | ((new (...args: any[]) => import("./types").TreeKey[]) | (() => import("./types").TreeKey[]))[], unknown, unknown, () => [], boolean>;
        readonly indent: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 16, boolean>;
        readonly itemSize: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, number, boolean>;
        readonly icon: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) | ((new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly expandOnClickNode: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly checkOnClickNode: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly currentNodeKey: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("./types").TreeKey & {}) | (() => import("./types").TreeKey) | ((new (...args: any[]) => import("./types").TreeKey & {}) | (() => import("./types").TreeKey))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly accordion: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly filterMethod: {
            readonly type: import("vue").PropType<import("./types").FilterMethod>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly perfMode: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    }>> & {
        "onCurrent-change"?: ((data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => any) | undefined;
        "onNode-expand"?: ((data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => any) | undefined;
        onCheck?: ((data: import("../../tree/src/tree.type").TreeNodeData, checkedInfo: import("./types").CheckedInfo) => any) | undefined;
        "onCheck-change"?: ((data: import("../../tree/src/tree.type").TreeNodeData, checked: boolean) => any) | undefined;
        "onNode-click"?: ((data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode, e: MouseEvent) => any) | undefined;
        "onNode-contextmenu"?: ((event: Event, data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => any) | undefined;
        "onNode-collapse"?: ((data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => any) | undefined;
    }>>;
    emit: ((event: "current-change", data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => void) & ((event: "node-expand", data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => void) & ((event: "check-change", data: import("../../tree/src/tree.type").TreeNodeData, checked: boolean) => void) & ((event: "node-click", data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode, e: MouseEvent) => void) & ((event: "node-contextmenu", event: Event, data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => void) & ((event: "node-collapse", data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => void) & ((event: "check", data: import("../../tree/src/tree.type").TreeNodeData, checkedInfo: import("./types").CheckedInfo) => void);
    slots: Readonly<{
        [name: string]: import("vue").Slot | undefined;
    }>;
    treeNodeSize: import("vue").ComputedRef<number>;
    t: import("element-plus/es/hooks").Translator;
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    flattenTree: import("vue").ComputedRef<import("./types").TreeNode[]>;
    isNotEmpty: import("vue").ComputedRef<boolean>;
    toggleExpand: (node: import("./types").TreeNode) => void;
    isExpanded: (node: import("./types").TreeNode) => boolean;
    isIndeterminate: (node: import("./types").TreeNode) => boolean;
    isChecked: (node: import("./types").TreeNode) => boolean;
    isDisabled: (node: import("./types").TreeNode) => boolean;
    isCurrent: (node: import("./types").TreeNode) => boolean;
    isForceHiddenExpandIcon: (node: import("./types").TreeNode) => boolean;
    handleNodeClick: (node: import("./types").TreeNode, e: MouseEvent) => void;
    handleNodeCheck: (node: import("./types").TreeNode, checked: import("../..").CheckboxValueType) => void;
    toggleCheckbox: (node: import("./types").TreeNode, isChecked: import("../..").CheckboxValueType, nodeClick?: boolean) => void;
    getCurrentNode: () => import("./types").TreeNodeData | undefined;
    getCurrentKey: () => import("./types").TreeKey | undefined;
    setCurrentKey: (key: import("./types").TreeKey) => void;
    getCheckedKeys: (leafOnly?: boolean) => import("./types").TreeKey[];
    getCheckedNodes: (leafOnly?: boolean) => import("./types").TreeNodeData[];
    getHalfCheckedKeys: () => import("./types").TreeKey[];
    getHalfCheckedNodes: () => import("./types").TreeNodeData[];
    setChecked: (key: import("./types").TreeKey, isChecked: boolean) => void;
    setCheckedKeys: (keys: import("./types").TreeKey[]) => void;
    filter: (query: string) => void;
    setData: (data: import("./types").TreeData) => void;
    getNode: (data: import("./types").TreeNodeData | import("./types").TreeKey) => import("./types").TreeNode | undefined;
    expandNode: (node: import("./types").TreeNode) => void;
    collapseNode: (node: import("./types").TreeNode) => void;
    setExpandedKeys: (keys: import("./types").TreeKey[]) => void;
    FixedSizeList: import("vue").DefineComponent<{
        readonly className: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly containerElement: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | Element) & {}) | (() => string | Element) | ((new (...args: any[]) => (string | Element) & {}) | (() => string | Element))[], unknown, unknown, "div", boolean>;
        readonly data: import("../../../utils").EpPropFinalized<(new (...args: any[]) => any[]) | (() => any[]) | ((new (...args: any[]) => any[]) | (() => any[]))[], unknown, unknown, () => [], boolean>;
        readonly direction: import("../../../utils").EpPropFinalized<StringConstructor, "ltr" | "rtl", never, "ltr", false>;
        readonly height: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly innerElement: import("../../../utils").EpPropFinalized<readonly [StringConstructor, ObjectConstructor], unknown, unknown, "div", boolean>;
        readonly style: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("vue").StyleValue & {}) | (() => import("vue").StyleValue) | ((new (...args: any[]) => import("vue").StyleValue & {}) | (() => import("vue").StyleValue))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly useIsScrolling: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly width: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<readonly [NumberConstructor, StringConstructor], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly perfMode: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly scrollbarAlwaysOn: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly cache: import("../../../utils").EpPropFinalized<NumberConstructor, never, never, 2, false>;
        readonly estimatedItemSize: {
            readonly type: import("vue").PropType<number>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly layout: import("../../../utils").EpPropFinalized<StringConstructor, "horizontal" | "vertical", never, "vertical", false>;
        readonly initScrollOffset: import("../../../utils").EpPropFinalized<NumberConstructor, never, never, 0, false>;
        readonly total: {
            readonly type: import("vue").PropType<number>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly itemSize: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (number | import("element-plus/es/components/virtual-list").ItemSize) & {}) | (() => number | import("element-plus/es/components/virtual-list").ItemSize) | ((new (...args: any[]) => (number | import("element-plus/es/components/virtual-list").ItemSize) & {}) | (() => number | import("element-plus/es/components/virtual-list").ItemSize))[], never, never>>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }, {
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        clientSize: import("vue").ComputedRef<string | number | undefined>;
        estimatedTotalSize: import("vue").ComputedRef<number>;
        windowStyle: import("vue").ComputedRef<(string | import("vue").CSSProperties | import("vue").StyleValue[] | {
            [x: string]: string;
            position: string;
            WebkitOverflowScrolling: string;
            willChange: string;
        } | undefined)[]>;
        windowRef: import("vue").Ref<HTMLElement | undefined>;
        innerRef: import("vue").Ref<HTMLElement | undefined>;
        innerStyle: import("vue").ComputedRef<{
            height: string;
            pointerEvents: string | undefined;
            width: string;
        }>;
        itemsToRender: import("vue").ComputedRef<number[]>;
        scrollbarRef: import("vue").Ref<any>;
        states: import("vue").Ref<{
            isScrolling: boolean;
            scrollDir: string;
            scrollOffset: number;
            updateRequested: boolean;
            isScrollbarDragging: boolean;
            scrollbarAlwaysOn: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        }>;
        getItemStyle: (idx: number) => import("vue").CSSProperties;
        onScroll: (e: Event) => void;
        onScrollbarScroll: (distanceToGo: number, totalSteps: number) => void;
        onWheel: (e: WheelEvent) => void;
        scrollTo: (offset: number) => void;
        scrollToItem: (idx: number, alignment?: import("element-plus/es/components/virtual-list").Alignment) => void;
        resetScrollTop: () => void;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("scroll" | "itemRendered")[], "scroll" | "itemRendered", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly className: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly containerElement: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | Element) & {}) | (() => string | Element) | ((new (...args: any[]) => (string | Element) & {}) | (() => string | Element))[], unknown, unknown, "div", boolean>;
        readonly data: import("../../../utils").EpPropFinalized<(new (...args: any[]) => any[]) | (() => any[]) | ((new (...args: any[]) => any[]) | (() => any[]))[], unknown, unknown, () => [], boolean>;
        readonly direction: import("../../../utils").EpPropFinalized<StringConstructor, "ltr" | "rtl", never, "ltr", false>;
        readonly height: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly innerElement: import("../../../utils").EpPropFinalized<readonly [StringConstructor, ObjectConstructor], unknown, unknown, "div", boolean>;
        readonly style: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("vue").StyleValue & {}) | (() => import("vue").StyleValue) | ((new (...args: any[]) => import("vue").StyleValue & {}) | (() => import("vue").StyleValue))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly useIsScrolling: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly width: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<readonly [NumberConstructor, StringConstructor], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly perfMode: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly scrollbarAlwaysOn: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly cache: import("../../../utils").EpPropFinalized<NumberConstructor, never, never, 2, false>;
        readonly estimatedItemSize: {
            readonly type: import("vue").PropType<number>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly layout: import("../../../utils").EpPropFinalized<StringConstructor, "horizontal" | "vertical", never, "vertical", false>;
        readonly initScrollOffset: import("../../../utils").EpPropFinalized<NumberConstructor, never, never, 0, false>;
        readonly total: {
            readonly type: import("vue").PropType<number>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly itemSize: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (number | import("element-plus/es/components/virtual-list").ItemSize) & {}) | (() => number | import("element-plus/es/components/virtual-list").ItemSize) | ((new (...args: any[]) => (number | import("element-plus/es/components/virtual-list").ItemSize) & {}) | (() => number | import("element-plus/es/components/virtual-list").ItemSize))[], never, never>>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }>> & {
        onScroll?: ((...args: any[]) => any) | undefined;
        onItemRendered?: ((...args: any[]) => any) | undefined;
    }, {
        readonly className: string;
        readonly layout: import("../../../utils").EpPropMergeType<StringConstructor, "horizontal" | "vertical", never>;
        readonly direction: import("../../../utils").EpPropMergeType<StringConstructor, "ltr" | "rtl", never>;
        readonly data: any[];
        readonly scrollbarAlwaysOn: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly containerElement: import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | Element) & {}) | (() => string | Element) | ((new (...args: any[]) => (string | Element) & {}) | (() => string | Element))[], unknown, unknown>;
        readonly innerElement: import("../../../utils").EpPropMergeType<readonly [StringConstructor, ObjectConstructor], unknown, unknown>;
        readonly useIsScrolling: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly perfMode: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly cache: number;
        readonly initScrollOffset: number;
    }>;
    ElTreeNode: import("vue").DefineComponent<{
        readonly node: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeNode) | (() => import("./types").TreeNode) | ((new (...args: any[]) => import("./types").TreeNode) | (() => import("./types").TreeNode))[], unknown, unknown, () => import("../../../utils").Mutable<{
            readonly key: -1;
            readonly level: -1;
            readonly data: {};
        }>, boolean>;
        readonly expanded: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly checked: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly indeterminate: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly showCheckbox: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly current: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly hiddenExpandIcon: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly itemSize: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, number, boolean>;
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly node: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeNode) | (() => import("./types").TreeNode) | ((new (...args: any[]) => import("./types").TreeNode) | (() => import("./types").TreeNode))[], unknown, unknown, () => import("../../../utils").Mutable<{
                readonly key: -1;
                readonly level: -1;
                readonly data: {};
            }>, boolean>;
            readonly expanded: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly checked: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly indeterminate: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly showCheckbox: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly current: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly hiddenExpandIcon: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly itemSize: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, number, boolean>;
        }>> & {
            onClick?: ((node: import("./types").TreeNode, e: MouseEvent) => any) | undefined;
            onToggle?: ((node: import("./types").TreeNode) => any) | undefined;
            onCheck?: ((node: import("./types").TreeNode, checked: import("../..").CheckboxValueType) => any) | undefined;
        }>>;
        emit: ((event: "click", node: import("./types").TreeNode, e: MouseEvent) => void) & ((event: "check", node: import("./types").TreeNode, checked: import("../..").CheckboxValueType) => void) & ((event: "toggle", node: import("./types").TreeNode) => void);
        tree: import("./types").TreeContext | undefined;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        indent: import("vue").ComputedRef<number>;
        icon: import("vue").ComputedRef<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) | ((new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>))[], unknown, unknown>>;
        handleClick: (e: MouseEvent) => void;
        handleExpandIconClick: () => void;
        handleCheckChange: (value: import("../..").CheckboxValueType) => void;
        handleContextMenu: (event: Event) => void;
        ElIcon: import("../../../utils").SFCWithInstall<import("vue").DefineComponent<{
            readonly size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }, {
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                readonly size: {
                    readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly color: {
                    readonly type: import("vue").PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
            }>> & {
                [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
            }>>;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            style: import("vue").ComputedRef<import("vue").CSSProperties>;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }>>, {}>> & Record<string, any>;
        ElCheckbox: import("../../../utils").SFCWithInstall<import("vue").DefineComponent<{
            modelValue: {
                type: (NumberConstructor | BooleanConstructor | StringConstructor)[];
                default: undefined;
            };
            label: {
                type: (ObjectConstructor | NumberConstructor | BooleanConstructor | StringConstructor)[];
            };
            indeterminate: BooleanConstructor;
            disabled: BooleanConstructor;
            checked: BooleanConstructor;
            name: {
                type: StringConstructor;
                default: undefined;
            };
            trueLabel: {
                type: (NumberConstructor | StringConstructor)[];
                default: undefined;
            };
            falseLabel: {
                type: (NumberConstructor | StringConstructor)[];
                default: undefined;
            };
            id: {
                type: StringConstructor;
                default: undefined;
            };
            controls: {
                type: StringConstructor;
                default: undefined;
            };
            border: BooleanConstructor;
            size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            tabindex: (NumberConstructor | StringConstructor)[];
            validateEvent: {
                type: BooleanConstructor;
                default: boolean;
            };
        }, {
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                modelValue: {
                    type: (NumberConstructor | BooleanConstructor | StringConstructor)[];
                    default: undefined;
                };
                label: {
                    type: (ObjectConstructor | NumberConstructor | BooleanConstructor | StringConstructor)[];
                };
                indeterminate: BooleanConstructor;
                disabled: BooleanConstructor;
                checked: BooleanConstructor;
                name: {
                    type: StringConstructor;
                    default: undefined;
                };
                trueLabel: {
                    type: (NumberConstructor | StringConstructor)[];
                    default: undefined;
                };
                falseLabel: {
                    type: (NumberConstructor | StringConstructor)[];
                    default: undefined;
                };
                id: {
                    type: StringConstructor;
                    default: undefined;
                };
                controls: {
                    type: StringConstructor;
                    default: undefined;
                };
                border: BooleanConstructor;
                size: {
                    readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                tabindex: (NumberConstructor | StringConstructor)[];
                validateEvent: {
                    type: BooleanConstructor;
                    default: boolean;
                };
            }>> & {
                onChange?: ((val: import("../..").CheckboxValueType) => any) | undefined;
                "onUpdate:modelValue"?: ((val: import("../..").CheckboxValueType) => any) | undefined;
            }>>;
            slots: Readonly<{
                [name: string]: import("vue").Slot | undefined;
            }>;
            inputId: import("vue").Ref<string | undefined>;
            isLabeledByFormItem: import("vue").ComputedRef<boolean>;
            isChecked: import("vue").ComputedRef<boolean>;
            isDisabled: import("vue").ComputedRef<boolean>;
            isFocused: import("vue").Ref<boolean>;
            checkboxSize: import("vue").ComputedRef<"" | "default" | "small" | "large">;
            hasOwnLabel: import("vue").ComputedRef<boolean>;
            model: import("vue").WritableComputedRef<any>;
            handleChange: (e: Event) => void;
            onClickRoot: (e: MouseEvent) => Promise<void>;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            compKls: import("vue").ComputedRef<string[]>;
            spanKls: import("vue").ComputedRef<string[]>;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            "update:modelValue": (val: import("../..").CheckboxValueType) => boolean;
            change: (val: import("../..").CheckboxValueType) => boolean;
        }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            modelValue: {
                type: (NumberConstructor | BooleanConstructor | StringConstructor)[];
                default: undefined;
            };
            label: {
                type: (ObjectConstructor | NumberConstructor | BooleanConstructor | StringConstructor)[];
            };
            indeterminate: BooleanConstructor;
            disabled: BooleanConstructor;
            checked: BooleanConstructor;
            name: {
                type: StringConstructor;
                default: undefined;
            };
            trueLabel: {
                type: (NumberConstructor | StringConstructor)[];
                default: undefined;
            };
            falseLabel: {
                type: (NumberConstructor | StringConstructor)[];
                default: undefined;
            };
            id: {
                type: StringConstructor;
                default: undefined;
            };
            controls: {
                type: StringConstructor;
                default: undefined;
            };
            border: BooleanConstructor;
            size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            tabindex: (NumberConstructor | StringConstructor)[];
            validateEvent: {
                type: BooleanConstructor;
                default: boolean;
            };
        }>> & {
            onChange?: ((val: import("../..").CheckboxValueType) => any) | undefined;
            "onUpdate:modelValue"?: ((val: import("../..").CheckboxValueType) => any) | undefined;
        }, {
            modelValue: string | number | boolean;
            id: string;
            disabled: boolean;
            name: string;
            validateEvent: boolean;
            border: boolean;
            indeterminate: boolean;
            checked: boolean;
            trueLabel: string | number;
            falseLabel: string | number;
            controls: string;
        }>> & {
            CheckboxButton: import("vue").DefineComponent<{
                modelValue: {
                    type: (NumberConstructor | BooleanConstructor | StringConstructor)[];
                    default: undefined;
                };
                label: {
                    type: (ObjectConstructor | NumberConstructor | BooleanConstructor | StringConstructor)[];
                };
                indeterminate: BooleanConstructor;
                disabled: BooleanConstructor;
                checked: BooleanConstructor;
                name: {
                    type: StringConstructor;
                    default: undefined;
                };
                trueLabel: {
                    type: (NumberConstructor | StringConstructor)[];
                    default: undefined;
                };
                falseLabel: {
                    type: (NumberConstructor | StringConstructor)[];
                    default: undefined;
                };
                id: {
                    type: StringConstructor;
                    default: undefined;
                };
                controls: {
                    type: StringConstructor;
                    default: undefined;
                };
                border: BooleanConstructor;
                size: {
                    readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                tabindex: (NumberConstructor | StringConstructor)[];
                validateEvent: {
                    type: BooleanConstructor;
                    default: boolean;
                };
            }, {
                props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                    modelValue: {
                        type: (NumberConstructor | BooleanConstructor | StringConstructor)[];
                        default: undefined;
                    };
                    label: {
                        type: (ObjectConstructor | NumberConstructor | BooleanConstructor | StringConstructor)[];
                    };
                    indeterminate: BooleanConstructor;
                    disabled: BooleanConstructor;
                    checked: BooleanConstructor;
                    name: {
                        type: StringConstructor;
                        default: undefined;
                    };
                    trueLabel: {
                        type: (NumberConstructor | StringConstructor)[];
                        default: undefined;
                    };
                    falseLabel: {
                        type: (NumberConstructor | StringConstructor)[];
                        default: undefined;
                    };
                    id: {
                        type: StringConstructor;
                        default: undefined;
                    };
                    controls: {
                        type: StringConstructor;
                        default: undefined;
                    };
                    border: BooleanConstructor;
                    size: {
                        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    tabindex: (NumberConstructor | StringConstructor)[];
                    validateEvent: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                }>> & {
                    onChange?: ((val: import("../..").CheckboxValueType) => any) | undefined;
                    "onUpdate:modelValue"?: ((val: import("../..").CheckboxValueType) => any) | undefined;
                }>>;
                slots: Readonly<{
                    [name: string]: import("vue").Slot | undefined;
                }>;
                isFocused: import("vue").Ref<boolean>;
                isChecked: import("vue").ComputedRef<boolean>;
                isDisabled: import("vue").ComputedRef<boolean>;
                checkboxButtonSize: import("vue").ComputedRef<"" | "default" | "small" | "large">;
                model: import("vue").WritableComputedRef<any>;
                handleChange: (e: Event) => void;
                checkboxGroup: ({
                    modelValue?: import("vue").WritableComputedRef<any> | undefined;
                    changeEvent?: ((...args: any) => any) | undefined;
                } & import("vue").ToRefs<Pick<import("../..").CheckboxGroupProps, "fill" | "size" | "disabled" | "validateEvent" | "max" | "min" | "textColor">>) | undefined;
                ns: {
                    namespace: import("vue").ComputedRef<string>;
                    b: (blockSuffix?: string) => string;
                    e: (element?: string | undefined) => string;
                    m: (modifier?: string | undefined) => string;
                    be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                    em: (element?: string | undefined, modifier?: string | undefined) => string;
                    bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                    bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                    is: {
                        (name: string, state: boolean | undefined): string;
                        (name: string): string;
                    };
                    cssVar: (object: Record<string, string>) => Record<string, string>;
                    cssVarName: (name: string) => string;
                    cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                    cssVarBlockName: (name: string) => string;
                };
                activeStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
                labelKls: import("vue").ComputedRef<string[]>;
            }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
                "update:modelValue": (val: import("../..").CheckboxValueType) => boolean;
                change: (val: import("../..").CheckboxValueType) => boolean;
            }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
                modelValue: {
                    type: (NumberConstructor | BooleanConstructor | StringConstructor)[];
                    default: undefined;
                };
                label: {
                    type: (ObjectConstructor | NumberConstructor | BooleanConstructor | StringConstructor)[];
                };
                indeterminate: BooleanConstructor;
                disabled: BooleanConstructor;
                checked: BooleanConstructor;
                name: {
                    type: StringConstructor;
                    default: undefined;
                };
                trueLabel: {
                    type: (NumberConstructor | StringConstructor)[];
                    default: undefined;
                };
                falseLabel: {
                    type: (NumberConstructor | StringConstructor)[];
                    default: undefined;
                };
                id: {
                    type: StringConstructor;
                    default: undefined;
                };
                controls: {
                    type: StringConstructor;
                    default: undefined;
                };
                border: BooleanConstructor;
                size: {
                    readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                tabindex: (NumberConstructor | StringConstructor)[];
                validateEvent: {
                    type: BooleanConstructor;
                    default: boolean;
                };
            }>> & {
                onChange?: ((val: import("../..").CheckboxValueType) => any) | undefined;
                "onUpdate:modelValue"?: ((val: import("../..").CheckboxValueType) => any) | undefined;
            }, {
                modelValue: string | number | boolean;
                id: string;
                disabled: boolean;
                name: string;
                validateEvent: boolean;
                border: boolean;
                indeterminate: boolean;
                checked: boolean;
                trueLabel: string | number;
                falseLabel: string | number;
                controls: string;
            }>;
            CheckboxGroup: import("vue").DefineComponent<{
                readonly modelValue: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("../..").CheckboxGroupValueType) | (() => import("../..").CheckboxGroupValueType) | ((new (...args: any[]) => import("../..").CheckboxGroupValueType) | (() => import("../..").CheckboxGroupValueType))[], unknown, unknown, () => never[], boolean>;
                readonly disabled: BooleanConstructor;
                readonly min: NumberConstructor;
                readonly max: NumberConstructor;
                readonly size: {
                    readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly label: StringConstructor;
                readonly fill: StringConstructor;
                readonly textColor: StringConstructor;
                readonly tag: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "div", boolean>;
                readonly validateEvent: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            }, {
                props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                    readonly modelValue: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("../..").CheckboxGroupValueType) | (() => import("../..").CheckboxGroupValueType) | ((new (...args: any[]) => import("../..").CheckboxGroupValueType) | (() => import("../..").CheckboxGroupValueType))[], unknown, unknown, () => never[], boolean>;
                    readonly disabled: BooleanConstructor;
                    readonly min: NumberConstructor;
                    readonly max: NumberConstructor;
                    readonly size: {
                        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly label: StringConstructor;
                    readonly fill: StringConstructor;
                    readonly textColor: StringConstructor;
                    readonly tag: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "div", boolean>;
                    readonly validateEvent: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                }>> & {
                    onChange?: ((val: import("../..").CheckboxValueType[]) => any) | undefined;
                    "onUpdate:modelValue"?: ((val: import("../..").CheckboxGroupValueType) => any) | undefined;
                }>>;
                emit: ((event: "update:modelValue", val: import("../..").CheckboxGroupValueType) => void) & ((event: "change", val: import("../..").CheckboxValueType[]) => void);
                ns: {
                    namespace: import("vue").ComputedRef<string>;
                    b: (blockSuffix?: string) => string;
                    e: (element?: string | undefined) => string;
                    m: (modifier?: string | undefined) => string;
                    be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                    em: (element?: string | undefined, modifier?: string | undefined) => string;
                    bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                    bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                    is: {
                        (name: string, state: boolean | undefined): string;
                        (name: string): string;
                    };
                    cssVar: (object: Record<string, string>) => Record<string, string>;
                    cssVarName: (name: string) => string;
                    cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                    cssVarBlockName: (name: string) => string;
                };
                formItem: import("element-plus/es/components/form").FormItemContext | undefined;
                groupId: import("vue").Ref<string | undefined>;
                isLabeledByFormItem: import("vue").ComputedRef<boolean>;
                changeEvent: (value: import("../..").CheckboxGroupValueType) => Promise<void>;
                modelValue: import("vue").WritableComputedRef<import("../..").CheckboxGroupValueType>;
            }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
                "update:modelValue": (val: import("../..").CheckboxGroupValueType) => boolean;
                change: (val: import("../..").CheckboxValueType[]) => boolean;
            }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
                readonly modelValue: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("../..").CheckboxGroupValueType) | (() => import("../..").CheckboxGroupValueType) | ((new (...args: any[]) => import("../..").CheckboxGroupValueType) | (() => import("../..").CheckboxGroupValueType))[], unknown, unknown, () => never[], boolean>;
                readonly disabled: BooleanConstructor;
                readonly min: NumberConstructor;
                readonly max: NumberConstructor;
                readonly size: {
                    readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly label: StringConstructor;
                readonly fill: StringConstructor;
                readonly textColor: StringConstructor;
                readonly tag: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "div", boolean>;
                readonly validateEvent: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            }>> & {
                onChange?: ((val: import("../..").CheckboxValueType[]) => any) | undefined;
                "onUpdate:modelValue"?: ((val: import("../..").CheckboxGroupValueType) => any) | undefined;
            }, {
                readonly modelValue: import("../..").CheckboxGroupValueType;
                readonly disabled: boolean;
                readonly validateEvent: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
                readonly tag: string;
            }>;
        };
        ElNodeContent: import("vue").DefineComponent<{
            readonly node: {
                readonly type: import("vue").PropType<import("./types").TreeNode>;
                readonly required: true;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
            [key: string]: any;
        }> | import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
            [key: string]: any;
        }>[], unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly node: {
                readonly type: import("vue").PropType<import("./types").TreeNode>;
                readonly required: true;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }>>, {}>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        click: (node: import("./types").TreeNode, e: MouseEvent) => boolean;
        toggle: (node: import("./types").TreeNode) => boolean;
        check: (node: import("./types").TreeNode, checked: import("../..").CheckboxValueType) => boolean;
    }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly node: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeNode) | (() => import("./types").TreeNode) | ((new (...args: any[]) => import("./types").TreeNode) | (() => import("./types").TreeNode))[], unknown, unknown, () => import("../../../utils").Mutable<{
            readonly key: -1;
            readonly level: -1;
            readonly data: {};
        }>, boolean>;
        readonly expanded: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly checked: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly indeterminate: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly showCheckbox: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly current: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly hiddenExpandIcon: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly itemSize: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, number, boolean>;
    }>> & {
        onClick?: ((node: import("./types").TreeNode, e: MouseEvent) => any) | undefined;
        onToggle?: ((node: import("./types").TreeNode) => any) | undefined;
        onCheck?: ((node: import("./types").TreeNode, checked: import("../..").CheckboxValueType) => any) | undefined;
    }, {
        readonly disabled: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly expanded: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly current: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly indeterminate: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly checked: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly node: import("./types").TreeNode;
        readonly itemSize: number;
        readonly showCheckbox: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly hiddenExpandIcon: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    }>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    "node-click": (data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode, e: MouseEvent) => MouseEvent;
    "node-expand": (data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => import("./types").TreeNode;
    "node-collapse": (data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => import("./types").TreeNode;
    "current-change": (data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => import("./types").TreeNode;
    check: (data: import("../../tree/src/tree.type").TreeNodeData, checkedInfo: import("./types").CheckedInfo) => import("./types").CheckedInfo;
    "check-change": (data: import("../../tree/src/tree.type").TreeNodeData, checked: boolean) => boolean;
    "node-contextmenu": (event: Event, data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => import("./types").TreeNode;
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly data: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeData) | (() => import("./types").TreeData) | ((new (...args: any[]) => import("./types").TreeData) | (() => import("./types").TreeData))[], unknown, unknown, () => [], boolean>;
    readonly emptyText: {
        readonly type: import("vue").PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly height: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 200, boolean>;
    readonly props: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeOptionProps) | (() => import("./types").TreeOptionProps) | ((new (...args: any[]) => import("./types").TreeOptionProps) | (() => import("./types").TreeOptionProps))[], unknown, unknown, () => import("../../../utils").Mutable<{
        readonly children: import("./virtual-tree").TreeOptionsEnum.CHILDREN;
        readonly label: import("./virtual-tree").TreeOptionsEnum.LABEL;
        readonly disabled: import("./virtual-tree").TreeOptionsEnum.DISABLED;
        readonly value: import("./virtual-tree").TreeOptionsEnum.KEY;
    }>, boolean>;
    readonly highlightCurrent: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly showCheckbox: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly defaultCheckedKeys: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeKey[]) | (() => import("./types").TreeKey[]) | ((new (...args: any[]) => import("./types").TreeKey[]) | (() => import("./types").TreeKey[]))[], unknown, unknown, () => [], boolean>;
    readonly checkStrictly: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly defaultExpandedKeys: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeKey[]) | (() => import("./types").TreeKey[]) | ((new (...args: any[]) => import("./types").TreeKey[]) | (() => import("./types").TreeKey[]))[], unknown, unknown, () => [], boolean>;
    readonly indent: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 16, boolean>;
    readonly itemSize: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, number, boolean>;
    readonly icon: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) | ((new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly expandOnClickNode: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    readonly checkOnClickNode: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly currentNodeKey: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("./types").TreeKey & {}) | (() => import("./types").TreeKey) | ((new (...args: any[]) => import("./types").TreeKey & {}) | (() => import("./types").TreeKey))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly accordion: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly filterMethod: {
        readonly type: import("vue").PropType<import("./types").FilterMethod>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly perfMode: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
}>> & {
    "onCurrent-change"?: ((data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => any) | undefined;
    "onNode-expand"?: ((data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => any) | undefined;
    onCheck?: ((data: import("../../tree/src/tree.type").TreeNodeData, checkedInfo: import("./types").CheckedInfo) => any) | undefined;
    "onCheck-change"?: ((data: import("../../tree/src/tree.type").TreeNodeData, checked: boolean) => any) | undefined;
    "onNode-click"?: ((data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode, e: MouseEvent) => any) | undefined;
    "onNode-contextmenu"?: ((event: Event, data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => any) | undefined;
    "onNode-collapse"?: ((data: import("../../tree/src/tree.type").TreeNodeData, node: import("./types").TreeNode) => any) | undefined;
}, {
    readonly props: import("./types").TreeOptionProps;
    readonly height: number;
    readonly data: import("./types").TreeData;
    readonly checkStrictly: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly accordion: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly indent: number;
    readonly perfMode: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly itemSize: number;
    readonly showCheckbox: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly defaultCheckedKeys: import("./types").TreeKey[];
    readonly defaultExpandedKeys: import("./types").TreeKey[];
    readonly expandOnClickNode: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly checkOnClickNode: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly highlightCurrent: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
}>;
export default _default;
