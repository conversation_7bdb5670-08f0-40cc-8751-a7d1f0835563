package com.bimowu.resume.common.service;

import com.bimowu.resume.config.PDFGenerationConfig;

/**
 * PDF配置管理器接口
 * 负责管理PDF生成的各种配置参数
 */
public interface PDFConfigManager {
    
    /**
     * 获取页面配置
     * @return 页面配置
     */
    PageConfig getPageConfig();
    
    /**
     * 获取字体配置
     * @return 字体配置
     */
    FontConfig getFontConfig();
    
    /**
     * 获取渲染配置
     * @return 渲染配置
     */
    RenderConfig getRenderConfig();
    
    /**
     * 更新配置
     * @param config PDF生成配置
     */
    void updateConfig(PDFGenerationConfig config);
    
    /**
     * 重置为默认配置
     */
    void resetToDefault();
    
    /**
     * 页面配置
     */
    class PageConfig {
        private String pageSize = "A4";
        private float marginTop = 20f;
        private float marginBottom = 20f;
        private float marginLeft = 20f;
        private float marginRight = 20f;
        private String orientation = "portrait";
        
        // getters and setters
        public String getPageSize() { return pageSize; }
        public void setPageSize(String pageSize) { this.pageSize = pageSize; }
        
        public float getMarginTop() { return marginTop; }
        public void setMarginTop(float marginTop) { this.marginTop = marginTop; }
        
        public float getMarginBottom() { return marginBottom; }
        public void setMarginBottom(float marginBottom) { this.marginBottom = marginBottom; }
        
        public float getMarginLeft() { return marginLeft; }
        public void setMarginLeft(float marginLeft) { this.marginLeft = marginLeft; }
        
        public float getMarginRight() { return marginRight; }
        public void setMarginRight(float marginRight) { this.marginRight = marginRight; }
        
        public String getOrientation() { return orientation; }
        public void setOrientation(String orientation) { this.orientation = orientation; }
    }
    
    /**
     * 字体配置
     */
    class FontConfig {
        private String defaultFontFamily = "SimSun";
        private float defaultFontSize = 12f;
        private String encoding = "UTF-8";
        private boolean embedFonts = true;
        
        // getters and setters
        public String getDefaultFontFamily() { return defaultFontFamily; }
        public void setDefaultFontFamily(String defaultFontFamily) { this.defaultFontFamily = defaultFontFamily; }
        
        public float getDefaultFontSize() { return defaultFontSize; }
        public void setDefaultFontSize(float defaultFontSize) { this.defaultFontSize = defaultFontSize; }
        
        public String getEncoding() { return encoding; }
        public void setEncoding(String encoding) { this.encoding = encoding; }
        
        public boolean isEmbedFonts() { return embedFonts; }
        public void setEmbedFonts(boolean embedFonts) { this.embedFonts = embedFonts; }
    }
    
    /**
     * 渲染配置
     */
    class RenderConfig {
        private float dpi = 300f;
        private boolean enableJavaScript = false;
        private int timeoutSeconds = 30;
        private boolean strictMode = false;
        
        // getters and setters
        public float getDpi() { return dpi; }
        public void setDpi(float dpi) { this.dpi = dpi; }
        
        public boolean isEnableJavaScript() { return enableJavaScript; }
        public void setEnableJavaScript(boolean enableJavaScript) { this.enableJavaScript = enableJavaScript; }
        
        public int getTimeoutSeconds() { return timeoutSeconds; }
        public void setTimeoutSeconds(int timeoutSeconds) { this.timeoutSeconds = timeoutSeconds; }
        
        public boolean isStrictMode() { return strictMode; }
        public void setStrictMode(boolean strictMode) { this.strictMode = strictMode; }
    }
}