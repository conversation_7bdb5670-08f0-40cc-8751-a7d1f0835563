package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.common.service.AdvancedPDFGenerator;
import com.bimowu.resume.common.service.FontManager;
import com.bimowu.resume.config.PDFGenerationConfig;
import com.bimowu.resume.dto.ExtractedStyles;
import com.itextpdf.text.pdf.BaseFont;
import org.jsoup.Jsoup;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StringUtils;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Rectangle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;

/**
 * Flying Saucer PDF生成器实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service("flyingSaucerPDFGenerator")
public class FlyingSaucerPDFGenerator implements AdvancedPDFGenerator {
    
    @Autowired
    private FontManager fontManager;
    
    @Autowired
    private PDFGenerationConfig pdfConfig;

    @Override
    public GeneratorInfo getGeneratorInfo() {
        GeneratorInfo info = new GeneratorInfo();
        info.setName("Flying Saucer PDF Generator");
        info.setVersion("1.0");
        info.setSupportedFeatures(new String[]{"HTML to PDF", "CSS support", "Font embedding"});
        return info;
    }
    
    @Override
    public ValidationResult validateHTML(String htmlContent) {
        ValidationResult result = new ValidationResult(true);
        
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            result.setValid(false);
            result.setErrors(new String[]{"HTML内容为空"});
            return result;
        }
        
        try {
            // 尝试解析HTML
            org.w3c.dom.Document document = parseHTML(htmlContent);
            if (document == null) {
                result.setValid(false);
                result.setErrors(new String[]{"HTML内容无法解析"});
                return result;
            }
            
            // 使用Jsoup检查基本结构
            org.jsoup.nodes.Document jsoupDoc = Jsoup.parse(htmlContent);
            if (jsoupDoc.body() == null || jsoupDoc.body().text().trim().isEmpty()) {
                result.setValid(false);
                result.setErrors(new String[]{"HTML内容没有文本"});
                return result;
            }
            
            result.setValid(true);
            return result;
            
        } catch (Exception e) {
            result.setValid(false);
            result.setErrors(new String[]{"HTML解析错误: " + e.getMessage()});
            return result;
        }
    }

    //----在用
    @Override
    public boolean generatePDFFromHTML(String htmlContent, OutputStream outputStream) {
        try {
            return generatePDFFromHTMLAndCSS(htmlContent, "", outputStream);
        } catch (Exception e) {
            log.error("Flying Saucer PDF生成失败", e);
            return false;
        }
    }
    // XXX 在用
    @Override
    public boolean generatePDFFromHTMLAndCSS(String htmlContent, String cssContent, OutputStream outputStream) {
        try {
            log.info("开始使用Flying Saucer生成PDF");
            
            // 预处理HTML内容
            String processedHtml = preprocessHTML(htmlContent, pdfConfig);
            
            // 如果有CSS内容，将其嵌入到HTML中
            if (StringUtils.hasText(cssContent)) {
                processedHtml = embedCSSIntoHTML(processedHtml, cssContent);
            }
            
            // 创建PDF渲染器
            ITextRenderer renderer = new ITextRenderer();
            ITextFontResolver fontResolver = renderer.getFontResolver();
            // 配置字体
            configureFonts(fontResolver);

            // 应用页面配置
            applyPageConfig(renderer, pdfConfig);
            // 设置文档
//            Document document = parseHTML(processedHtml);
            renderer.setDocumentFromString(processedHtml);
            
            // 布局文档
            renderer.layout();
            
            // 生成PDF
            renderer.createPDF(outputStream);

            log.info("PDF生成成功");
            return true;
            
        } catch (Exception e) {
            log.error("Flying Saucer PDF生成失败", e);
            return false;
        }
    }

    /*
     * 以下方法不实现，因为它们不属于AdvancedPDFGenerator接口
     */

    /**
     * 生成PDF（原方法，不属于接口）
     * @param html HTML内容
     * @param config PDF配置
     * @return PDF字节数组
     */
    public byte[] generatePDF(String html, PDFGenerationConfig config) {
        try {
            log.info("开始使用Flying Saucer生成PDF");
            
            // 预处理HTML内容
            String processedHtml = preprocessHTML(html, config);
            
            // 创建PDF渲染器
            ITextRenderer renderer = new ITextRenderer();

            // 配置字体
            ITextFontResolver fontResolver = renderer.getFontResolver();
            
            // 配置字体
            configureFonts(fontResolver);
            
            // 应用页面配置
            applyPageConfig(renderer, config);
            
            // 设置文档
            Document document = parseHTML(processedHtml);
            renderer.setDocument(document, null);
            
            // 布局文档
            renderer.layout();
            
            // 生成PDF
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            renderer.createPDF(outputStream);
            
            byte[] pdfBytes = outputStream.toByteArray();
            log.info("PDF生成成功，大小: {} KB", pdfBytes.length / 1024);
            
            return pdfBytes;
            
        } catch (Exception e) {
            log.error("Flying Saucer PDF生成失败", e);
            throw new RuntimeException("PDF生成失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成PDF（原方法，不属于接口）
     * @param html HTML内容
     * @return PDF字节数组
     */
    public byte[] generatePDF(String html) {
        return generatePDF(html, pdfConfig);
    }
    
    /**
     * 使用模板ID生成PDF
     */
    public byte[] generatePDFForTemplate(String html) {
        // 由于缺少PDFConfigManager的具体实现，暂时使用默认配置
        return generatePDF(html, pdfConfig);
    }
    
    /**
     * 预处理HTML内容
     */
    private String preprocessHTML(String html, PDFGenerationConfig config) {
        if (html == null || html.trim().isEmpty()) {
            throw new IllegalArgumentException("HTML内容不能为空");
        }
        
        // 确保HTML格式正确
        if (!html.trim().startsWith("<!DOCTYPE") && !html.trim().startsWith("<html")) {
            html = "<!DOCTYPE html><html><head><meta charset=\"UTF-8\"/></head><body>" + html + "</body></html>";
        }
        
        // 处理自闭合标签
        html = html.replaceAll("<img([^>]*?)(?<!/)>", "<img$1/>");
        html = html.replaceAll("<br(?!/)>", "<br/>");
        html = html.replaceAll("<hr(?!/)>", "<hr/>");
        html = html.replaceAll("<input([^>]*?)(?<!/)>", "<input$1/>");
        
        // 确保meta标签正确
        if (!html.contains("charset")) {
            html = html.replace("<head>", "<head><meta charset=\"UTF-8\"/>");
        }
        
        // 注入页面CSS（由于缺少configManager，使用默认配置）
        String pageCSS = generatePageCSS(config);
        if (!pageCSS.isEmpty()) {
            String cssTag = "<style type=\"text/css\">\n" + pageCSS + "</style>";
            if (html.contains("</head>")) {
                html = html.replace("</head>", cssTag + "\n</head>");
            } else {
                html = html.replace("<head>", "<head>" + cssTag);
            }
        }
        
        log.debug("HTML预处理完成");
        return html;
    }
    
    /**
     * 生成页面CSS
     * @param config 配置信息
     * @return CSS内容
     */
    private String generatePageCSS(PDFGenerationConfig config) {
        if (config == null || config.getPage() == null) {
            return "";
        }
        
        StringBuilder css = new StringBuilder();
        PDFGenerationConfig.PageConfig pageConfig = config.getPage();
        PDFGenerationConfig.PageConfig.MarginConfig margin = pageConfig.getMargin();
        
        css.append("@page { ");
        if (margin != null) {
            css.append("margin: ")
               .append(margin.getTop()).append("pt ")
               .append(margin.getRight()).append("pt ")
               .append(margin.getBottom()).append("pt ")
               .append(margin.getLeft()).append("pt; ");
        }
        css.append("size: ").append(pageConfig.getSize()).append("; ");
        css.append("}");
        
        return css.toString();
    }
    
    /**
     * 解析HTML为DOM文档
     */
    private Document parseHTML(String html) throws ParserConfigurationException, IOException, SAXException {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setNamespaceAware(false);
        factory.setValidating(false);
        
        // 禁用DTD验证以提高性能
        factory.setFeature("http://apache.org/xml/features/nonvalidating/load-dtd-grammar", false);
        factory.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
        
        DocumentBuilder builder = factory.newDocumentBuilder();
        
        // 设置错误处理器，忽略警告
        builder.setErrorHandler(null);
        
        ByteArrayInputStream inputStream = new ByteArrayInputStream(html.getBytes(StandardCharsets.UTF_8));
        return builder.parse(inputStream);
    }
    
    /**
     * 配置字体（修复JAR包中字体文件访问问题）
     */
    private void configureFonts(ITextFontResolver fontResolver) {
        try {
            log.info("开始配置PDF字体");
            // 方案2：尝试从classpath加载字体文件
            if (tryLoadClasspathFonts(fontResolver)) {
                log.info("成功加载classpath字体");
                return;
            }
            
            log.info("字体配置完成");

        } catch (OutOfMemoryError e) {
            log.error("字体配置时内存不足，建议增加JVM内存: -Xmx2g", e);
            throw new RuntimeException("字体配置内存不足", e);
        } catch (Exception e) {
            log.error("字体配置失败", e);
            // 不抛出异常，允许PDF生成继续进行（使用系统默认字体）
        }
    }
    
    /**
     * 尝试加载系统字体
     */
    private boolean tryLoadSystemFonts(ITextFontResolver fontResolver) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            boolean success = false;
            log.info("开始尝试加载系统字体: {}", os);
            
            // 优先尝试加载中文字体
            String[] chineseFonts = {
                // Linux中文字体
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
                "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
                "/usr/share/fonts/truetype/arphic/uming.ttc",
                // Windows中文字体
                "C:/Windows/Fonts/simsun.ttc",
                "C:/Windows/Fonts/simhei.ttf",
                "C:/Windows/Fonts/msyh.ttc",
                // macOS中文字体
                "/System/Library/Fonts/PingFang.ttc",
                "/System/Library/Fonts/STSong.ttc"
            };
            
            for (String fontPath : chineseFonts) {
                try {
                    java.io.File fontFile = new java.io.File(fontPath);
                    if (fontFile.exists()) {
                        // 根据字体文件名设置合适的字体名称
                        String fontName = "SimSun"; // 默认使用SimSun
                        if (fontPath.contains("simhei") || fontPath.contains("SimHei")) {
                            fontName = "SimHei";
                        } else if (fontPath.contains("msyh") || fontPath.contains("YaHei")) {
                            fontName = "Microsoft YaHei";
                        } else if (fontPath.contains("PingFang")) {
                            fontName = "PingFang SC";
                        } else if (fontPath.contains("STSong")) {
                            fontName = "STSong";
                        } else if (fontPath.contains("wqy-microhei")) {
                            fontName = "WenQuanYi Micro Hei";
                        } else if (fontPath.contains("Noto")) {
                            fontName = "Noto Sans CJK SC";
                        }
                        
                        fontResolver.addFont(fontPath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
                        log.info("成功加载系统字体: {} -> {}", fontPath, fontName);
                        success = true;
                        
                        // 找到一个可用的中文字体就足够了
                        break;
                    } else {
                        log.debug("字体文件不存在: {}", fontPath);
                    }
                } catch (Exception e) {
                    log.debug("加载系统字体失败: {} - {}", fontPath, e.getMessage());
                }
            }
            
            log.info("系统字体加载结果: {}", success ? "成功" : "失败");
            return success;
        } catch (Exception e) {
            log.warn("系统字体加载失败", e);
            return false;
        }
    }
    
    /**
     * 尝试从classpath加载字体文件
     */
    private boolean tryLoadClasspathFonts(ITextFontResolver fontResolver) {
        try {
            ClassPathResource simsunResource = new ClassPathResource("fonts/simsun.ttf");
            ClassPathResource simheiResource = new ClassPathResource("fonts/simhei.ttf");
            ClassPathResource sourceHanBoldResource = new ClassPathResource("fonts/SourceHanSerifSC-Bold.otf");
            
            boolean success = false;
            
            // 尝试加载SimSun字体
            if (simsunResource.exists()) {
                try {
                    // 使用临时文件方式
                    java.io.InputStream inputStream = simsunResource.getInputStream();
                    java.io.File tempFile = java.io.File.createTempFile("simsun", ".ttf");
                    tempFile.deleteOnExit();
                    
                    try (java.io.FileOutputStream fos = new java.io.FileOutputStream(tempFile)) {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            fos.write(buffer, 0, bytesRead);
                        }
                    }
                    inputStream.close();
                    
                    // 关键修复：使用正确的addFont方法签名和临时文件路径
                    fontResolver.addFont(tempFile.getAbsolutePath(), "SimSun", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, "SimSun");

                    log.info("成功加载SimSun字体（临时文件）: {}", tempFile.getAbsolutePath());
                    success = true;
                } catch (Exception e) {
                    log.warn("SimSun字体加载失败: {}", e.getMessage());
                }
            } else {
                log.warn("SimSun字体文件不存在: fonts/simsun.ttf");
            }
            
            // 尝试加载SimHei字体
            if (simheiResource.exists()) {
                try {
                    java.io.InputStream inputStream = simheiResource.getInputStream();
                    java.io.File tempFile = java.io.File.createTempFile("simhei", ".ttf");
                    tempFile.deleteOnExit();
                    
                    try (java.io.FileOutputStream fos = new java.io.FileOutputStream(tempFile)) {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            fos.write(buffer, 0, bytesRead);
                        }
                    }
                    inputStream.close();
                    
                    // 关键修复：使用正确的addFont方法签名和临时文件路径
                    fontResolver.addFont(tempFile.getAbsolutePath(), "SimHei", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, "SimHei");
                    // 为Flying Saucer注册SimHei作为SimSun的粗体变体
                    fontResolver.addFont(tempFile.getAbsolutePath(), "SimSun-Bold", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, "SimHei");
                    log.info("成功加载SimHei字体（临时文件）: {}", tempFile.getAbsolutePath());
                    success = true;
                } catch (Exception e) {
                    log.warn("SimHei字体加载失败: {}", e.getMessage());
                }
            } else {
                log.warn("SimHei字体文件不存在: fonts/simhei.ttf");
            }

            // 加载SourceHanSerifSC-Bold字体用于加粗效果
            if (sourceHanBoldResource.exists()) {
                try {
                    // 创建临时文件
                    File tempFile = File.createTempFile("SourceHanSerifSC-Bold", ".otf");
                    tempFile.deleteOnExit();

                    // 复制字体文件到临时位置
                    try (InputStream inputStream = sourceHanBoldResource.getInputStream();
                         FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, bytesRead);
                        }
                    }

                    // 注册字体
                    fontResolver.addFont(tempFile.getAbsolutePath(), "SourceHanSerifSC-Bold", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, "SourceHanSerifSC-Bold");
                    // 同时注册为SimSun的粗体变体
                    fontResolver.addFont(tempFile.getAbsolutePath(), "SimSun-Bold", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, "SourceHanSerifSC-Bold");
                    log.info("成功加载SourceHanSerifSC-Bold字体: {}", tempFile.getAbsolutePath());
                    success = true;
                } catch (Exception e) {
                    log.warn("SourceHanSerifSC-Bold字体加载失败: {}", e.getMessage());
                }
            } else {
                log.warn("SourceHanSerifSC-Bold字体文件不存在: fonts/SourceHanSerifSC-Bold.otf");
            }

            return success;
        } catch (Exception e) {
            log.warn("classpath字体加载失败", e);
            return false;
        }
    }
    
    /**
     * 设置字体映射（最后的备用方案）
     */
    private void setupFontMapping(ITextFontResolver fontResolver) {
        try {
            // 添加字体目录（修正参数）
            fontResolver.addFontDirectory("/usr/share/fonts", BaseFont.NOT_EMBEDDED);
            fontResolver.addFontDirectory("C:/Windows/Fonts", BaseFont.NOT_EMBEDDED);
            log.info("已添加系统字体目录");
        } catch (Exception e) {
            log.warn("添加字体目录失败: {}", e.getMessage());
        }
    }
    
    /**
     * 获取页面大小
     */
    private Rectangle getPageSize(PDFGenerationConfig config) {
        if (config == null || config.getPage() == null) {
            return PageSize.A4;
        }
        
        String pageSize = config.getPage().getSize().toUpperCase();
        
        switch (pageSize) {
            case "A3":
                return PageSize.A3;
            case "A4":
                return PageSize.A4;
            case "A5":
                return PageSize.A5;
            case "LETTER":
                return PageSize.LETTER;
            case "LEGAL":
                return PageSize.LEGAL;
            default:
                return PageSize.A4;
        }
    }
    
    /**
     * 应用页面配置
     */
    private void applyPageConfig(ITextRenderer renderer, PDFGenerationConfig config) {
        try {
            // 设置页面大小
            Rectangle pageSize = getPageSize(config);
            
            // 如果是横向，旋转页面
            if (config.getPage() != null && 
                "landscape".equalsIgnoreCase(config.getPage().getOrientation())) {
                pageSize = pageSize.rotate();
            }
            
            // 应用页边距
            // 页边距通过CSS控制，这里记录配置用于后续CSS生成
            log.debug("页面配置应用完成");
                
        } catch (Exception e) {
            log.warn("页面配置应用失败: {}", e.getMessage());
        }
    }
    
    /**
     * 将CSS嵌入到HTML中
     */
    private String embedCSSIntoHTML(String htmlContent, String cssContent) {
        if (cssContent == null || cssContent.trim().isEmpty()) {
            return htmlContent;
        }
        
        // 查找</head>标签的位置
        int headEndIndex = htmlContent.indexOf("</head>");
        if (headEndIndex == -1) {
            // 如果没有</head>标签，查找<body>标签
            int bodyStartIndex = htmlContent.indexOf("<body");
            if (bodyStartIndex != -1) {
                // 在<body>标签前插入<head>和CSS
                String headWithCSS = "<head><style type=\"text/css\">" + cssContent + "</style></head>";
                return htmlContent.substring(0, bodyStartIndex) + headWithCSS + htmlContent.substring(bodyStartIndex);
            } else {
                // 如果都没有，直接在开头添加
                return "<head><style type=\"text/css\">" + cssContent + "</style></head>" + htmlContent;
            }
        } else {
            // 在</head>标签前插入CSS
            String styleTag = "<style type=\"text/css\">" + cssContent + "</style>";
            return htmlContent.substring(0, headEndIndex) + styleTag + htmlContent.substring(headEndIndex);
        }
    }
}