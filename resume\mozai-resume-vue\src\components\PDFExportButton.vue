<template>
  <div class="pdf-export-button">
    <!-- PDF操作下拉菜单 -->
    <el-dropdown @command="handleCommand" :loading="loading">
      <el-button type="success" :loading="loading">
        PDF操作 <i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="export">
            <i class="el-icon-download"></i> 导出PDF
          </el-dropdown-item>
          <el-dropdown-item command="preview">
            <i class="el-icon-view"></i> 预览PDF
          </el-dropdown-item>
          <el-dropdown-item command="status" divided>
            <i class="el-icon-info"></i> 系统状态
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    
    <!-- 系统状态对话框 -->
    <el-dialog
      title="PDF系统状态"
      v-model="statusDialogVisible"
      width="600px"
      :close-on-click-modal="false">
      
      <div v-if="systemStatus" class="status-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="status-item">
              <span class="label">当前引擎:</span>
              <el-tag :type="systemStatus.newSystemEnabled ? 'success' : 'warning'">
                {{ systemStatus.newSystemEnabled ? 'Flying Saucer' : 'XMLWorker' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="status-item">
              <span class="label">系统健康:</span>
              <el-tag :type="systemHealth.healthy ? 'success' : 'danger'">
                {{ systemHealth.healthy ? '正常' : '异常' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 15px;">
          <el-col :span="12">
            <div class="status-item">
              <span class="label">A/B测试:</span>
              <el-tag :type="systemStatus.abTestEnabled ? 'primary' : 'info'">
                {{ systemStatus.abTestEnabled ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="status-item">
              <span class="label">流量比例:</span>
              <span>{{ systemStatus.trafficPercent }}%</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 15px;">
          <el-col :span="12">
            <div class="status-item">
              <span class="label">降级模式:</span>
              <el-tag :type="systemStatus.inFallbackMode ? 'danger' : 'success'">
                {{ systemStatus.inFallbackMode ? '是' : '否' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="status-item">
              <span class="label">失败次数:</span>
              <el-tag :type="systemStatus.failureCount > 0 ? 'warning' : 'success'">
                {{ systemStatus.failureCount }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        
        <!-- 性能指标 -->
        <div class="performance-section" style="margin-top: 20px;">
          <h4>性能指标</h4>
          <el-progress 
            :percentage="successRate" 
            :color="successRate > 90 ? '#67c23a' : successRate > 70 ? '#e6a23c' : '#f56c6c'"
            :format="format => `成功率 ${format}%`">
          </el-progress>
          
          <div style="margin-top: 10px;">
            <span class="metric-text">平均响应时间: {{ avgResponseTime }}ms</span>
            <span class="metric-text" style="margin-left: 20px;">今日请求: {{ todayRequests }}</span>
          </div>
        </div>
      </div>
      
      <div v-else class="loading-content">
        <el-loading-text>正在获取系统状态...</el-loading-text>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="statusDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="refreshStatus">刷新</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { 
  exportResumeToPdf, 
  previewResumePdf, 
  getPdfSystemStatus, 
  getPdfSystemHealth 
} from '@/api/resume'

export default {
  name: 'PDFExportButton',
  props: {
    resumeId: {
      type: [String, Number],
      required: true
    },
    resumeData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      statusDialogVisible: false,
      systemStatus: null,
      systemHealth: { healthy: false },
      successRate: 95,
      avgResponseTime: 2340,
      todayRequests: 156
    }
  },
  methods: {
    // 处理下拉菜单命令
    handleCommand(command) {
      switch (command) {
        case 'export':
          this.exportPdf()
          break
        case 'preview':
          this.previewPdf()
          break
        case 'status':
          this.showSystemStatus()
          break
      }
    },
    
    // 导出PDF
    async exportPdf() {
      this.loading = true
      const loadingMessage = this.$message({
        message: '正在生成PDF，请稍候...',
        type: 'info',
        duration: 0
      })
      
      try {
        const startTime = Date.now()
        const response = await exportResumeToPdf(this.resumeId)
        const duration = Date.now() - startTime
        
        // 创建下载链接
        const blob = new Blob([response], { type: 'application/pdf' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        
        // 生成文件名
        const fileName = `${this.resumeData.information?.name || '简历'}_${new Date().toISOString().slice(0, 10)}.pdf`
        link.download = fileName
        
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        loadingMessage.close()
        this.$message.success('PDF导出成功！')
        
        // 触发导出成功事件
        this.$emit('export-success', { type: 'pdf', duration, fileName })
        
      } catch (error) {
        loadingMessage.close()
        console.error('PDF导出失败:', error)
        
        // 根据错误类型显示不同的错误信息
        let errorMessage = 'PDF导出失败，请稍后重试'
        if (error.response) {
          if (error.response.status === 404) {
            errorMessage = '简历不存在或已被删除'
          } else if (error.response.status === 403) {
            errorMessage = '没有权限导出此简历'
          } else if (error.response.status === 503) {
            errorMessage = '服务器繁忙，请稍后重试'
          }
        } else if (error.code === 'ECONNABORTED') {
          errorMessage = '导出超时，请稍后重试'
        }
        
        this.$message.error(errorMessage)
        
        // 触发导出失败事件
        this.$emit('export-error', { type: 'pdf', error: error.message })
        
      } finally {
        this.loading = false
      }
    },
    
    // 预览PDF
    async previewPdf() {
      const loadingMessage = this.$message({
        message: '正在生成PDF预览...',
        type: 'info',
        duration: 0
      })
      
      try {
        const response = await previewResumePdf(this.resumeId)
        
        // 创建预览窗口
        const blob = new Blob([response], { type: 'application/pdf' })
        const url = window.URL.createObjectURL(blob)
        
        // 在新窗口中打开PDF
        const previewWindow = window.open(url, '_blank')
        if (!previewWindow) {
          // 如果弹窗被阻止，提供下载选项
          this.$confirm('浏览器阻止了弹窗，是否下载PDF文件？', '提示', {
            confirmButtonText: '下载',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            const link = document.createElement('a')
            link.href = url
            link.download = `${this.resumeData.information?.name || '简历'}_预览.pdf`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
          }).finally(() => {
            window.URL.revokeObjectURL(url)
          })
        } else {
          // 预览窗口关闭时清理URL
          const checkClosed = setInterval(() => {
            if (previewWindow.closed) {
              window.URL.revokeObjectURL(url)
              clearInterval(checkClosed)
            }
          }, 1000)
        }
        
        loadingMessage.close()
        this.$message.success('PDF预览生成成功')
        
        // 触发预览成功事件
        this.$emit('preview-success', { type: 'pdf' })
        
      } catch (error) {
        loadingMessage.close()
        console.error('PDF预览失败:', error)
        this.$message.error('PDF预览失败，请稍后重试')
        
        // 触发预览失败事件
        this.$emit('preview-error', { type: 'pdf', error: error.message })
      }
    },
    
    // 显示系统状态
    async showSystemStatus() {
      this.statusDialogVisible = true
      await this.refreshStatus()
    },
    
    // 刷新系统状态
    async refreshStatus() {
      try {
        const [statusResponse, healthResponse] = await Promise.all([
          getPdfSystemStatus(),
          getPdfSystemHealth()
        ])
        
        if (statusResponse.success) {
          this.systemStatus = statusResponse.data
        }
        
        this.systemHealth = healthResponse
        
        // 模拟性能数据（实际项目中应该从后端获取）
        this.successRate = Math.floor(Math.random() * 10) + 90
        this.avgResponseTime = Math.floor(Math.random() * 1000) + 1500
        this.todayRequests = Math.floor(Math.random() * 100) + 100
        
      } catch (error) {
        console.error('获取系统状态失败:', error)
        this.$message.error('获取系统状态失败')
      }
    }
  }
}
</script>

<style scoped>
.pdf-export-button {
  display: inline-block;
}

.status-content {
  padding: 10px 0;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.status-item .label {
  margin-right: 10px;
  color: #606266;
  font-weight: 500;
  min-width: 80px;
}

.performance-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
}

.metric-text {
  color: #606266;
  font-size: 13px;
}

.loading-content {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}
</style>