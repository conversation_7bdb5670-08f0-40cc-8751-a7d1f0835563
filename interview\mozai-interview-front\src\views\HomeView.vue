<template>
  <div class="home-container">
    <!-- 主横幅 -->
    <section class="hero-section">
      <div class="hero-content">
        <h1>墨崽AI智能面试系统</h1>
        <p class="description">
          通过AI技术打造的智能面试平台，为您提供专业的面试评估和训练
        </p>
        <div class="hero-buttons">
          <el-button type="primary" size="large" @click="startInterview">开始面试</el-button>
          <el-button size="large" @click="learnMore">了解更多</el-button>
        </div>
      </div>
      <div class="hero-image">
        <img src="../assets/interview-illustration.svg" alt="面试插图" />
      </div>
    </section>

    <!-- 功能特点 -->
    <section class="features-section">
      <div class="section-title">
        <h2>功能特点</h2>
        <p>我们提供全方位的面试解决方案</p>
      </div>
      
      <div class="features">
        <div class="feature-card">
          <el-icon class="feature-icon"><Document /></el-icon>
          <h3>简历分析</h3>
          <p>上传您的简历，AI会根据您的经历生成针对性问题，帮助您准备更符合自身情况的面试</p>
        </div>
        
        <div class="feature-card">
          <el-icon class="feature-icon"><VideoCamera /></el-icon>
          <h3>AI数字人面试</h3>
          <p>由逼真的AI数字人进行面试，提供真实面试体验，帮助您克服面试紧张感</p>
        </div>
        
        <div class="feature-card">
          <el-icon class="feature-icon"><PieChart /></el-icon>
          <h3>全面评估</h3>
          <p>对回答内容、表情、环境等多维度评分，帮助您了解自己的优势和不足</p>
        </div>
        
        <div class="feature-card">
          <el-icon class="feature-icon"><TrendCharts /></el-icon>
          <h3>进度追踪</h3>
          <p>记录每次面试结果，分析您的进步情况，提供针对性的改进建议</p>
        </div>
      </div>
    </section>

    <!-- 使用流程 -->
    <section class="process-section">
      <div class="section-title">
        <h2>使用流程</h2>
        <p>简单四步，轻松完成面试训练</p>
      </div>
      
      <el-steps :active="1" finish-status="success" simple>
        <el-step title="选择面试类型" description="选择您想要进行的面试类型" />
        <el-step title="上传简历" description="上传您的简历以获取个性化面试问题" />
        <el-step title="AI面试" description="由AI数字人进行面试互动" />
        <el-step title="获取评估报告" description="获取详细的面试表现评估报告" />
      </el-steps>
      
      <div class="action-button">
        <el-button type="primary" @click="startInterview">立即开始</el-button>
      </div>
    </section>

    <!-- 用户评价 -->
    <section class="testimonials-section">
      <div class="section-title">
        <h2>用户评价</h2>
        <p>听听其他用户怎么说</p>
      </div>
      
      <el-carousel :interval="4000" type="card" height="280px">
        <el-carousel-item v-for="(testimonial, index) in testimonials" :key="index">
          <div class="testimonial-card">
            <div class="user-info">
              <el-avatar :size="60" :src="testimonial.avatar">{{ testimonial.name.charAt(0) }}</el-avatar>
              <div>
                <h4>{{ testimonial.name }}</h4>
                <p>{{ testimonial.position }}</p>
              </div>
            </div>
            <div class="testimonial-content">
              <p>"{{ testimonial.content }}"</p>
            </div>
            <div class="testimonial-rating">
              <el-rate v-model="testimonial.rating" disabled show-score text-color="#ff9900"></el-rate>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </section>


    <!-- 页脚 -->
    <footer class="site-footer">


      <div class="footer-bottom">
        <p>© 2023 墨崽AI面试系统. 保留所有权利.</p>

      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Document, 
  VideoCamera, 
  PieChart, 
  TrendCharts, 
  Location, 
  Phone, 
  Message, 
  Platform, 
  ChatDotRound, 
  Share 
} from '@element-plus/icons-vue'
import axios from "axios";

const router = useRouter()

const startInterview = () => {
  router.push('/selection')
}

const learnMore = () => {
  const element = document.querySelector('.features-section')
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

// 用户评价数据
const testimonials = ref([
  {
    name: '李明',
    position: '软件工程师',
    avatar: '',
    content: '通过墨崽AI面试系统的训练，我成功通过了三家知名科技公司的面试。系统提供的反馈非常专业，帮助我发现了很多自己没注意到的问题。',
    rating: 5
  },
  {
    name: '张婷',
    position: '人力资源经理',
    avatar: '',
    content: '作为招聘负责人，我推荐候选人使用墨崽AI面试系统进行练习。它能很好地模拟我们公司的面试流程，让候选人更有准备。',
    rating: 4.5
  },
  {
    name: '王伟',
    position: '应届毕业生',
    avatar: '',
    content: '没有面试经验的我通过墨崽AI面试系统进行了多次模拟训练，大大提升了我的自信心。最终我成功获得了心仪公司的offer！',
    rating: 5
  },
  {
    name: '刘芳',
    position: '产品经理',
    avatar: '',
    content: '系统的分析非常全面，不仅评价了我的回答内容，还给出了关于肢体语言和表情的建议，这在其他平台上很少见。',
    rating: 4.7
  }
])
</script>

<style scoped>
.home-container {
  width: 100%;
}

/* 主横幅样式 */
.hero-section {
  display: flex;
  align-items: center;
  padding: 80px 10% 60px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.hero-content {
  flex: 1;
  padding-right: 40px;
}

.hero-content h1 {
  font-size: 3rem;
  margin-bottom: 20px;
  color: #409EFF;
  font-weight: 700;
}

.hero-content .description {
  font-size: 1.2rem;
  color: #606266;
  margin-bottom: 40px;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 15px;
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-image img {
  max-width: 100%;
  height: auto;
}

/* 通用部分样式 */
section {
  padding: 80px 10%;
}

.section-title {
  text-align: center;
  margin-bottom: 50px;
}

.section-title h2 {
  font-size: 2.5rem;
  color: #303133;
  margin-bottom: 15px;
}

.section-title p {
  font-size: 1.2rem;
  color: #909399;
}

/* 功能特点部分 */
.features-section {
  background-color: #fff;
}

.features {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: center;
}

.feature-card {
  flex: 1 1 300px;
  padding: 30px;
  background: #f9f9f9;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 3rem;
  color: #409EFF;
  margin-bottom: 20px;
}

.feature-card h3 {
  font-size: 1.5rem;
  color: #303133;
  margin-bottom: 15px;
}

.feature-card p {
  color: #606266;
  line-height: 1.6;
}

/* 使用流程部分 */
.process-section {
  background-color: #f5f7fa;
}

.action-button {
  text-align: center;
  margin-top: 40px;
}

/* 用户评价部分 */
.testimonials-section {
  background-color: #fff;
}

.testimonial-card {
  height: 100%;
  padding: 30px;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.user-info div {
  margin-left: 15px;
}

.user-info h4 {
  font-size: 1.2rem;
  margin: 0 0 5px;
  color: #303133;
}

.user-info p {
  margin: 0;
  color: #909399;
}

.testimonial-content {
  flex: 1;
  margin-bottom: 20px;
}

.testimonial-content p {
  color: #606266;
  line-height: 1.6;
}

.testimonial-rating {
  margin-top: auto;
}

/* FAQ部分 */
.faq-section {
  background-color: #f5f7fa;
}

/* 联系我们部分 */
.contact-section {
  background-color: #fff;
}

.contact-container {
  display: flex;
  gap: 40px;
}

.contact-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.contact-item .el-icon {
  font-size: 24px;
  color: #409EFF;
  margin-right: 15px;
}

.contact-form {
  flex: 1;
}

/* 页脚样式 */
.site-footer {
  background-color: #303133;
  color: #fff;
  padding: 60px 10% 30px;
}

.footer-content {
  display: flex;
  margin-bottom: 40px;
}

.footer-logo {
  flex: 1;
}

.footer-logo h3 {
  font-size: 1.8rem;
  margin: 0 0 10px;
}

.footer-logo p {
  color: #909399;
}

.footer-links {
  flex: 3;
  display: flex;
  justify-content: space-around;
}

.link-group h4 {
  font-size: 1.2rem;
  margin-bottom: 20px;
  color: #E4E7ED;
}

.link-group ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.link-group li {
  margin-bottom: 10px;
}

.link-group a {
  color: #909399;
  text-decoration: none;
  transition: color 0.3s;
}

.link-group a:hover {
  color: #409EFF;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid #606266;
}

.footer-bottom p {
  margin: 0;
  color: #909399;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links a {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #606266;
  color: #fff;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.social-links a:hover {
  background-color: #409EFF;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .hero-section {
    flex-direction: column;
    padding: 60px 5%;
  }
  
  .hero-content {
    padding-right: 0;
    margin-bottom: 40px;
    text-align: center;
  }
  
  .hero-buttons {
    justify-content: center;
  }
  
  section {
    padding: 60px 5%;
  }
  
  .contact-container {
    flex-direction: column;
  }
  
  .footer-content {
    flex-direction: column;
  }
  
  .footer-logo {
    margin-bottom: 30px;
    text-align: center;
  }
  
  .footer-links {
    flex-wrap: wrap;
  }
  
  .link-group {
    flex: 1 1 40%;
    margin-bottom: 30px;
  }
}

@media (max-width: 576px) {
  .hero-content h1 {
    font-size: 2.2rem;
  }
  
  .section-title h2 {
    font-size: 2rem;
  }
  
  .features {
    flex-direction: column;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 20px;
  }
  
  .link-group {
    flex: 1 1 100%;
  }
}
</style> 