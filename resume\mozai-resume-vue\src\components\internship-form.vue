<template>
  <div class="form-container">
    <div class="internship-list">
      <div v-for="(internship, index) in formData" :key="index" class="internship-item">
        <div class="internship-header">
          <h3>实习经历 #{{ index + 1 }}</h3>
          <el-button 
            v-if="formData.length > 1" 
            type="danger" 
            size="small" 
            icon="Delete" 
            circle 
            @click="removeInternship(index)"
          ></el-button>
        </div>
        
        <el-form :model="internship" label-width="100px">
          <el-form-item label="公司名称" required>
            <el-input v-model="internship.company" placeholder="请输入公司名称" />
          </el-form-item>
          
          <el-form-item label="职位" required>
            <el-input v-model="internship.position" placeholder="请输入您的实习职位" />
          </el-form-item>
          
          <el-form-item label="工作地点">
            <el-input v-model="internship.location" placeholder="请输入实习城市" />
          </el-form-item>
          
          <el-form-item label="实习时间" required>
            <el-date-picker
              v-model="internship.startDate"
              type="month"
              placeholder="开始时间"
              format="YYYY-MM"
              value-format="YYYY-MM"
              style="width: 160px;"
            />
            <span class="date-separator">至</span>
            <el-date-picker
              v-model="internship.endDate"
              type="month"
              placeholder="结束时间（在职则留空）"
              format="YYYY-MM"
              value-format="YYYY-MM"
              style="width: 160px;"
            />
            <el-checkbox v-model="internship.currentJob" style="margin-left: 10px">至今</el-checkbox>
          </el-form-item>
          
          <el-form-item label="部门">
            <el-input v-model="internship.department" placeholder="请输入您所在的部门" />
          </el-form-item>
          
          <el-form-item label="实习类型">
            <el-select v-model="internship.jobType" placeholder="请选择实习类型">
              <el-option label="全职实习" value="全职实习" />
              <el-option label="兼职实习" value="兼职实习" />
              <el-option label="毕业实习" value="毕业实习" />
              <el-option label="日常实习" value="日常实习" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="工作描述" required>
            <el-input
              type="textarea"
              v-model="internship.description"
              placeholder="请描述您的主要实习职责、成就和贡献"
              :rows="4"
            />
          </el-form-item>
          
          <el-form-item label="实习收获">
            <el-input
              type="textarea"
              v-model="internship.gains"
              placeholder="请描述您在实习中的收获和学习"
              :rows="3"
            />
          </el-form-item>
        </el-form>
        
        <el-divider v-if="index < formData.length - 1" />
      </div>
    </div>
    
    <div class="add-internship">
      <el-button type="dashed" @click="addInternship" icon="Plus">添加实习经历</el-button>
    </div>

    <div class="form-actions">
      <el-button type="primary" @click="saveForm">保存信息</el-button>
      <el-button @click="resetForm">重置</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = ref([])

// 初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && newVal.length) {
    formData.value = JSON.parse(JSON.stringify(newVal))
  } else {
    // 如果没有数据，创建一个空白的实习经历
    formData.value = [createEmptyInternship()]
  }
}, { immediate: true, deep: true })

// 创建空白的实习经历对象
function createEmptyInternship() {
  return {
    company: '',
    position: '',
    location: '',
    startDate: '',
    endDate: '',
    currentJob: false,
    department: '',
    jobType: '全职实习',
    description: '',
    gains: ''
  }
}

// 添加实习经历
const addInternship = () => {
  formData.value.push(createEmptyInternship())
}

// 移除实习经历
const removeInternship = (index) => {
  formData.value.splice(index, 1)
}

// 保存表单
const saveForm = () => {
  // 基本验证
  let isValid = true
  formData.value.forEach((internship, index) => {
    if (!internship.company || !internship.position || !internship.startDate || !internship.description) {
      ElMessage.warning(`实习经历 #${index + 1} 中的公司名称、职位、开始时间和工作描述为必填项`)
      isValid = false
    }
  })
  
  if (!isValid) return
  
  // 处理"至今"情况
  const processedData = formData.value.map(internship => {
    const processed = { ...internship }
    if (processed.currentJob) {
      processed.endDate = '至今'
    }
    return processed
  })
  
  emit('update', processedData)
  ElMessage.success('实习经历信息已保存')
}

// 重置表单
const resetForm = () => {
  if (props.data && props.data.length) {
    formData.value = JSON.parse(JSON.stringify(props.data))
  } else {
    formData.value = [createEmptyInternship()]
  }
  ElMessage.info('已重置为上次保存的内容')
}
</script>

<style scoped>
.form-container {
  padding: 20px;
}

.internship-list {
  margin-bottom: 20px;
}

.internship-item {
  margin-bottom: 20px;
}

.internship-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.internship-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.date-separator {
  margin: 0 10px;
}

.add-internship {
  margin: 20px 0;
  text-align: center;
}

.add-internship .el-button {
  width: 100%;
  border-style: dashed;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style> 