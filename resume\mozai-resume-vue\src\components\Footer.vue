<template>
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h3>产品</h3>
          <ul>
            <li><router-link to="/templates">简历模板</router-link></li>
            <li><router-link to="/airesume">AI写简历</router-link></li>
            <li><router-link to="/">简历优化</router-link></li>
            <li><router-link to="/">面试辅导</router-link></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h3>服务</h3>
          <ul>
            <li><router-link to="/">简历优化</router-link></li>
            <li><router-link to="/">面试辅导</router-link></li>
            <li><router-link to="/">简历范文</router-link></li>
            <li><router-link to="/">简历攻略</router-link></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h3>公司</h3>
          <ul>
            <li><router-link to="/">关于我们</router-link></li>
            <li><router-link to="/">联系我们</router-link></li>
            <li><router-link to="/">服务协议</router-link></li>
            <li><router-link to="/">隐私声明</router-link></li>
          </ul>
        </div>
        
        <div class="footer-section contact">
          <h3>联系我们</h3>
          <p>客服邮箱：<EMAIL></p>
          <p>工作时间：周一至周五 9:00-18:00</p>
          <div class="qrcode">
            <div class="qrcode-placeholder">
              <img src="/qrcode.png" alt="公众号二维码" onerror="this.onerror=null; this.src='data:image/svg+xml;utf8,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%22110%22 height=%22110%22><rect width=%22110%22 height=%22110%22 fill=%22%23f0f7ff%22 /><text x=%2255%22 y=%2255%22 font-size=%2220%22 text-anchor=%22middle%22 alignment-baseline=%22middle%22 fill=%22%231e88e5%22>扫码关注</text></svg>'" />
            </div>
            <p>扫码关注公众号</p>
          </div>
        </div>
      </div>
      
      <div class="copyright">
        <p>Copyright © 2023-{{ new Date().getFullYear() }} 墨崽简历 版权所有</p>
      </div>
    </div>
  </footer>
</template>

<script setup>
</script>

<style scoped>
.footer {
  background-color: #f8fafc;
  padding: 60px 0 25px;
  margin-top: 60px;
  border-top: 1px solid #eaecef;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 35px;
}

.footer-section {
  flex: 1;
  min-width: 180px;
  margin-bottom: 25px;
}

.footer-section h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 18px;
  color: #303133;
  position: relative;
  padding-bottom: 10px;
  display: inline-block;
}

.footer-section h3:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30px;
  height: 2px;
  background-color: #1e88e5;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section ul li {
  margin-bottom: 12px;
}

.footer-section ul li a {
  color: #606266;
  font-size: 14px;
  transition: all 0.3s;
  text-decoration: none;
}

.footer-section ul li a:hover {
  color: #1e88e5;
}

.contact p {
  color: #606266;
  font-size: 14px;
  margin-bottom: 12px;
  line-height: 1.6;
}

.qrcode {
  margin-top: 18px;
  text-align: center;
}

.qrcode-placeholder {
  width: 110px;
  height: 110px;
  margin: 0 auto 8px;
  background-color: #f0f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #e0e6ed;
}

.qrcode-placeholder img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.qrcode p {
  font-size: 13px;
  color: #606266;
  margin: 5px 0 0;
}

.copyright {
  text-align: center;
  padding-top: 25px;
  border-top: 1px solid #eaecef;
}

.copyright p {
  color: #909399;
  font-size: 13px;
}

@media (max-width: 768px) {
  .footer {
    padding: 40px 0 20px;
  }
  
  .footer-content {
    flex-direction: column;
  }
  
  .footer-section {
    margin-bottom: 30px;
  }
  
  .qrcode-placeholder {
    width: 100px;
    height: 100px;
  }
}
</style> 