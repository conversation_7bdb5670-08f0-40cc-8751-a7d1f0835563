<template>
  <div class="upload-container">
    <div class="upload-content">
      <h1 class="title">上传正式面试信息</h1>
      <p class="description">请填写您的面试信息并上传面试音视频</p>
      
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="rules" 
        label-position="top" 
        class="upload-form"
      >
        <el-form-item label="面试公司" prop="company">
          <el-input v-model="formData.company" placeholder="请输入面试公司名称" />
        </el-form-item>
        
        <el-form-item label="面试岗位" prop="position">
          <el-select v-model="formData.position" placeholder="请选择面试岗位" style="width: 100%">
            <el-option label="开发" :value="1" />
            <el-option label="技术支持" :value="2" />
            <el-option label="测试" :value="3" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="面试时间" prop="interviewTime">
          <CustomDatePicker 
            v-model="formData.interviewTime" 
            placeholder="请选择面试时间"
            @change="handleCustomDateChange"
          />
        </el-form-item>
        
        <el-form-item label="面试音视频" prop="mediaFile">
          <el-upload
            class="media-uploader"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :file-list="fileList"
            accept="audio/*,video/*"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                支持上传音频或视频文件，大小不超过500MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        
        <div class="form-actions">
          <el-button @click="goBack">取消</el-button>
          <el-button 
            type="primary" 
            :loading="uploading" 
            @click="submitForm(formRef)"
          >
            提交
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, FormInstance, UploadUserFile } from 'element-plus'
import { createInterview, uploadInterviewVideoOss } from '@/api/interview'
import { InterviewType } from '@/types/interview'
import { FormalInterview } from '@/types/interview'
import { formatDate, toISOString } from '@/utils/dateFormat'
import CustomDatePicker from '@/components/CustomDatePicker.vue'

const router = useRouter()
const formRef = ref<FormInstance>()
const uploading = ref(false)
const fileList = ref<UploadUserFile[]>([])

// 表单数据
const formData = reactive({
  company: '',
  position: '',
  interviewTime: '',
  mediaFile: null as File | null
})

// 表单验证规则
const rules = {
  company: [
    { required: true, message: '请输入面试公司', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  position: [
    { required: true, message: '请选择面试岗位', trigger: 'change' }
  ],
  interviewTime: [
    { required: true, message: '请选择面试时间', trigger: 'change' }
  ],
  mediaFile: [
    { required: true, message: '请上传面试音视频文件', trigger: 'change' }
  ]
}

// 获取当前日期时间格式化字符串
const getCurrentDateTime = () => {
  const now = new Date();
  return formatDate(now.toString());
}

// 组件挂载时设置默认日期
onMounted(() => {
  // 设置默认日期为当前时间
  const now = new Date();
  formData.interviewTime = formatDate(now.toString());
})

// 处理自定义日期选择器变更
const handleCustomDateChange = (val: string) => {
  console.log('自定义日期选择器变更:', val);
  formData.interviewTime = val;
}

// 文件变更处理
const handleFileChange = (file: UploadUserFile) => {
  // 检查文件类型
  if (!file.raw?.type.startsWith('audio/') && !file.raw?.type.startsWith('video/')) {
    ElMessage.error('请上传音频或视频文件')
    fileList.value = []
    return false
  }
  
  // 检查文件大小（500MB）
  const maxSize = 500 * 1024 * 1024
  if (file.size && file.size > maxSize) {
    ElMessage.error('文件大小不能超过500MB')
    fileList.value = []
    return false
  }
  
  formData.mediaFile = file.raw || null
  return true
}

// 文件移除处理
const handleFileRemove = () => {
  formData.mediaFile = null
  fileList.value = []
}

// 提交表单
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  await formEl.validate(async (valid) => {
    if (valid) {
      uploading.value = true
      try {
        // 检查是否有媒体文件
        if (!formData.mediaFile) {
          ElMessage.error('请上传面试音视频文件')
          uploading.value = false
          return
        }
        
        // 检查并格式化日期
        let interviewTime = formData.interviewTime;
        if (interviewTime) {
          // 如果日期格式不正确，尝试修复
          if (interviewTime.includes('yyyy') || interviewTime.includes('Fr')) {
            const now = new Date();
            interviewTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
            console.log('日期格式不正确，已修复为当前时间:', interviewTime);
          }
        }
        
        // 构建面试数据
        const interviewData: Partial<FormalInterview> = {
          type: InterviewType.FORMAL,
          company: formData.company,
          position: formData.position,
          interviewTime: interviewTime, // 使用修正后的日期格式
          status: 'pending',
          videoUrl: formData.mediaFile.name // 先使用文件名，后续会更新为实际URL
        }
        
        console.log('提交面试数据:', interviewData)
        
        // 创建面试记录
        const interviewId = await createInterview(interviewData)
        
        if (interviewId) {
          console.log('面试记录创建成功，ID:', interviewId)
          
          let uploadSuccess = false;
          
          try {
            // 尝试使用OSS直传上传视频文件
            uploadSuccess = await uploadInterviewVideoOss(
              interviewId, 
              formData.mediaFile,
              (percent) => {
                // 这里可以处理上传进度
                console.log(`上传进度: ${percent}%`)
              }
            )
          } catch (error) {
            console.error('OSS直传失败:', error);
            // 不再使用传统上传方式，直接显示错误
            uploadSuccess = false;
          }
          
          if (uploadSuccess) {
            ElMessage.success('面试信息和视频提交成功')
            router.push({ path: '/interviews', query: { tab: 'formal' } })
          } else {
            ElMessage.error('视频上传失败，请稍后重试')
            // 不自动跳转，让用户可以重试上传
          }
        } else {
          ElMessage.error('提交面试信息失败')
        }
      } catch (error) {
        console.error('提交面试信息失败:', error)
        ElMessage.error('提交失败，请稍后重试')
      } finally {
        uploading.value = false
      }
    } else {
      ElMessage.warning('请完善表单信息')
    }
  })
}

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
.upload-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 40px 20px;
}

.upload-content {
  width: 100%;
  max-width: 800px;
  background-color: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.title {
  text-align: center;
  margin-bottom: 10px;
  color: #303133;
  font-size: 24px;
}

.description {
  text-align: center;
  margin-bottom: 30px;
  color: #909399;
}

.upload-form {
  margin-top: 20px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
  gap: 10px;
}

.media-uploader {
  width: 100%;
}
</style> 