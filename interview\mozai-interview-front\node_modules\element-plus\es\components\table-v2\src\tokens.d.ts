import type { Injection<PERSON><PERSON>, Ref } from 'vue';
import type { UseNamespaceReturn } from 'element-plus/es/hooks';
import type { KeyType } from './types';
export declare type TableV2Context = {
    isScrolling: Ref<boolean>;
    hoveringRowKey: Ref<null | KeyType>;
    isResetting: Ref<boolean>;
    ns: UseNamespaceReturn;
};
export declare const TableV2InjectionKey: InjectionKey<TableV2Context>;
