import{d as Y,r as c,H as D,T as j,m as G,c as a,a as s,q as f,g as p,b as i,F as q,i as H,s as u,w as m,k as n,U as h,f as y,u as J,E as Q,e as C,o as l,V as X,W as Z,_ as ss}from"./index-4df1abd4.js";import{I as r,g as ts,f as w}from"./interview-b37c71d4.js";const es={class:"interview-list-container"},os={class:"header"},as={class:"tabs"},ls={key:0,class:"interview-section"},ns={key:0,class:"loading-state"},is={key:1,class:"empty-state"},ds={key:2,class:"interview-grid"},us={class:"card-header"},cs={class:"card-content"},rs={class:"info-item"},vs={class:"value"},_s={class:"info-item"},ps={class:"value"},ms={class:"info-item"},fs={class:"value"},ys={key:0,class:"info-item"},bs={class:"value"},ks={key:1,class:"info-item"},gs={class:"value"},Ms={key:0,class:"score"},hs={key:1,class:"no-evaluation"},Cs={class:"card-actions"},Is=["onClick"],Ns={key:1,class:"btn btn-disabled",disabled:""},xs={key:1,class:"interview-section"},Ts={class:"action-bar"},ws={key:0,class:"loading-state"},Os={key:1,class:"empty-state"},Rs={key:2,class:"interview-grid"},$s={class:"card-header"},Fs={class:"card-content"},Ls={class:"info-item"},Vs={class:"value"},Es={class:"info-item"},Bs={class:"value"},zs={class:"info-item"},As={class:"value"},Ks={key:0,class:"info-item"},Ss={class:"value"},Us={class:"info-item"},Ds={class:"value"},qs={key:0,class:"score"},Hs={key:1,class:"no-evaluation"},Ps={key:1,class:"info-item"},Ws={class:"value"},Ys={class:"card-actions"},js=["onClick"],Gs={key:1,class:"btn btn-disabled",disabled:""},Js={key:0,class:"modal-body"},Qs={class:"evaluation-score"},Xs={class:"score-value"},Zs={class:"evaluation-section"},st={class:"feedback-text"},tt={class:"evaluation-section"},et={class:"feedback-text"},ot={class:"evaluation-section"},at={class:"feedback-text"},lt={class:"evaluation-time"},nt={class:"modal-header"},it={class:"modal-body"},dt={controls:"",class:"media-video"},ut=["src"],ct=Y({__name:"InterviewListView",setup(rt){const O=J(),d=c("mock"),I=c(!1),v=c(null),k=c(!1),N=c(!1),x=c(""),T=c(""),g=c([]),R=D(()=>g.value.filter(e=>e.type===r.MOCK)),$=D(()=>g.value.filter(e=>e.type===r.FORMAL)),F=(e,t)=>{v.value=e,I.value=!0},L=()=>{I.value=!1,v.value=null},V=()=>{O.push("/interviews/formal/upload")},P=e=>{e.videoUrl&&(x.value=e.videoUrl,T.value=`${e.company} - ${e.position}`,N.value=!0)},E=()=>{N.value=!1,x.value="",T.value=""},b=async e=>{k.value=!0;try{const t=await ts(e);g.value=t,console.log(g.value)}catch(t){console.error(`获取${e}面试列表失败:`,t),Q.error(`获取${e}面试列表失败，请稍后重试`)}finally{k.value=!1}};j(d,e=>{b(e==="mock"?r.MOCK:r.FORMAL)}),G(()=>{O.currentRoute.value.query.tab==="formal"&&(d.value="formal"),b(d.value==="mock"?r.MOCK:r.FORMAL)});const B=e=>{if(typeof e=="number")return{1:"开发",2:"技术支持",3:"测试"}[e]||`未知岗位(${e})`;if(e&&!isNaN(Number(e))){const _=Number(e);return{1:"开发",2:"技术支持",3:"测试"}[_]||`未知岗位(${e})`}return{dev:"开发岗",support:"技术支持岗",test:"测试岗",开发:"开发",技术支持:"技术支持",测试:"测试"}[e]||e},W=e=>({hr:"HR面试",tech:"技术面试"})[e]||e,z=e=>e==null?"未评定":e===1?"面试通过":"面试未通过",A=e=>e==null?"info":e===1?"success":"danger";return(e,t)=>{const _=C("el-skeleton"),M=C("el-tag"),K=C("el-icon"),S=C("el-button");return l(),a("div",es,[s("div",os,[t[4]||(t[4]=s("h1",{class:"title"},"我的面试",-1)),s("div",as,[s("button",{class:f(["tab-btn",{active:d.value==="mock"}]),onClick:t[0]||(t[0]=()=>{d.value="mock",b(p(r).MOCK)})}," 模拟面试 ",2),s("button",{class:f(["tab-btn",{active:d.value==="formal"}]),onClick:t[1]||(t[1]=()=>{d.value="formal",b(p(r).FORMAL)})}," 正式面试 ",2)])]),d.value==="mock"?(l(),a("div",ls,[k.value?(l(),a("div",ns,[i(_,{rows:3,animated:""}),i(_,{rows:3,animated:""})])):R.value.length===0?(l(),a("div",is,t[5]||(t[5]=[s("div",{class:"empty-icon"},"📝",-1),s("p",null,"暂无模拟面试记录",-1)]))):(l(),a("div",ds,[(l(!0),a(q,null,H(R.value,o=>(l(),a("div",{key:o.id,class:f(["interview-card mock-card",{"non-clickable":!0}])},[s("div",us,[t[6]||(t[6]=s("span",{class:"interview-type"},"模拟面试",-1)),s("span",{class:f(["status",o.status])},n(o.status==="completed"?"已完成":o.status==="in_progress"?"进行中":o.status==="waiting_result"?"等待结果":"待处理"),3)]),s("div",cs,[s("div",rs,[t[7]||(t[7]=s("span",{class:"label"},"面试环节：",-1)),s("span",vs,n(W(o.stage)),1)]),s("div",_s,[t[8]||(t[8]=s("span",{class:"label"},"面试岗位：",-1)),s("span",ps,n(B(o.position)),1)]),s("div",ms,[t[9]||(t[9]=s("span",{class:"label"},"面试时间：",-1)),s("span",fs,n(p(w)(o.interviewTime)),1)]),o.result!==void 0&&o.status!=="waiting_result"?(l(),a("div",ys,[t[10]||(t[10]=s("span",{class:"label"},"面试结果：",-1)),s("span",bs,[i(M,{type:A(o.result),size:"small"},{default:m(()=>[y(n(z(o.result)),1)]),_:2},1032,["type"])])])):u("",!0),o.status!=="waiting_result"?(l(),a("div",ks,[t[11]||(t[11]=s("span",{class:"label"},"面试评价：",-1)),s("span",gs,[o.evaluation?(l(),a("span",Ms,n(o.evaluation.score)+"分 ",1)):(l(),a("span",hs,"暂无评价"))])])):u("",!0)]),s("div",Cs,[o.evaluation&&o.status!=="waiting_result"?(l(),a("button",{key:0,onClick:h(U=>F(o.evaluation),["stop"]),class:"btn btn-primary"}," 查看评价 ",8,Is)):(l(),a("button",Ns," 暂无评价 "))])]))),128))]))])):u("",!0),d.value==="formal"?(l(),a("div",xs,[s("div",Ts,[i(S,{type:"primary",onClick:V},{default:m(()=>[i(K,null,{default:m(()=>[i(p(X))]),_:1}),t[12]||(t[12]=y("上传正式面试 ",-1))]),_:1,__:[12]})]),k.value?(l(),a("div",ws,[i(_,{rows:3,animated:""}),i(_,{rows:3,animated:""})])):$.value.length===0?(l(),a("div",Os,[t[14]||(t[14]=s("div",{class:"empty-icon"},"💼",-1)),t[15]||(t[15]=s("p",null,"暂无正式面试记录",-1)),i(S,{type:"primary",onClick:V,style:{"margin-top":"20px"}},{default:m(()=>t[13]||(t[13]=[y(" 上传正式面试 ",-1)])),_:1,__:[13]})])):(l(),a("div",Rs,[(l(!0),a(q,null,H($.value,o=>(l(),a("div",{key:o.id,class:f(["interview-card formal-card",{"non-clickable":!0}])},[s("div",$s,[t[16]||(t[16]=s("span",{class:"interview-type"},"正式面试",-1)),s("span",{class:f(["status",o.status])},n(o.status==="completed"?"已完成":o.status==="in_progress"?"进行中":o.status==="waiting_result"?"等待结果":"待处理"),3)]),s("div",Fs,[s("div",Ls,[t[17]||(t[17]=s("span",{class:"label"},"面试公司：",-1)),s("span",Vs,n(o.company),1)]),s("div",Es,[t[18]||(t[18]=s("span",{class:"label"},"面试岗位：",-1)),s("span",Bs,n(B(o.position)),1)]),s("div",zs,[t[19]||(t[19]=s("span",{class:"label"},"面试时间：",-1)),s("span",As,n(p(w)(o.interviewTime)),1)]),o.result!==void 0&&o.status!=="waiting_result"?(l(),a("div",Ks,[t[20]||(t[20]=s("span",{class:"label"},"面试结果：",-1)),s("span",Ss,[i(M,{type:A(o.result),size:"small"},{default:m(()=>[y(n(z(o.result)),1)]),_:2},1032,["type"])])])):u("",!0),s("div",Us,[t[21]||(t[21]=s("span",{class:"label"},"面试评价：",-1)),s("span",Ds,[o.evaluation?(l(),a("span",qs,n(o.evaluation.score)+"分 ",1)):(l(),a("span",Hs,"暂无评价"))])]),o.videoUrl?(l(),a("div",Ps,[t[23]||(t[23]=s("span",{class:"label"},"面试音视频：",-1)),s("span",Ws,[i(M,{size:"small",type:"success",onClick:U=>P(o),class:"media-tag"},{default:m(()=>[i(K,null,{default:m(()=>[i(p(Z))]),_:1}),t[22]||(t[22]=y(" 已上传 (点击播放) ",-1))]),_:2,__:[22]},1032,["onClick"])])])):u("",!0)]),s("div",Ys,[o.evaluation?(l(),a("button",{key:0,onClick:h(U=>F(o.evaluation),["stop"]),class:"btn btn-primary"}," 查看评价 ",8,js)):(l(),a("button",Gs," 暂无评价 "))])]))),128))]))])):u("",!0),I.value?(l(),a("div",{key:2,class:"modal-overlay",onClick:L},[s("div",{class:"modal-content",onClick:t[2]||(t[2]=h(()=>{},["stop"]))},[s("div",{class:"modal-header"},[t[24]||(t[24]=s("h3",null,"面试评价详情",-1)),s("button",{onClick:L,class:"close-btn"},"×")]),v.value?(l(),a("div",Js,[s("div",Qs,[t[25]||(t[25]=s("span",{class:"score-label"},"总分：",-1)),s("span",Xs,n(v.value.score)+"分",1)]),s("div",Zs,[t[26]||(t[26]=s("h4",null,"整体反馈",-1)),s("p",st,n(v.value.feedback),1)]),s("div",tt,[t[27]||(t[27]=s("h4",null,"优势表现",-1)),s("p",et,n(v.value.strengths),1)]),s("div",ot,[t[28]||(t[28]=s("h4",null,"改进建议",-1)),s("p",at,n(v.value.improvements),1)]),s("div",lt," 评价时间："+n(p(w)(new Date().toISOString())),1)])):u("",!0)])])):u("",!0),N.value?(l(),a("div",{key:3,class:"modal-overlay",onClick:E},[s("div",{class:"modal-content",onClick:t[3]||(t[3]=h(()=>{},["stop"]))},[s("div",nt,[s("h3",null,n(T.value),1),s("button",{onClick:E,class:"close-btn"},"×")]),s("div",it,[s("video",dt,[s("source",{src:x.value,type:"video/mp4"},null,8,ut),t[29]||(t[29]=y(" Your browser does not support the video tag. ",-1))])])])])):u("",!0)])}}});const pt=ss(ct,[["__scopeId","data-v-3d67fe5e"]]);export{pt as default};
