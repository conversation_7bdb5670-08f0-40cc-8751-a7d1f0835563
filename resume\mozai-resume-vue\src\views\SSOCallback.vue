<template>
  <div class="sso-callback">
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>登录成功，正在处理...</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SSOCallback',
  data() {
    return {
      redirectTo: '/'
    }
  },
  mounted() {
    this.processCallback()
  },
  methods: {
    processCallback() {
      // 从URL中获取token
      const token = this.$route.query.token
      
      if (token) {
        console.log('SSO回调页面处理token:', token)
        
        // 保存token到本地存储
        localStorage.setItem('token', token)
        
        // 获取重定向URL（如果有）
        const redirectUrl = this.$route.query.redirectUrl || this.redirectTo
        
        // 延迟一小段时间后重定向，以便用户看到成功消息
        setTimeout(() => {
          this.$router.replace(redirectUrl)
        }, 1500)
      } else {
        console.error('未在URL中找到token')
        // 如果没有token，重定向到登录页面
        this.$router.replace('/login')
      }
    }
  }
}
</script>

<style scoped>
.sso-callback {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading-container {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  display: inline-block;
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style> 