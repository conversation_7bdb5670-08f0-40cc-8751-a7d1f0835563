package com.bimowu.config;

import com.bimowu.interview.base.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public BaseResponse<String> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.error("文件大小超过限制", e);
        return BaseResponse.error(413, "上传文件过大，请确保文件小于10MB");
    }
    
    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public BaseResponse<String> handleException(Exception e) {
        log.error("系统异常", e);
        return BaseResponse.error(500, "服务器太忙了，让它休息一会吧！");
    }
} 