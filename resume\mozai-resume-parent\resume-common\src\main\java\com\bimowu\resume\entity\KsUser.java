package com.bimowu.resume.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户对象 ks_user
 */
@Data
@TableName("ks_user")
public class KsUser {
    /** 主键 */
    @TableId
    private Long uId;

    /** 邮箱 */
    private String email;

    /** 昵称 */
    private String nickname;

    /** 密码 */
    private String passwd;

    /** 密码类型 */
    private Integer passType;

    /** 手机号 */
    private String phone;

    /** 用户状态，0表示正常 */
    private Integer state;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 最后登录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lasrLoginTime;

    /** 备注 */
    private String memo;

    /** 用户级别 1:超级管理员 0:普通会员 */
    private Integer userLevel;

    /** 外部用户ID */
    private String foreignUserId;

    /** 体系id */
    private Integer directionTypeId;

    /** 是否需要检查权限1：不需要 0：需要(如果已经线下报名，免费学习所有课程) */
    private Integer isCheck;

    /** 学校type */
    private Integer schoolType;

    /** 学校名称 */
    private String schoolName;

    /** 0免费散客，22实训客户，66付费用户，99内部员工 */
    private Integer userType;

}
