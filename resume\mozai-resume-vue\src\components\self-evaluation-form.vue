<template>
  <div class="form-container">
    <div v-if="showEditor">
      <div class="editor-header">
        <div class="editor-actions">
          <el-button type="success" size="small" @click="polishContent" :disabled="!formData.content">
            <el-icon class="icon"><magic-stick /></el-icon> AI润色
          </el-button>
        </div>
      </div>
      
      <div class="editor-content">
        <MdEditor
          v-model="formData.content"
          height="300px"
          :toolbars="toolbars"
          :preview="true"
          :previewTheme="'default'"
          :showCodeRowNumber="true"
          :previewOnly="false"
          :previewWidth="'50%'"
          @onSave="saveForm"
          @change="handleContentChange"
          placeholder="请输入您的自我评价，建议包含个人优势、性格特点、工作风格、职业规划等内容"
        />
      </div>
      
      <div class="tips">
        <el-alert
          title="撰写建议"
          type="info"
          :closable="false"
          show-icon
        >
          <div class="tips-content">
            <p>1. 描述自己的性格特点和软技能，如：团队协作能力、沟通能力、学习能力等</p>
            <p>2. 强调与目标职位相关的技能和特质</p>
            <p>3. 简单介绍自己的职业规划</p>
            <p>4. 保持简洁，通常100-200字为宜</p>
            <p>5. 可以使用加粗、斜体等富文本格式增强表现力</p>
          </div>
        </el-alert>
      </div>
    </div>
    
    <!-- AI润色对话框 -->
    <el-dialog
      v-model="polishDialogVisible"
      title="AI润色"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="polishLoading" class="loading-container">
        <el-skeleton :rows="5" animated />
        <div class="loading-text">AI正在润色您的内容...</div>
      </div>
      <div v-else>
        <div class="polish-result">
          <div class="original-content">
            <h4>原始内容</h4>
            <div class="content-box" v-html="markdownToHtml(formData.content)"></div>
          </div>
          <div class="polished-content">
            <h4>润色后内容</h4>
            <div class="content-box" v-html="markdownToHtml(polishedContent)"></div>
          </div>
        </div>
        <div class="polish-options">
          <h4>请选择要使用的版本</h4>
          <el-radio-group v-model="selectedVersion">
            <el-radio :label="0">使用原始内容</el-radio>
            <el-radio :label="1">使用润色后内容</el-radio>
          </el-radio-group>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="polishDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyPolishedContent" :disabled="polishLoading">
            应用所选内容
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { MdEditor } from 'md-editor-v3'
import { MagicStick, Close, EditPen } from '@element-plus/icons-vue'
import 'md-editor-v3/lib/style.css'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// 控制编辑器显示/隐藏
const showEditor = ref(false)

// 暴露showEditor变量，让父组件可以访问
defineExpose({
  showEditor
})

// 富文本编辑器配置
const toolbars = [
  'bold',
  'italic',
  'strikethrough',
  'title',
  'sub',
  'sup',
  'quote',
  'unorderedList',
  'orderedList',
  'codeRow',
  'link',
  'save',
  'pageFullscreen',
  'fullscreen',
  'preview',
]

const preview = ref(true)

// 表单数据
const formData = ref({
  evaId: '',
  resumeId: '',
  selfEvaluation: ''
})

// AI润色相关
const polishDialogVisible = ref(false)
const polishLoading = ref(false)
const polishedContent = ref('')
const selectedVersion = ref(1)

// 初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal) {
    // 处理对象形式的数据
    if (typeof newVal === 'object') {
      // 映射字段名称，确保与后端一致
      formData.value.evaId = newVal.id || '';
      formData.value.resumeId = newVal.resumeId || '';
      formData.value.selfEvaluation = newVal.description || '';
    } else {
      // 处理字符串形式的数据
      formData.value.selfEvaluation = typeof newVal === 'string' ? newVal : 
                              Array.isArray(newVal) ? newVal.join('\n') : '';
    }
  } else {
    formData.value.evaId = '';
    formData.value.resumeId = '';
    formData.value.selfEvaluation = '';
  }
  // 确保编辑器内容正确
  formData.value.content = formData.value.selfEvaluation;
  
  // 确保编辑器显示
  showEditor.value = true;
}, { immediate: true, deep: true })

// 监听内容变化，实时更新
watch(() => formData.value.content, (newVal) => {
  formData.value.selfEvaluation = newVal;
  // 保持原有的对象结构，包括evaId和resumeId
  emit('update', {
    evaId: formData.value.evaId,
    resumeId: formData.value.resumeId,
    selfEvaluation: newVal
  });
}, { immediate: false })

// 隐藏编辑器
const hideEditor = () => {
  showEditor.value = false
  saveForm()
}

// Markdown转HTML的简单实现
const markdownToHtml = (markdown) => {
  if (!markdown) return '';
  return markdown
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>');
}

// 润色内容
const polishContent = () => {
  if (!formData.value.content) {
    ElMessage.warning('请先输入内容再使用AI润色功能')
    return
  }
  
  polishDialogVisible.value = true
  polishLoading.value = true
  
  // 模拟API请求
  setTimeout(() => {
    // 这里应该是调用后端AI润色API的地方
    // 为演示目的，这里使用简单的模拟润色逻辑
    polishedContent.value = simulatePolishing(formData.value.content)
    polishLoading.value = false
  }, 1500)
}

// 模拟润色逻辑（实际项目中应该替换为真实API调用）
const simulatePolishing = (text) => {
  // 这只是一个非常简单的模拟，实际项目中应该调用NLP API
  const improvements = [
    { from: '我很努力', to: '我**始终坚持高标准、高要求**，持续努力提升自我' },
    { from: '学习能力强', to: '具备**快速学习和适应新技术的能力**，能够在短时间内掌握业务需求和技术要点' },
    { from: '团队合作', to: '善于在团队中协作，能够与不同角色的团队成员**高效沟通**，共同达成目标' },
    { from: '责任心强', to: '**强烈的责任感**驱使我对待每一项任务都精益求精，确保高质量完成' },
    { from: '解决问题', to: '善于分析复杂问题，提出**创新性解决方案**，提高工作效率' }
  ]
  
  let result = text
  
  // 添加专业术语和修饰
  if (!result.includes('自驱力')) {
    result = result.replace(/主动性/g, '**自驱力**')
  }
  
  if (!result.includes('全局意识')) {
    result = result.replace(/思考问题/g, '具备**全局意识**思考问题')
  }
  
  // 应用词语改进
  improvements.forEach(item => {
    result = result.replace(new RegExp(item.from, 'g'), item.to)
  })
  
  // 如果内容太短，添加一些通用内容
  if (result.length < 100) {
    result += '。我注重**工作与生活的平衡**，保持持续学习的习惯，紧跟行业发展趋势。未来，我希望能在专业领域不断深耕，为团队和企业创造更大的价值。'
  }
  
  return result
}

// 应用润色后的内容
const applyPolishedContent = () => {
  if (selectedVersion.value === 1) {
    formData.value.content = polishedContent.value;
    formData.value.selfEvaluation = polishedContent.value;
  }
  polishDialogVisible.value = false
  ElMessage.success('已应用所选内容')
  
  // 确保更新到父组件
  emit('update', {
    evaId: formData.value.evaId,
    resumeId: formData.value.resumeId,
    selfEvaluation: formData.value.selfEvaluation
  });
}

// 保存表单
const saveForm = () => {
  // 这里可以添加表单验证逻辑
  emit('update', {
    evaId: formData.value.evaId,
    resumeId: formData.value.resumeId,
    selfEvaluation: formData.value.selfEvaluation
  })
}

// 重置表单
const resetForm = () => {
  formData.value.content = props.data || ''
  ElMessage.info('已重置为上次保存的内容')
}

// 处理内容变化
const handleContentChange = (content) => {
  formData.value.content = content;
  formData.value.selfEvaluation = content;
}
</script>

<style scoped>
.form-container {
  margin-bottom: 20px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.editor-title {
  font-weight: bold;
  font-size: 16px;
}

.editor-actions {
  display: flex;
  gap: 10px;
}

.icon {
  margin-right: 4px;
}

.tips {
  margin-top: 15px;
}

.tips-content {
  font-size: 13px;
  color: #666;
}

.tips-content p {
  margin: 5px 0;
}

.loading-container {
  padding: 20px;
  text-align: center;
}

.loading-text {
  margin-top: 15px;
  color: #409EFF;
}

.polish-result {
  display: flex;
  gap: 20px;
}

.original-content, .polished-content {
  flex: 1;
}

.content-box {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  min-height: 200px;
  max-height: 300px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.polish-options {
  margin-top: 20px;
}

h4 {
  margin-bottom: 10px;
  font-size: 16px;
  color: #333;
}
</style> 