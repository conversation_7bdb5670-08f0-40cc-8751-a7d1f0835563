<template>
  <div class="selection-container">
    <div class="selection-content">
      <h1>选择面试类型</h1>
      <p class="description">
        请选择您要参加的面试类型和岗位，系统将根据您的选择生成相应的面试问题。
      </p>
      
      <div class="selection-area">
        <div class="selection-group">
          <h2>面试环节</h2>
          <div class="option-cards">
            <div 
              class="option-card" 
              :class="{ active: selectedStage === 'hr' }"
              @click="selectedStage = 'hr'"
            >
              <el-icon><User /></el-icon>
              <h3>HR面试</h3>
              <p>侧重考察个人软实力、职业规划及团队协作能力</p>
            </div>
            <div 
              class="option-card" 
              :class="{ active: selectedStage === 'tech' }"
              @click="selectedStage = 'tech'"
            >
              <el-icon><Monitor /></el-icon>
              <h3>技术面试</h3>
              <p>侧重考察专业技能、解决问题的能力及技术深度</p>
            </div>
          </div>
        </div>
        
        <div class="selection-group">
          <h2>面试岗位</h2>
          <div class="option-cards">
            <div
              v-for="item in categoryList"
              :key="item.catId"
              class="option-card"
              :class="{ active: selectedPosition === item.catId }"
              @click="selectedPosition = item.catId"
            >
              <!-- <el-icon><Promotion /></el-icon> -->
              <el-icon v-if="item.catId === 1"><Promotion /></el-icon>
              <el-icon v-else-if="item.catId === 2"><Postcard /></el-icon>
              <el-icon v-else-if="item.catId === 3"><Service /></el-icon>
              <h3>{{ item.name }}</h3>
              <p>{{ item.description }}</p>
            </div>
          </div>
        </div>
        
        <div class="selection-group">
          <h2>工作经验</h2>
          <div class="option-cards">
            <div 
              class="option-card" 
              :class="{ active: selectedExperience === 'fresh' }"
              @click="selectedExperience = 'fresh'"
            >
              <el-icon><Trophy /></el-icon>
              <h3>应届生</h3>
              <p>刚毕业或即将毕业，无全职工作经验</p>
            </div>
            <div 
              class="option-card" 
              :class="{ active: selectedExperience === '1-3' }"
              @click="selectedExperience = '1-3'"
            >
              <el-icon><Postcard /></el-icon>
              <h3>1-3年</h3>
              <p>初级工程师，有1-3年工作经验</p>
            </div>
            <div 
              class="option-card" 
              :class="{ active: selectedExperience === '3-5' }"
              @click="selectedExperience = '3-5'"
            >
              <el-icon><Briefcase /></el-icon>
              <h3>3-5年</h3>
              <p>中级工程师，有3-5年相关工作经验</p>
            </div>
            <div 
              class="option-card" 
              :class="{ active: selectedExperience === '5+' }"
              @click="selectedExperience = '5+'"
            >
              <el-icon><Opportunity /></el-icon>
              <h3>5年以上</h3>
              <p>高级工程师，有5年以上丰富工作经验</p>
            </div>
          </div>
        </div>
        
        <div class="actions">
          <el-button @click="goBack">返回</el-button>
          <el-button 
            type="primary" 
            @click="continueToUpload" 
            :disabled="!selectedStage || !selectedPosition || !selectedExperience || loading"
            :loading="loading"
          >
            {{ loading ? '生成面试问题中...' : '继续' }}
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 加载遮罩层 -->
    <div class="loading-overlay" v-if="loading">
      <div class="loading-content">
        <el-icon class="loading-icon"><svg class="circular" viewBox="0 0 50 50"><circle class="path" cx="25" cy="25" r="20" fill="none" /></svg></el-icon>
        <p>面试问题生成中，请耐心等待...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { User, Monitor, Promotion, Service, Trophy, Postcard, Briefcase, Opportunity } from '@element-plus/icons-vue'
import { useInterviewStore } from '@/store'
import api from '@/api'
import { ElMessage } from 'element-plus'

const router = useRouter()
const interviewStore = useInterviewStore()

const selectedStage = ref('')
const selectedPosition = ref('')
const selectedExperience = ref('')
const loading = ref(false)

const goBack = () => {
  router.push('/')
}

const continueToUpload = async () => {
  // 显示加载状态
  loading.value = true
  
  try {
    // 添加成功标志位，只有成功创建面试且获取到interviewId时才为true
    let shouldProceed = false;
    
    // 保存选择到store
    interviewStore.setInterviewType({
      stage: selectedStage.value,
      position: selectedPosition.value,
      experience: selectedExperience.value
    })
    
    // 准备面试数据，不再依赖用户ID
    const interviewData = {
      type: 'mock',
      // 移除userId字段，后端将从token中获取
      candidateName: '面试者', // 使用默认名称，后端会从token中获取真实用户名
      position: interviewStore.interviewType.position,
      stage: interviewStore.interviewType.stage,
      experience: interviewStore.interviewType.experience,
      questions: [] // 添加空的questions数组以符合API类型要求
    }
    
    // 设置默认问题，以防API调用失败
    const defaultQuestions = ['请介绍一下你自己', '你的优势是什么?', '你为什么选择这个岗位?']
    
    try {
      // 创建面试
      ElMessage.info('正在生成面试问题，请耐心等待...')
      const response = await api.createInterview(interviewData)
      
      // 安全地处理响应
      let interviewId = null
      
      // 从响应中提取interviewId
      if (response && typeof response === 'object' && 'data' in response && response.data && 'interviewId' in response.data) {
        interviewId = response.data.interviewId
      }
      
      if (interviewId) {
        // 保存面试ID到store
        interviewStore.setInterviewId(interviewId)
        
        try {
          // 获取面试问题
          const questionsResponse = await api.getInterviewQuestions(interviewId)
          
          // 安全地处理问题响应
          if (questionsResponse && typeof questionsResponse === 'object' && 
              'questions' in questionsResponse && 
              Array.isArray(questionsResponse.questions)) {
            interviewStore.setQuestions(questionsResponse.questions)
            ElMessage.success('面试问题生成成功')
          } else {
            interviewStore.setQuestions(defaultQuestions)
            ElMessage.warning('无法获取面试问题，将使用默认问题')
          }
        } catch (error) {
          interviewStore.setQuestions(defaultQuestions)
          ElMessage.warning('获取面试问题失败，将使用默认问题')
        }
        // 成功获取到interviewId，可以跳转
        shouldProceed = true;
      } else {
        interviewStore.setQuestions(defaultQuestions)
      }
    } catch (error) {
      console.error('创建面试失败:', error)
      
      // 检查错误是否与resumeId有关
      const errorMessage = error?.toString() || ''
      const isResumeError = errorMessage.includes('resumeId') || 
                           errorMessage.includes('简历') || 
                           (error && typeof error === 'object' && 
                            'message' in error && 
                            typeof error.message === 'string' && 
                            (error.message.includes('resumeId') || error.message.includes('简历')))
      
      if (isResumeError) {
        ElMessage.error('简历未创建，请先在简历系统创建对应简历')
        loading.value = false // 关闭加载状态
        return // 阻止继续执行和页面跳转
      }
      
      // 其他错误的情况下，设置默认问题并继续
      interviewStore.setQuestions(defaultQuestions)
      ElMessage.warning('创建面试失败，将使用默认问题')
    }
    
    // 只有在shouldProceed为true时才跳转
    if (shouldProceed) {
      router.push('/interview')
    } else {
      loading.value = false // 关闭加载状态
    }
  } catch (error) {
    console.error('处理继续按钮点击事件失败:', error)
    
    // 检查错误是否与resumeId有关，即使是最外层的错误也不跳转
    const errorMessage = error?.toString() || ''
    const isResumeError = errorMessage.includes('resumeId') || 
                         errorMessage.includes('简历') || 
                         (error && typeof error === 'object' && 
                          'message' in error && 
                          typeof error.message === 'string' && 
                          (error.message.includes('resumeId') || error.message.includes('简历')))
    
    if (isResumeError) {
      ElMessage.error('简历未创建，请先在简历系统创建对应简历')
      loading.value = false // 关闭加载状态
      return // 阻止跳转
    }
    
    // 其他错误才允许跳转
    router.push('/interview')
  } finally {
    // 确保无论如何都关闭加载状态
    loading.value = false
  }
}

const categoryList = ref()

onMounted(async () => {
  try {
    const res = await api.getResumeCategoryList()
    const { code, message, data } = res
    if (code !== 0) {
      throw new Error(message)
    }
    categoryList.value = data
  } catch (e) {
    console.error('获取简历分类失败', e)
  }
})

</script>

<style scoped>
.selection-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  position: relative;
}

.selection-content {
  width: 100%;
  max-width: 900px;
  background-color: white;
  border-radius: 10px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

h1 {
  font-size: 2rem;
  margin-bottom: 20px;
  color: #409EFF;
  text-align: center;
}

h2 {
  font-size: 1.5rem;
  margin: 30px 0 20px;
  color: #303133;
}

.description {
  font-size: 1.1rem;
  color: #606266;
  margin-bottom: 30px;
  text-align: center;
  line-height: 1.6;
}

.selection-area {
  margin-top: 20px;
}

.selection-group {
  margin-bottom: 30px;
}

.option-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.option-card {
  flex: 1;
  min-width: 200px;
  padding: 25px;
  border-radius: 8px;
  background-color: #f9f9f9;
  border: 2px solid #f9f9f9;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
}

.option-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.option-card.active {
  border-color: #409EFF;
  background-color: #ecf5ff;
}

.option-card .el-icon {
  font-size: 2.5rem;
  color: #409EFF;
  margin-bottom: 15px;
}

.option-card h3 {
  font-size: 1.3rem;
  margin-bottom: 10px;
  color: #303133;
}

.option-card p {
  color: #606266;
  line-height: 1.5;
}

.actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40px;
}

@media (max-width: 768px) {
  .option-cards {
    flex-direction: column;
  }

  .option-card {
    margin-bottom: 15px;
  }
}

/* 加载遮罩层样式 - 保留这部分 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.loading-icon {
  font-size: 40px;
  margin-bottom: 15px;
  color: #409EFF;
  animation: rotate 2s linear infinite;
}

.circular {
  height: 40px;
  width: 40px;
  animation: loading-rotate 2s linear infinite;
}

.path {
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-width: 2;
  stroke: #409EFF;
  stroke-linecap: round;
  animation: loading-dash 1.5s ease-in-out infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40px;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120px;
  }
}
</style> 