package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.common.service.PDFConfigManager;
import com.bimowu.resume.config.PDFGenerationConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * PDF配置管理器实现
 */
@Slf4j
@Service
public class PDFConfigManagerImpl implements PDFConfigManager {
    
    @Autowired
    private PDFGenerationConfig pdfGenerationConfig;
    
    private PageConfig pageConfig;
    private FontConfig fontConfig;
    private RenderConfig renderConfig;
    
    @Override
    public PageConfig getPageConfig() {
        if (pageConfig == null) {
            initializePageConfig();
        }
        return pageConfig;
    }
    
    @Override
    public FontConfig getFontConfig() {
        if (fontConfig == null) {
            initializeFontConfig();
        }
        return fontConfig;
    }
    
    @Override
    public RenderConfig getRenderConfig() {
        if (renderConfig == null) {
            initializeRenderConfig();
        }
        return renderConfig;
    }
    
    @Override
    public void updateConfig(PDFGenerationConfig config) {
        this.pdfGenerationConfig = config;
        
        // 重新初始化配置
        initializePageConfig();
        initializeFontConfig();
        initializeRenderConfig();
        
        log.info("PDF配置已更新");
    }
    
    @Override
    public void resetToDefault() {
        pageConfig = null;
        fontConfig = null;
        renderConfig = null;
        
        // 重新初始化为默认配置
        initializePageConfig();
        initializeFontConfig();
        initializeRenderConfig();
        
        log.info("PDF配置已重置为默认值");
    }
    
    /**
     * 初始化页面配置
     */
    private void initializePageConfig() {
        pageConfig = new PageConfig();
        pageConfig.setPageSize(pdfGenerationConfig.getPage().getSize());
        pageConfig.setMarginTop(pdfGenerationConfig.getPage().getMargin().getTop());
        pageConfig.setMarginBottom(pdfGenerationConfig.getPage().getMargin().getBottom());
        pageConfig.setMarginLeft(pdfGenerationConfig.getPage().getMargin().getLeft());
        pageConfig.setMarginRight(pdfGenerationConfig.getPage().getMargin().getRight());
        pageConfig.setOrientation(pdfGenerationConfig.getPage().getOrientation());
    }

    /**
     * 初始化字体配置
     */
    private void initializeFontConfig() {
        fontConfig = new FontConfig();
        fontConfig.setDefaultFontFamily("SimSun");
        fontConfig.setDefaultFontSize(12f);
        fontConfig.setEncoding("UTF-8");
        fontConfig.setEmbedFonts(true);
    }

    /**
     * 初始化渲染配置
     */
    private void initializeRenderConfig() {
        renderConfig = new RenderConfig();
        renderConfig.setDpi(pdfGenerationConfig.getPage().getDpi());
        renderConfig.setEnableJavaScript(false); // 配置中未找到此属性，使用默认值
        renderConfig.setTimeoutSeconds(pdfGenerationConfig.getTimeoutSeconds());
        renderConfig.setStrictMode(false);
    }
}
