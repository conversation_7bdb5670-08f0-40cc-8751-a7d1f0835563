package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.common.service.StyleConsistencyValidator;
import com.bimowu.resume.dto.ExtractedStyles;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 样式一致性验证器实现
 */
@Slf4j
@Service
public class StyleConsistencyValidatorImpl implements StyleConsistencyValidator {
    
    private static final double SIMILARITY_THRESHOLD = 0.8; // 相似度阈值
    
    @Override
    public boolean validateConsistency(ExtractedStyles frontendStyles, ExtractedStyles backendStyles) {
        if (frontendStyles == null || backendStyles == null) {
            return false;
        }
        
        // 验证各个组件的一致性
        boolean fontConsistent = validateFontConsistency(frontendStyles.getFonts(), backendStyles.getFonts());
        boolean colorConsistent = validateColorConsistency(frontendStyles.getColors(), backendStyles.getColors());
        boolean layoutConsistent = validateLayoutConsistency(frontendStyles.getLayout(), backendStyles.getLayout());
        boolean cssConsistent = validateCSSConsistency(frontendStyles.getCss(), backendStyles.getCss());
        
        log.debug("样式一致性验证结果 - 字体: {}, 颜色: {}, 布局: {}, CSS: {}", 
                 fontConsistent, colorConsistent, layoutConsistent, cssConsistent);
        
        return fontConsistent && colorConsistent && layoutConsistent && cssConsistent;
    }
    
    @Override
    public List<String> findDifferences(ExtractedStyles frontendStyles, ExtractedStyles backendStyles) {
        List<String> differences = new ArrayList<>();
        
        if (frontendStyles == null && backendStyles == null) {
            return differences;
        }
        
        if (frontendStyles == null) {
            differences.add("前端样式为空");
            return differences;
        }
        
        if (backendStyles == null) {
            differences.add("后端样式为空");
            return differences;
        }
        
        // 检查字体差异
        differences.addAll(findFontDifferences(frontendStyles.getFonts(), backendStyles.getFonts()));
        
        // 检查颜色差异
        differences.addAll(findColorDifferences(frontendStyles.getColors(), backendStyles.getColors()));
        
        // 检查布局差异
        differences.addAll(findLayoutDifferences(frontendStyles.getLayout(), backendStyles.getLayout()));
        
        // 检查CSS差异
        differences.addAll(findCSSDifferences(frontendStyles.getCss(), backendStyles.getCss()));
        
        return differences;
    }
    
    @Override
    public double calculateSimilarity(ExtractedStyles frontendStyles, ExtractedStyles backendStyles) {
        if (frontendStyles == null || backendStyles == null) {
            return 0.0;
        }
        
        double fontSimilarity = calculateFontSimilarity(frontendStyles.getFonts(), backendStyles.getFonts());
        double colorSimilarity = calculateColorSimilarity(frontendStyles.getColors(), backendStyles.getColors());
        double layoutSimilarity = calculateLayoutSimilarity(frontendStyles.getLayout(), backendStyles.getLayout());
        double cssSimilarity = calculateCSSSimilarity(frontendStyles.getCss(), backendStyles.getCss());
        
        // 加权平均计算总相似度
        double totalSimilarity = (fontSimilarity * 0.3 + colorSimilarity * 0.2 + 
                                 layoutSimilarity * 0.2 + cssSimilarity * 0.3);
        
        log.debug("相似度计算结果 - 字体: {}, 颜色: {}, 布局: {}, CSS: {}, 总体: {}", 
                 fontSimilarity, colorSimilarity, layoutSimilarity, cssSimilarity, totalSimilarity);
        
        return totalSimilarity;
    }
    
    @Override
    public boolean validateFontConsistency(List<ExtractedStyles.FontInfo> frontendFonts, 
                                          List<ExtractedStyles.FontInfo> backendFonts) {
        if (CollectionUtils.isEmpty(frontendFonts) && CollectionUtils.isEmpty(backendFonts)) {
            return true;
        }
        
        if (CollectionUtils.isEmpty(frontendFonts) || CollectionUtils.isEmpty(backendFonts)) {
            return false;
        }
        
        if (frontendFonts.size() != backendFonts.size()) {
            return false;
        }
        
        // 检查主要字体是否一致
        for (ExtractedStyles.FontInfo frontendFont : frontendFonts) {
            boolean found = backendFonts.stream().anyMatch(backendFont -> 
                Objects.equals(frontendFont.getFamily(), backendFont.getFamily()) &&
                Objects.equals(frontendFont.getStyle(), backendFont.getStyle()) &&
                frontendFont.getWeight() == backendFont.getWeight()
            );
            
            if (!found) {
                return false;
            }
        }
        
        return true;
    }
    
    @Override
    public boolean validateColorConsistency(ExtractedStyles.ColorPalette frontendColors, 
                                           ExtractedStyles.ColorPalette backendColors) {
        if (frontendColors == null && backendColors == null) {
            return true;
        }
        
        if (frontendColors == null || backendColors == null) {
            return false;
        }
        
        return Objects.equals(frontendColors.getPrimary(), backendColors.getPrimary()) &&
               Objects.equals(frontendColors.getSecondary(), backendColors.getSecondary()) &&
               Objects.equals(frontendColors.getBackground(), backendColors.getBackground()) &&
               Objects.equals(frontendColors.getText(), backendColors.getText());
    }
    
    @Override
    public boolean validateLayoutConsistency(ExtractedStyles.LayoutInfo frontendLayout, 
                                            ExtractedStyles.LayoutInfo backendLayout) {
        if (frontendLayout == null && backendLayout == null) {
            return true;
        }
        
        if (frontendLayout == null || backendLayout == null) {
            return false;
        }
        
        return frontendLayout.getWidth() == backendLayout.getWidth() &&
               frontendLayout.getHeight() == backendLayout.getHeight() &&
               Objects.equals(frontendLayout.getDisplay(), backendLayout.getDisplay());
    }
    
    /**
     * 验证CSS一致性
     */
    private boolean validateCSSConsistency(String frontendCSS, String backendCSS) {
        if (StringUtils.isEmpty(frontendCSS) && StringUtils.isEmpty(backendCSS)) {
            return true;
        }
        
        if (StringUtils.isEmpty(frontendCSS) || StringUtils.isEmpty(backendCSS)) {
            return false;
        }
        
        // 简化的CSS比较，实际应该进行更复杂的CSS解析和比较
        String normalizedFrontend = normalizeCSS(frontendCSS);
        String normalizedBackend = normalizeCSS(backendCSS);
        
        return normalizedFrontend.equals(normalizedBackend);
    }
    
    /**
     * 查找字体差异
     */
    private List<String> findFontDifferences(List<ExtractedStyles.FontInfo> frontendFonts, 
                                            List<ExtractedStyles.FontInfo> backendFonts) {
        List<String> differences = new ArrayList<>();
        
        if (CollectionUtils.isEmpty(frontendFonts) && !CollectionUtils.isEmpty(backendFonts)) {
            differences.add("前端缺少字体配置");
            return differences;
        }
        
        if (!CollectionUtils.isEmpty(frontendFonts) && CollectionUtils.isEmpty(backendFonts)) {
            differences.add("后端缺少字体配置");
            return differences;
        }
        
        if (!CollectionUtils.isEmpty(frontendFonts) && !CollectionUtils.isEmpty(backendFonts)) {
            for (ExtractedStyles.FontInfo frontendFont : frontendFonts) {
                boolean found = backendFonts.stream().anyMatch(backendFont -> 
                    Objects.equals(frontendFont.getFamily(), backendFont.getFamily())
                );
                
                if (!found) {
                    differences.add(String.format("字体 '%s' 在后端配置中缺失", frontendFont.getFamily()));
                }
            }
        }
        
        return differences;
    }
    
    /**
     * 查找颜色差异
     */
    private List<String> findColorDifferences(ExtractedStyles.ColorPalette frontendColors, 
                                             ExtractedStyles.ColorPalette backendColors) {
        List<String> differences = new ArrayList<>();
        
        if (frontendColors == null && backendColors != null) {
            differences.add("前端缺少颜色配置");
            return differences;
        }
        
        if (frontendColors != null && backendColors == null) {
            differences.add("后端缺少颜色配置");
            return differences;
        }
        
        if (frontendColors != null && backendColors != null) {
            if (!Objects.equals(frontendColors.getPrimary(), backendColors.getPrimary())) {
                differences.add(String.format("主色调不一致: 前端='%s', 后端='%s'", 
                               frontendColors.getPrimary(), backendColors.getPrimary()));
            }
            
            if (!Objects.equals(frontendColors.getBackground(), backendColors.getBackground())) {
                differences.add(String.format("背景色不一致: 前端='%s', 后端='%s'", 
                               frontendColors.getBackground(), backendColors.getBackground()));
            }
        }
        
        return differences;
    }
    
    /**
     * 查找布局差异
     */
    private List<String> findLayoutDifferences(ExtractedStyles.LayoutInfo frontendLayout, 
                                              ExtractedStyles.LayoutInfo backendLayout) {
        List<String> differences = new ArrayList<>();
        
        if (frontendLayout == null && backendLayout != null) {
            differences.add("前端缺少布局配置");
            return differences;
        }
        
        if (frontendLayout != null && backendLayout == null) {
            differences.add("后端缺少布局配置");
            return differences;
        }
        
        if (frontendLayout != null && backendLayout != null) {
            if (frontendLayout.getWidth() != backendLayout.getWidth()) {
                differences.add(String.format("宽度不一致: 前端=%d, 后端=%d", 
                               frontendLayout.getWidth(), backendLayout.getWidth()));
            }
            
            if (frontendLayout.getHeight() != backendLayout.getHeight()) {
                differences.add(String.format("高度不一致: 前端=%d, 后端=%d", 
                               frontendLayout.getHeight(), backendLayout.getHeight()));
            }
        }
        
        return differences;
    }
    
    /**
     * 查找CSS差异
     */
    private List<String> findCSSDifferences(String frontendCSS, String backendCSS) {
        List<String> differences = new ArrayList<>();
        
        if (StringUtils.isEmpty(frontendCSS) && !StringUtils.isEmpty(backendCSS)) {
            differences.add("前端缺少CSS样式");
            return differences;
        }
        
        if (!StringUtils.isEmpty(frontendCSS) && StringUtils.isEmpty(backendCSS)) {
            differences.add("后端缺少CSS样式");
            return differences;
        }
        
        if (!StringUtils.isEmpty(frontendCSS) && !StringUtils.isEmpty(backendCSS)) {
            String normalizedFrontend = normalizeCSS(frontendCSS);
            String normalizedBackend = normalizeCSS(backendCSS);
            
            if (!normalizedFrontend.equals(normalizedBackend)) {
                differences.add("CSS样式内容不一致");
            }
        }
        
        return differences;
    }
    
    /**
     * 计算字体相似度
     */
    private double calculateFontSimilarity(List<ExtractedStyles.FontInfo> frontendFonts, 
                                          List<ExtractedStyles.FontInfo> backendFonts) {
        if (CollectionUtils.isEmpty(frontendFonts) && CollectionUtils.isEmpty(backendFonts)) {
            return 1.0;
        }
        
        if (CollectionUtils.isEmpty(frontendFonts) || CollectionUtils.isEmpty(backendFonts)) {
            return 0.0;
        }
        
        int matchCount = 0;
        for (ExtractedStyles.FontInfo frontendFont : frontendFonts) {
            boolean found = backendFonts.stream().anyMatch(backendFont -> 
                Objects.equals(frontendFont.getFamily(), backendFont.getFamily())
            );
            if (found) {
                matchCount++;
            }
        }
        
        return (double) matchCount / Math.max(frontendFonts.size(), backendFonts.size());
    }
    
    /**
     * 计算颜色相似度
     */
    private double calculateColorSimilarity(ExtractedStyles.ColorPalette frontendColors, 
                                           ExtractedStyles.ColorPalette backendColors) {
        if (frontendColors == null && backendColors == null) {
            return 1.0;
        }
        
        if (frontendColors == null || backendColors == null) {
            return 0.0;
        }
        
        int matchCount = 0;
        int totalCount = 0;
        
        // 比较主要颜色
        if (Objects.equals(frontendColors.getPrimary(), backendColors.getPrimary())) {
            matchCount++;
        }
        totalCount++;
        
        if (Objects.equals(frontendColors.getBackground(), backendColors.getBackground())) {
            matchCount++;
        }
        totalCount++;
        
        if (Objects.equals(frontendColors.getText(), backendColors.getText())) {
            matchCount++;
        }
        totalCount++;
        
        return (double) matchCount / totalCount;
    }
    
    /**
     * 计算布局相似度
     */
    private double calculateLayoutSimilarity(ExtractedStyles.LayoutInfo frontendLayout, 
                                            ExtractedStyles.LayoutInfo backendLayout) {
        if (frontendLayout == null && backendLayout == null) {
            return 1.0;
        }
        
        if (frontendLayout == null || backendLayout == null) {
            return 0.0;
        }
        
        int matchCount = 0;
        int totalCount = 0;
        
        // 比较宽度
        if (frontendLayout.getWidth() == backendLayout.getWidth()) {
            matchCount++;
        }
        totalCount++;
        
        // 比较高度
        if (frontendLayout.getHeight() == backendLayout.getHeight()) {
            matchCount++;
        }
        totalCount++;
        
        // 比较显示类型
        if (Objects.equals(frontendLayout.getDisplay(), backendLayout.getDisplay())) {
            matchCount++;
        }
        totalCount++;
        
        return (double) matchCount / totalCount;
    }
    
    /**
     * 计算CSS相似度
     */
    private double calculateCSSSimilarity(String frontendCSS, String backendCSS) {
        if (StringUtils.isEmpty(frontendCSS) && StringUtils.isEmpty(backendCSS)) {
            return 1.0;
        }
        
        if (StringUtils.isEmpty(frontendCSS) || StringUtils.isEmpty(backendCSS)) {
            return 0.0;
        }
        
        String normalizedFrontend = normalizeCSS(frontendCSS);
        String normalizedBackend = normalizeCSS(backendCSS);
        
        // 简单的字符串相似度计算
        return calculateStringSimilarity(normalizedFrontend, normalizedBackend);
    }
    
    /**
     * 标准化CSS
     */
    private String normalizeCSS(String css) {
        if (StringUtils.isEmpty(css)) {
            return "";
        }
        
        return css.replaceAll("\\s+", " ")
                  .replaceAll("/\\*.*?\\*/", "")
                  .trim()
                  .toLowerCase();
    }
    
    /**
     * 计算字符串相似度
     */
    private double calculateStringSimilarity(String str1, String str2) {
        if (str1.equals(str2)) {
            return 1.0;
        }
        
        int maxLength = Math.max(str1.length(), str2.length());
        if (maxLength == 0) {
            return 1.0;
        }
        
        int editDistance = calculateEditDistance(str1, str2);
        return 1.0 - (double) editDistance / maxLength;
    }
    
    /**
     * 计算编辑距离
     */
    private int calculateEditDistance(String str1, String str2) {
        int m = str1.length();
        int n = str2.length();
        
        int[][] dp = new int[m + 1][n + 1];
        
        for (int i = 0; i <= m; i++) {
            dp[i][0] = i;
        }
        
        for (int j = 0; j <= n; j++) {
            dp[0][j] = j;
        }
        
        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                if (str1.charAt(i - 1) == str2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = 1 + Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]);
                }
            }
        }
        
        return dp[m][n];
    }
}