# 简历制作系统前端项目

## 跨平台构建说明

本项目已经配置为同时支持 Windows 和 Linux 环境下的开发与构建。

### 环境要求
- Node.js >= 14.0.0
- npm >= 6.0.0

### 安装依赖

```bash
# 在项目根目录下执行
npm install
```

### 开发模式

```bash
# 启动开发服务器
npm run dev
```

### 生产构建

```bash
# 构建生产环境版本
npm run build

# 针对不同环境的构建
npm run build:dev    # 开发环境
npm run build:test   # 测试环境
npm run build:prod   # 生产环境
```

### 预览构建结果

```bash
npm run preview
```

## 平台兼容性说明

### Windows 与 Linux 平台兼容

项目已在 package.json 中配置了必要的跨平台依赖：

```json
"optionalDependencies": {
  "@esbuild/linux-x64": "0.18.20",
  "@esbuild/win32-x64": "0.18.20"
}
```

这样配置可以确保项目在不同操作系统下都能正常构建。如果在构建过程中遇到 esbuild 相关的错误，可以尝试手动安装对应平台的包：

#### Windows 平台
```bash
npm install @esbuild/win32-x64@0.18.20 --save-dev
```

#### Linux 平台
```bash
npm install @esbuild/linux-x64@0.18.20 --save-dev
```

### 构建时可能的问题及解决方案

如果在 Linux 服务器上构建时出现 `@esbuild/linux-x64` 相关错误，请确保：

1. Node.js 和 npm 版本满足要求
2. 已安装 Linux 平台所需的 esbuild 依赖包
3. 没有使用 `--no-optional` 或 `--omit=optional` 标志安装依赖

如果问题仍然存在，可以尝试清除 node_modules 并重新安装：

```bash
rm -rf node_modules
rm -f package-lock.json
npm cache clean --force
npm install
```

## 技术栈

- Vue 3
- Vite
- Element Plus
- Vue Router
- Pinia
- Axios 