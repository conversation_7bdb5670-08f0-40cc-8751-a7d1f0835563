import type { UploadContentProps } from './upload-content';
declare const _default: import("vue").DefineComponent<{
    readonly beforeUpload: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
        (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
        (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly beforeRemove: {
        readonly type: import("vue").PropType<(uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => import("../../../utils").Awaitable<boolean>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onRemove: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onChange: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onPreview: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./upload").UploadFile) => void) | (() => (uploadFile: import("./upload").UploadFile) => void) | {
        (): (uploadFile: import("./upload").UploadFile) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (uploadFile: import("./upload").UploadFile) => void) | (() => (uploadFile: import("./upload").UploadFile) => void) | {
        (): (uploadFile: import("./upload").UploadFile) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onSuccess: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onProgress: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onError: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onExceed: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
        (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
        (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly action: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
    readonly headers: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly method: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
    readonly data: import("../../../utils").EpPropFinalized<ObjectConstructor, unknown, unknown, () => import("../../../utils").Mutable<{}>, boolean>;
    readonly multiple: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly name: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
    readonly drag: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly withCredentials: BooleanConstructor;
    readonly showFileList: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    readonly accept: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly type: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "select", boolean>;
    readonly fileList: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]) | ((new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
    readonly autoUpload: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
    readonly httpRequest: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
        (): import("./upload").UploadRequestHandler;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
        (): import("./upload").UploadRequestHandler;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, import("./upload").UploadRequestHandler, boolean>;
    readonly disabled: BooleanConstructor;
    readonly limit: NumberConstructor;
}, {
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly beforeUpload: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly beforeRemove: {
            readonly type: import("vue").PropType<(uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => import("../../../utils").Awaitable<boolean>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly onRemove: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
            (): (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
            (): (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onChange: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
            (): (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
            (): (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onPreview: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./upload").UploadFile) => void) | (() => (uploadFile: import("./upload").UploadFile) => void) | {
            (): (uploadFile: import("./upload").UploadFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (uploadFile: import("./upload").UploadFile) => void) | (() => (uploadFile: import("./upload").UploadFile) => void) | {
            (): (uploadFile: import("./upload").UploadFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onSuccess: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
            (): (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
            (): (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onProgress: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
            (): (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
            (): (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onError: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
            (): (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
            (): (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onExceed: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly action: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
        readonly headers: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly method: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
        readonly data: import("../../../utils").EpPropFinalized<ObjectConstructor, unknown, unknown, () => import("../../../utils").Mutable<{}>, boolean>;
        readonly multiple: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly name: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
        readonly drag: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly withCredentials: BooleanConstructor;
        readonly showFileList: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly accept: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly type: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "select", boolean>;
        readonly fileList: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]) | ((new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
        readonly autoUpload: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
        readonly httpRequest: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
            (): import("./upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
            (): import("./upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, import("./upload").UploadRequestHandler, boolean>;
        readonly disabled: BooleanConstructor;
        readonly limit: NumberConstructor;
    }>> & {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
    }>>;
    disabled: import("vue").ComputedRef<boolean>;
    uploadRef: import("vue").ShallowRef<({
        $: import("vue").ComponentInternalInstance;
        $data: {};
        $props: Partial<{
            readonly type: string;
            readonly disabled: boolean;
            readonly name: string;
            readonly drag: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly multiple: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly data: Record<string, any>;
            readonly onError: (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
            readonly onProgress: (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
            readonly action: string;
            readonly method: string;
            readonly showFileList: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly accept: string;
            readonly fileList: import("./upload").UploadUserFile[];
            readonly autoUpload: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly listType: import("../../../utils").EpPropMergeType<StringConstructor, "text" | "picture" | "picture-card", unknown>;
            readonly httpRequest: import("./upload").UploadRequestHandler;
            readonly withCredentials: boolean;
            readonly beforeUpload: (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            readonly onRemove: (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
            readonly onSuccess: (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
            readonly onExceed: (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
            readonly onStart: (rawFile: import("./upload").UploadRawFile) => void;
        }> & Omit<Readonly<import("vue").ExtractPropTypes<{
            readonly beforeUpload: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onRemove: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onStart: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
                (): (rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
                (): (rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onSuccess: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onProgress: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onError: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onExceed: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly action: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
            readonly headers: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly method: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
            readonly data: import("../../../utils").EpPropFinalized<ObjectConstructor, unknown, unknown, () => import("../../../utils").Mutable<{}>, boolean>;
            readonly multiple: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly name: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
            readonly drag: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly withCredentials: BooleanConstructor;
            readonly showFileList: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly accept: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly type: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "select", boolean>;
            readonly fileList: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]) | ((new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
            readonly autoUpload: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
            readonly httpRequest: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
                (): import("./upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
                (): import("./upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, import("./upload").UploadRequestHandler, boolean>;
            readonly disabled: BooleanConstructor;
            readonly limit: NumberConstructor;
        }>> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, "type" | "disabled" | "name" | "drag" | "multiple" | "data" | "onError" | "onProgress" | "action" | "method" | "showFileList" | "accept" | "fileList" | "autoUpload" | "listType" | "httpRequest" | "withCredentials" | "beforeUpload" | "onRemove" | "onSuccess" | "onExceed" | "onStart">;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        };
        $slots: Readonly<{
            [name: string]: import("vue").Slot | undefined;
        }>;
        $root: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $parent: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $emit: (event: string, ...args: any[]) => void;
        $el: any;
        $options: import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
            readonly beforeUpload: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onRemove: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onStart: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
                (): (rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
                (): (rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onSuccess: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onProgress: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onError: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onExceed: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly action: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
            readonly headers: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly method: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
            readonly data: import("../../../utils").EpPropFinalized<ObjectConstructor, unknown, unknown, () => import("../../../utils").Mutable<{}>, boolean>;
            readonly multiple: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly name: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
            readonly drag: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly withCredentials: BooleanConstructor;
            readonly showFileList: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly accept: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly type: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "select", boolean>;
            readonly fileList: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]) | ((new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
            readonly autoUpload: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
            readonly httpRequest: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
                (): import("./upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
                (): import("./upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, import("./upload").UploadRequestHandler, boolean>;
            readonly disabled: BooleanConstructor;
            readonly limit: NumberConstructor;
        }>>, {
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                readonly beforeUpload: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                    (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                    (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, () => void, boolean>;
                readonly onRemove: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
                    (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
                    (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, () => void, boolean>;
                readonly onStart: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
                    (): (rawFile: import("./upload").UploadRawFile) => void;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
                    (): (rawFile: import("./upload").UploadRawFile) => void;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, () => void, boolean>;
                readonly onSuccess: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
                    (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
                    (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, () => void, boolean>;
                readonly onProgress: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
                    (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
                    (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, () => void, boolean>;
                readonly onError: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
                    (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
                    (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, () => void, boolean>;
                readonly onExceed: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
                    (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
                    (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, () => void, boolean>;
                readonly action: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
                readonly headers: {
                    readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly method: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
                readonly data: import("../../../utils").EpPropFinalized<ObjectConstructor, unknown, unknown, () => import("../../../utils").Mutable<{}>, boolean>;
                readonly multiple: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
                readonly name: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
                readonly drag: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
                readonly withCredentials: BooleanConstructor;
                readonly showFileList: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly accept: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
                readonly type: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "select", boolean>;
                readonly fileList: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]) | ((new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
                readonly autoUpload: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
                readonly httpRequest: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
                    (): import("./upload").UploadRequestHandler;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
                    (): import("./upload").UploadRequestHandler;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, import("./upload").UploadRequestHandler, boolean>;
                readonly disabled: BooleanConstructor;
                readonly limit: NumberConstructor;
            }>> & {
                [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
            }>>;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            disabled: import("vue").ComputedRef<boolean>;
            requests: import("vue").ShallowRef<Record<string, XMLHttpRequest | Promise<unknown>>>;
            inputRef: import("vue").ShallowRef<HTMLInputElement | undefined>;
            uploadFiles: (files: File[]) => void;
            upload: (rawFile: import("./upload").UploadRawFile) => Promise<void>;
            doUpload: (rawFile: import("./upload").UploadRawFile, beforeData?: Record<string, any> | undefined) => void;
            handleChange: (e: Event) => void;
            handleClick: () => void;
            handleKeydown: () => void;
            abort: (file?: import("./upload").UploadFile | undefined) => void;
            UploadDragger: import("vue").DefineComponent<{
                readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            }, {
                COMPONENT_NAME: string;
                emit: (event: "file", file: File[]) => void;
                uploaderContext: import("./constants").UploadContext;
                ns: {
                    namespace: import("vue").ComputedRef<string>;
                    b: (blockSuffix?: string) => string;
                    e: (element?: string | undefined) => string;
                    m: (modifier?: string | undefined) => string;
                    be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                    em: (element?: string | undefined, modifier?: string | undefined) => string;
                    bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                    bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                    is: {
                        (name: string, state: boolean | undefined): string;
                        (name: string): string;
                    };
                    cssVar: (object: Record<string, string>) => Record<string, string>;
                    cssVarName: (name: string) => string;
                    cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                    cssVarBlockName: (name: string) => string;
                };
                dragover: import("vue").Ref<boolean>;
                disabled: import("vue").ComputedRef<boolean>;
                onDrop: (e: DragEvent) => void;
                onDragover: () => void;
            }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
                file: (file: File[]) => boolean;
            }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
                readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            }>> & {
                onFile?: ((file: File[]) => any) | undefined;
            }, {
                readonly disabled: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            }>;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
            readonly type: string;
            readonly disabled: boolean;
            readonly name: string;
            readonly drag: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly multiple: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly data: Record<string, any>;
            readonly onError: (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
            readonly onProgress: (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
            readonly action: string;
            readonly method: string;
            readonly showFileList: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly accept: string;
            readonly fileList: import("./upload").UploadUserFile[];
            readonly autoUpload: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly listType: import("../../../utils").EpPropMergeType<StringConstructor, "text" | "picture" | "picture-card", unknown>;
            readonly httpRequest: import("./upload").UploadRequestHandler;
            readonly withCredentials: boolean;
            readonly beforeUpload: (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            readonly onRemove: (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
            readonly onSuccess: (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
            readonly onExceed: (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
            readonly onStart: (rawFile: import("./upload").UploadRawFile) => void;
        }> & {
            beforeCreate?: ((() => void) | (() => void)[]) | undefined;
            created?: ((() => void) | (() => void)[]) | undefined;
            beforeMount?: ((() => void) | (() => void)[]) | undefined;
            mounted?: ((() => void) | (() => void)[]) | undefined;
            beforeUpdate?: ((() => void) | (() => void)[]) | undefined;
            updated?: ((() => void) | (() => void)[]) | undefined;
            activated?: ((() => void) | (() => void)[]) | undefined;
            deactivated?: ((() => void) | (() => void)[]) | undefined;
            beforeDestroy?: ((() => void) | (() => void)[]) | undefined;
            beforeUnmount?: ((() => void) | (() => void)[]) | undefined;
            destroyed?: ((() => void) | (() => void)[]) | undefined;
            unmounted?: ((() => void) | (() => void)[]) | undefined;
            renderTracked?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            renderTriggered?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            errorCaptured?: (((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void) | ((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void)[]) | undefined;
        };
        $forceUpdate: () => void;
        $nextTick: typeof import("vue").nextTick;
        $watch(source: string | Function, cb: Function, options?: import("vue").WatchOptions<boolean> | undefined): import("vue").WatchStopHandle;
    } & Readonly<import("vue").ExtractPropTypes<{
        readonly beforeUpload: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onRemove: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
            (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
            (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onStart: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
            (): (rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
            (): (rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onSuccess: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
            (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
            (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onProgress: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
            (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
            (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onError: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
            (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
            (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onExceed: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly action: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
        readonly headers: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly method: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
        readonly data: import("../../../utils").EpPropFinalized<ObjectConstructor, unknown, unknown, () => import("../../../utils").Mutable<{}>, boolean>;
        readonly multiple: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly name: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
        readonly drag: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly withCredentials: BooleanConstructor;
        readonly showFileList: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly accept: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly type: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "select", boolean>;
        readonly fileList: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]) | ((new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
        readonly autoUpload: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
        readonly httpRequest: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
            (): import("./upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
            (): import("./upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, import("./upload").UploadRequestHandler, boolean>;
        readonly disabled: BooleanConstructor;
        readonly limit: NumberConstructor;
    }>> & import("vue").ShallowUnwrapRef<{
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly beforeUpload: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onRemove: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onStart: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
                (): (rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
                (): (rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onSuccess: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onProgress: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onError: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onExceed: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly action: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
            readonly headers: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly method: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
            readonly data: import("../../../utils").EpPropFinalized<ObjectConstructor, unknown, unknown, () => import("../../../utils").Mutable<{}>, boolean>;
            readonly multiple: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly name: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
            readonly drag: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly withCredentials: BooleanConstructor;
            readonly showFileList: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly accept: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly type: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "select", boolean>;
            readonly fileList: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]) | ((new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
            readonly autoUpload: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
            readonly httpRequest: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
                (): import("./upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
                (): import("./upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, import("./upload").UploadRequestHandler, boolean>;
            readonly disabled: BooleanConstructor;
            readonly limit: NumberConstructor;
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        disabled: import("vue").ComputedRef<boolean>;
        requests: import("vue").ShallowRef<Record<string, XMLHttpRequest | Promise<unknown>>>;
        inputRef: import("vue").ShallowRef<HTMLInputElement | undefined>;
        uploadFiles: (files: File[]) => void;
        upload: (rawFile: import("./upload").UploadRawFile) => Promise<void>;
        doUpload: (rawFile: import("./upload").UploadRawFile, beforeData?: Record<string, any> | undefined) => void;
        handleChange: (e: Event) => void;
        handleClick: () => void;
        handleKeydown: () => void;
        abort: (file?: import("./upload").UploadFile | undefined) => void;
        UploadDragger: import("vue").DefineComponent<{
            readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        }, {
            COMPONENT_NAME: string;
            emit: (event: "file", file: File[]) => void;
            uploaderContext: import("./constants").UploadContext;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            dragover: import("vue").Ref<boolean>;
            disabled: import("vue").ComputedRef<boolean>;
            onDrop: (e: DragEvent) => void;
            onDragover: () => void;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            file: (file: File[]) => boolean;
        }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        }>> & {
            onFile?: ((file: File[]) => any) | undefined;
        }, {
            readonly disabled: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        }>;
    }> & {} & import("vue").ComponentCustomProperties) | undefined>;
    abort: (file: import("./upload").UploadFile) => void;
    submit: () => void;
    clearFiles: (states?: import("./upload").UploadStatus[]) => void;
    uploadFiles: import("vue").Ref<{
        name: string;
        percentage?: number | undefined;
        status: import("./upload").UploadStatus;
        size?: number | undefined;
        response?: unknown;
        uid: number;
        url?: string | undefined;
        raw?: {
            uid: number;
            readonly lastModified: number;
            readonly name: string;
            readonly webkitRelativePath: string;
            readonly size: number;
            readonly type: string;
            arrayBuffer: () => Promise<ArrayBuffer>;
            slice: (start?: number | undefined, end?: number | undefined, contentType?: string | undefined) => Blob;
            stream: () => ReadableStream<any>;
            text: () => Promise<string>;
        } | undefined;
    }[]> | import("vue").WritableComputedRef<import("./upload").UploadFiles>;
    handleStart: (rawFile: import("./upload").UploadRawFile) => void;
    handleError: (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
    handleRemove: (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
    handleSuccess: (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
    handleProgress: (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
    isPictureCard: import("vue").ComputedRef<boolean>;
    uploadContentProps: import("vue").ComputedRef<UploadContentProps>;
    UploadList: import("vue").DefineComponent<{
        readonly files: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadFiles) | (() => import("./upload").UploadFiles) | ((new (...args: any[]) => import("./upload").UploadFiles) | (() => import("./upload").UploadFiles))[], unknown, unknown, () => never[], boolean>;
        readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly handlePreview: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./upload").UploadFile) => void) | (() => (uploadFile: import("./upload").UploadFile) => void) | {
            (): (uploadFile: import("./upload").UploadFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (uploadFile: import("./upload").UploadFile) => void) | (() => (uploadFile: import("./upload").UploadFile) => void) | {
            (): (uploadFile: import("./upload").UploadFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
    }, {
        emit: (event: "remove", file: import("./upload").UploadFile) => void;
        t: import("../../..").Translator;
        nsUpload: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        nsIcon: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        nsList: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        disabled: import("vue").ComputedRef<boolean>;
        focusing: import("vue").Ref<boolean>;
        handleRemove: (file: import("./upload").UploadFile) => void;
        ElIcon: import("../../../utils").SFCWithInstall<import("vue").DefineComponent<{
            readonly size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }, {
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                readonly size: {
                    readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly color: {
                    readonly type: import("vue").PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
            }>> & {
                [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
            }>>;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            style: import("vue").ComputedRef<import("vue").CSSProperties>;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }>>, {}>> & Record<string, any>;
        Check: import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>;
        CircleCheck: import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>;
        Close: import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>;
        Delete: import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>;
        Document: import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>;
        ZoomIn: import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>;
        ElProgress: import("../../../utils").SFCWithInstall<import("vue").DefineComponent<{
            readonly type: import("../../../utils").EpPropFinalized<StringConstructor, "circle" | "line" | "dashboard", unknown, "line", boolean>;
            readonly percentage: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
            readonly status: import("../../../utils").EpPropFinalized<StringConstructor, "" | "success" | "warning" | "exception", unknown, "", boolean>;
            readonly indeterminate: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly duration: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 3, boolean>;
            readonly strokeWidth: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 6, boolean>;
            readonly strokeLinecap: import("../../../utils").EpPropFinalized<(new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square") | ((new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square"))[], unknown, unknown, "round", boolean>;
            readonly textInside: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 126, boolean>;
            readonly showText: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly color: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | import("../..").ProgressFn | import("../..").ProgressColor[]) & {}) | (() => string | import("../..").ProgressFn | import("../..").ProgressColor[]) | ((new (...args: any[]) => (string | import("../..").ProgressFn | import("../..").ProgressColor[]) & {}) | (() => string | import("../..").ProgressFn | import("../..").ProgressColor[]))[], unknown, unknown, "", boolean>;
            readonly striped: BooleanConstructor;
            readonly stripedFlow: BooleanConstructor;
            readonly format: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("../..").ProgressFn) | (() => import("../..").ProgressFn) | {
                (): import("../..").ProgressFn;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => import("../..").ProgressFn) | (() => import("../..").ProgressFn) | {
                (): import("../..").ProgressFn;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, (percentage: number) => string, boolean>;
        }, {
            STATUS_COLOR_MAP: Record<string, string>;
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                readonly type: import("../../../utils").EpPropFinalized<StringConstructor, "circle" | "line" | "dashboard", unknown, "line", boolean>;
                readonly percentage: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
                readonly status: import("../../../utils").EpPropFinalized<StringConstructor, "" | "success" | "warning" | "exception", unknown, "", boolean>;
                readonly indeterminate: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
                readonly duration: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 3, boolean>;
                readonly strokeWidth: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 6, boolean>;
                readonly strokeLinecap: import("../../../utils").EpPropFinalized<(new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square") | ((new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square"))[], unknown, unknown, "round", boolean>;
                readonly textInside: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
                readonly width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 126, boolean>;
                readonly showText: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly color: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | import("../..").ProgressFn | import("../..").ProgressColor[]) & {}) | (() => string | import("../..").ProgressFn | import("../..").ProgressColor[]) | ((new (...args: any[]) => (string | import("../..").ProgressFn | import("../..").ProgressColor[]) & {}) | (() => string | import("../..").ProgressFn | import("../..").ProgressColor[]))[], unknown, unknown, "", boolean>;
                readonly striped: BooleanConstructor;
                readonly stripedFlow: BooleanConstructor;
                readonly format: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("../..").ProgressFn) | (() => import("../..").ProgressFn) | {
                    (): import("../..").ProgressFn;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => import("../..").ProgressFn) | (() => import("../..").ProgressFn) | {
                    (): import("../..").ProgressFn;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, (percentage: number) => string, boolean>;
            }>> & {
                [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
            }>>;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            barStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
            relativeStrokeWidth: import("vue").ComputedRef<string>;
            radius: import("vue").ComputedRef<number>;
            trackPath: import("vue").ComputedRef<string>;
            perimeter: import("vue").ComputedRef<number>;
            rate: import("vue").ComputedRef<1 | 0.75>;
            strokeDashoffset: import("vue").ComputedRef<string>;
            trailPathStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
            circlePathStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
            stroke: import("vue").ComputedRef<string>;
            statusIcon: import("vue").ComputedRef<import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>>;
            progressTextSize: import("vue").ComputedRef<number>;
            content: import("vue").ComputedRef<string>;
            getColors: (color: import("../..").ProgressColor[]) => import("../..").ProgressColor[];
            getCurrentColor: (percentage: number) => string;
            ElIcon: import("../../../utils").SFCWithInstall<import("vue").DefineComponent<{
                readonly size: {
                    readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly color: {
                    readonly type: import("vue").PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
            }, {
                props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                    readonly size: {
                        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly color: {
                        readonly type: import("vue").PropType<string>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                }>> & {
                    [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
                }>>;
                ns: {
                    namespace: import("vue").ComputedRef<string>;
                    b: (blockSuffix?: string) => string;
                    e: (element?: string | undefined) => string;
                    m: (modifier?: string | undefined) => string;
                    be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                    em: (element?: string | undefined, modifier?: string | undefined) => string;
                    bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                    bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                    is: {
                        (name: string, state: boolean | undefined): string;
                        (name: string): string;
                    };
                    cssVar: (object: Record<string, string>) => Record<string, string>;
                    cssVarName: (name: string) => string;
                    cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                    cssVarBlockName: (name: string) => string;
                };
                style: import("vue").ComputedRef<import("vue").CSSProperties>;
            }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
                readonly size: {
                    readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly color: {
                    readonly type: import("vue").PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
            }>>, {}>> & Record<string, any>;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly type: import("../../../utils").EpPropFinalized<StringConstructor, "circle" | "line" | "dashboard", unknown, "line", boolean>;
            readonly percentage: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
            readonly status: import("../../../utils").EpPropFinalized<StringConstructor, "" | "success" | "warning" | "exception", unknown, "", boolean>;
            readonly indeterminate: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly duration: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 3, boolean>;
            readonly strokeWidth: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 6, boolean>;
            readonly strokeLinecap: import("../../../utils").EpPropFinalized<(new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square") | ((new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square"))[], unknown, unknown, "round", boolean>;
            readonly textInside: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 126, boolean>;
            readonly showText: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly color: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | import("../..").ProgressFn | import("../..").ProgressColor[]) & {}) | (() => string | import("../..").ProgressFn | import("../..").ProgressColor[]) | ((new (...args: any[]) => (string | import("../..").ProgressFn | import("../..").ProgressColor[]) & {}) | (() => string | import("../..").ProgressFn | import("../..").ProgressColor[]))[], unknown, unknown, "", boolean>;
            readonly striped: BooleanConstructor;
            readonly stripedFlow: BooleanConstructor;
            readonly format: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("../..").ProgressFn) | (() => import("../..").ProgressFn) | {
                (): import("../..").ProgressFn;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => import("../..").ProgressFn) | (() => import("../..").ProgressFn) | {
                (): import("../..").ProgressFn;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, (percentage: number) => string, boolean>;
        }>>, {
            readonly type: import("../../../utils").EpPropMergeType<StringConstructor, "circle" | "line" | "dashboard", unknown>;
            readonly width: number;
            readonly color: import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | import("../..").ProgressFn | import("../..").ProgressColor[]) & {}) | (() => string | import("../..").ProgressFn | import("../..").ProgressColor[]) | ((new (...args: any[]) => (string | import("../..").ProgressFn | import("../..").ProgressColor[]) & {}) | (() => string | import("../..").ProgressFn | import("../..").ProgressColor[]))[], unknown, unknown>;
            readonly strokeLinecap: import("../../../utils").EpPropMergeType<(new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square") | ((new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square"))[], unknown, unknown>;
            readonly strokeWidth: number;
            readonly indeterminate: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly format: import("../..").ProgressFn;
            readonly percentage: number;
            readonly status: import("../../../utils").EpPropMergeType<StringConstructor, "" | "success" | "warning" | "exception", unknown>;
            readonly duration: number;
            readonly textInside: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly showText: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly striped: boolean;
            readonly stripedFlow: boolean;
        }>> & Record<string, any>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        remove: (file: import("./upload").UploadFile) => boolean;
    }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly files: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadFiles) | (() => import("./upload").UploadFiles) | ((new (...args: any[]) => import("./upload").UploadFiles) | (() => import("./upload").UploadFiles))[], unknown, unknown, () => never[], boolean>;
        readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly handlePreview: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./upload").UploadFile) => void) | (() => (uploadFile: import("./upload").UploadFile) => void) | {
            (): (uploadFile: import("./upload").UploadFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (uploadFile: import("./upload").UploadFile) => void) | (() => (uploadFile: import("./upload").UploadFile) => void) | {
            (): (uploadFile: import("./upload").UploadFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
    }>> & {
        onRemove?: ((file: import("./upload").UploadFile) => any) | undefined;
    }, {
        readonly disabled: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly listType: import("../../../utils").EpPropMergeType<StringConstructor, "text" | "picture" | "picture-card", unknown>;
        readonly files: import("./upload").UploadFiles;
        readonly handlePreview: (uploadFile: import("./upload").UploadFile) => void;
    }>;
    UploadContent: import("vue").DefineComponent<{
        readonly beforeUpload: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onRemove: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
            (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
            (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onStart: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
            (): (rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
            (): (rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onSuccess: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
            (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
            (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onProgress: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
            (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
            (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onError: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
            (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
            (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onExceed: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly action: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
        readonly headers: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly method: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
        readonly data: import("../../../utils").EpPropFinalized<ObjectConstructor, unknown, unknown, () => import("../../../utils").Mutable<{}>, boolean>;
        readonly multiple: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly name: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
        readonly drag: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly withCredentials: BooleanConstructor;
        readonly showFileList: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly accept: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly type: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "select", boolean>;
        readonly fileList: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]) | ((new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
        readonly autoUpload: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
        readonly httpRequest: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
            (): import("./upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
            (): import("./upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, import("./upload").UploadRequestHandler, boolean>;
        readonly disabled: BooleanConstructor;
        readonly limit: NumberConstructor;
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly beforeUpload: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onRemove: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onStart: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
                (): (rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
                (): (rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onSuccess: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onProgress: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onError: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
                (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onExceed: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly action: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
            readonly headers: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly method: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
            readonly data: import("../../../utils").EpPropFinalized<ObjectConstructor, unknown, unknown, () => import("../../../utils").Mutable<{}>, boolean>;
            readonly multiple: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly name: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
            readonly drag: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly withCredentials: BooleanConstructor;
            readonly showFileList: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly accept: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly type: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "select", boolean>;
            readonly fileList: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]) | ((new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
            readonly autoUpload: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
            readonly httpRequest: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
                (): import("./upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
                (): import("./upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, import("./upload").UploadRequestHandler, boolean>;
            readonly disabled: BooleanConstructor;
            readonly limit: NumberConstructor;
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        disabled: import("vue").ComputedRef<boolean>;
        requests: import("vue").ShallowRef<Record<string, XMLHttpRequest | Promise<unknown>>>;
        inputRef: import("vue").ShallowRef<HTMLInputElement | undefined>;
        uploadFiles: (files: File[]) => void;
        upload: (rawFile: import("./upload").UploadRawFile) => Promise<void>;
        doUpload: (rawFile: import("./upload").UploadRawFile, beforeData?: Record<string, any> | undefined) => void;
        handleChange: (e: Event) => void;
        handleClick: () => void;
        handleKeydown: () => void;
        abort: (file?: import("./upload").UploadFile | undefined) => void;
        UploadDragger: import("vue").DefineComponent<{
            readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        }, {
            COMPONENT_NAME: string;
            emit: (event: "file", file: File[]) => void;
            uploaderContext: import("./constants").UploadContext;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            dragover: import("vue").Ref<boolean>;
            disabled: import("vue").ComputedRef<boolean>;
            onDrop: (e: DragEvent) => void;
            onDragover: () => void;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            file: (file: File[]) => boolean;
        }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        }>> & {
            onFile?: ((file: File[]) => any) | undefined;
        }, {
            readonly disabled: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        }>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly beforeUpload: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onRemove: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
            (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | (() => (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void) | {
            (): (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onStart: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
            (): (rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => void) | (() => (rawFile: import("./upload").UploadRawFile) => void) | {
            (): (rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onSuccess: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
            (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./upload").UploadRawFile) => unknown) | {
            (): (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onProgress: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
            (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | (() => (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void) | {
            (): (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onError: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
            (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | (() => (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void) | {
            (): (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onExceed: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly action: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
        readonly headers: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly method: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
        readonly data: import("../../../utils").EpPropFinalized<ObjectConstructor, unknown, unknown, () => import("../../../utils").Mutable<{}>, boolean>;
        readonly multiple: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly name: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
        readonly drag: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly withCredentials: BooleanConstructor;
        readonly showFileList: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly accept: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly type: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "select", boolean>;
        readonly fileList: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]) | ((new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
        readonly autoUpload: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
        readonly httpRequest: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
            (): import("./upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
            (): import("./upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, import("./upload").UploadRequestHandler, boolean>;
        readonly disabled: BooleanConstructor;
        readonly limit: NumberConstructor;
    }>>, {
        readonly type: string;
        readonly disabled: boolean;
        readonly name: string;
        readonly drag: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly multiple: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly data: Record<string, any>;
        readonly onError: (err: import("./ajax").UploadAjaxError, rawFile: import("./upload").UploadRawFile) => void;
        readonly onProgress: (evt: import("./upload").UploadProgressEvent, rawFile: import("./upload").UploadRawFile) => void;
        readonly action: string;
        readonly method: string;
        readonly showFileList: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly accept: string;
        readonly fileList: import("./upload").UploadUserFile[];
        readonly autoUpload: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly listType: import("../../../utils").EpPropMergeType<StringConstructor, "text" | "picture" | "picture-card", unknown>;
        readonly httpRequest: import("./upload").UploadRequestHandler;
        readonly withCredentials: boolean;
        readonly beforeUpload: (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
        readonly onRemove: (file: import("./upload").UploadFile | import("./upload").UploadRawFile, rawFile?: import("./upload").UploadRawFile | undefined) => void;
        readonly onSuccess: (response: any, rawFile: import("./upload").UploadRawFile) => unknown;
        readonly onExceed: (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
        readonly onStart: (rawFile: import("./upload").UploadRawFile) => void;
    }>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly beforeUpload: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
        (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
        (): (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly beforeRemove: {
        readonly type: import("vue").PropType<(uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => import("../../../utils").Awaitable<boolean>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onRemove: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onChange: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onPreview: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./upload").UploadFile) => void) | (() => (uploadFile: import("./upload").UploadFile) => void) | {
        (): (uploadFile: import("./upload").UploadFile) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (uploadFile: import("./upload").UploadFile) => void) | (() => (uploadFile: import("./upload").UploadFile) => void) | {
        (): (uploadFile: import("./upload").UploadFile) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onSuccess: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onProgress: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onError: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | (() => (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void) | {
        (): (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onExceed: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
        (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void) | {
        (): (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly action: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
    readonly headers: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly method: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
    readonly data: import("../../../utils").EpPropFinalized<ObjectConstructor, unknown, unknown, () => import("../../../utils").Mutable<{}>, boolean>;
    readonly multiple: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly name: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
    readonly drag: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly withCredentials: BooleanConstructor;
    readonly showFileList: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    readonly accept: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly type: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "select", boolean>;
    readonly fileList: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]) | ((new (...args: any[]) => import("./upload").UploadUserFile[]) | (() => import("./upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
    readonly autoUpload: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    readonly listType: import("../../../utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
    readonly httpRequest: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
        (): import("./upload").UploadRequestHandler;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => import("./upload").UploadRequestHandler) | (() => import("./upload").UploadRequestHandler) | {
        (): import("./upload").UploadRequestHandler;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, import("./upload").UploadRequestHandler, boolean>;
    readonly disabled: BooleanConstructor;
    readonly limit: NumberConstructor;
}>>, {
    readonly type: string;
    readonly onChange: (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
    readonly disabled: boolean;
    readonly name: string;
    readonly drag: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly multiple: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly data: Record<string, any>;
    readonly onError: (error: Error, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
    readonly onProgress: (evt: import("./upload").UploadProgressEvent, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
    readonly action: string;
    readonly method: string;
    readonly showFileList: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly accept: string;
    readonly fileList: import("./upload").UploadUserFile[];
    readonly autoUpload: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly listType: import("../../../utils").EpPropMergeType<StringConstructor, "text" | "picture" | "picture-card", unknown>;
    readonly httpRequest: import("./upload").UploadRequestHandler;
    readonly withCredentials: boolean;
    readonly beforeUpload: (rawFile: import("./upload").UploadRawFile) => import("../../../utils").Awaitable<boolean | void | File | Blob | null | undefined>;
    readonly onRemove: (uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
    readonly onPreview: (uploadFile: import("./upload").UploadFile) => void;
    readonly onSuccess: (response: any, uploadFile: import("./upload").UploadFile, uploadFiles: import("./upload").UploadFiles) => void;
    readonly onExceed: (files: File[], uploadFiles: import("./upload").UploadUserFile[]) => void;
}>;
export default _default;
