import * as faceapi from 'face-api.js'

// 模型路径
const MODEL_URL = '/interview-ai/models'

// 检查模型是否已加载
let modelsLoaded = false

// 加载人脸检测模型
export const loadFaceDetectionModels = async () => {
  try {
    // 如果模型已加载，直接返回成功
    if (modelsLoaded) {
      console.log('人脸检测模型已加载，跳过加载过程')
      return true
    }
    
    console.log('开始加载人脸检测模型，路径:', MODEL_URL)
    
    // 先检查模型是否可用
    try {
      // 尝试请求模型清单文件
      const manifestResponse = await fetch(`${MODEL_URL}/tiny_face_detector_model-weights_manifest.json`)
      if (!manifestResponse.ok) {
        throw new Error(`模型清单文件请求失败: ${manifestResponse.status} ${manifestResponse.statusText}`)
      }
      
      const manifest = await manifestResponse.json()
      console.log('模型清单加载成功:', manifest)
    } catch (error) {
      console.error('模型文件检查失败:', error)
      return false
    }
    
    // 设置较长的超时时间
    const loadOptions = {
      fetchOptions: {
        timeout: 30000 // 30秒超时
      }
    }
    
    // 顺序加载模型，确保每个模型都加载成功
    console.log('加载微型人脸检测器模型...')
    await faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL)
    console.log('微型人脸检测器模型加载成功')
    
    console.log('加载人脸特征点模型...')
    await faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL)
    console.log('人脸特征点模型加载成功')
    
    console.log('加载人脸表情模型...')
    await faceapi.nets.faceExpressionNet.loadFromUri(MODEL_URL)
    console.log('人脸表情模型加载成功')
    
    modelsLoaded = true
    console.log('所有人脸检测模型加载成功')
    return true
  } catch (error) {
    console.error('加载人脸检测模型失败:', error)
    return false
  }
}

// 分析面部表情
export const analyzeFacialExpressions = async (video: HTMLVideoElement) => {
  if (!video) return null
  
  // 确保模型已加载
  if (!modelsLoaded) {
    console.warn('模型尚未加载，尝试加载模型')
    const loaded = await loadFaceDetectionModels()
    if (!loaded) {
      console.error('无法加载模型，无法分析面部表情')
      return {
        detected: false,
        expressions: null,
        message: '人脸检测模型未加载'
      }
    }
  }
  
  try {
    const detections = await faceapi
      .detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
      .withFaceLandmarks()
      .withFaceExpressions()
    
    if (!detections.length) {
      return {
        detected: false,
        expressions: null,
        message: '未检测到人脸'
      }
    }
    
    // 获取表情数据
    const expressions = detections[0].expressions
    
    // 分析主要表情
    let dominantExpression = ''
    let maxScore = 0
    
    Object.entries(expressions).forEach(([expression, score]) => {
      if (score > maxScore) {
        maxScore = score
        dominantExpression = expression
      }
    })
    
    // 计算微笑程度
    const smileScore = expressions.happy || 0
    
    // 计算表情丰富度 (表情变化的程度)
    const expressionVariety = Object.values(expressions).reduce((sum, score) => sum + (score > 0.1 ? 1 : 0), 0)
    
    return {
      detected: true,
      expressions,
      dominantExpression,
      smileScore,
      expressionVariety,
      message: '检测成功'
    }
  } catch (error) {
    console.error('分析面部表情失败:', error)
    return {
      detected: false,
      expressions: null,
      message: '分析面部表情失败'
    }
  }
}

// 分析光线条件
export const analyzeLighting = async (video: HTMLVideoElement) => {
  if (!video) return null
  
  try {
    // 创建canvas来分析视频帧
    const canvas = document.createElement('canvas')
    canvas.width = video.videoWidth || video.width || 640
    canvas.height = video.videoHeight || video.height || 480
    const ctx = canvas.getContext('2d')
    
    if (!ctx) return null
    
    // 绘制当前视频帧到canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
    
    // 获取图像数据
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
    const data = imageData.data
    
    // 计算亮度
    let totalBrightness = 0
    let pixelCount = 0
    
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i]
      const g = data[i + 1]
      const b = data[i + 2]
      
      // 计算像素亮度 (使用相对亮度公式)
      const brightness = 0.2126 * r + 0.7152 * g + 0.0722 * b
      totalBrightness += brightness
      pixelCount++
    }
    
    const averageBrightness = totalBrightness / pixelCount
    
    // 分析亮度是否合适
    let lightingQuality = 'good'
    let lightingScore = 100
    
    if (averageBrightness < 40) {
      lightingQuality = 'too_dark'
      lightingScore = Math.round((averageBrightness / 40) * 100)
    } else if (averageBrightness > 220) {
      lightingQuality = 'too_bright'
      lightingScore = Math.round(((255 - averageBrightness) / 35) * 100)
    } else {
      // 理想亮度范围为80-180
      if (averageBrightness < 80) {
        lightingScore = 70 + Math.round((averageBrightness - 40) / 40 * 30)
      } else if (averageBrightness > 180) {
        lightingScore = 70 + Math.round((220 - averageBrightness) / 40 * 30)
      } else {
        lightingScore = 100
      }
    }
    
    return {
      averageBrightness,
      lightingQuality,
      lightingScore,
      message: `光线质量: ${lightingQuality}, 得分: ${lightingScore}`
    }
  } catch (error) {
    console.error('分析光线条件失败:', error)
    return null
  }
}

// 检测人脸并绘制到canvas上
export const detectAndDrawFace = async (video: HTMLVideoElement, canvas: HTMLCanvasElement) => {
  if (!video || !canvas) return
  
  // 确保模型已加载
  if (!modelsLoaded) {
    console.warn('模型尚未加载，无法检测人脸')
    return
  }
  
  try {
    const displaySize = { 
      width: video.videoWidth || video.width || 640, 
      height: video.videoHeight || video.height || 480 
    }
    faceapi.matchDimensions(canvas, displaySize)
    
    const detections = await faceapi
      .detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
      .withFaceLandmarks()
      .withFaceExpressions()
    
    const resizedDetections = faceapi.resizeResults(detections, displaySize)
    
    const ctx = canvas.getContext('2d')
    if (ctx) {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      faceapi.draw.drawDetections(canvas, resizedDetections)
      faceapi.draw.drawFaceLandmarks(canvas, resizedDetections)
      faceapi.draw.drawFaceExpressions(canvas, resizedDetections)
    }
    
    return detections
  } catch (error) {
    console.error('检测并绘制人脸失败:', error)
    return null
  }
} 