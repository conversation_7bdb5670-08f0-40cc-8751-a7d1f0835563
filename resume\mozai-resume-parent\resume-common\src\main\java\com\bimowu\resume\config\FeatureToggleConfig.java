package com.bimowu.resume.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 功能开关配置
 * 用于控制新旧PDF生成系统的切换
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "feature.toggle")
public class FeatureToggleConfig {
    
    /**
     * PDF生成功能开关
     */
    private PdfFeatures pdf = new PdfFeatures();
    
    /**
     * A/B测试配置
     */
    private AbTestConfig abTest = new AbTestConfig();
    
    @Data
    public static class PdfFeatures {
        /**
         * 是否启用新的Flying Saucer PDF生成器
         */
        private boolean enableFlyingSaucer = true;
        
        /**
         * 是否启用样式同步功能
         */
        private boolean enableStyleSync = true;
        
        /**
         * 是否启用样式验证
         */
        private boolean enableStyleValidation = false;
        
        /**
         * 是否启用错误恢复机制
         */
        private boolean enableErrorRecovery = true;
        
        /**
         * 是否启用性能监控
         */
        private boolean enablePerformanceMonitoring = true;
        
        /**
         * 降级阈值：连续失败次数超过此值时降级到旧系统
         */
        private int fallbackThreshold = 3;
        
        /**
         * 降级时间窗口（分钟）
         */
        private int fallbackWindowMinutes = 10;
    }
    
    @Data
    public static class AbTestConfig {
        /**
         * 是否启用A/B测试
         */
        private boolean enabled = false;
        
        /**
         * 新系统流量比例（0-100）
         */
        private int newSystemTrafficPercent = 50;
        
        /**
         * 测试用户ID列表（强制使用新系统）
         */
        private String testUserIds = "";
        
        /**
         * 白名单用户ID列表（强制使用旧系统）
         */
        private String whitelistUserIds = "";
    }
}