import{P as a}from"./index-d8f61d92.js";const u={getResumeCategoryList(){return a.get("/resume/categoryList")},getInterviewQuestions(e){return a.get(`/interviews/${e}/questions`)},analyzeResume(e){const t=new FormData;return t.append("file",e),a.post("/resume/analyze",t,{headers:{"Content-Type":"multipart/form-data"},timeout:12e4})},createInterview(e){const t={...e,type:e.type||"mock"};return a.post("/interviews",t)},generateQuestions(e){return a.post("/interview/questions",{resumeText:e},{timeout:12e4})},getDigitalHumanModels(){return a.get("/digital-human/models",{timeout:12e4})},analyzeInterview(e){const t=new FormData;return t.append("video",e.video),t.append("audio",e.audio),t.append("questions",JSON.stringify(e.questions)),t.append("answers",JSON.stringify(e.answers)),a.post("/interview/analyze",t,{headers:{"Content-Type":"multipart/form-data"},timeout:12e4})},speechToText(e){const t=new FormData;return t.append("audio",e),a.post("/speech-to-text",t,{headers:{"Content-Type":"multipart/form-data"},timeout:12e4})},transcribeInterview(e,t,r,o){const i=["audio/wav","audio/mpeg","audio/mp4","audio/x-m4a","audio/wma","audio/aac","audio/ogg","audio/amr","audio/flac"];!i.includes(e.type)&&!i.some(s=>e.name.endsWith(s.split("/")[1]))&&console.warn(`警告: 文件格式 ${e.type} 可能不被支持，文件名: ${e.name}`);const n=new FormData;return n.append("audio",e),n.append("questionIndex",t.toString()),n.append("interviewId",r),o&&n.append("question",o),a.post("/speechToText/transcribe",n,{headers:{"Content-Type":"multipart/form-data"},timeout:12e4})},getInterviewTranscriptions(e){return a.get(`/interview/transcriptions/${e}`,{timeout:12e4})},uploadInterviewVideo(e,t){const r=new FormData;return r.append("video",t),a.post(`/interviews/${e}/video`,r,{headers:{"Content-Type":"multipart/form-data"},timeout:3e5})},updateInterviewVideoUrl(e,t){return a.post(`/interviews/${e}/video-url`,{videoUrl:t})},analyzeFacialExpressions(e){const t=new FormData;return t.append("video",e),a.post("/analyze/expressions",t,{headers:{"Content-Type":"multipart/form-data"},timeout:12e4})},analyzeLighting(e){const t=new FormData;return t.append("video",e),a.post("/analyze/lighting",t,{headers:{"Content-Type":"multipart/form-data"},timeout:12e4})},analyzeNoise(e){const t=new FormData;return t.append("audio",e),a.post("/analyze/noise",t,{headers:{"Content-Type":"multipart/form-data"},timeout:12e4})},updateInterviewStatus(e){return a.post(`/interviews/${e}/status`)},getInterviewDetail(e){return a.get(`/interviews/${e}`)}};export{u as a};
