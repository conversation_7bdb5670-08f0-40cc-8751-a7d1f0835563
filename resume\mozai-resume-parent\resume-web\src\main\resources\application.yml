server:
  tomcat:
    max-connections: 1000
    max-threads: 200
  servlet:
    context-path: /resume

spring:
  profiles:
    active: pred
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER

#mybatis:
#  mapper-locations: classpath*:com/bimowu/resume/common/dao/mappers/*.xml
#  type-aliases-package: com.bimowu.resume.common.model
oss:
  endpoint: oss-cn-beijing.aliyuncs.com
  accessKeyId: LTAI5tLmkL7ebnMBvdmZ1eF7
  accessKeySecret: ******************************
  bucketName: bmw-pic

mybatis-plus:
  mapper-locations: classpath:/mappers/**/*.xml
  type-aliases-package: com.bimowu.resume.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: is_delete # 全局逻辑删除字段名
      logic-delete-value: 1 # 逻辑已删除值
      logic-not-delete-value: 0 # 逻辑未删除值

logging:
  level:
    com.bimowu.interview: debug
# AI模型配置
ai:
  # 替换为自己的API密钥
  api-key: 064be122-b45a-463b-95a3-90211243de81
  model-name: deepseek-v3-250324
  # AI使用次数上限配置
  usage-limit:
    default: 10  # 默认每个用户可使用的AI次数
# 阿里云配置
aliyun:
  accessKey: LTAI5tQ2bXYzSNY9DK2nkVRo
  accessKeySecret: ******************************
  region: cn-shanghai     # 修改为上海区域，与语音识别服务一致
  appKey: d31AwPkbLMQfER7z
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com    # 修改为与region一致的端点
    accessKeyId: LTAI5tEm5vMpTW7AB2BjHtYD
    accessKeySecret: ******************************
    bucketName: bmw-pic

# PDF生成配置
pdf:
  generation:
    engine: flying-saucer
    timeout-seconds: 30
    max-concurrent: 10
    page:
      size: A4
      orientation: portrait
      dpi: 300
      margin:
        top: 20
        bottom: 20
        left: 20
        right: 20
    cache:
      enabled: true
      ttl: 3600
      max-size: 1000
      type: memory
    performance:
      pool-core-size: 5
      pool-max-size: 10
      pool-idle-timeout: 60
      async-enabled: false
      memory-limit-mb: 512
    template:
      base-path: templates/pdf
      sync-interval: 3600
      validation-enabled: true
  fonts:
    default-family: SimSun
    fonts:
      - name: SimSun
        path: fonts/simsun.ttf
        family: SimSun
        style: normal
        weight: 400
        is-default: true
        embedded: true
      - name: Microsoft YaHei
        path: fonts/simhei.ttf
        family: Microsoft YaHei
        style: normal
        weight: 400
        embedded: true
      - name: SimHei
        path: fonts/simhei.ttf
        family: SimHei
        style: normal
        weight: 700
        embedded: true
      - name: Arial
        path: fonts/simsun.ttf
        family: Arial
        style: normal
        weight: 400
        embedded: true
    aliases:
      Song: SimSun
      MicrosoftYaHei: Microsoft YaHei
      SimHei: SimHei        e
      mbedded: true
      宋体: SimSun
      微软雅黑: Microsoft YaHei
      黑体: SimHei

# 功能开关配置
feature:
  toggle:
    pdf:
      enable-flying-saucer: true
      enable-style-sync: true
      enable-style-validation: false
      enable-error-recovery: true
      enable-performance-monitoring: true
      fallback-threshold: 3
      fallback-window-minutes: 10
    ab-test:
      enabled: false
      new-system-traffic-percent: 50
      test-user-ids: ""
      whitelist-user-ids: ""