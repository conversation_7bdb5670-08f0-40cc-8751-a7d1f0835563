declare const _default: import("vue").DefineComponent<{
    readonly alwaysOn: BooleanConstructor;
    readonly fullTransition: BooleanConstructor;
    readonly transitionProps: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null) | ((new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null))[], unknown, unknown, null, boolean>;
    readonly teleported: BooleanConstructor;
    readonly to: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined) | ((new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined))[], unknown, unknown, "body", boolean>;
    readonly ariaLabel: StringConstructor;
    readonly arrowPadding: import("../../../utils").EpPropFinalized<(new (...args: any[]) => number & {}) | (() => number) | ((new (...args: any[]) => number & {}) | (() => number))[], unknown, unknown, 5, boolean>;
    readonly effect: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly contentClass: StringConstructor;
    readonly placement: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement) | ((new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement))[], "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end", unknown, "bottom", boolean>;
    readonly reference: import("../../../utils").EpPropFinalized<(new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null) | ((new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null))[], unknown, unknown, null, boolean>;
    readonly offset: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 8, boolean>;
    readonly strategy: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy) | ((new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy))[], "fixed" | "absolute", unknown, "absolute", boolean>;
    readonly showArrow: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly onBlur: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onClick: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onFocus: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onMouseDown: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onMouseEnter: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onMouseLeave: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
    readonly height: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
    readonly style: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null) | ((new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null))[], unknown, unknown, null, boolean>;
    readonly delayDuration: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 300, boolean>;
    readonly defaultOpen: BooleanConstructor;
    readonly open: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
    readonly onOpenChange: {
        readonly type: import("vue").PropType<(state: boolean) => void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly 'onUpdate:open': {
        readonly type: import("vue").PropType<(state: boolean) => void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}, {
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly alwaysOn: BooleanConstructor;
        readonly fullTransition: BooleanConstructor;
        readonly transitionProps: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null) | ((new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null))[], unknown, unknown, null, boolean>;
        readonly teleported: BooleanConstructor;
        readonly to: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined) | ((new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined))[], unknown, unknown, "body", boolean>;
        readonly ariaLabel: StringConstructor;
        readonly arrowPadding: import("../../../utils").EpPropFinalized<(new (...args: any[]) => number & {}) | (() => number) | ((new (...args: any[]) => number & {}) | (() => number))[], unknown, unknown, 5, boolean>;
        readonly effect: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly contentClass: StringConstructor;
        readonly placement: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement) | ((new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement))[], "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end", unknown, "bottom", boolean>;
        readonly reference: import("../../../utils").EpPropFinalized<(new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null) | ((new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null))[], unknown, unknown, null, boolean>;
        readonly offset: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 8, boolean>;
        readonly strategy: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy) | ((new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy))[], "fixed" | "absolute", unknown, "absolute", boolean>;
        readonly showArrow: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly onBlur: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly onClick: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly onFocus: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly onMouseDown: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly onMouseEnter: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly onMouseLeave: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
        readonly height: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
        readonly style: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null) | ((new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null))[], unknown, unknown, null, boolean>;
        readonly delayDuration: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 300, boolean>;
        readonly defaultOpen: BooleanConstructor;
        readonly open: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
        readonly onOpenChange: {
            readonly type: import("vue").PropType<(state: boolean) => void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly 'onUpdate:open': {
            readonly type: import("vue").PropType<(state: boolean) => void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }>> & {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
    }>>;
    refedProps: import("vue").ToRefs<Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly alwaysOn: BooleanConstructor;
        readonly fullTransition: BooleanConstructor;
        readonly transitionProps: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null) | ((new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null))[], unknown, unknown, null, boolean>;
        readonly teleported: BooleanConstructor;
        readonly to: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined) | ((new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined))[], unknown, unknown, "body", boolean>;
        readonly ariaLabel: StringConstructor;
        readonly arrowPadding: import("../../../utils").EpPropFinalized<(new (...args: any[]) => number & {}) | (() => number) | ((new (...args: any[]) => number & {}) | (() => number))[], unknown, unknown, 5, boolean>;
        readonly effect: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly contentClass: StringConstructor;
        readonly placement: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement) | ((new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement))[], "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end", unknown, "bottom", boolean>;
        readonly reference: import("../../../utils").EpPropFinalized<(new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null) | ((new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null))[], unknown, unknown, null, boolean>;
        readonly offset: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 8, boolean>;
        readonly strategy: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy) | ((new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy))[], "fixed" | "absolute", unknown, "absolute", boolean>;
        readonly showArrow: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly onBlur: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly onClick: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly onFocus: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly onMouseDown: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly onMouseEnter: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly onMouseLeave: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
        readonly height: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
        readonly style: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null) | ((new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null))[], unknown, unknown, null, boolean>;
        readonly delayDuration: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 300, boolean>;
        readonly defaultOpen: BooleanConstructor;
        readonly open: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
        readonly onOpenChange: {
            readonly type: import("vue").PropType<(state: boolean) => void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly 'onUpdate:open': {
            readonly type: import("vue").PropType<(state: boolean) => void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }>> & {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
    }>>>;
    arrowProps: {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        readonly offset?: number | undefined;
        readonly height?: number | undefined;
        readonly width?: number | undefined;
        readonly effect?: string | undefined;
        readonly placement?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement) | ((new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement))[], "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end", unknown> | undefined;
        readonly strategy?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy) | ((new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy))[], "fixed" | "absolute", unknown> | undefined;
        readonly style?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null) | ((new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null))[], unknown, unknown> | undefined;
        readonly ariaLabel?: string | undefined;
        readonly teleported?: boolean | undefined;
        readonly onFocus?: ((e: Event) => boolean | void) | undefined;
        readonly onBlur?: ((e: Event) => boolean | void) | undefined;
        readonly open?: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown> | undefined;
        readonly onClick?: ((e: Event) => boolean | void) | undefined;
        readonly showArrow?: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown> | undefined;
        readonly contentClass?: string | undefined;
        readonly to?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined) | ((new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined))[], unknown, unknown>;
        readonly reference?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null) | ((new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null))[], unknown, unknown> | undefined;
        readonly alwaysOn?: boolean | undefined;
        readonly delayDuration?: number | undefined;
        readonly onOpenChange?: ((state: boolean) => void) | undefined;
        readonly "onUpdate:open"?: ((state: boolean) => void) | undefined;
        readonly defaultOpen?: boolean | undefined;
        readonly onMouseDown?: ((e: Event) => boolean | void) | undefined;
        readonly onMouseEnter?: ((e: Event) => boolean | void) | undefined;
        readonly onMouseLeave?: ((e: Event) => boolean | void) | undefined;
        readonly arrowPadding?: number | undefined;
        readonly transitionProps?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null) | ((new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null))[], unknown, unknown> | undefined;
        readonly fullTransition?: boolean | undefined;
    };
    contentProps: {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        readonly offset?: number | undefined;
        readonly height?: number | undefined;
        readonly width?: number | undefined;
        readonly effect?: string | undefined;
        readonly placement?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement) | ((new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement))[], "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end", unknown> | undefined;
        readonly strategy?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy) | ((new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy))[], "fixed" | "absolute", unknown> | undefined;
        readonly style?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null) | ((new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null))[], unknown, unknown> | undefined;
        readonly ariaLabel?: string | undefined;
        readonly teleported?: boolean | undefined;
        readonly onFocus?: ((e: Event) => boolean | void) | undefined;
        readonly onBlur?: ((e: Event) => boolean | void) | undefined;
        readonly open?: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown> | undefined;
        readonly onClick?: ((e: Event) => boolean | void) | undefined;
        readonly showArrow?: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown> | undefined;
        readonly contentClass?: string | undefined;
        readonly to?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined) | ((new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined))[], unknown, unknown>;
        readonly reference?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null) | ((new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null))[], unknown, unknown> | undefined;
        readonly alwaysOn?: boolean | undefined;
        readonly delayDuration?: number | undefined;
        readonly onOpenChange?: ((state: boolean) => void) | undefined;
        readonly "onUpdate:open"?: ((state: boolean) => void) | undefined;
        readonly defaultOpen?: boolean | undefined;
        readonly onMouseDown?: ((e: Event) => boolean | void) | undefined;
        readonly onMouseEnter?: ((e: Event) => boolean | void) | undefined;
        readonly onMouseLeave?: ((e: Event) => boolean | void) | undefined;
        readonly arrowPadding?: number | undefined;
        readonly transitionProps?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null) | ((new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null))[], unknown, unknown> | undefined;
        readonly fullTransition?: boolean | undefined;
    };
    rootProps: {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        readonly offset?: number | undefined;
        readonly height?: number | undefined;
        readonly width?: number | undefined;
        readonly effect?: string | undefined;
        readonly placement?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement) | ((new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement))[], "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end", unknown> | undefined;
        readonly strategy?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy) | ((new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy))[], "fixed" | "absolute", unknown> | undefined;
        readonly style?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null) | ((new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null))[], unknown, unknown> | undefined;
        readonly ariaLabel?: string | undefined;
        readonly teleported?: boolean | undefined;
        readonly onFocus?: ((e: Event) => boolean | void) | undefined;
        readonly onBlur?: ((e: Event) => boolean | void) | undefined;
        readonly open?: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown> | undefined;
        readonly onClick?: ((e: Event) => boolean | void) | undefined;
        readonly showArrow?: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown> | undefined;
        readonly contentClass?: string | undefined;
        readonly to?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined) | ((new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined))[], unknown, unknown>;
        readonly reference?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null) | ((new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null))[], unknown, unknown> | undefined;
        readonly alwaysOn?: boolean | undefined;
        readonly delayDuration?: number | undefined;
        readonly onOpenChange?: ((state: boolean) => void) | undefined;
        readonly "onUpdate:open"?: ((state: boolean) => void) | undefined;
        readonly defaultOpen?: boolean | undefined;
        readonly onMouseDown?: ((e: Event) => boolean | void) | undefined;
        readonly onMouseEnter?: ((e: Event) => boolean | void) | undefined;
        readonly onMouseLeave?: ((e: Event) => boolean | void) | undefined;
        readonly arrowPadding?: number | undefined;
        readonly transitionProps?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null) | ((new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null))[], unknown, unknown> | undefined;
        readonly fullTransition?: boolean | undefined;
    };
    triggerProps: {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        readonly offset?: number | undefined;
        readonly height?: number | undefined;
        readonly width?: number | undefined;
        readonly effect?: string | undefined;
        readonly placement?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement) | ((new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement))[], "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end", unknown> | undefined;
        readonly strategy?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy) | ((new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy))[], "fixed" | "absolute", unknown> | undefined;
        readonly style?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null) | ((new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null))[], unknown, unknown> | undefined;
        readonly ariaLabel?: string | undefined;
        readonly teleported?: boolean | undefined;
        readonly onFocus?: ((e: Event) => boolean | void) | undefined;
        readonly onBlur?: ((e: Event) => boolean | void) | undefined;
        readonly open?: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown> | undefined;
        readonly onClick?: ((e: Event) => boolean | void) | undefined;
        readonly showArrow?: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown> | undefined;
        readonly contentClass?: string | undefined;
        readonly to?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined) | ((new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined))[], unknown, unknown>;
        readonly reference?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null) | ((new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null))[], unknown, unknown> | undefined;
        readonly alwaysOn?: boolean | undefined;
        readonly delayDuration?: number | undefined;
        readonly onOpenChange?: ((state: boolean) => void) | undefined;
        readonly "onUpdate:open"?: ((state: boolean) => void) | undefined;
        readonly defaultOpen?: boolean | undefined;
        readonly onMouseDown?: ((e: Event) => boolean | void) | undefined;
        readonly onMouseEnter?: ((e: Event) => boolean | void) | undefined;
        readonly onMouseLeave?: ((e: Event) => boolean | void) | undefined;
        readonly arrowPadding?: number | undefined;
        readonly transitionProps?: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null) | ((new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null))[], unknown, unknown> | undefined;
        readonly fullTransition?: boolean | undefined;
    };
    TooltipV2Root: import("vue").DefineComponent<{
        readonly delayDuration: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 300, boolean>;
        readonly defaultOpen: BooleanConstructor;
        readonly open: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
        readonly onOpenChange: {
            readonly type: import("vue").PropType<(state: boolean) => void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly 'onUpdate:open': {
            readonly type: import("vue").PropType<(state: boolean) => void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly delayDuration: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 300, boolean>;
            readonly defaultOpen: BooleanConstructor;
            readonly open: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
            readonly onOpenChange: {
                readonly type: import("vue").PropType<(state: boolean) => void>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly 'onUpdate:open': {
                readonly type: import("vue").PropType<(state: boolean) => void>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        _open: import("vue").Ref<boolean>;
        triggerRef: import("vue").Ref<HTMLElement | null>;
        open: import("vue").WritableComputedRef<boolean>;
        isOpenDelayed: import("vue").ComputedRef<boolean>;
        onDelayedOpen: import("@vueuse/shared").Fn;
        clearTimer: import("@vueuse/shared").Fn;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        contentId: import("vue").Ref<string>;
        onNormalOpen: () => void;
        onDelayOpen: () => void;
        onOpen: () => void;
        onClose: () => void;
        onChange: (open: boolean) => void;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly delayDuration: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 300, boolean>;
        readonly defaultOpen: BooleanConstructor;
        readonly open: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
        readonly onOpenChange: {
            readonly type: import("vue").PropType<(state: boolean) => void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly 'onUpdate:open': {
            readonly type: import("vue").PropType<(state: boolean) => void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }>>, {
        readonly open: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly delayDuration: number;
        readonly defaultOpen: boolean;
    }>;
    TooltipV2Arrow: import("vue").DefineComponent<{
        side: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("./common").TooltipV2Sides & {}) | (() => import("./common").TooltipV2Sides) | ((new (...args: any[]) => import("./common").TooltipV2Sides & {}) | (() => import("./common").TooltipV2Sides))[], import("./common").TooltipV2Sides, unknown>>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
        height: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
        style: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null) | ((new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null))[], unknown, unknown, null, boolean>;
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            side: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("./common").TooltipV2Sides & {}) | (() => import("./common").TooltipV2Sides) | ((new (...args: any[]) => import("./common").TooltipV2Sides & {}) | (() => import("./common").TooltipV2Sides))[], import("./common").TooltipV2Sides, unknown>>;
                readonly required: true;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
            height: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
            style: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null) | ((new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null))[], unknown, unknown, null, boolean>;
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        arrowRef: import("vue").Ref<HTMLElement | null>;
        arrowStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        side: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("./common").TooltipV2Sides & {}) | (() => import("./common").TooltipV2Sides) | ((new (...args: any[]) => import("./common").TooltipV2Sides & {}) | (() => import("./common").TooltipV2Sides))[], import("./common").TooltipV2Sides, unknown>>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
        height: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
        style: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null) | ((new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null))[], unknown, unknown, null, boolean>;
    }>>, {
        height: number;
        width: number;
        style: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null) | ((new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null))[], unknown, unknown>;
    }>;
    TooltipV2Content: import("vue").DefineComponent<{
        nowrap: BooleanConstructor;
        ariaLabel: StringConstructor;
        arrowPadding: import("../../../utils").EpPropFinalized<(new (...args: any[]) => number & {}) | (() => number) | ((new (...args: any[]) => number & {}) | (() => number))[], unknown, unknown, 5, boolean>;
        effect: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        contentClass: StringConstructor;
        placement: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement) | ((new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement))[], "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end", unknown, "bottom", boolean>;
        reference: import("../../../utils").EpPropFinalized<(new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null) | ((new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null))[], unknown, unknown, null, boolean>;
        offset: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 8, boolean>;
        strategy: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy) | ((new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy))[], "fixed" | "absolute", unknown, "absolute", boolean>;
        showArrow: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            nowrap: BooleanConstructor;
            ariaLabel: StringConstructor;
            arrowPadding: import("../../../utils").EpPropFinalized<(new (...args: any[]) => number & {}) | (() => number) | ((new (...args: any[]) => number & {}) | (() => number))[], unknown, unknown, 5, boolean>;
            effect: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            contentClass: StringConstructor;
            placement: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement) | ((new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement))[], "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end", unknown, "bottom", boolean>;
            reference: import("../../../utils").EpPropFinalized<(new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null) | ((new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null))[], unknown, unknown, null, boolean>;
            offset: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 8, boolean>;
            strategy: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy) | ((new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy))[], "fixed" | "absolute", unknown, "absolute", boolean>;
            showArrow: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        triggerRef: import("vue").Ref<HTMLElement | null>;
        contentId: import("vue").Ref<string>;
        placement: import("vue").Ref<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement) | ((new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement))[], "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end", unknown>>;
        strategy: import("vue").Ref<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy) | ((new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy))[], "fixed" | "absolute", unknown>>;
        arrowRef: import("vue").Ref<HTMLElement | null>;
        referenceRef: import("vue").Ref<HTMLElement | import("@floating-ui/dom").VirtualElement | undefined>;
        contentRef: import("vue").Ref<HTMLElement | undefined>;
        middlewareData: import("vue").Ref<{
            [x: string]: any;
            arrow?: {
                x?: number | undefined;
                y?: number | undefined;
                centerOffset: number;
            } | undefined;
            autoPlacement?: {
                index?: number | undefined;
                overflows: {
                    placement: import("@floating-ui/core").Placement;
                    overflows: number[];
                }[];
            } | undefined;
            flip?: {
                index?: number | undefined;
                overflows: {
                    placement: import("@floating-ui/core").Placement;
                    overflows: number[];
                }[];
            } | undefined;
            hide?: {
                referenceHidden?: boolean | undefined;
                escaped?: boolean | undefined;
                referenceHiddenOffsets?: {
                    top: number;
                    bottom: number;
                    right: number;
                    left: number;
                } | undefined;
                escapedOffsets?: {
                    top: number;
                    bottom: number;
                    right: number;
                    left: number;
                } | undefined;
            } | undefined;
            offset?: {
                x: number;
                y: number;
            } | undefined;
            shift?: {
                x: number;
                y: number;
            } | undefined;
        }>;
        x: import("vue").Ref<number | undefined>;
        y: import("vue").Ref<number | undefined>;
        update: () => Promise<void>;
        zIndex: number;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        side: import("vue").ComputedRef<string>;
        contentStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
        arrowStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
        contentClass: import("vue").ComputedRef<(string | undefined)[]>;
        ElVisuallyHidden: import("vue").DefineComponent<{
            readonly style: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("vue").StyleValue & {}) | (() => import("vue").StyleValue) | ((new (...args: any[]) => import("vue").StyleValue & {}) | (() => import("vue").StyleValue))[], unknown, unknown, () => {}, boolean>;
        }, {
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                readonly style: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("vue").StyleValue & {}) | (() => import("vue").StyleValue) | ((new (...args: any[]) => import("vue").StyleValue & {}) | (() => import("vue").StyleValue))[], unknown, unknown, () => {}, boolean>;
            }>> & {
                [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
            }>>;
            computedStyle: import("vue").ComputedRef<import("vue").StyleValue>;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly style: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("vue").StyleValue & {}) | (() => import("vue").StyleValue) | ((new (...args: any[]) => import("vue").StyleValue & {}) | (() => import("vue").StyleValue))[], unknown, unknown, () => {}, boolean>;
        }>>, {
            readonly style: import("vue").StyleValue;
        }>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        nowrap: BooleanConstructor;
        ariaLabel: StringConstructor;
        arrowPadding: import("../../../utils").EpPropFinalized<(new (...args: any[]) => number & {}) | (() => number) | ((new (...args: any[]) => number & {}) | (() => number))[], unknown, unknown, 5, boolean>;
        effect: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        contentClass: StringConstructor;
        placement: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement) | ((new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement))[], "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end", unknown, "bottom", boolean>;
        reference: import("../../../utils").EpPropFinalized<(new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null) | ((new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null))[], unknown, unknown, null, boolean>;
        offset: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 8, boolean>;
        strategy: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy) | ((new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy))[], "fixed" | "absolute", unknown, "absolute", boolean>;
        showArrow: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    }>>, {
        offset: number;
        effect: string;
        placement: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement) | ((new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement))[], "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end", unknown>;
        strategy: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy) | ((new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy))[], "fixed" | "absolute", unknown>;
        nowrap: boolean;
        showArrow: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        reference: import("../../../utils").EpPropMergeType<(new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null) | ((new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null))[], unknown, unknown>;
        arrowPadding: number;
    }>;
    TooltipV2Trigger: import("vue").DefineComponent<{
        onBlur: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onClick: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onFocus: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onMouseDown: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onMouseEnter: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onMouseLeave: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        nowrap: BooleanConstructor;
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            onBlur: {
                readonly type: import("vue").PropType<(e: Event) => boolean | void>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            onClick: {
                readonly type: import("vue").PropType<(e: Event) => boolean | void>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            onFocus: {
                readonly type: import("vue").PropType<(e: Event) => boolean | void>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            onMouseDown: {
                readonly type: import("vue").PropType<(e: Event) => boolean | void>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            onMouseEnter: {
                readonly type: import("vue").PropType<(e: Event) => boolean | void>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            onMouseLeave: {
                readonly type: import("vue").PropType<(e: Event) => boolean | void>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            nowrap: BooleanConstructor;
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        onClose: () => void;
        onOpen: () => void;
        onDelayOpen: () => void;
        triggerRef: import("vue").Ref<HTMLElement | null>;
        contentId: import("vue").Ref<string>;
        isMousedown: boolean;
        setTriggerRef: (el: HTMLElement | null) => void;
        onMouseup: () => void;
        onMouseenter: (event: Event) => void;
        onMouseleave: (event: Event) => void;
        onMousedown: (event: Event) => void;
        onFocus: (event: Event) => void;
        onBlur: (event: Event) => void;
        onClick: (event: Event) => void;
        events: {
            blur: (event: Event) => void;
            click: (event: Event) => void;
            focus: (event: Event) => void;
            mousedown: (event: Event) => void;
            mouseenter: (event: Event) => void;
            mouseleave: (event: Event) => void;
        };
        setEvents: <T extends (e: Event) => void>(el: HTMLElement | null | undefined, events: Record<string, T>, type: "addEventListener" | "removeEventListener") => void;
        ForwardRef: import("vue").DefineComponent<{
            readonly setRef: {
                readonly type: import("vue").PropType<import("./forward-ref").RefSetter>;
                readonly required: true;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly onlyChild: BooleanConstructor;
        }, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly setRef: {
                readonly type: import("vue").PropType<import("./forward-ref").RefSetter>;
                readonly required: true;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly onlyChild: BooleanConstructor;
        }>>, {
            readonly onlyChild: boolean;
        }>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        onBlur: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onClick: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onFocus: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onMouseDown: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onMouseEnter: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onMouseLeave: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        nowrap: BooleanConstructor;
    }>>, {
        nowrap: boolean;
    }>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly alwaysOn: BooleanConstructor;
    readonly fullTransition: BooleanConstructor;
    readonly transitionProps: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null) | ((new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null))[], unknown, unknown, null, boolean>;
    readonly teleported: BooleanConstructor;
    readonly to: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined) | ((new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined))[], unknown, unknown, "body", boolean>;
    readonly ariaLabel: StringConstructor;
    readonly arrowPadding: import("../../../utils").EpPropFinalized<(new (...args: any[]) => number & {}) | (() => number) | ((new (...args: any[]) => number & {}) | (() => number))[], unknown, unknown, 5, boolean>;
    readonly effect: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly contentClass: StringConstructor;
    readonly placement: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement) | ((new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement))[], "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end", unknown, "bottom", boolean>;
    readonly reference: import("../../../utils").EpPropFinalized<(new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null) | ((new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null))[], unknown, unknown, null, boolean>;
    readonly offset: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 8, boolean>;
    readonly strategy: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy) | ((new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy))[], "fixed" | "absolute", unknown, "absolute", boolean>;
    readonly showArrow: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly onBlur: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onClick: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onFocus: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onMouseDown: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onMouseEnter: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onMouseLeave: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
    readonly height: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
    readonly style: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null) | ((new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null))[], unknown, unknown, null, boolean>;
    readonly delayDuration: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 300, boolean>;
    readonly defaultOpen: BooleanConstructor;
    readonly open: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
    readonly onOpenChange: {
        readonly type: import("vue").PropType<(state: boolean) => void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly 'onUpdate:open': {
        readonly type: import("vue").PropType<(state: boolean) => void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}>>, {
    readonly offset: number;
    readonly height: number;
    readonly width: number;
    readonly effect: string;
    readonly placement: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement) | ((new (...args: any[]) => import("@floating-ui/core").Placement & {}) | (() => import("@floating-ui/core").Placement))[], "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end", unknown>;
    readonly strategy: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy) | ((new (...args: any[]) => import("@floating-ui/core").Strategy & {}) | (() => import("@floating-ui/core").Strategy))[], "fixed" | "absolute", unknown>;
    readonly style: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null) | ((new (...args: any[]) => import("vue").CSSProperties) | (() => import("vue").CSSProperties | null))[], unknown, unknown>;
    readonly teleported: boolean;
    readonly open: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly showArrow: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly to: import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined) | ((new (...args: any[]) => (string | import("vue").RendererElement | null | undefined) & {}) | (() => string | import("vue").RendererElement | null | undefined))[], unknown, unknown>;
    readonly reference: import("../../../utils").EpPropMergeType<(new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null) | ((new (...args: any[]) => HTMLElement | import("@floating-ui/dom").VirtualElement) | (() => HTMLElement | import("@floating-ui/dom").VirtualElement | null))[], unknown, unknown>;
    readonly alwaysOn: boolean;
    readonly delayDuration: number;
    readonly defaultOpen: boolean;
    readonly arrowPadding: number;
    readonly transitionProps: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null) | ((new (...args: any[]) => import("vue").TransitionProps) | (() => import("vue").TransitionProps | null))[], unknown, unknown>;
    readonly fullTransition: boolean;
}>;
export default _default;
