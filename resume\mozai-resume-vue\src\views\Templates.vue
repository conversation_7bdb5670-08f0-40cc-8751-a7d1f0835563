<template>
  <MainLayout>
    <div class="templates-page">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">简历模板</h1>
          <p class="page-subtitle">精选各行业简历模板，助您打造专业简历</p>
        </div>

        <div class="filter-section">
          <div class="filter-tabs">
            <el-tabs v-model="activeTab" @tab-change="handleTabChange">
              <el-tab-pane label="全部模板" name="all"></el-tab-pane>
              <el-tab-pane label="热门模板" name="hot"></el-tab-pane>
            </el-tabs>
          </div>
        </div>

        <div class="templates-grid">
          <div v-for="template in filteredTemplates" :key="template.id" class="template-item">
            <div class="template-card">
              <div class="template-image">
                <img :src="template.thumbnail" :alt="template.name" />
                <div class="template-overlay">
                  <el-button
                      type="primary"
                      class="use-template-btn"
                      @click="handleUseTemplate(template.id)"
                  >
                    立即使用
                  </el-button>
                </div>
              </div>
              <div class="template-info">
                <h3>{{ template.name }}</h3>
                <div class="template-features">
                  <span>支持：</span>
                  <span class="feature-item">7种语言</span>
                  <span class="feature-item">16种颜色</span>
                  <span v-for="(feature, idx) in template.features" :key="idx" class="feature-item">
                    {{ feature }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="filteredTemplates.length === 0" class="no-results">
          <el-empty description="没有找到符合条件的模板" />
        </div>
      </div>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import MainLayout from '@/layouts/MainLayout.vue'
import { useResumeStore } from '@/stores/resume'

const router = useRouter()
const resumeStore = useResumeStore()

// 模板数据 - 只使用当前系统的模板
const templates = ref([...resumeStore.templates])

// 筛选条件
const activeTab = ref('all')

// 筛选模板
const filteredTemplates = computed(() => {
  let result = [...templates.value]

  // 标签筛选
  if (activeTab.value === 'all') {
    // 全部模板显示所有模板
  } else if (activeTab.value === 'hot') {
    // 热门模板可以根据需求进行筛选，例如按照使用频率
    // 这里暂时不做特殊处理
  }

  return result
})

// 标签切换处理
const handleTabChange = () => {
  // 标签切换时的处理逻辑（如有需要）
}

// 模板使用处理
const handleUseTemplate = (templateId) => {
  console.log('立即使用按钮被点击，模板ID：', templateId);

  // 创建新简历并保存到store
  const newResume = resumeStore.createEmptyResume(templateId);
  resumeStore.setCurrentResume(newResume);

  console.log('准备跳转到编辑器页面');
  // 直接跳转到编辑器页面 - 确保路径正确
  router.push('/editor/new');
}
</script>

<style scoped>
.templates-page {
  padding: 50px 0;
  background-color: #f8fafc;
}

.page-header {
  text-align: center;
  margin-bottom: 45px;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 12px;
  color: #303133;
  position: relative;
  display: inline-block;
}

.page-title:after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: #1e88e5;
  border-radius: 3px;
}

.page-subtitle {
  font-size: 16px;
  color: #606266;
}

.filter-section {
  margin-bottom: 35px;
  padding: 20px 25px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #eaecef;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(290px, 1fr));
  gap: 35px;
  margin-bottom: 45px;
}

.template-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
  border: 1px solid #eaecef;
}

.template-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 22px rgba(0, 0, 0, 0.12);
}

.template-image {
  position: relative;
  height: 420px;
  overflow: hidden;
  border-radius: 8px 8px 0 0;
}

.template-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.5s;
  background-color: #f8f9fa;
  padding: 10px;
}

.template-image:hover img {
  transform: scale(1.02);
}

.template-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.65);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-image:hover .template-overlay {
  opacity: 1;
}

.use-template-btn {
  padding: 12px 28px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s;
  display: inline-block;
  cursor: pointer;
  text-decoration: none;
  background-color: #1e88e5 !important;
  color: white !important;
  border: none !important;
  box-shadow: 0 2px 6px rgba(30, 136, 229, 0.4);
}

.use-template-btn:hover {
  background-color: #1976d2 !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 10px rgba(30, 136, 229, 0.5) !important;
}

.template-info {
  padding: 18px;
  border-top: 1px solid #eaecef;
}

.template-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #303133;
}

.template-features {
  font-size: 12px;
  color: #909399;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.feature-item {
  margin-right: 8px;
  color: #1e88e5;
  background: rgba(30, 136, 229, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
}

.no-results {
  margin: 50px 0;
  text-align: center;
}

@media (max-width: 768px) {
  .templates-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 25px;
  }

  .template-image {
    height: 380px;
  }
}
</style> 