<template>
  <div class="form-container">
    <div class="skills-header">
      <h3>添加技能</h3>
      <el-button type="primary" size="small" @click="showAddSkillDialog">添加技能</el-button>
    </div>

    <div v-if="formData.length === 0" class="empty-skills">
      <el-empty description="暂无技能，请点击'添加技能'按钮添加">
        <el-button type="primary" @click="showAddSkillDialog">添加技能</el-button>
      </el-empty>
    </div>

    <div v-else class="skills-list">
      <div v-for="(skill, index) in formData" :key="index" class="skill-item">
        <div class="skill-content">
          <div class="skill-name">{{ skill.name }}</div>
          <div class="skill-level">
            <el-rate
                v-model="skill.level"
                :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                :texts="levelTexts"
                show-text
            />
          </div>
        </div>
        <div class="skill-actions">
          <el-button type="primary" size="small" plain @click="editSkill(index)">编辑</el-button>
          <el-button type="success" size="small" plain @click="polishSkillDescription(skill, index)" v-if="skill.description">AI润色</el-button>
          <el-button type="danger" size="small" plain @click="removeSkill(index)">删除</el-button>
        </div>
      </div>
    </div>

    <!-- 添加/编辑技能对话框 -->
    <el-dialog
        v-model="dialogVisible"
        :title="isEditing ? '编辑技能' : '添加技能'"
        width="500px"
    >
      <el-form :model="currentSkill" label-width="100px">
        <el-form-item label="技能名称" required>
          <el-autocomplete
              v-model="currentSkill.name"
              :fetch-suggestions="querySkillSuggestions"
              placeholder="请输入或选择技能名称"
              clearable
              style="width: 100%;"
              @change="handleSkillNameChange"
          >
            <template #suffix>
              <el-tooltip content="可以输入编程语言、软件、工具或其他专业技能">
                <el-icon><question-filled /></el-icon>
              </el-tooltip>
            </template>
          </el-autocomplete>
        </el-form-item>

        <el-form-item label="熟练程度" required>
          <el-rate
              v-model="currentSkill.level"
              :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
              :texts="levelTexts"
              show-text
              @change="handleSkillLevelChange"
          />
        </el-form-item>

        <el-form-item label="技能描述">
          <el-input
              type="textarea"
              v-model="currentSkill.description"
              placeholder="描述你对该技能的掌握情况，如项目经验、使用年限等"
              :rows="3"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveSkill">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- AI润色对话框 -->
    <el-dialog
        v-model="polishDialogVisible"
        title="AI润色"
        width="600px"
        :before-close="() => { polishDialogVisible = false; polishedContent = ''; skillToPolish = null; }"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
      <el-form :model="skillToPolish" label-width="100px">
        <el-form-item label="技能名称">
          <span>{{ skillToPolish.name }}</span>
        </el-form-item>
        <el-form-item label="当前描述">
          <el-input
              type="textarea"
              v-model="skillToPolish.description"
              :rows="5"
              placeholder="请输入当前描述"
              readonly
          />
        </el-form-item>
        <el-form-item label="AI润色内容">
          <el-input
              type="textarea"
              v-model="polishedContent"
              :rows="5"
              placeholder="AI润色后的内容"
              :autosize="{ minRows: 5, maxRows: 10 }"
          />
        </el-form-item>
        <el-form-item label="润色版本">
          <el-radio-group v-model="selectedVersion">
            <el-radio :label="1">版本1</el-radio>
            <el-radio :label="2">版本2</el-radio>
            <el-radio :label="3">版本3</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="polishDialogVisible = false; polishedContent = ''; skillToPolish = null;">取消</el-button>
          <el-button type="primary" @click="applyPolishedContent" :loading="polishLoading">应用</el-button>
        </span>
      </template>
    </el-dialog>

    <div class="form-actions">
      <el-button type="primary" @click="saveForm">保存所有技能</el-button>
      <el-button @click="resetForm">重置</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getSkillSegmentList, polishSkill } from '../api/resume'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['update'])

// 熟练度文本描述
const levelTexts = ['一般', '良好', '熟练', '擅长', '精通']

// 表单数据
const formData = ref([])

// 对话框相关
const dialogVisible = ref(false)
const currentSkill = ref({ name: '', level: 3, description: '' })
const editingIndex = ref(-1)
const isEditing = ref(false)

// 添加AI润色相关
const polishDialogVisible = ref(false)
const polishLoading = ref(false)
const polishedContent = ref('')
const selectedVersion = ref(1)
const skillToPolish = ref(null)

// 技能列表建议
const skillSuggestions = [
  // 编程语言
  { value: 'JavaScript', category: '编程语言' },
  { value: 'TypeScript', category: '编程语言' },
  { value: 'HTML/CSS', category: '编程语言' },
  { value: 'Python', category: '编程语言' },
  { value: 'Java', category: '编程语言' },
  { value: 'C++', category: '编程语言' },
  { value: 'C#', category: '编程语言' },
  { value: 'PHP', category: '编程语言' },
  { value: 'Go', category: '编程语言' },
  { value: 'Ruby', category: '编程语言' },
  { value: 'Rust', category: '编程语言' },
  { value: 'Swift', category: '编程语言' },
  { value: 'Kotlin', category: '编程语言' },
  { value: 'SQL', category: '编程语言' },

  // 前端框架
  { value: 'React', category: '前端框架' },
  { value: 'Vue.js', category: '前端框架' },
  { value: 'Angular', category: '前端框架' },
  { value: 'jQuery', category: '前端框架' },
  { value: 'Bootstrap', category: '前端框架' },
  { value: 'Tailwind CSS', category: '前端框架' },
  { value: 'Sass/SCSS', category: '前端框架' },
  { value: 'Less', category: '前端框架' },

  // 后端框架
  { value: 'Node.js', category: '后端框架' },
  { value: 'Express', category: '后端框架' },
  { value: 'Django', category: '后端框架' },
  { value: 'Flask', category: '后端框架' },
  { value: 'Spring Boot', category: '后端框架' },
  { value: 'Laravel', category: '后端框架' },
  { value: 'ASP.NET Core', category: '后端框架' },

  // 数据库
  { value: 'MySQL', category: '数据库' },
  { value: 'PostgreSQL', category: '数据库' },
  { value: 'MongoDB', category: '数据库' },
  { value: 'Redis', category: '数据库' },
  { value: 'SQLite', category: '数据库' },
  { value: 'Oracle', category: '数据库' },
  { value: 'SQL Server', category: '数据库' },

  // 工具和平台
  { value: 'Git', category: '工具和平台' },
  { value: 'Docker', category: '工具和平台' },
  { value: 'Kubernetes', category: '工具和平台' },
  { value: 'AWS', category: '工具和平台' },
  { value: 'Azure', category: '工具和平台' },
  { value: 'Google Cloud', category: '工具和平台' },
  { value: 'Linux', category: '工具和平台' },
  { value: 'Webpack', category: '工具和平台' },
  { value: 'Bash/Shell', category: '工具和平台' },

  // 设计工具
  { value: 'Photoshop', category: '设计工具' },
  { value: 'Illustrator', category: '设计工具' },
  { value: 'Figma', category: '设计工具' },
  { value: 'Sketch', category: '设计工具' },
  { value: 'Adobe XD', category: '设计工具' },

  // 其他技能
  { value: '数据分析', category: '其他技能' },
  { value: '机器学习', category: '其他技能' },
  { value: '深度学习', category: '其他技能' },
  { value: 'UI/UX设计', category: '其他技能' },
  { value: '项目管理', category: '其他技能' },
  { value: '敏捷开发', category: '其他技能' },
  { value: 'DevOps', category: '其他技能' },
  { value: 'SEO', category: '其他技能' },
  { value: '自动化测试', category: '其他技能' },
  { value: '网络安全', category: '其他技能' },
  { value: '微信小程序', category: '其他技能' },
  { value: '英语', category: '其他技能' },
  { value: '日语', category: '其他技能' },
  { value: '韩语', category: '其他技能' },
]

// 初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && newVal.length) {
    formData.value = JSON.parse(JSON.stringify(newVal))
  } else {
    formData.value = []
  }
}, { immediate: true, deep: true })

// 技能搜索建议
const querySkillSuggestions = (queryString, cb) => {
  const results = queryString
      ? skillSuggestions.filter(skill =>
          skill.value.toLowerCase().includes(queryString.toLowerCase())
      )
      : skillSuggestions

  // 按类别分组
  const groupedResults = {}
  results.forEach(skill => {
    if (!groupedResults[skill.category]) {
      groupedResults[skill.category] = []
    }
    groupedResults[skill.category].push(skill)
  })

  // 转换为展示格式
  const suggestions = []
  Object.keys(groupedResults).forEach(category => {
    suggestions.push({
      value: '',
      disabled: true,
      label: category
    })

    groupedResults[category].forEach(skill => {
      suggestions.push({
        value: skill.value
      })
    })
  })

  cb(suggestions)
}

// 将技能等级转换为中文描述
const getLevelText = (level) => {
  return levelTexts[level - 1] || '熟练' // 默认返回"熟练"
}

// 获取技能描述
const fetchSkillDescription = async (skillId, skillLevel) => {
  if (!skillId || !skillLevel) return
  
  try {
    // 如果skillId不是数字，表示传入的是名称而不是ID
    const id = Number(skillId) || null
    if (!id) {
      console.error('获取技能描述失败: skillId不是有效的数字ID')
      return
    }
    
    // 将数字等级转换为中文描述
    const proficiency = getLevelText(skillLevel)
    
    const response = await getSkillSegmentList(id, proficiency)
    if (response && response.data && response.data.length > 0) {
      // 更新当前技能描述，使用第一个段落的文本
      currentSkill.value.description = response.data[0].text || ''
    }
  } catch (error) {
    console.error('获取技能描述失败:', error)
  }
}

// 处理技能名称变化
const handleSkillNameChange = () => {
  // 检查是否是数字ID，否则作为字符串处理
  const skillId = Number.isInteger(Number(currentSkill.value.name)) ? Number(currentSkill.value.name) : null;
  
  if (skillId && currentSkill.value.level) {
    fetchSkillDescription(skillId, currentSkill.value.level)
  }
}

// 处理熟练度变化
const handleSkillLevelChange = () => {
  // 检查是否是数字ID，否则作为字符串处理
  const skillId = Number.isInteger(Number(currentSkill.value.name)) ? Number(currentSkill.value.name) : null;
  
  if (skillId && currentSkill.value.level) {
    fetchSkillDescription(skillId, currentSkill.value.level)
  }
}

// 润色技能描述
const polishSkillDescription = (skill, index) => {
  if (!skill.description) {
    ElMessage.warning('请先输入技能描述再使用AI润色功能')
    return
  }
  
  skillToPolish.value = { ...skill, index }
  polishDialogVisible.value = true
  polishLoading.value = true
  
  // 调用后端AI润色API
  polishSkill(skill.description).then(res => {
    if (res.code === 0 || res.code === 200) {
      polishedContent.value = res.data
    } else {
      // 显示后端返回的具体错误消息
      const errorMsg = res.message || res.msg || '润色失败'
      ElMessage.error(errorMsg)
      polishedContent.value = skill.description
    }
    polishLoading.value = false
  }).catch(error => {
    console.error('润色请求失败', error)
    // 显示具体的错误消息
    ElMessage.error(error.message || '润色请求失败，请稍后重试')
    polishedContent.value = skill.description
    polishLoading.value = false
  })
}

// 应用润色后的内容
const applyPolishedContent = () => {
  if (selectedVersion.value === 1 && skillToPolish.value) {
    const index = skillToPolish.value.index
    formData.value[index].description = polishedContent.value
  }
  polishDialogVisible.value = false
  ElMessage.success('内容已更新')
}

// 显示添加技能对话框
const showAddSkillDialog = () => {
  isEditing.value = false
  currentSkill.value = { name: '', level: 3, description: '' }
  dialogVisible.value = true
}

// 编辑技能
const editSkill = (index) => {
  isEditing.value = true
  editingIndex.value = index
  currentSkill.value = { ...formData.value[index] }
  dialogVisible.value = true
}

// 移除技能
const removeSkill = (index) => {
  formData.value.splice(index, 1)
}

// 保存当前编辑的技能
const saveSkill = () => {
  if (!currentSkill.value.name.trim()) {
    ElMessage.warning('请输入技能名称')
    return
  }

  // 检查是否已存在相同技能（仅在添加时检查）
  if (!isEditing.value) {
    const existingIndex = formData.value.findIndex(skill =>
        skill.name.toLowerCase() === currentSkill.value.name.toLowerCase()
    )

    if (existingIndex >= 0) {
      ElMessage.warning('该技能已存在，请编辑现有技能或添加不同的技能')
      return
    }
  }

  if (isEditing.value) {
    // 编辑现有技能
    formData.value[editingIndex.value] = { ...currentSkill.value }
  } else {
    // 添加新技能
    formData.value.push({ ...currentSkill.value })
  }

  dialogVisible.value = false
  ElMessage.success(isEditing.value ? '技能已更新' : '技能已添加')
}

// 保存所有技能
const saveForm = () => {
  if (formData.value.length === 0) {
    ElMessage.warning('请至少添加一项技能')
    return
  }

  emit('update', formData.value)
  ElMessage.success('技能信息已保存')
}

// 重置表单
const resetForm = () => {
  if (props.data && props.data.length) {
    formData.value = JSON.parse(JSON.stringify(props.data))
  } else {
    formData.value = []
  }
  ElMessage.info('已重置为上次保存的内容')
}
</script>

<style scoped>
.form-container {
  padding: 20px;
}

.skills-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.skills-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.empty-skills {
  margin: 40px 0;
  text-align: center;
}

.skills-list {
  margin-bottom: 20px;
}

.skill-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 10px;
  background-color: #f8f9fc;
  transition: all 0.3s;
}

.skill-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.skill-content {
  flex: 1;
}

.skill-name {
  font-weight: 500;
  margin-bottom: 5px;
  color: #303133;
}

.skill-level {
  display: flex;
  align-items: center;
}

.skill-actions {
  display: flex;
  gap: 10px;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style> 