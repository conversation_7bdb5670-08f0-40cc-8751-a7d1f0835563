// 测试Playwright PDF生成集成
// 这个脚本用于验证Playwright PDF生成功能是否正常工作

const fs = require('fs');
const path = require('path');

console.log('=== Playwright PDF生成集成测试 ===');

// 测试1: 检查Playwright是否已安装
console.log('\n1. 检查Playwright安装状态...');
try {
    const { execSync } = require('child_process');
    const playwrightVersion = execSync('npx playwright --version', { encoding: 'utf8' });
    console.log('✅ Playwright已安装:', playwrightVersion.trim());
} catch (error) {
    console.log('❌ Playwright未安装或无法访问');
    console.log('请运行: npm install -g playwright');
    console.log('然后运行: npx playwright install');
    process.exit(1);
}

// 测试2: 检查浏览器是否可用
console.log('\n2. 检查浏览器可用性...');
try {
    const { execSync } = require('child_process');
    execSync('npx playwright install chromium', { encoding: 'utf8' });
    console.log('✅ Chromium浏览器已安装');
} catch (error) {
    console.log('❌ 浏览器安装失败:', error.message);
    process.exit(1);
}

// 测试3: 创建测试HTML文件
console.log('\n3. 创建测试HTML文件...');
const testHtml = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>测试简历</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        .section { margin-bottom: 20px; }
        .info { background: #f5f5f5; padding: 10px; }
    </style>
</head>
<body>
    <h1>测试简历</h1>
    <div class="section">
        <h2>基本信息</h2>
        <div class="info">
            <p><strong>姓名:</strong> 张三</p>
            <p><strong>电话:</strong> 13800138000</p>
            <p><strong>邮箱:</strong> <EMAIL></p>
        </div>
    </div>
    <div class="section">
        <h2>教育经历</h2>
        <div class="info">
            <p><strong>学校:</strong> 北京大学</p>
            <p><strong>专业:</strong> 计算机科学与技术</p>
            <p><strong>学历:</strong> 本科</p>
        </div>
    </div>
    <div class="section">
        <h2>工作经验</h2>
        <div class="info">
            <p><strong>公司:</strong> 腾讯科技</p>
            <p><strong>职位:</strong> 软件工程师</p>
            <p><strong>时间:</strong> 2020-2023</p>
        </div>
    </div>
</body>
</html>
`;

const testHtmlPath = path.join(__dirname, 'test-resume.html');
fs.writeFileSync(testHtmlPath, testHtml);
console.log('✅ 测试HTML文件已创建:', testHtmlPath);

// 测试4: 使用Playwright生成PDF
console.log('\n4. 测试PDF生成...');
try {
    const { execSync } = require('child_process');
    const outputPath = path.join(__dirname, 'test-resume.pdf');
    
    const command = `npx playwright pdf "${testHtmlPath}" "${outputPath}"`;
    console.log('执行命令:', command);
    
    execSync(command, { encoding: 'utf8' });
    
    if (fs.existsSync(outputPath)) {
        const stats = fs.statSync(outputPath);
        console.log('✅ PDF生成成功!');
        console.log('文件大小:', (stats.size / 1024).toFixed(2), 'KB');
        console.log('文件路径:', outputPath);
    } else {
        console.log('❌ PDF文件未生成');
    }
} catch (error) {
    console.log('❌ PDF生成失败:', error.message);
    process.exit(1);
}

// 测试5: 清理测试文件
console.log('\n5. 清理测试文件...');
try {
    fs.unlinkSync(testHtmlPath);
    console.log('✅ 测试HTML文件已删除');
} catch (error) {
    console.log('⚠️ 清理HTML文件失败:', error.message);
}

console.log('\n=== 测试完成 ===');
console.log('✅ Playwright PDF生成集成测试通过!');
console.log('\n现在可以启动Spring Boot应用程序并测试简历下载功能。');
console.log('简历下载将优先使用Playwright生成PDF，确保与前端显示完全一致。'); 