<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 14px;
            font-weight: normal;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 0;
            color: #777777; /* 采用模板3的颜色方案 */
            line-height: 1.6;
            background: white;
            width: 100%;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif !important;
            }
        }

        .resume-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 0;
            box-sizing: border-box;
            background: white;
        }

        .header {
            background: #4A90E2;
            color: white;
            width: 100%;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .header-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 15px 30px;
            position: relative;
            box-sizing: border-box;
            width: 100%;
        }

        .header-name {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
            flex-shrink: 0;
        }

        .contact-info {
            position: absolute;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 15px;
            align-items: center;
            font-size: 12px;
            flex-wrap: nowrap;
            white-space: nowrap;
        }

        .contact-item {
            color: white;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            gap: 3px;
            flex-shrink: 0;
        }

        .icon {
            font-size: 14px;
            margin-right: 5px;
            display: inline-block;
            width: 16px;
            text-align: center;
        }

        .section {
            margin-bottom: 0px;
            background: white;
            padding: 8px 5px;
            /* 允许内容跨页显示 */
            page-break-inside: auto;
            orphans: 2;
            widows: 2;
        }

        .section-title {
            color: #4A90E2;
            padding-bottom: 5px;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 0;
            letter-spacing: 0.5px;
            display: inline-block;
        }

        .title-line-container {
            margin-bottom: 8px;
            height: 2px;
            position: relative;
        }

        .title-blue-line {
            background: #4A90E2;
            height: 2px;
            width: 60px;
            position: absolute;
            left: 0;
            top: 0;
        }

        .title-gray-line {
            background: #e0e0e0;
            height: 2px;
            position: absolute;
            left: 60px;
            right: 0;
            top: 0;
        }

        .item {
            margin-bottom: 15px;
            padding: 0;
            /* 允许项目内容跨页 */
            page-break-inside: auto;
            orphans: 1;
            widows: 1;
        }

        .item:last-child {
            margin-bottom: 0;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            align-items: flex-start;
        }

        .item-time {
            color: #333; /* 采用模板3的颜色方案 */
            font-size: 14px;
            min-width: 120px;
            flex-shrink: 0;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
        }

        .item-title {
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #333;
            font-size: 14px;
            flex: 1;
            text-align: center;
            margin: 0 15px;
        }

        .item-detail {
            color: #333; /* 采用模板3的颜色方案 */
            font-size: 14px;
            min-width: 120px;
            text-align: right;
            flex-shrink: 0;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
        }

        .item-content {
            text-align: justify;
            font-size: 13px;
            line-height: 1.6;
            color: #777777; /* 采用模板3的颜色方案 */
            margin-top: 8px;
            padding-left: 120px;
            /* 允许内容跨页 */
            page-break-inside: auto;
        }

        ul {
            margin-top: 5px;
            margin-bottom: 5px;
            padding-left: 140px;
        }

        li {
            margin-bottom: 3px;
            font-size: 13px;
            line-height: 1.6;
            color: #777777; /* 采用模板3的颜色方案 */
        }

        .skill-item {
            margin-bottom: 10px;
        }

        .skill-name {
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #333;
            margin-bottom: 5px;
        }

        .skill-description {
            color: #777777; /* 采用模板3的颜色方案 */
            font-size: 13px;
            line-height: 1.6;
            margin-left: 0;
        }

        .section-content {
            font-size: 13px;
            line-height: 1.6;
            color: #777777; /* 采用模板3的颜色方案 */
            text-align: left;
        }

        /* 教育经历专用样式 */
        .education-item {
            margin-bottom: 15px;
            padding: 0;
        }

        .education-item:last-child {
            margin-bottom: 0;
        }

        .education-header {
            display: table;
            width: 100%;
            margin-bottom: 8px;
            table-layout: fixed;
        }

        .education-time {
            display: table-cell;
            color: #333;
            font-size: 14px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 25%;
            vertical-align: middle;
        }

        .education-school {
            display: table-cell;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #333;
            font-size: 14px;
            width: 50%;
            text-align: center;
            vertical-align: middle;
        }

        .education-major {
            display: table-cell;
            color: #333;
            font-size: 14px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 25%;
            text-align: right;
            vertical-align: middle;
        }

        .education-courses {
            text-align: left;
            font-size: 13px;
            line-height: 1.6;
            color: #777777; /* 采用模板3的颜色方案 */
            margin-top: 8px;
            padding-left: 0;
        }

        /* 工作经验样式 */
        .work-item {
            margin-bottom: 15px;
        }

        .work-header {
            display: table;
            width: 100%;
            margin-bottom: 8px;
            table-layout: fixed;
        }

        .work-time {
            display: table-cell;
            color: #333;
            font-size: 14px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 25%;
            vertical-align: middle;
        }

        .work-company {
            display: table-cell;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #333;
            font-size: 14px;
            width: 50%;
            text-align: center;
            vertical-align: middle;
        }

        .work-position {
            display: table-cell;
            color: #333;
            font-size: 14px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 25%;
            text-align: right;
            vertical-align: middle;
        }

        .work-content {
            text-align: left;
            font-size: 13px;
            line-height: 1.6;
            color: #777777; /* 采用模板3的颜色方案 */
            margin-top: 8px;
            padding-left: 0;
        }

        /* 项目经验样式 */
        .project-item {
            margin-bottom: 15px;
        }

        .project-header {
            display: table;
            width: 100%;
            margin-bottom: 8px;
            table-layout: fixed;
        }

        .project-time {
            display: table-cell;
            color: #333;
            font-size: 14px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 25%;
            vertical-align: middle;
        }

        .project-name {
            display: table-cell;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #333;
            font-size: 14px;
            width: 50%;
            text-align: center;
            vertical-align: middle;
        }

        .project-role {
            display: table-cell;
            color: #333;
            font-size: 14px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 25%;
            text-align: right;
            vertical-align: middle;
        }

        .project-url {
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 5px;
            color: #333;
        }

        .project-content {
            text-align: left;
            font-size: 13px;
            line-height: 1.6;
            color: #777777; /* 采用模板3的颜色方案 */
            margin-top: 8px;
            padding-left: 0;
        }

        /* 内容区域整体样式 */
        .content-area {
            padding: 0;
            background: white;
        }

        strong, b,
        .item-content strong,
        .work-content strong,
        .project-content strong,
        .section-content strong,
        .skill-description strong,
        .education-courses strong {
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #000000 !important;
            font-weight: bold !important;
        }

        /* 响应式和打印优化 */
        @media print {
            body {
                background: white;
            }
            /* 允许section内容跨页显示，避免大面积空白 */
            .section {
                page-break-inside: auto;
            }
            /* 确保项目内容可以跨页 */
            .item {
                page-break-inside: auto;
            }
            /* 允许列表项跨页 */
            ul {
                page-break-inside: auto;
            }
            li {
                page-break-inside: auto;
            }
            /* 允许技能项目跨页 */
            .skill-item {
                page-break-inside: auto;
            }
        }
    </style>
</head>
<body>
<!-- 页眉部分 - 独立于容器外，确保占满整行 -->
<div class="header">
    <div class="header-content">
        <span class="header-name">${name}</span>
        <span class="contact-info">
                <span class="contact-item"><span class="icon">●</span>${age}岁</span>
                <span class="contact-item"><span class="icon">☎</span>${phone}</span>
                <span class="contact-item"><span class="icon">@</span>${email}</span>
                <span class="contact-item"><span class="icon">★</span>${jobObjective}</span>
            </span>
    </div>
</div>

<div class="resume-container">
    <!-- 内容区域 -->
    <div class="content-area">
        <!-- 教育经历 -->
        ${education}

        <!-- 工作经历 -->
        ${work}

        <!-- 项目经验 -->
        ${projects}

        <!-- 练手项目 -->
        ${practices}

        <!-- 技能特长 -->
        ${skills}

        <!-- 证书奖项 -->
        ${certificates}

        <!-- 校园经历 -->
        ${campus}

        <!-- 兴趣爱好 -->
        ${interests}

        <!-- 自我评价 -->
        ${selfEvaluation}
    </div>
</div>
</body>
</html> 