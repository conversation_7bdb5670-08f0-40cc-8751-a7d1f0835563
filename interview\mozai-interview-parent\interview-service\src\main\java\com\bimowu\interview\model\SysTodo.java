package com.bimowu.interview.model;

import lombok.Data;

import java.util.Date;

/**
 * 待办实体类
 */
@Data
public class SysTodo {

    /**
     * 待办ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 简历ID
     */
    private Long resumeId;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;
    
    /**
     * 链接URL
     */
    private String url;
    
    /**
     * 待办类型
     * 0：创建简历
     * 1：知识学习
     * 2：hr面试
     * 3：技术面试
     * 4：正式面试
     */
    private Integer todoType;

    /**
     * 状态：0-未完成，1-已完成
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 