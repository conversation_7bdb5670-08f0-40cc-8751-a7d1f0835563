<template>
  <div class="result-container">
    <div class="result-content">
      <h1>面试评估结果</h1>
      
      <!-- 等待结果状态显示 -->
      <div v-if="interviewResults.interview.status === 1" class="waiting-result">
        <div class="waiting-icon">
          <el-icon class="is-loading"><Loading /></el-icon>
        </div>
        <h3>面试结果处理中</h3>
        <p>您的面试已完成，我们正在分析您的表现，请耐心等待结果...</p>
      </div>

      <!-- 已完成状态显示具体结果 -->
      <div v-else class="result-summary">
        <div class="overall-score">
          <div class="score-circle">
            <span class="score-number">{{ interviewResults.interview.overallScore }}</span>
          </div>
          <h3>总体评分</h3>
        </div>

        <div class="feedback-box">
          <h3>AI 评价反馈</h3>
          <p>{{ interviewResults.interview.feedback }}</p>
        </div>
      </div>
      
<!--      <div class="detail-scores">-->
<!--        <h3>详细评分</h3>-->
<!--        <div class="score-items">-->
<!--          <div class="score-item">-->
<!--            <span class="score-label">回答内容</span>-->
<!--            <el-progress :percentage="interviewResults.scores.content" :format="formatScore" />-->
<!--          </div>-->
<!--          <div class="score-item">-->
<!--            <span class="score-label">面部表情</span>-->
<!--            <el-progress :percentage="interviewResults.scores.expression" :format="formatScore" />-->
<!--          </div>-->
<!--          <div class="score-item">-->
<!--            <span class="score-label">光线环境</span>-->
<!--            <el-progress :percentage="interviewResults.scores.lighting" :format="formatScore" />-->
<!--          </div>-->
<!--          <div class="score-item">-->
<!--            <span class="score-label">噪音控制</span>-->
<!--            <el-progress :percentage="interviewResults.scores.noise" :format="formatScore" />-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
      
      <!-- 只在非等待结果状态时显示面试问答记录 -->
      <div v-if="interviewResults.interview.status !== 1" class="interview-records">
        <h3>面试问答记录</h3>
        <!-- 加载中状态 -->
        <div v-if="isLoading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>

        <el-collapse v-else>
          <el-collapse-item
            v-for="(question, index) in Object.entries(interviewResults.questions)"
            :key="index"
            :title="`问题 ${index + 1}: ${question[1]}`"
          >
            <div class="answer-content">
              <div class="transcription-section">
                <h4>我的回答</h4>
                <div class="transcription-details">
                  <div class="transcription-detail">
                    <p>{{ interviewResults.transcriptions[index] || '无回答内容' }}</p>
                  </div>
                </div>
              </div>
              <div class="answer-section">
                <h4>参考答案</h4>
                <div class="answer-details">
                  <div class="answer-detail">
                    <p>{{ interviewResults.answers[index] || '无参考答案' }}</p>
                  </div>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      
<!--      <div class="media-records">-->
<!--        <h3>面试录像回放</h3>-->
<!--        <div v-if="videoUrl" class="video-playback">-->
<!--          <video controls :src="videoUrl"></video>-->
<!--        </div>-->
<!--        <div v-else-if="isVideoUploading" class="uploading-progress">-->
<!--          <el-progress :percentage="uploadProgress" :stroke-width="10" />-->
<!--          <p>视频自动上传中，请稍候...</p>-->
<!--        </div>-->
<!--        <div v-else class="video-upload-container">-->
<!--          <div class="upload-area">-->
<!--            <p>录像暂不可用</p>-->
<!--            <div class="upload-actions">-->
<!--              <el-upload-->
<!--                class="video-uploader"-->
<!--                :auto-upload="false"-->
<!--                :show-file-list="true"-->
<!--                :limit="1"-->
<!--                :on-change="handleVideoChange"-->
<!--                accept="video/*"-->
<!--              >-->
<!--                <template #trigger>-->
<!--                  <el-button type="primary" plain>选择视频文件</el-button>-->
<!--                </template>-->
<!--              </el-upload>-->
<!--              <el-button -->
<!--                type="success" -->
<!--                :disabled="!selectedVideoFile" -->
<!--                @click="uploadInterviewVideo"-->
<!--                :loading="isVideoUploading"-->
<!--              >-->
<!--                上传面试视频-->
<!--              </el-button>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
<!--        -->
<!--        <h3>面试录音回放</h3>-->
<!--        <div v-if="audioUrl" class="audio-playback">-->
<!--          <audio controls :src="audioUrl"></audio>-->
<!--        </div>-->
<!--        <div v-else class="no-recording">-->
<!--          <p>录音暂不可用</p>-->
<!--        </div>-->
<!--      </div>-->
      
      <div class="actions">
        <el-button @click="downloadReport">下载面试报告</el-button>
        <el-button type="primary" @click="startNewInterview">开始新的面试</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { useInterviewStore } from '@/store'
import axios from 'axios'
import api from '@/api'
import httpClient from '@/utils/http'

const router = useRouter()
const interviewStore = useInterviewStore()

const videoUrl = ref('')
const audioUrl = ref('')
const isLoading = ref(false)
const transcriptionDetails = ref<any[]>([])
const selectedVideoFile = ref<File | null>(null)
const isVideoUploading = ref(false)
const uploadProgress = ref(0)

const interviewResults = computed(() => interviewStore.interviewResults)
const questions = computed(() => interviewStore.questions)
const transcriptions = computed(() => interviewStore.transcriptions)

// 处理视频文件选择
const handleVideoChange = (file: any) => {
  if (file && file.raw) {
    // 检查文件大小，限制为100MB
    const isLt100M = file.size / 1024 / 1024 < 100
    if (!isLt100M) {
      ElMessage.error('视频文件大小不能超过100MB!')
      return false
    }
    
    // 检查文件类型
    const isVideo = file.raw.type.indexOf('video/') === 0
    if (!isVideo) {
      ElMessage.error('请上传视频文件!')
      return false
    }
    
    selectedVideoFile.value = file.raw
    ElMessage.success('视频文件已选择，点击上传按钮开始上传')
    return true
  }
  return false
}

// 上传面试视频
const uploadInterviewVideo = async () => {
  if (!selectedVideoFile.value) {
    ElMessage.warning('请先选择视频文件')
    return
  }
  
  if (!interviewStore.interviewId) {
    ElMessage.error('面试ID不存在，无法上传视频')
    return
  }
  
  try {
    isVideoUploading.value = true
    uploadProgress.value = 0
    
    // 强制使用mp4扩展名，确保后端兼容性
    const fileExtension = 'mp4';
    
    // 获取原始MIME类型，但设置为标准视频类型
    let mimeType = selectedVideoFile.value.type;
    console.log(`原始视频MIME类型: ${mimeType}`);
    
    // 简化MIME类型，移除codecs信息
    if (mimeType && mimeType.includes(';')) {
      mimeType = mimeType.split(';')[0];
    }
    
    // 如果MIME类型不是标准视频类型，则使用通用视频类型
    if (!mimeType || !mimeType.startsWith('video/')) {
      mimeType = 'video/mp4';
    }
    
    console.log(`使用文件扩展名: ${fileExtension}, 简化MIME类型: ${mimeType}`);
    
    // 将选择的文件重命名为正确的扩展名
    const videoFile = new File(
      [selectedVideoFile.value], 
      `interview-${interviewStore.interviewId}.${fileExtension}`, 
      { type: mimeType }
    )
    
    // 导入OSS上传工具
    try {
      // 使用OSS直传上传视频
      const { uploadInterviewVideoOss } = await import('@/api/interview')
      
      // 调用OSS直传API上传视频
      const success = await uploadInterviewVideoOss(
        interviewStore.interviewId,
        videoFile,
        (percent) => {
          // 更新上传进度
          uploadProgress.value = percent
        }
      )
      
      // 检查上传结果
      if (success) {
        // 重新获取面试详情，获取视频URL
        const interviewDetail = await api.getInterviewDetail(interviewStore.interviewId)
        if (interviewDetail && interviewDetail.success && interviewDetail.interview) {
          videoUrl.value = interviewDetail.interview.videoUrl
        }
        ElMessage.success('视频上传成功')
      } else {
        ElMessage.error('视频上传失败')
      }
    } catch (ossError) {
      console.error('OSS直传失败:', ossError)
      ElMessage.error(`视频上传失败: ${ossError instanceof Error ? ossError.message : String(ossError)}`)
      
      // 提示用户联系管理员使用传统方式上传
      ElMessage({
        message: '请联系管理员使用传统方式上传视频',
        type: 'warning',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('视频上传失败:', error)
    ElMessage.error('视频上传失败，请稍后重试')
  } finally {
    isVideoUploading.value = false
    uploadProgress.value = 100
  }
}

// 格式化分数
const formatScore = (percentage: number) => {
  return `${percentage}分`
}

// 获取完整的转写记录
const fetchTranscriptionDetails = async () => {
  if (!interviewStore.interviewId) {
    console.error('没有面试ID，无法获取转写记录')
    return
  }
  
  isLoading.value = true
  
  try {
    console.log(`获取面试ID: ${interviewStore.interviewId} 的转写详情`)
    
    const response = await httpClient.get(`/api/speech/transcriptions/${interviewStore.interviewId}`)
    
    if (response.data.success) {
      console.log('成功获取转写详情:', response.data.transcriptions)
      transcriptionDetails.value = response.data.transcriptions || []
    } else {
      console.error('获取转写详情失败:', response.data.message)
      ElMessage.warning('获取转写详情失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('获取转写详情异常:', error)
    ElMessage.error('获取转写详情异常，请稍后再试')
  } finally {
    isLoading.value = false
  }
}

// 根据问题索引获取详细转写记录
const getTranscriptionDetail = (questionIndex: number) => {
  return transcriptionDetails.value.find(item => item.questionIndex === questionIndex)
}

// 下载面试报告
const downloadReport = () => {
  // 生成报告内容
  const reportContent = `
AI面试评估报告
====================

总体评分: ${interviewResults.value.interview.overallScore}分

AI评价反馈:
${interviewResults.value.interview.feedback}

面试问答记录:
${Object.entries(interviewResults.value.questions).map(([index, question]) => {
  return `
问题${parseInt(index) + 1}: ${question}
转写文本: ${interviewResults.value.transcriptions[index] || '未转写'}
`}).join('\n')}

报告生成时间: ${new Date().toLocaleString()}
  `
  
  // 创建下载链接
  const blob = new Blob([reportContent], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `面试报告_${new Date().toISOString().slice(0, 10)}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('面试报告已下载')
}

// 开始新的面试
const startNewInterview = () => {
  interviewStore.resetInterview()
  router.push('/')
}

onMounted(async () => {
  // 检查是否有面试记录（允许等待结果状态访问）
  if (!interviewResults.value.interview.id) {
    ElMessage.warning('没有面试记录，请先完成面试')
    router.push('/')
    return
  }

  // 如果不是等待结果状态且没有评分，则跳转
  if (interviewResults.value.interview.status !== 1 && !interviewResults.value.interview.overallScore) {
    ElMessage.warning('没有面试结果，请先完成面试')
    router.push('/')
    return
  }

  // 如果有视频URL，设置视频播放地址
  if (interviewResults.value.interview.videoUrl) {
    videoUrl.value = interviewResults.value.interview.videoUrl
  }

  // 只在非等待结果状态时获取详细转写记录
  if (interviewResults.value.interview.status !== 1) {
    await fetchTranscriptionDetails()
  }
})
</script>

<style scoped>
.result-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 30px 20px;
}

.result-content {
  max-width: 1000px;
  margin: 0 auto;
  background-color: white;
  border-radius: 10px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

h1 {
  text-align: center;
  color: #409EFF;
  margin-bottom: 30px;
}

h3 {
  color: #303133;
  margin-bottom: 15px;
}

h4 {
  color: #606266;
  margin-bottom: 10px;
  font-size: 1rem;
}

.result-summary {
  display: flex;
  margin-bottom: 40px;
  gap: 30px;
}

.overall-score {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #42b983 0%, #33a06f 100%);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
}

.score-number {
  font-size: 2.5rem;
  font-weight: bold;
}

.feedback-box {
  flex: 1;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
}

.detail-scores {
  margin-bottom: 40px;
}

.score-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.score-item {
  margin-bottom: 15px;
}

.score-label {
  display: block;
  margin-bottom: 8px;
  color: #606266;
}

.interview-records {
  margin-bottom: 40px;
}

.answer-content {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.transcription-section,
.answer-section {
  margin-bottom: 20px;
}

.transcription-section h4, 
.answer-section h4 {
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 16px;
}

.transcription-section {
  background-color: #e6f1f9;
  border-left: 4px solid #409EFF;
  padding: 16px;
  border-radius: 6px;
}

.transcription-section h4 {
  color: #409EFF;
}

.answer-section {
  margin-top: 20px;
  background-color: #f0f9eb;
  border-left: 4px solid #67C23A;
  padding: 16px;
  border-radius: 6px;
}

.answer-section h4 {
  color: #67C23A;
}

.answer-details,
.transcription-details {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.answer-detail p,
.transcription-detail p {
  margin: 0;
  line-height: 1.8;
  color: #303133;
  font-size: 15px;
}

.el-collapse-item {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.el-collapse-item__header {
  font-size: 16px;
  font-weight: 500;
  padding: 16px 20px;
  background-color: #ffffff;
  border-bottom: 1px solid #ebeef5;
}

.el-collapse-item__content {
  padding: 0;
}

.el-collapse-item__wrap {
  border: none;
}

.media-records {
  margin-bottom: 40px;
}

.video-playback video,
.audio-playback audio {
  width: 100%;
  margin-bottom: 20px;
}

.no-recording {
  padding: 20px;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 5px;
  margin-bottom: 20px;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.transcription-details {
  margin-bottom: 15px;
}

.transcription-detail {
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 15px;
}

.audio-player-container {
  margin-top: 15px;
  padding: 10px;
  background-color: #e6f1fc;
  border-radius: 6px;
  border: 1px solid #d0e2f5;
}

.answer-audio {
  width: 100%;
  height: 40px;
  outline: none;
  margin-top: 5px;
}

.waiting-result {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
  border-radius: 12px;
  margin-bottom: 30px;
}

.waiting-icon {
  font-size: 48px;
  color: #E6A23C;
  margin-bottom: 20px;
}

.waiting-result h3 {
  color: #E6A23C;
  margin-bottom: 15px;
  font-size: 24px;
}

.waiting-result p {
  color: #666;
  font-size: 16px;
  line-height: 1.6;
}

.loading-container {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.video-upload-container {
  margin-bottom: 20px;
}

.upload-area {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  text-align: center;
}

.upload-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin-top: 15px;
}

.video-uploader {
  width: 100%;
}

.uploading-progress {
  padding: 20px;
  background-color: #f0f9eb;
  border-radius: 8px;
  text-align: center;
}

.uploading-progress p {
  margin-top: 10px;
  color: #67c23a;
  font-weight: 500;
}

@media (max-width: 768px) {
  .result-summary {
    flex-direction: column;
  }
  
  .score-items {
    grid-template-columns: 1fr;
  }
}
</style> 