package com.bimowu.interview.dao;

import com.bimowu.interview.model.SysProperty;
import com.bimowu.interview.utils.bean.CommonQueryBean;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 
 * SysProperty数据库操作接口类
 * 
 **/

@Repository
public interface SysPropertyDao{


	/**
	 * 
	 * 查询（根据主键ID查询）
	 * 
	 **/
	SysProperty selectByPrimaryKey(@Param("id") Long id);

	/**
	 * 
	 * 删除（根据主键ID删除）
	 * 
	 **/
	int deleteByPrimaryKey(@Param("id") Long id);

	/**
	 * 
	 * 添加
	 * 
	 **/
	int insert(SysProperty record);

	/**
	 * 
	 * 修改 （匹配有值的字段）
	 * 
	 **/
	int updateByPrimaryKeySelective(SysProperty record);

	/**
	 * 
	 * list分页查询
	 * 
	 **/
	List<SysProperty> list4Page(@Param("record") SysProperty record, @Param("commonQueryParam") CommonQueryBean query);

	/**
	 * 
	 * count查询
	 * 
	 **/
	long count(@Param("record") SysProperty record);

	/**
	 * 
	 * list查询
	 * 
	 **/
	List<SysProperty> list(@Param("record") SysProperty record);

}