package com.bimowu.resume.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.bimowu.resume.common.service.ResumeAiUsageService;
import com.bimowu.resume.exception.BaseException;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionChoice;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * AI 调用工具类
 */
@Service
@Slf4j
public class AiManager {

    // AI 调用客服端
    @Autowired
    private ArkService arkService;
    
    @Autowired
    private ResumeAiUsageService resumeAiUsageService;

    @Value("${ai.model-name}")
    private String modelName;
    
    /**
     * 检查用户是否有可用的AI次数
     * 
     * @param userId 用户ID
     * @throws BaseException 如果用户没有可用次数，抛出异常
     */
    private void checkUserAiUsage(Long userId) {
        // 初始化用户AI使用记录（如果不存在）
        resumeAiUsageService.initUserAiCount(userId, null);
        
        // 检查用户是否有可用次数
        if (!resumeAiUsageService.hasAvailableCount(userId)) {
            throw new BaseException(500,"当前AI润色次数已用完，请联系管理员！");
        }
    }
    
    /**
     * 减少用户AI使用次数
     * 
     * @param userId 用户ID
     * @throws BaseException 如果减少失败，抛出异常
     */
    private void decreaseUserAiUsage(Long userId) {
        resumeAiUsageService.decreaseCount(userId);
    }
    
    /**
     * 根据项目经验生成面试问题
     * 
     * @param userId 用户ID
     * @param projectDescription 项目经验描述
     * @param position 面试岗位
     * @param count 需要生成的问题数量
     * @return 生成的面试问题列表
     */
    public List<String> generateInterviewQuestions(Long userId, String projectDescription, String position, Integer count) {
        // 检查用户AI使用次数
        checkUserAiUsage(userId);
        
        final List<ChatMessage> messages = new ArrayList<>();
        
        // 系统提示词，引导AI生成高质量的面试问题
        final String questionGenerationPrompt = 
                "你是一位资深技术面试官，需要根据候选人的项目经验生成专业的技术面试问题。" +
                "请基于候选人的项目描述，生成" + count + "个针对" + position + "岗位的高质量面试问题。" +
                "问题应该：\n" +
                "1. 与项目技术栈和背景相关\n" +
                "2. 能够考察候选人的专业能力和项目理解\n" +
                "3. 包含一定的深度，能够区分候选人的技术水平\n" +
                "4. 注重实际问题解决能力的考察\n" +
                "5. 难度不要太高，面向大四在校生\n" +
                "请直接返回问题列表，每个问题一行，不要有额外的前缀或解释。";
        
        final ChatMessage systemMessage = ChatMessage.builder()
                .role(ChatMessageRole.SYSTEM)
                .content(questionGenerationPrompt)
                .build();
        messages.add(systemMessage);
        
        // 用户消息，提供项目经验描述
        final ChatMessage userMessage = ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content("候选人项目经验：" + projectDescription)
                .build();
        messages.add(userMessage);
        
        // 调用AI生成问题
        String response = doChat(userId, messages);
        
        // 解析返回的问题列表
        List<String> questions = new ArrayList<>();
        String[] lines = response.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (!line.isEmpty()) {
                // 去除可能的序号前缀
                line = line.replaceAll("^\\d+\\.\\s*", "").trim();
                questions.add(line);
            }
        }
        
        // 确保问题数量不超过请求的数量
        if (questions.size() > count) {
            questions = questions.subList(0, count);
        }
        
        return questions;
    }

    /**
     * 润色项目经验描述
     * 
     * @param userId 用户ID
     * @param projectContent 原始项目经验内容
     * @return 润色后的项目经验内容
     */
    public String polishProjectExperience(Long userId, String projectContent) {
        // 检查用户AI使用次数
        checkUserAiUsage(userId);
        
        final List<ChatMessage> messages = new ArrayList<>();
        log.info("<润色项目经验描述:>" + projectContent);
        
        // 系统提示词，引导AI润色项目经验
        final String projectPolishPrompt = 
                "你是一位专业的简历优化顾问，擅长润色项目经验描述。请对提供的项目经验内容进行优化，使其更加专业、有说服力，并突出个人贡献和成果。" +
                "润色时请遵循以下原则：\n" +
                "1. 保持原有项目的基本信息和技术栈不变\n" +
                "2. 使用更专业、更精确的词汇描述职责和成果\n" +
                "3. 突出个人在项目中的贡献和解决的关键问题\n" +
                "4. 尽可能量化成果（如提升效率百分比、减少成本等）\n" +
                "5. 优化语言表达，使描述更加流畅、专业\n" +
                "6. 保持内容的真实性，不过度夸大\n" +
                "7. 控制在合适的篇幅，不要过于冗长\n" +
                "请直接返回润色后的完整内容，不要添加解释或前缀。";
        
        final ChatMessage systemMessage = ChatMessage.builder()
                .role(ChatMessageRole.SYSTEM)
                .content(projectPolishPrompt)
                .build();
        messages.add(systemMessage);
        
        // 用户消息，提供原始项目经验内容
        final ChatMessage userMessage = ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content("原始项目经验：\n" + projectContent)
                .build();
        messages.add(userMessage);
        
        // 调用AI润色
        return doChat(userId, messages);
    }

    /**
     * 润色技能特长描述
     * 
     * @param userId 用户ID
     * @param skillContent 原始技能特长内容
     * @return 润色后的技能特长内容
     */
    public String polishSkill(Long userId, String skillContent) {
        // 检查用户AI使用次数
        checkUserAiUsage(userId);
        
        final List<ChatMessage> messages = new ArrayList<>();
        log.info("<润色技能特长描述:>" + skillContent);

        // 系统提示词，引导AI润色技能特长
        final String skillPolishPrompt = 
                "你是一位专业的简历优化顾问，擅长润色技能特长描述。请对提供的技能特长内容进行优化，使其更加专业、具体，并能够展示应聘者的专业水平。" +
                "润色时请遵循以下原则：\n" +
                "1. 保持原有技能名称和熟练度不变\n" +
                "2. 使用更专业、更精确的词汇描述技能掌握情况\n" +
                "3. 适当添加技能应用场景或项目经验\n" +
                "4. 突出技能的专业深度和实际应用能力\n" +
                "5. 优化语言表达，使描述更加简洁、专业\n" +
                "6. 保持内容的真实性，不过度夸大\n" +
                "请直接返回润色后的完整内容，不要添加解释或前缀。";
        
        final ChatMessage systemMessage = ChatMessage.builder()
                .role(ChatMessageRole.SYSTEM)
                .content(skillPolishPrompt)
                .build();
        messages.add(systemMessage);
        
        // 用户消息，提供原始技能特长内容
        final ChatMessage userMessage = ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content("原始技能特长：\n" + skillContent)
                .build();
        messages.add(userMessage);
        
        // 调用AI润色
        return doChat(userId, messages);
    }

    /**
     * 润色证书奖项描述
     * 
     * @param userId 用户ID
     * @param certificateContent 原始证书奖项内容
     * @return 润色后的证书奖项内容
     */
    public String polishCertificate(Long userId, String certificateContent) {
        // 检查用户AI使用次数
        checkUserAiUsage(userId);
        
        final List<ChatMessage> messages = new ArrayList<>();
        log.info("<润色证书奖项描述:>" + certificateContent);

        // 系统提示词，引导AI润色证书奖项
        final String certificatePolishPrompt = 
                "你是一位专业的简历优化顾问，擅长润色证书奖项描述。请对提供的证书奖项内容进行优化，使其更加专业、有说服力。" +
                "润色时请遵循以下原则：\n" +
                "1. 保持原有证书/奖项名称、颁发机构和获得时间不变\n" +
                "2. 使用更专业、更精确的词汇描述证书/奖项\n" +
                "3. 适当补充证书/奖项的含金量、认可度或竞争情况\n" +
                "4. 对于重要奖项，可强调获奖原因或评选范围\n" +
                "5. 优化排序，将最相关、最重要的证书/奖项放在前面\n" +
                "6. 保持内容的真实性，不过度夸大\n" +
                "请直接返回润色后的完整内容，不要添加解释或前缀。";
        
        final ChatMessage systemMessage = ChatMessage.builder()
                .role(ChatMessageRole.SYSTEM)
                .content(certificatePolishPrompt)
                .build();
        messages.add(systemMessage);
        
        // 用户消息，提供原始证书奖项内容
        final ChatMessage userMessage = ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content("原始证书奖项：\n" + certificateContent)
                .build();
        messages.add(userMessage);
        
        // 调用AI润色
        return doChat(userId, messages);
    }

    /**
     * 润色校园经历描述
     * 
     * @param userId 用户ID
     * @param campusContent 原始校园经历内容
     * @return 润色后的校园经历内容
     */
    public String polishCampus(Long userId, String campusContent) {
        // 检查用户AI使用次数
        checkUserAiUsage(userId);
        
        final List<ChatMessage> messages = new ArrayList<>();
        log.info("<润色校园经历描述:>" + campusContent);

        // 系统提示词，引导AI润色校园经历
        final String campusPolishPrompt = 
                "你是一位专业的简历优化顾问，擅长润色校园经历描述。请对提供的校园经历内容进行优化，使其更加专业、有说服力，并突出个人能力和成果。" +
                "润色时请遵循以下原则：\n" +
                "1. 保持原有活动/组织名称、职位和时间不变\n" +
                "2. 使用更专业、更精确的词汇描述职责和成果\n" +
                "3. 突出在校园活动中培养的软技能（如领导力、团队协作等）\n" +
                "4. 尽可能量化成果和影响力\n" +
                "5. 强调经历与求职目标的相关性\n" +
                "6. 优化语言表达，使描述更加流畅、专业\n" +
                "7. 保持内容的真实性，不过度夸大\n" +
                "请直接返回润色后的完整内容，不要添加解释或前缀。";
        
        final ChatMessage systemMessage = ChatMessage.builder()
                .role(ChatMessageRole.SYSTEM)
                .content(campusPolishPrompt)
                .build();
        messages.add(systemMessage);
        
        // 用户消息，提供原始校园经历内容
        final ChatMessage userMessage = ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content("原始校园经历：\n" + campusContent)
                .build();
        messages.add(userMessage);
        
        // 调用AI润色
        return doChat(userId, messages);
    }

    /**
     * 润色兴趣爱好描述
     * 
     * @param userId 用户ID
     * @param interestContent 原始兴趣爱好内容
     * @return 润色后的兴趣爱好内容
     */
    public String polishInterest(Long userId, String interestContent) {
        // 检查用户AI使用次数
        checkUserAiUsage(userId);
        
        final List<ChatMessage> messages = new ArrayList<>();
        log.info("<润色兴趣爱好描述:>" + interestContent);
        
        // 系统提示词，引导AI润色兴趣爱好
        final String interestPolishPrompt = 
                "你是一位专业的简历优化顾问，擅长润色兴趣爱好描述。请对提供的兴趣爱好内容进行优化，使其更加专业、有吸引力，并能够展示应聘者的个性特点。" +
                "润色时请遵循以下原则：\n" +
                "1. 保持原有兴趣爱好类型不变\n" +
                "2. 使用更生动、更精确的词汇描述兴趣爱好\n" +
                "3. 适当关联兴趣爱好培养的能力与职场技能\n" +
                "4. 展示通过兴趣爱好获得的成就或收获\n" +
                "5. 突出兴趣爱好反映的积极个性特质\n" +
                "6. 优化语言表达，使描述更加简洁、生动\n" +
                "7. 保持内容的真实性，不过度包装\n" +
                "请直接返回润色后的完整内容，不要添加解释或前缀。";
        
        final ChatMessage systemMessage = ChatMessage.builder()
                .role(ChatMessageRole.SYSTEM)
                .content(interestPolishPrompt)
                .build();
        messages.add(systemMessage);
        
        // 用户消息，提供原始兴趣爱好内容
        final ChatMessage userMessage = ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content("原始兴趣爱好：\n" + interestContent)
                .build();
        messages.add(userMessage);
        
        // 调用AI润色
        return doChat(userId, messages);
    }

    /**
     * 润色工作经验描述
     * 
     * @param userId 用户ID
     * @param workContent 原始工作经验内容
     * @return 润色后的工作经验内容
     */
    public String polishWork(Long userId, String workContent) {
        // 检查用户AI使用次数
        checkUserAiUsage(userId);
        
        final List<ChatMessage> messages = new ArrayList<>();
        log.info("<润色工作经验描述:>" + workContent);
        
        // 系统提示词，引导AI润色工作经验
        final String workPolishPrompt = 
                "你是一位专业的简历优化顾问，擅长润色工作经验描述。请对提供的工作经验内容进行优化，使其更加专业、有说服力，并突出个人贡献和成果。" +
                "润色时请遵循以下原则：\n" +
                "1. 保持原有工作单位、职位和时间不变\n" +
                "2. 使用更专业、更精确的词汇描述职责和成果\n" +
                "3. 突出个人在工作中的贡献和解决的关键问题\n" +
                "4. 尽可能量化成果（如提升效率百分比、减少成本等）\n" +
                "5. 使用行动导向的语言，以动词开头描述成就\n" +
                "6. 强调与求职目标相关的技能和经验\n" +
                "7. 优化语言表达，使描述更加流畅、专业\n" +
                "8. 保持内容的真实性，不过度夸大\n" +
                "请直接返回润色后的完整内容，不要添加解释或前缀。";
        
        final ChatMessage systemMessage = ChatMessage.builder()
                .role(ChatMessageRole.SYSTEM)
                .content(workPolishPrompt)
                .build();
        messages.add(systemMessage);
        
        // 用户消息，提供原始工作经验内容
        final ChatMessage userMessage = ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content("原始工作经验：\n" + workContent)
                .build();
        messages.add(userMessage);
        
        // 调用AI润色
        return doChat(userId, messages);
    }

    /**
     * 更通用的方法，允许用户传入任意条消息列表，并扣减AI使用次数
     *
     * @param userId 用户ID
     * @param chatMessageList 消息列表
     * @return AI响应内容
     */
    public String doChat(Long userId, List<ChatMessage> chatMessageList) {
        // 单次调用
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                // 指定您创建的方舟推理接入点 ID，此处已帮您修改为您的推理接入点 ID
                .model(modelName)
                .messages(chatMessageList)
                .build();

        List<ChatCompletionChoice> choiceList = arkService.createChatCompletion(chatCompletionRequest)
                .getChoices();
        if (CollUtil.isEmpty(choiceList)) {
            throw new RuntimeException("AI 没有返回任何内容");
        }
        String content = (String) choiceList.get(0).getMessage().getContent();
        System.out.println("AI 返回内容：" + content);
        
        // 减少用户AI使用次数
        if (userId != null) {
            decreaseUserAiUsage(userId);
        }
        
        return content;
    }
    
    /**
     * 兼容旧版本的doChat方法，不扣减AI使用次数
     *
     * @param chatMessageList 消息列表
     * @return AI响应内容
     */
    public String doChat(List<ChatMessage> chatMessageList) {
        return doChat(null, chatMessageList);
    }
}
