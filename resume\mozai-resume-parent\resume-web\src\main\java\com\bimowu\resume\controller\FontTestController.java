package com.bimowu.resume.controller;

import com.bimowu.resume.utils.ChineseFontFixer;
import com.bimowu.resume.utils.FontUtil;
import com.bimowu.resume.utils.Result;
import com.bimowu.resume.utils.MemoryManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 字体测试控制器
 * 用于测试和调试中文字体显示问题
 */
@RestController
@RequestMapping("/api/font-test")
@Slf4j
public class FontTestController {
    
    /**
     * 测试字体加载状态
     */
    @GetMapping("/status")
    public Result<Map<String, Object>> getFontStatus() {
        log.info("获取字体状态");
        
        Map<String, Object> status = new HashMap<>();
        
        // 检查字体文件是否可用
        status.put("simsun_available", FontUtil.isFontAvailable("SimSun"));
        status.put("yahei_available", FontUtil.isFontAvailable("Microsoft YaHei"));
        status.put("simhei_available", FontUtil.isFontAvailable("SimHei"));
        
        // 获取默认中文字体
        status.put("default_chinese_font", FontUtil.getDefaultChineseFont());
        
        // 测试base64字体生成
        String base64Font = FontUtil.getBase64Font("SimSun");
        status.put("base64_font_available", base64Font != null);
        if (base64Font != null) {
            status.put("base64_font_size", base64Font.length());
        }
        
        // 生成字体CSS
        String fontCSS = FontUtil.getChineseFontCSS();
        status.put("font_css_length", fontCSS.length());
        
        return Result.success(status);
    }
    
    /**
     * 获取字体测试HTML页面
     */
    @GetMapping("/test-page")
    public ResponseEntity<String> getFontTestPage() {
        log.info("生成字体测试页面");
        
        String testHtml = ChineseFontFixer.generateFontTestHtml();
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_HTML);
        headers.add("Content-Disposition", "inline; filename=font-test.html");
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(testHtml);
    }
    
    /**
     * 测试HTML中文字体修复
     */
    @PostMapping("/fix-html")
    public Result<Map<String, Object>> testHtmlFix(@RequestBody Map<String, String> request) {
        String originalHtml = request.get("html");
        if (originalHtml == null || originalHtml.trim().isEmpty()) {
            return Result.error("HTML内容不能为空");
        }
        
        log.info("测试HTML中文字体修复，原始长度: {}", originalHtml.length());
        
        // 检查是否包含中文字符
        boolean containsChinese = ChineseFontFixer.containsChineseCharacters(originalHtml);
        String chineseChars = ChineseFontFixer.extractChineseCharacters(originalHtml);
        
        // 修复HTML
        String fixedHtml = ChineseFontFixer.fixChineseFontInHtml(originalHtml);
        
        Map<String, Object> result = new HashMap<>();
        result.put("original_length", originalHtml.length());
        result.put("fixed_length", fixedHtml.length());
        result.put("contains_chinese", containsChinese);
        result.put("chinese_char_count", chineseChars.length());
        result.put("chinese_chars_sample", chineseChars.length() > 20 ? chineseChars.substring(0, 20) + "..." : chineseChars);
        result.put("fixed_html", fixedHtml);
        
        return Result.success(result);
    }
    
    /**
     * 运行完整的字体测试
     */
    @PostMapping("/run-test")
    public Result<Map<String, Object>> runFontTest() {
        log.info("运行完整的字体测试");
        
        Map<String, Object> testResults = new HashMap<>();
        
        try {
            // 运行字体加载测试
            FontUtil.testFontLoading();
            testResults.put("font_loading_test", "completed");
            
            // 测试字体CSS生成
            String fontCSS = FontUtil.getChineseFontCSS();
            testResults.put("font_css_generated", true);
            testResults.put("font_css_length", fontCSS.length());
            
            // 测试HTML修复
            String testHtml = "<html><body><h1>测试标题</h1><p>这是一个中文测试：你好世界！</p></body></html>";
            String fixedHtml = ChineseFontFixer.fixChineseFontInHtml(testHtml);
            testResults.put("html_fix_test", "completed");
            testResults.put("html_fix_length_increase", fixedHtml.length() - testHtml.length());
            
            // 检查字体文件
            testResults.put("simsun_available", FontUtil.isFontAvailable("SimSun"));
            testResults.put("base64_generation", FontUtil.getBase64Font("SimSun") != null);
            
            testResults.put("test_status", "success");
            testResults.put("message", "所有字体测试完成");
            
        } catch (Exception e) {
            log.error("字体测试失败", e);
            testResults.put("test_status", "failed");
            testResults.put("error_message", e.getMessage());
        }
        
        return Result.success(testResults);
    }
    
    /**
     * 获取字体CSS内容
     */
    @GetMapping("/css")
    public ResponseEntity<String> getFontCSS() {
        log.info("获取字体CSS内容");
        
        String fontCSS = FontUtil.getChineseFontCSS();
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.valueOf("text/css"));
        headers.add("Content-Disposition", "inline; filename=chinese-font.css");
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(fontCSS);
    }
    
    /**
     * 清理和重置字体缓存（如果有的话）
     */
    @PostMapping("/reset")
    public Result<String> resetFontCache() {
        log.info("重置字体缓存");
        
        try {
            // 执行GC清理内存
            MemoryManager.forceGC();
            
            // 重新测试字体加载
            FontUtil.testFontLoading();
            
            return Result.success("字体缓存重置完成");
        } catch (Exception e) {
            log.error("重置字体缓存失败", e);
            return Result.error("重置失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取内存使用情况
     */
    @GetMapping("/memory")
    public Result<MemoryManager.MemoryInfo> getMemoryInfo() {
        log.info("获取内存使用情况");
        
        try {
            MemoryManager.MemoryInfo memoryInfo = MemoryManager.getMemoryInfo();
            return Result.success(memoryInfo);
        } catch (Exception e) {
            log.error("获取内存信息失败", e);
            return Result.error("获取内存信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 强制执行垃圾回收
     */
    @PostMapping("/gc")
    public Result<String> forceGarbageCollection() {
        log.info("强制执行垃圾回收");
        
        try {
            MemoryManager.MemoryInfo beforeGC = MemoryManager.getMemoryInfo();
            MemoryManager.forceGC();
            MemoryManager.MemoryInfo afterGC = MemoryManager.getMemoryInfo();
            
            String message = String.format("GC完成 - GC前使用率: %.1f%%, GC后使用率: %.1f%%", 
                beforeGC.getUsagePercent(), afterGC.getUsagePercent());
            
            return Result.success(message);
        } catch (Exception e) {
            log.error("强制GC失败", e);
            return Result.error("强制GC失败: " + e.getMessage());
        }
    }
}