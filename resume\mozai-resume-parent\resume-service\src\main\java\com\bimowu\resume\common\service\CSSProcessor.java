package com.bimowu.resume.common.service;

import com.bimowu.resume.dto.ExtractedStyles;

/**
 * CSS处理器接口
 * 负责CSS样式的解析、转换和优化
 */
public interface CSSProcessor {
    
    /**
     * 处理Vue样式，转换为标准CSS
     * @param vueCSS Vue样式内容
     * @return 标准CSS
     */
    String processVueStyles(String vueCSS);
    
    /**
     * 优化CSS用于PDF生成
     * @param css CSS内容
     * @return 优化后的CSS
     */
    String optimizeForPDF(String css);
    
    /**
     * 标准化CSS样式
     * @param css CSS内容
     * @return 标准化后的CSS
     */
    String normalizeCSSStyles(String css);
    
    /**
     * 处理响应式CSS，转换为固定尺寸
     * @param css CSS内容
     * @param targetWidth 目标宽度
     * @param targetHeight 目标高度
     * @return 处理后的CSS
     */
    String processResponsiveCSS(String css, int targetWidth, int targetHeight);
    
    /**
     * 合并多个CSS样式
     * @param cssContents CSS内容数组
     * @return 合并后的CSS
     */
    String mergeCSS(String... cssContents);
    
    /**
     * 验证CSS语法
     * @param css CSS内容
     * @return 是否有效
     */
    boolean validateCSS(String css);
    
    /**
     * 提取CSS中的字体信息
     * @param css CSS内容
     * @return 字体信息数组
     */
    ExtractedStyles.FontInfo[] extractFontInfo(String css);
    
    /**
     * 提取CSS中的颜色信息
     * @param css CSS内容
     * @return 颜色调色板
     */
    ExtractedStyles.ColorPalette extractColorPalette(String css);
    
    /**
     * 压缩CSS内容
     * @param css CSS内容
     * @return 压缩后的CSS
     */
    String compressCSS(String css);
    
    /**
     * 添加PDF特定的CSS规则
     * @param css CSS内容
     * @return 添加规则后的CSS
     */
    String addPDFSpecificRules(String css);
    
    /**
     * 处理CSS中的相对路径
     * @param css CSS内容
     * @param basePath 基础路径
     * @return 处理后的CSS
     */
    String resolveRelativePaths(String css, String basePath);
    
    /**
     * 移除不支持的CSS属性
     * @param css CSS内容
     * @return 处理后的CSS
     */
    String removeUnsupportedProperties(String css);
}