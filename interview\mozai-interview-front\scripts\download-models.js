const fs = require('fs');
const path = require('path');
const https = require('https');

// 模型文件列表 - 更新为完整的模型文件列表
const modelFiles = [
  // 微型人脸检测器模型
  'tiny_face_detector_model-shard1',
  'tiny_face_detector_model-weights_manifest.json',
  
  // 人脸特征点模型
  'face_landmark_68_model-shard1',
  'face_landmark_68_model-weights_manifest.json',
  
  // 人脸表情模型
  'face_expression_model-shard1',
  'face_expression_model-weights_manifest.json'
];

// GitHub原始内容URL
const baseUrl = 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights';

// 目标目录
const targetDir = path.resolve(__dirname, '../public/models');
const distDir = path.resolve(__dirname, '../dist-interview-ai/models');

// 确保目录存在
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
  console.log(`目录已创建: ${targetDir}`);
}

if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
  console.log(`目录已创建: ${distDir}`);
}

// 下载文件函数
function downloadFile(url, targetPath) {
  return new Promise((resolve, reject) => {
    console.log(`开始下载: ${url}`);
    
    const file = fs.createWriteStream(targetPath);
    
    const request = https.get(url, response => {
      if (response.statusCode !== 200) {
        reject(new Error(`下载失败，状态码: ${response.statusCode}`));
        return;
      }
      
      const totalBytes = parseInt(response.headers['content-length'] || '0', 10);
      let downloadedBytes = 0;
      
      response.on('data', (chunk) => {
        downloadedBytes += chunk.length;
        if (totalBytes > 0) {
          const percentage = Math.round((downloadedBytes / totalBytes) * 100);
          process.stdout.write(`\r下载进度: ${percentage}% (${downloadedBytes}/${totalBytes} 字节)`);
        }
      });
      
      response.pipe(file);
      
      file.on('finish', () => {
        process.stdout.write('\n');
        file.close();
        
        // 检查文件大小
        const stats = fs.statSync(targetPath);
        if (totalBytes > 0 && stats.size !== totalBytes) {
          reject(new Error(`文件大小不匹配: 期望 ${totalBytes} 字节，实际 ${stats.size} 字节`));
          return;
        }
        
        console.log(`下载完成: ${targetPath} (${stats.size} 字节)`);
        resolve();
      });
    });
    
    request.on('error', err => {
      fs.unlink(targetPath, () => {});
      reject(err);
    });
    
    file.on('error', err => {
      fs.unlink(targetPath, () => {});
      reject(err);
    });
  });
}

// 复制文件函数
function copyFile(source, target) {
  return new Promise((resolve, reject) => {
    console.log(`复制文件: ${source} -> ${target}`);
    
    const rd = fs.createReadStream(source);
    const wr = fs.createWriteStream(target);
    
    rd.on('error', reject);
    wr.on('error', reject);
    wr.on('finish', resolve);
    
    rd.pipe(wr);
  });
}

// 下载所有模型文件
async function downloadAllModels() {
  try {
    console.log('开始下载模型文件...');
    
    for (const file of modelFiles) {
      const url = `${baseUrl}/${file}`;
      const targetPath = path.join(targetDir, file);
      const distPath = path.join(distDir, file);
      
      // 尝试下载，失败时重试
      let success = false;
      let attempts = 0;
      const maxAttempts = 3;
      
      while (!success && attempts < maxAttempts) {
        attempts++;
        try {
          await downloadFile(url, targetPath);
          success = true;
        } catch (error) {
          console.error(`下载失败 (尝试 ${attempts}/${maxAttempts}):`, error.message);
          if (attempts >= maxAttempts) {
            throw new Error(`下载 ${file} 失败，已达到最大重试次数`);
          }
          console.log(`3秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
      
      // 复制到dist目录
      try {
        await copyFile(targetPath, distPath);
        console.log(`已复制到dist目录: ${distPath}`);
      } catch (error) {
        console.error(`复制到dist目录失败:`, error.message);
        throw error;
      }
    }
    
    console.log('所有模型文件下载完成！');
  } catch (error) {
    console.error('下载过程中出错:', error);
    process.exit(1);
  }
}

// 执行下载
downloadAllModels(); 