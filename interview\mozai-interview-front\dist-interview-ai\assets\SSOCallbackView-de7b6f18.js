import{d as l,l as r,m as _,c as S,a as c,u,o as i,_ as d}from"./index-4df1abd4.js";const p={class:"sso-callback"},k=l({__name:"SSOCallbackView",setup(f){const t=u(),n=r();return _(async()=>{console.log("SSO回调组件已加载");try{const o=window.location.href;console.log("处理SSO回调URL:",o);let e=null;const s=o.match(/[?&]token=([^&#]+)/);if(s&&s[1]&&(e=s[1],console.log("在查询参数中找到token:",e)),!e){const a=o.match(/\/token=([^&#\/]+)/);a&&a[1]&&(e=a[1],console.log("在URL路径中找到token:",e))}e?(console.log("保存SSO回调中的token:",e),n.setToken(e),console.log("使用token获取用户信息"),await n.fetchUserInfo(),console.log("跳转到首页"),t.replace("/")):(console.warn("SSO回调URL中未找到token"),await n.fetchUserInfo(),t.replace("/"))}catch(o){console.error("处理SSO回调时出错:",o),t.replace("/")}}),(o,e)=>(i(),S("div",p,e[0]||(e[0]=[c("h1",null,"处理SSO登录...",-1),c("p",null,"正在处理身份验证，请稍候...",-1)])))}});const m=d(k,[["__scopeId","data-v-ab56dd1e"]]);export{m as default};
