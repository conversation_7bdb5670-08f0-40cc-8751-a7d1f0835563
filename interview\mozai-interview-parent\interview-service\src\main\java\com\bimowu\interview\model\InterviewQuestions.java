package com.bimowu.interview.model;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 面试问题表
 * @TableName interview_questions
 */
@TableName(value ="interview_questions")
@Data
public class InterviewQuestions implements Serializable {
    /**
     * 面试ID
     */
    @TableId(value = "interview_id", type = com.baomidou.mybatisplus.annotation.IdType.INPUT)
    private String interviewId;

    /**
     * 问题列表JSON
     */
    private String questions;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}