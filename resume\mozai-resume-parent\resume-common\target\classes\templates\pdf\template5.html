<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 14px;
            font-weight: normal;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 0;
            color: #333;
            line-height: 1.7;
        }

        .resume-template {
            width: 100%;
            font-family: inherit !important;
            color: #333;
            background-color: #fff;
            line-height: 1.7;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
            }
        }

        .resume-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px 30px;
        }

        /* Header Styles */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 15px;
            border-bottom: 1px solid #000;
            margin-bottom: 20px;
        }

        .header-info {
            flex-grow: 1;
            margin-right: 20px;
        }

        .name {
            font-size: 28px;
            font-weight: bold;
            color: #000;
            margin-bottom: 5px;
        }

        .contact-info {
            font-size: 14px;
            color: #333;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .info-item {
            display: flex;
            align-items: center;
        }

        .icon {
            margin-right: 5px;
            color: #666;
            font-size: 13px;
            font-style: bold;
        }

        .avatar-container {
            width: 80px;
            height: 100px;
            overflow: hidden;
            flex-shrink: 0;
            border: 1px solid #ccc;
        }

        .avatar {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Section Title Styles */
        .section-title {
            margin-top: 20px;
            margin-bottom: 12px;
        }

        .section-title h2 {
            font-size: 18px;
            color: #000;
            margin: 0;
            font-weight: bold;
            display: inline-block;
            border-bottom: 2px solid #c0392b;
            padding-bottom: 3px;
        }

        /* Section Content Styles */
        .section-content {
            margin-top: 8px;
            font-size: 14px;
            color: #333;
            line-height: 1.7;
        }

        /* Styles for individual items within sections */
        .education-item,
        .work-item,
        .project-item,
        .skill-item {
            margin-bottom: 15px;
        }

        .education-item:last-child,
        .work-item:last-child,
        .project-item:last-child,
        .skill-item:last-child {
            margin-bottom: 0;
        }

        .item-header {
            justify-content: space-between;
            align-items: baseline;
            margin-bottom: 5px;
            font-size: 15px;
            font-weight: bold;
        }

        .item-header-left {
            display: flex;
            justify-content: space-between;
        }

        .item-time {
            flex-shrink: 0;
            font-weight: bold;
            font-size: 14px;
        }

        /* Education Specific Styles */
        .edu-school {
            color: #000;
            margin-right: 5px;
        }

        .edu-degree {
            font-weight: bold;
            margin-right: 10px;
        }

        .edu-major {
            color: #333;
            font-size: 14px;
            font-weight: bold;
        }

        .edu-courses {
            margin-top: 5px;
        }

        /* Work Specific Styles */
        .work-company {
            color: #000;
            margin-right: 10px;
        }

        .work-position {
            color: #333;
            font-size: 14px;
            font-weight: bold;
        }

        /* Project Specific Styles */
        .project-title {
            color: #000;
            margin-right: 10px;
        }

        .project-tech {
            color: #555;
            font-size: 14px;
            margin-bottom: 5px;
            font-weight: bold;
        }

        /* Common styles for lists */
        ul {
            list-style-type: disc;
            padding-left: 20px;
            margin: 0;
        }

        li {
            margin-bottom: 5px;
            line-height: 1.7;
        }

        /* Common styles for paragraphs */
        p {
            margin: 0 0 5px 0;
            line-height: 1.7;
        }

        strong {
            font-weight: bold;
        }

        em {
            font-style: italic;
        }

        /* Styles for headers in markdown */
        h1, h2, h3 {
            margin-top: 10px;
            margin-bottom: 5px;
            font-weight: bold;
        }

        a {
            color: #4472c4;
            text-decoration: underline;
        }

        /* Skills section styles */
        .skill-description-item {
            margin-bottom: 10px;
        }

        .skill-description-body {
            line-height: 1.7;
            text-align: justify;
        }

        .certificates-section,
        .interests-section {
            margin-top: 20px;
        }

        .section-content ul {
            padding-left: 20px;
            margin: 0;
        }

        .section-content li {
            margin-bottom: 5px;
            line-height: 1.7;
        }

        .section-content p {
            margin: 0 0 5px 0;
            line-height: 1.7;
        }

        /* Skill description styles */
        .skill-item-description {
            margin-bottom: 10px;
            line-height: 1.7;
        }

        /* Evaluation content styles */
        .evaluation-content {
            line-height: 1.7;
            text-align: justify;
        }
    </style>
</head>
<body>
<div class="resume-template template-5">
    <div class="resume-container">
        <!-- 头部信息区域 -->
        <div class="header">
            <div class="header-info">
                <div class="name">${name}</div>
                <div class="contact-info">
                    <span class="info-item"><i class="icon">📞</i> ${phone}</span>
                    <span class="info-item"><i class="icon">📧</i> ${email}</span>
                    <span class="info-item"><i class="icon">🏠</i> ${hometown}</span>
                    <span class="info-item"><i class="icon">🎂</i> 年龄：${age}岁</span>
                </div>
            </div>
            <div class="avatar-container">
                ${avatar}
            </div>
        </div>

        <!-- 教育背景 -->
        ${education}

        <!-- 工作经验 -->
        ${work}

        <!-- 项目经验 -->
        ${projects}

        <!-- 练手项目 -->
        ${practices}

        <!-- 技能特长 -->
        ${skills}

        <!-- 证书奖项 -->
        ${certificates}

        <!-- 兴趣爱好 -->
        ${interests}

        <!-- 个人总结/自我评价 -->
        ${selfEvaluation}
    </div>
</div>
</body>
</html> 