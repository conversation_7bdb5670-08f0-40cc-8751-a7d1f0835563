package com.bimowu.resume.controller;

import com.bimowu.resume.common.service.impl.PDFGenerationStrategy;
import com.bimowu.resume.common.utils.ResumeExportUtilEnhanced;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * PDF系统管理控制器
 * 用于管理PDF生成系统的切换和监控
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/pdf-system")
@Api(tags = "PDF系统管理")
public class PDFSystemController {
    
    @Autowired
    private PDFGenerationStrategy pdfGenerationStrategy;
    
    /**
     * 获取PDF系统状态
     */
    @ApiOperation("获取PDF系统状态")
    @GetMapping("/status")
    public Map<String, Object> getSystemStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            PDFGenerationStrategy.SystemStatus status = ResumeExportUtilEnhanced.getSystemStatus();
            if (status != null) {
                result.put("success", true);
                result.put("data", status);
            } else {
                result.put("success", false);
                result.put("message", "PDF生成策略未初始化");
            }
        } catch (Exception e) {
            log.error("获取PDF系统状态失败", e);
            result.put("success", false);
            result.put("message", "获取状态失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查Playwright状态
     */
    @ApiOperation("检查Playwright PDF生成器状态")
    @GetMapping("/playwright/status")
    public Map<String, Object> getPlaywrightStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里需要注入PlaywrightPDFGenerator来检查状态
            // 由于当前架构限制，我们通过策略来获取信息
            PDFGenerationStrategy.SystemStatus systemStatus = ResumeExportUtilEnhanced.getSystemStatus();
            
            Map<String, Object> playwrightInfo = new HashMap<>();
            playwrightInfo.put("available", false);
            playwrightInfo.put("status", "Unknown");
            playwrightInfo.put("version", "Unknown");
            playwrightInfo.put("message", "Playwright状态检查需要通过系统状态获取");
            
            // 检查系统环境
            String nodeVersion = checkCommand("node --version");
            String npmVersion = checkCommand("npm --version");
            String npxVersion = checkCommand("npx --version");
            String playwrightVersion = checkCommand("npx playwright --version");
            
            Map<String, String> environment = new HashMap<>();
            environment.put("node", nodeVersion != null ? nodeVersion : "Not Available");
            environment.put("npm", npmVersion != null ? npmVersion : "Not Available");
            environment.put("npx", npxVersion != null ? npxVersion : "Not Available");
            environment.put("playwright", playwrightVersion != null ? playwrightVersion : "Not Available");
            
            playwrightInfo.put("environment", environment);
            
            // 提供安装建议
            if (nodeVersion == null) {
                playwrightInfo.put("suggestion", "请安装 Node.js。参考: PLAYWRIGHT_SETUP.md");
            } else if (npxVersion == null) {
                playwrightInfo.put("suggestion", "Node.js 已安装但 npx 不可用。请检查 npm 安装。");
            } else if (playwrightVersion == null) {
                playwrightInfo.put("suggestion", "请安装 Playwright: npm install -g playwright && npx playwright install");
            } else {
                playwrightInfo.put("suggestion", "环境看起来正常。如果仍有问题，请检查权限和路径配置。");
            }
            
            result.put("success", true);
            result.put("data", playwrightInfo);
            
        } catch (Exception e) {
            log.error("检查Playwright状态失败", e);
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查命令是否可用
     */
    private String checkCommand(String command) {
        try {
            ProcessBuilder pb = new ProcessBuilder();
            if (System.getProperty("os.name").toLowerCase().contains("windows")) {
                pb.command("cmd", "/c", command);
            } else {
                pb.command("sh", "-c", command);
            }
            
            Process process = pb.start();
            boolean finished = process.waitFor(5, java.util.concurrent.TimeUnit.SECONDS);
            
            if (finished && process.exitValue() == 0) {
                try (java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(process.getInputStream()))) {
                    return reader.readLine();
                }
            }
        } catch (Exception e) {
            log.debug("检查命令 '{}' 失败: {}", command, e.getMessage());
        }
        return null;
    }
    
    /**
     * 切换到新PDF系统
     */
    @ApiOperation("切换到新PDF系统")
    @PostMapping("/switch-to-new")
    public Map<String, Object> switchToNewSystem() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            ResumeExportUtilEnhanced.switchToNewSystem();
            result.put("success", true);
            result.put("message", "已切换到新PDF生成系统");
            log.info("管理员手动切换到新PDF生成系统");
        } catch (Exception e) {
            log.error("切换到新PDF系统失败", e);
            result.put("success", false);
            result.put("message", "切换失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 切换到旧PDF系统
     */
    @ApiOperation("切换到旧PDF系统")
    @PostMapping("/switch-to-legacy")
    public Map<String, Object> switchToLegacySystem() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            ResumeExportUtilEnhanced.switchToLegacySystem();
            result.put("success", true);
            result.put("message", "已切换到旧PDF生成系统");
            log.info("管理员手动切换到旧PDF生成系统");
        } catch (Exception e) {
            log.error("切换到旧PDF系统失败", e);
            result.put("success", false);
            result.put("message", "切换失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 测试PDF生成
     */
    @ApiOperation("测试PDF生成")
    @PostMapping("/test")
    public Map<String, Object> testPDFGeneration(@RequestParam Long resumeId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里可以添加测试逻辑
            result.put("success", true);
            result.put("message", "PDF生成测试完成");
            result.put("resumeId", resumeId);
        } catch (Exception e) {
            log.error("PDF生成测试失败", e);
            result.put("success", false);
            result.put("message", "测试失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取系统健康状态
     */
    @ApiOperation("获取系统健康状态")
    @GetMapping("/health")
    public Map<String, Object> getHealthStatus() {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> health = new HashMap<>();
        
        try {
            // 检查PDF生成策略
            boolean strategyHealthy = pdfGenerationStrategy != null;
            health.put("pdfGenerationStrategy", strategyHealthy);
            
            // 检查系统状态
            PDFGenerationStrategy.SystemStatus status = ResumeExportUtilEnhanced.getSystemStatus();
            boolean systemHealthy = status != null && !status.isInFallbackMode();
            health.put("systemStatus", systemHealthy);
            
            // 整体健康状态
            boolean overallHealthy = strategyHealthy && systemHealthy;
            
            result.put("success", true);
            result.put("healthy", overallHealthy);
            result.put("details", health);
            
        } catch (Exception e) {
            log.error("获取健康状态失败", e);
            result.put("success", false);
            result.put("healthy", false);
            result.put("message", "健康检查失败: " + e.getMessage());
        }
        
        return result;
    }
}