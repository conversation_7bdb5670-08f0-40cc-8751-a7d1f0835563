package com.bimowu.resume.config;

import com.bimowu.resume.utils.MemoryManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 内存优化的PDF配置
 */
@Configuration
@ConfigurationProperties(prefix = "pdf.memory")
@Slf4j
public class MemoryOptimizedPDFConfig {
    
    /**
     * 最大堆内存使用百分比
     */
    private double maxHeapUsagePercent = 80.0;
    
    /**
     * GC触发阈值
     */
    private double gcThreshold = 70.0;
    
    /**
     * 是否在PDF生成前执行GC
     */
    private boolean gcBeforeGeneration = true;
    
    /**
     * 是否启用内存监控
     */
    private boolean enableMemoryMonitoring = true;
    
    /**
     * 字体缓存大小限制（MB）
     */
    private int fontCacheSize = 2;
    
    /**
     * 是否使用轻量级字体方案
     */
    private boolean useLightweightFonts = true;
    
    /**
     * 最大并发PDF生成数
     */
    private int maxConcurrentGenerations = 3;
    
    /**
     * PDF生成内存预估（MB）
     */
    private int estimatedMemoryPerPDF = 100;
    
    /**
     * 检查是否可以进行PDF生成
     */
    public boolean canGeneratePDF() {
        if (!enableMemoryMonitoring) {
            return true;
        }
        
        MemoryManager.MemoryInfo memoryInfo = MemoryManager.getMemoryInfo();
        
        // 检查内存使用率
        if (memoryInfo.getUsagePercent() > maxHeapUsagePercent) {
            log.warn("内存使用率过高: {:.1f}% > {:.1f}%", 
                memoryInfo.getUsagePercent(), maxHeapUsagePercent);
            
            if (gcBeforeGeneration) {
                log.info("执行GC尝试释放内存");
                MemoryManager.forceGC();
                
                // 重新检查
                memoryInfo = MemoryManager.getMemoryInfo();
                if (memoryInfo.getUsagePercent() > maxHeapUsagePercent) {
                    log.error("GC后内存使用率仍然过高: {:.1f}%", memoryInfo.getUsagePercent());
                    return false;
                }
            } else {
                return false;
            }
        }
        
        // 检查可用内存
        long requiredMemory = estimatedMemoryPerPDF * 1024 * 1024L;
        if (!MemoryManager.hasEnoughMemory(requiredMemory)) {
            log.error("可用内存不足，无法生成PDF");
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取推荐的字体策略
     */
    public FontStrategy getRecommendedFontStrategy() {
        if (!enableMemoryMonitoring) {
            return FontStrategy.EMBEDDED;
        }
        
        MemoryManager.MemoryInfo memoryInfo = MemoryManager.getMemoryInfo();
        
        if (memoryInfo.getUsagePercent() > 75) {
            return FontStrategy.SYSTEM_ONLY;
        } else if (memoryInfo.getUsagePercent() > 60) {
            return FontStrategy.LIGHTWEIGHT;
        } else {
            return useLightweightFonts ? FontStrategy.LIGHTWEIGHT : FontStrategy.EMBEDDED;
        }
    }
    
    /**
     * 字体策略枚举
     */
    public enum FontStrategy {
        EMBEDDED,      // 嵌入字体文件
        LIGHTWEIGHT,   // 轻量级方案
        SYSTEM_ONLY    // 仅使用系统字体
    }
    
    // Getters and Setters
    public double getMaxHeapUsagePercent() {
        return maxHeapUsagePercent;
    }
    
    public void setMaxHeapUsagePercent(double maxHeapUsagePercent) {
        this.maxHeapUsagePercent = maxHeapUsagePercent;
    }
    
    public double getGcThreshold() {
        return gcThreshold;
    }
    
    public void setGcThreshold(double gcThreshold) {
        this.gcThreshold = gcThreshold;
    }
    
    public boolean isGcBeforeGeneration() {
        return gcBeforeGeneration;
    }
    
    public void setGcBeforeGeneration(boolean gcBeforeGeneration) {
        this.gcBeforeGeneration = gcBeforeGeneration;
    }
    
    public boolean isEnableMemoryMonitoring() {
        return enableMemoryMonitoring;
    }
    
    public void setEnableMemoryMonitoring(boolean enableMemoryMonitoring) {
        this.enableMemoryMonitoring = enableMemoryMonitoring;
    }
    
    public int getFontCacheSize() {
        return fontCacheSize;
    }
    
    public void setFontCacheSize(int fontCacheSize) {
        this.fontCacheSize = fontCacheSize;
    }
    
    public boolean isUseLightweightFonts() {
        return useLightweightFonts;
    }
    
    public void setUseLightweightFonts(boolean useLightweightFonts) {
        this.useLightweightFonts = useLightweightFonts;
    }
    
    public int getMaxConcurrentGenerations() {
        return maxConcurrentGenerations;
    }
    
    public void setMaxConcurrentGenerations(int maxConcurrentGenerations) {
        this.maxConcurrentGenerations = maxConcurrentGenerations;
    }
    
    public int getEstimatedMemoryPerPDF() {
        return estimatedMemoryPerPDF;
    }
    
    public void setEstimatedMemoryPerPDF(int estimatedMemoryPerPDF) {
        this.estimatedMemoryPerPDF = estimatedMemoryPerPDF;
    }
}