package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.config.FeatureToggleConfig;
import com.bimowu.resume.dto.ResumeFullSaveDto;
import com.bimowu.resume.utils.HtmlTemplateUtil;
import com.itextpdf.text.Document;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * PDF生成策略服务
 * 负责根据配置选择合适的PDF生成方式
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PDFGenerationStrategy {

    @Autowired
    private FeatureToggleConfig featureConfig;

    @Autowired(required = false)
    private FlyingSaucerPDFGenerator flyingSaucerGenerator;

    /**
     * 失败计数器 - 用于降级判断
     */
    private final ConcurrentHashMap<String, AtomicInteger> failureCounters = new ConcurrentHashMap<>();

    /**
     * 降级状态 - 记录哪些功能已降级
     */
    private final ConcurrentHashMap<String, Long> fallbackStatus = new ConcurrentHashMap<>();

    /**
     * 生成PDF
     *
     * @param resume 简历数据
     * @return PDF字节数组
     */
    public byte[] generatePDF(ResumeFullSaveDto resume) throws Exception {
//        Long userId = resume.getResumeVo() != null ? resume.getResumeVo().getUserId() : null;
        Long templateId = resume.getResumeVo() != null ? resume.getResumeVo().getTemplateId() : 1;
//
//        // 判断是否应该使用新系统
//        boolean useNewSystem = shouldUseNewSystem(userId, templateId);
//
//        if (true && !isInFallbackMode("flying-saucer")) {
        try {
            log.debug("使用Flying Saucer生成PDF - 模板ID: {}", templateId);
            return generateWithFlyingSaucer(resume, templateId);
        } catch (Exception e) {
            log.warn("Flying Saucer生成失败，降级到XMLWorker - 错误: {}", e.getMessage());
            recordFailure("flying-saucer");
            return generateWithXMLWorker(resume, templateId);
        }
//        } else {
//            log.debug("使用XMLWorker生成PDF - 用户ID: {}, 模板ID: {}", userId, templateId);
//            return generateWithXMLWorker(resume, templateId);
//        }
    }

    /**
     * 判断是否应该使用新系统
     */
    private boolean shouldUseNewSystem(Long userId, Integer templateId) {
        // 检查功能开关
        if (!featureConfig.getPdf().isEnableFlyingSaucer()) {
            return false;
        }

        // 检查A/B测试配置
        if (featureConfig.getAbTest().isEnabled()) {
            return shouldUseNewSystemForAbTest(userId);
        }

        return true;
    }

    /**
     * A/B测试判断逻辑
     */
    private boolean shouldUseNewSystemForAbTest(Long userId) {
        if (userId == null) {
            return false;
        }

        // 检查测试用户白名单
        String testUserIds = featureConfig.getAbTest().getTestUserIds();
        if (!testUserIds.isEmpty()) {
            List<String> testUsers = Arrays.asList(testUserIds.split(","));
            if (testUsers.contains(userId.toString())) {
                return true;
            }
        }

        // 检查旧系统白名单
        String whitelistUserIds = featureConfig.getAbTest().getWhitelistUserIds();
        if (!whitelistUserIds.isEmpty()) {
            List<String> whitelistUsers = Arrays.asList(whitelistUserIds.split(","));
            if (whitelistUsers.contains(userId.toString())) {
                return false;
            }
        }

        // 根据用户ID和流量比例判断
        int trafficPercent = featureConfig.getAbTest().getNewSystemTrafficPercent();
        return (userId % 100) < trafficPercent;
    }

    /**
     * 使用Flying Saucer生成PDF
     */
    private byte[] generateWithFlyingSaucer(ResumeFullSaveDto resume, Long templateId) throws Exception {
        if (flyingSaucerGenerator == null) {
            throw new IllegalStateException("Flying Saucer生成器未初始化");
        }

        // 读取HTML模板并填充内容
        String templateHtml = HtmlTemplateUtil.readTemplate(templateId);
        String filledHtml = HtmlTemplateUtil.fillTemplate(templateHtml, resume);

        // 使用Flying Saucer生成PDF
        return flyingSaucerGenerator.generatePDFForTemplate(filledHtml);
    }

    /**
     * 使用XMLWorker生成PDF（降级方案）
     */
    private byte[] generateWithXMLWorker(ResumeFullSaveDto resume, Long templateId) throws Exception {
        // 读取HTML模板并填充内容
        String templateHtml = HtmlTemplateUtil.readTemplate(templateId);
        String filledHtml = HtmlTemplateUtil.fillTemplate(templateHtml, resume);

        // 使用XMLWorker生成PDF
        Document document = new Document(PageSize.A4);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PdfWriter writer = PdfWriter.getInstance(document, baos);
        document.open();

        XMLWorkerHelper.getInstance().parseXHtml(
                writer,
                document,
                new ByteArrayInputStream(filledHtml.getBytes(StandardCharsets.UTF_8)),
                StandardCharsets.UTF_8
        );

        document.close();
        return baos.toByteArray();
    }

    /**
     * 记录失败次数
     */
    private void recordFailure(String component) {
        AtomicInteger counter = failureCounters.computeIfAbsent(component, k -> new AtomicInteger(0));
        int failures = counter.incrementAndGet();

        int threshold = featureConfig.getPdf().getFallbackThreshold();
        if (failures >= threshold) {
            // 进入降级模式
            long fallbackUntil = System.currentTimeMillis() +
                    featureConfig.getPdf().getFallbackWindowMinutes() * 60 * 1000L;
            fallbackStatus.put(component, fallbackUntil);

            log.warn("组件 {} 连续失败 {} 次，进入降级模式，持续到: {}",
                    component, failures, new java.util.Date(fallbackUntil));

            // 重置计数器
            counter.set(0);
        }
    }

    /**
     * 检查是否处于降级模式
     */
    private boolean isInFallbackMode(String component) {
        Long fallbackUntil = fallbackStatus.get(component);
        if (fallbackUntil == null) {
            return false;
        }

        if (System.currentTimeMillis() > fallbackUntil) {
            // 降级时间已过，移除降级状态
            fallbackStatus.remove(component);
            return false;
        }

        return true;
    }

    /**
     * 手动切换到新系统
     */
    public void switchToNewSystem() {
        fallbackStatus.clear();
        failureCounters.clear();
        log.info("手动切换到新PDF生成系统");
    }

    /**
     * 手动切换到旧系统
     */
    public void switchToLegacySystem() {
        fallbackStatus.put("flying-saucer", Long.MAX_VALUE);
        log.info("手动切换到旧PDF生成系统");
    }

    /**
     * 获取系统状态
     */
    public SystemStatus getSystemStatus() {
        SystemStatus status = new SystemStatus();
        status.setNewSystemEnabled(featureConfig.getPdf().isEnableFlyingSaucer());
        status.setAbTestEnabled(featureConfig.getAbTest().isEnabled());
        status.setTrafficPercent(featureConfig.getAbTest().getNewSystemTrafficPercent());
        status.setInFallbackMode(isInFallbackMode("flying-saucer"));
        status.setFailureCount(failureCounters.getOrDefault("flying-saucer", new AtomicInteger(0)).get());
        return status;
    }

    /**
     * 系统状态类
     */
    public static class SystemStatus {
        private boolean newSystemEnabled;
        private boolean abTestEnabled;
        private int trafficPercent;
        private boolean inFallbackMode;
        private int failureCount;

        // Getters and Setters
        public boolean isNewSystemEnabled() { return newSystemEnabled; }
        public void setNewSystemEnabled(boolean newSystemEnabled) { this.newSystemEnabled = newSystemEnabled; }

        public boolean isAbTestEnabled() { return abTestEnabled; }
        public void setAbTestEnabled(boolean abTestEnabled) { this.abTestEnabled = abTestEnabled; }

        public int getTrafficPercent() { return trafficPercent; }
        public void setTrafficPercent(int trafficPercent) { this.trafficPercent = trafficPercent; }

        public boolean isInFallbackMode() { return inFallbackMode; }
        public void setInFallbackMode(boolean inFallbackMode) { this.inFallbackMode = inFallbackMode; }

        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }
    }
}