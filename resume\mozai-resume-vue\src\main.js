import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import router from './router'

// 导入表单组件
import BasicForm from './components/basic-form.vue'
import JobForm from './components/job-form.vue'
import EducationForm from './components/education-form.vue'
import SkillsForm from './components/skills-form.vue'
import SelfEvaluationForm from './components/self-evaluation-form.vue'
import WorkForm from './components/work-form.vue'
import InternshipForm from './components/internship-form.vue'
import ProjectsForm from './components/projects-form.vue'
import CertificatesForm from './components/certificates-form.vue'
import PracticesForm from "@/components/practices-form.vue";
import CampusForm from './components/campus-form.vue'
import InterestsForm from './components/interests-form.vue'

import './assets/main.css'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册表单组件
app.component('basic-form', BasicForm)
app.component('job-form', JobForm)
app.component('education-form', EducationForm)
app.component('skills-form', SkillsForm)
app.component('self-evaluation-form', SelfEvaluationForm)
app.component('work-form', WorkForm)
app.component('internship-form', InternshipForm)
app.component('projects-form', ProjectsForm)
app.component('certificates-form', CertificatesForm)
app.component('campus-form', CampusForm)
app.component('interests-form', InterestsForm)
app.component('practices-form', PracticesForm)

app.mount('#app') 