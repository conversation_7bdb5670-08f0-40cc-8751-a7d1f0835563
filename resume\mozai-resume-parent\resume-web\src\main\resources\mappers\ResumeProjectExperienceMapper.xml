<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.resume.common.dao.ResumeProjectExperienceMapper">

    <select id="selectByResumeId" resultType="com.bimowu.resume.vo.ResumeProjectExperienceVo"
            parameterType="java.lang.Long">
        SELECT 
            exp_id as expId,
            resume_id as resumeId,
            project_id as proId,
            time_period as timePeriod,
            position_type as positionType,
            project_name as projectName,
            role,
            project_description as projectDescription
        FROM resume_project_experience 
        WHERE resume_id = #{resumeId} AND is_delete = 0
    </select>
</mapper>
