<template>
  <div class="form-container">
    <div v-if="showEditor">
      <div class="editor-header">
        <div class="editor-actions">
          <el-button type="success" size="small" @click="polishCampsContent" :disabled="!formData.content">
            <el-icon class="icon"><magic-stick /></el-icon> AI润色
          </el-button>
        </div>
      </div>
      
      <div class="editor-content">
        <MdEditor
          v-model="formData.content"
          height="300px"
          :toolbars="toolbars"
          :preview="true"
          :previewTheme="'default'"
          :showCodeRowNumber="true"
          :previewOnly="false"
          :previewWidth="'50%'"
          @onSave="saveForm"
          @change="handleContentChange"
          placeholder="请输入您的校园经历及相关描述..."
        />
      </div>
      
      <div class="tips">
        <el-alert
          title="撰写建议"
          type="info"
          :closable="false"
          show-icon
        >
          <div class="tips-content">
            <p>1. 描述您参与的学生组织、社团活动、校园项目等经历</p>
            <p>2. 可以提及担任的职务、完成的任务和取得的成果</p>
            <p>3. 展示校园经历如何培养了您的领导力、团队协作能力等</p>
            <p>4. 保持简洁，通常100-200字为宜</p>
            <p>5. 可以使用加粗、斜体等富文本格式增强表现力</p>
          </div>
        </el-alert>
      </div>
    </div>
    
    <!-- AI润色对话框 -->
    <el-dialog
      v-model="polishDialogVisible"
      title="AI润色"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="polishLoading" class="loading-container">
        <el-skeleton :rows="5" animated />
        <div class="loading-text">AI正在润色您的内容...</div>
      </div>
      <div v-else>
        <div class="polish-result">
          <div class="original-content">
            <h4>原始内容</h4>
            <div class="content-box" v-html="markdownToHtml(formData.content)"></div>
          </div>
          <div class="polished-content">
            <h4>润色后内容</h4>
            <div class="content-box" v-html="markdownToHtml(polishedContent)"></div>
          </div>
        </div>
        <div class="polish-options">
          <h4>请选择要使用的版本</h4>
          <el-radio-group v-model="selectedVersion">
            <el-radio :label="0">使用原始内容</el-radio>
            <el-radio :label="1">使用润色后内容</el-radio>
          </el-radio-group>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="polishDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyPolishedContent" :disabled="polishLoading">
            应用所选内容
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { MdEditor } from 'md-editor-v3'
import { MagicStick, Close, EditPen } from '@element-plus/icons-vue'
import { polishCampus } from '../api/resume'
import 'md-editor-v3/lib/style.css'

const props = defineProps({
  data: {
    type: [String, Object],
    required: true
  }
})

const emit = defineEmits(['update'])

// 控制编辑器显示/隐藏
const showEditor = ref(false)

// 暴露showEditor变量，让父组件可以访问
defineExpose({
  showEditor
})

// 富文本编辑器配置
const toolbars = [
  'bold',
  'italic',
  'strikethrough',
  'title',
  'sub',
  'sup',
  'quote',
  'unorderedList',
  'orderedList',
  'codeRow',
  'link',
  'save',
  'pageFullscreen',
  'fullscreen',
  'preview',
]

const preview = ref(true)

// 表单数据
const formData = ref({
  content: ''
})

// AI润色相关
const polishDialogVisible = ref(false)
const polishLoading = ref(false)
const polishedContent = ref('')
const selectedVersion = ref(1)

// 初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal) {
    // 如果是对象且有description属性，使用description
    if (typeof newVal === 'object' && newVal !== null) {
      formData.value.content = newVal.description || ''
    } else {
      // 否则直接使用值（字符串）
      formData.value.content = newVal || ''
    }
  } else {
    formData.value.content = ''
  }
}, { immediate: true, deep: true })

// 监听内容变化，实时更新
watch(() => formData.value.content, (newVal) => {
  emit('update', newVal)
}, { immediate: false })

// 隐藏编辑器
const hideEditor = () => {
  showEditor.value = false
  saveForm()
}

// Markdown转HTML的简单实现
const markdownToHtml = (markdown) => {
  if (!markdown) return '';
  return markdown
    // 处理加粗
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // 处理斜体
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // 处理标题
    .replace(/^### (.*?)$/gm, '<h3>$1</h3>')
    .replace(/^## (.*?)$/gm, '<h2>$1</h2>')
    .replace(/^# (.*?)$/gm, '<h1>$1</h1>')
    // 处理列表
    .replace(/^\- (.*?)$/gm, '<li>$1</li>')
    .replace(/(<\/li>\n<li>)/g, '</li><li>')
    .replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>')
    // 处理段落和换行
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>')
    .replace(/^(.+)$/, '<p>$1</p>');
}

// 润色内容
const polishCampsContent = () => {
  if (!formData.value.content) {
    ElMessage.warning('请先输入内容再使用AI润色功能')
    return
  }
  
  polishDialogVisible.value = true
  polishLoading.value = true
  
  // 调用后端AI润色API
  polishCampus(formData.value.content).then(res => {
    if (res.code === 0 || res.code === 200) {
      polishedContent.value = res.data
    } else {
      // 显示后端返回的具体错误消息
      const errorMsg = res.message || res.msg || '润色失败'
      ElMessage.error(errorMsg)
      polishedContent.value = formData.value.content
    }
    polishLoading.value = false
  }).catch(error => {
    console.error('润色请求失败', error)
    // 显示具体的错误消息
    ElMessage.error(error.message || '润色请求失败，请稍后重试')
    polishedContent.value = formData.value.content
    polishLoading.value = false
  })
}

// 应用润色后的内容
const applyPolishedContent = () => {
  if (selectedVersion.value === 1) {
    formData.value.content = polishedContent.value
  }
  polishDialogVisible.value = false
  ElMessage.success('已应用所选内容')
}

// 保存表单
const saveForm = () => {
  emit('update', formData.value.content)
  ElMessage.success('校园经历信息已保存')
}

// 重置表单
const resetForm = () => {
  formData.value.content = props.data || ''
  ElMessage.info('已重置为上次保存的内容')
}

// 处理内容变化
const handleContentChange = (newContent) => {
  formData.value.content = newContent
  emit('update', newContent)
}
</script>

<style scoped>
.form-container {
  /* 删除padding属性，使宽度与技能特长模块一致 */
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.editor-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.editor-actions {
  display: flex;
  gap: 10px;
}

.icon {
  margin-right: 5px;
}

.editor-content {
  margin-bottom: 20px;
}

.tips {
  margin: 20px 0;
}

.tips-content p {
  margin: 5px 0;
}

.loading-container {
  padding: 20px;
}

.loading-text {
  text-align: center;
  margin-top: 20px;
  color: #909399;
  font-size: 14px;
}

.polish-result {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.original-content,
.polished-content {
  flex: 1;
}

.original-content h4,
.polished-content h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
}

.content-box {
  padding: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f8f9fb;
  min-height: 150px;
  line-height: 1.6;
}

.polish-options {
  margin-top: 20px;
}

.polish-options h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style> 