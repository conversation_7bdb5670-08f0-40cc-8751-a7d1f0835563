<template>
  <div class="form-container">
    <div class="projects-header">
      <h3>添加项目经验</h3>
      <el-button type="primary" size="small" @click="showProjectSelectionDialog">添加项目</el-button>
    </div>
    
    <div v-if="formData.length === 0" class="empty-projects">
      <el-empty description="暂无项目经验，请点击'添加项目'按钮添加">
        <el-button type="primary" @click="showProjectSelectionDialog">添加项目</el-button>
      </el-empty>
    </div>
    
    <div v-else class="projects-list">
      <div v-for="(project, index) in formData" :key="index" class="project-item">
        <div class="project-content">
          <div class="project-name">{{ project.name }}</div>
          <div class="project-role">{{ project.role }}</div>
          <div class="project-period">
            {{ project.startDate }} - {{ project.endDate || '至今' }}
          </div>
          <div v-if="project.description" class="project-description">
            {{ project.description.substring(0, 80) + (project.description.length > 80 ? '...' : '') }}
          </div>
          <div v-if="project.selectedItems && project.selectedItems.length" class="project-items">
            <div v-for="(item, itemIndex) in project.selectedItems" :key="itemIndex" class="project-item-tag">
              <el-tag size="small">{{ item }}</el-tag>
            </div>
          </div>
        </div>
        <div class="project-actions">
          <el-button type="primary" size="small" plain @click="editProject(index)">编辑</el-button>
          <el-button type="danger" size="small" plain @click="removeProject(index)">删除</el-button>
        </div>
      </div>
    </div>
    
    <!-- 项目选择对话框 -->
    <el-dialog
      v-model="projectSelectionDialogVisible"
      title="选择项目"
      width="650px"
    >
      <div class="project-selection-toolbar">
        <el-select v-model="jobTypeFilter" placeholder="选择职位类型" clearable @change="filterProjects" style="width: 180px;">
          <el-option
            v-for="jobType in jobTypes"
            :key="jobType"
            :label="jobType"
            :value="jobType"
          />
        </el-select>
        
        <el-select
          v-model="selectedProjectIds"
          multiple
          collapse-tags
          collapse-tags-tooltip
          placeholder="选择项目"
          style="width: 380px; margin-left: 10px;"
        >
          <el-option
            v-for="project in filteredProjects"
            :key="project.id"
            :label="project.name"
            :value="project.id"
          >
            <div class="project-option">
              <span>{{ project.name }}</span>
              <el-tag size="small">{{ project.jobType }}</el-tag>
            </div>
            <div class="project-option-desc">{{ project.shortDescription }}</div>
          </el-option>
        </el-select>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="projectSelectionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="showProjectItemsDialog" :disabled="selectedProjectIds.length === 0">
            选择项目内容条款 (已选择 {{ selectedProjectIds.length }} 个项目)
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 项目内容条款选择对话框 -->
    <el-dialog
      v-model="projectItemsDialogVisible"
      title="选择项目内容条款"
      width="700px"
    >
      <div v-if="currentProjectItems.length === 0" class="empty-items">
        <el-empty description="该项目没有可选的内容条款"></el-empty>
      </div>
      
      <div v-else class="project-items-container">
        <el-alert
          title="请为每个项目选择相关的内容条款"
          type="info"
          :closable="false"
          style="margin-bottom: 15px;"
        ></el-alert>
        
        <div v-for="(project, index) in selectedProjects" :key="project.id" class="project-items-section">
          <h4>{{ project.name }}</h4>
          <el-divider></el-divider>
          
          <el-checkbox-group v-model="selectedItemsMap[project.id]" class="items-checkbox-group">
            <div v-for="(item, itemIndex) in projectItemsLibrary[project.jobType] || []" :key="itemIndex" class="item-checkbox-row">
              <el-checkbox :label="item">
                {{ item }}
              </el-checkbox>
            </div>
          </el-checkbox-group>
          
          <div style="margin-top: 15px;" v-if="index < selectedProjects.length - 1">
            <el-divider content-position="center">下一个项目</el-divider>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="backToProjectSelection">返回项目选择</el-button>
          <el-button type="primary" @click="addProjectsWithItems">
            完成并添加所选项目
          </el-button>
        </span>
      </template>
    </el-dialog>

    <div class="form-actions">
      <el-button type="primary" @click="saveForm">保存所有项目</el-button>
      <el-button @click="resetForm">重置</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, watch, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = ref([])

// 项目选择对话框
const projectSelectionDialogVisible = ref(false)
const jobTypeFilter = ref('')
const selectedProjectIds = ref([])

// 内容条款选择对话框
const projectItemsDialogVisible = ref(false)
const selectedItemsMap = reactive({})

// 职位类型列表 mock
// const jobTypes = [
//   '前端开发',
//   '后端开发',
//   '全栈开发',
//   '移动端开发',
//   '数据分析',
//   '人工智能',
//   'UI/UX设计',
//   '产品经理',
//   '项目经理',
//   '测试工程师',
//   '运维工程师',
//   '网络安全'
// ]

// 项目库 - 实际项目中应该从API获取
const projectLibrary = [
  {
    id: 1,
    name: '企业官网重构项目',
    jobType: '前端开发',
    shortDescription: '使用Vue.js和Element UI重构企业官网，提高用户体验和加载速度',
    descriptions: [
      '负责企业官网前端重构，使用Vue.js框架和Element UI组件库开发',
      '实现响应式布局，确保在不同设备上的良好展示效果',
      '优化前端性能，减少页面加载时间，提升用户体验'
    ]
  },
  {
    id: 2,
    name: '在线教育平台开发',
    jobType: '全栈开发',
    shortDescription: '使用Vue.js和Spring Boot开发在线教育平台，包含课程管理、学习跟踪等功能',
    descriptions: [
      '参与在线教育平台的全栈开发，负责前端和部分后端功能实现',
      '使用Vue.js开发前端界面，实现课程展示、视频播放、学习进度跟踪等功能',
      '使用Spring Boot开发后端API，处理用户认证、数据存储等核心业务'
    ]
  },
  {
    id: 3,
    name: '电商移动应用开发',
    jobType: '移动端开发',
    shortDescription: '使用React Native开发跨平台电商移动应用，包含商品浏览、购物车、支付等功能',
    descriptions: [
      '负责电商移动应用的React Native开发，实现跨平台部署',
      '开发商品展示、分类、搜索等核心购物功能',
      '实现购物车、下单、支付等电商交易流程'
    ]
  }
]

// 项目内容条款库 - 按职位类型分类
const projectItemsLibrary = {
  '前端开发': [
    '使用Vue.js构建单页面应用',
    '使用React开发组件化界面',
    '实现响应式布局兼容多种设备',
    '优化前端性能提升页面加载速度',
    '开发自定义UI组件库',
    '使用Webpack优化构建流程',
    '实现前端数据可视化',
    '开发PWA应用提升用户体验',
    '使用TypeScript增强代码健壮性',
    '集成第三方API和服务'
  ],
  '后端开发': [
    '使用Spring Boot构建微服务架构',
    '使用Redis做数据缓存',
    '使用MySQL设计数据库结构',
    '使用MongoDB存储非结构化数据',
    '实现RESTful API接口',
    '使用Security实现权限控制',
    '编写单元测试和集成测试',
    '使用Docker容器化部署',
    '实现分布式事务处理',
    '优化SQL查询提升性能'
  ],
  '全栈开发': [
    '前后端分离架构设计',
    '实现JWT认证授权机制',
    '使用WebSocket实现实时通信',
    '搭建CI/CD自动化部署流程',
    '前端界面和后端API协同开发',
    '数据库设计和优化',
    '实现高并发处理机制',
    '系统安全防护和加密',
    '第三方支付系统集成',
    '云服务架构设计与实现'
  ],
  '移动端开发': [
    '使用React Native开发跨平台应用',
    '使用Flutter构建原生体验应用',
    '实现移动端数据离线存储',
    '优化移动应用启动速度和性能',
    '集成推送通知功能',
    '实现移动支付功能',
    '使用原生模块增强功能',
    '适配不同尺寸和分辨率设备',
    '实现手势操作和动画效果',
    '移动应用安全性加固'
  ],
  '数据分析': [
    '使用Python进行数据清洗和预处理',
    '构建数据可视化报表',
    '实现用户行为分析系统',
    '开发数据挖掘算法',
    '构建推荐系统',
    '使用机器学习预测模型',
    '实时数据处理和分析',
    '建立数据质量监控体系',
    '设计数据仓库结构',
    'A/B测试设计与分析'
  ],
  '人工智能': [
    '开发深度学习模型',
    '实现自然语言处理功能',
    '构建计算机视觉系统',
    '训练和优化机器学习模型',
    '实现语音识别系统',
    '开发对话机器人',
    '图像识别与处理',
    '构建神经网络架构',
    '实现强化学习算法',
    '模型部署与工程化'
  ],
}

// 过滤项目列表
const filteredProjects = computed(() => {
  let result = projectLibrary

  if (jobTypeFilter.value) {
    result = result.filter(project => project.jobType === jobTypeFilter.value)
  }

  return result
})

// 获取当前选中的项目
const selectedProjects = computed(() => {
  return selectedProjectIds.value.map(id => 
    projectLibrary.find(project => project.id === id)
  ).filter(Boolean)
})

// 获取当前项目可选择的内容条款
const currentProjectItems = computed(() => {
  if (selectedProjects.value.length === 0) return []
  
  // 合并所有选中项目的职位类型对应的内容条款
  const items = new Set()
  selectedProjects.value.forEach(project => {
    const jobTypeItems = projectItemsLibrary[project.jobType] || []
    jobTypeItems.forEach(item => items.add(item))
  })
  
  return Array.from(items)
})

// 初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && newVal.length) {
    formData.value = JSON.parse(JSON.stringify(newVal))
  } else {
    formData.value = []
  }
}, { immediate: true, deep: true })

// 显示项目选择对话框
const showProjectSelectionDialog = () => {
  selectedProjectIds.value = []
  jobTypeFilter.value = ''
  projectSelectionDialogVisible.value = true
}

// 显示项目内容条款选择对话框
const showProjectItemsDialog = () => {
  // 清空之前选择的条款
  selectedProjectIds.value.forEach(id => {
    selectedItemsMap[id] = []
  })
  
  projectSelectionDialogVisible.value = false
  projectItemsDialogVisible.value = true
}

// 返回项目选择对话框
const backToProjectSelection = () => {
  projectItemsDialogVisible.value = false
  projectSelectionDialogVisible.value = true
}

// 添加项目及选中的内容条款
const addProjectsWithItems = () => {
  projectItemsDialogVisible.value = false
  
  let newProjects = []
  selectedProjectIds.value.forEach(id => {
    const project = projectLibrary.find(p => p.id === id)
    if (project) {
      newProjects.push({
        originalId: project.id,
        name: project.name,
        role: '开发者', // 默认角色
        startDate: '',
        endDate: '',
        description: project.shortDescription,
        selectedItems: selectedItemsMap[id] || []
      })
    }
  })
  
  formData.value = [...formData.value, ...newProjects]
  ElMessage.success(`已添加 ${newProjects.length} 个项目`)
}

// 编辑项目
const editProject = (index) => {
  ElMessage.info('编辑项目功能暂未实现')
}

// 移除项目
const removeProject = (index) => {
  formData.value.splice(index, 1)
  ElMessage.success('项目已删除')
}

// 过滤项目
const filterProjects = () => {
  // 由computed属性自动处理
}

// 保存表单
const saveForm = () => {
  emit('update', formData.value)
  ElMessage.success('项目经验信息已保存')
}

// 重置表单
const resetForm = () => {
  if (props.data && props.data.length) {
    formData.value = JSON.parse(JSON.stringify(props.data))
  } else {
    formData.value = []
  }
  ElMessage.info('已重置为上次保存的内容')
}
</script>

<style scoped>
.form-container {
  padding: 20px;
}

.projects-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.projects-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.empty-projects {
  margin: 40px 0;
  text-align: center;
}

.projects-list {
  margin-bottom: 20px;
}

.project-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 10px;
  background-color: #f8f9fc;
  transition: all 0.3s;
}

.project-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.project-content {
  flex: 1;
}

.project-name {
  font-weight: 500;
  margin-bottom: 5px;
  color: #303133;
}

.project-role, 
.project-period, 
.project-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.project-items {
  margin-top: 8px;
}

.project-item-tag {
  margin-bottom: 6px;
}

.project-actions {
  display: flex;
  gap: 10px;
}

.project-selection-toolbar {
  display: flex;
  margin-bottom: 15px;
}

.project-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.project-option-desc {
  font-size: 12px;
  color: #909399;
  white-space: normal;
  line-height: 1.4;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.project-items-container {
  max-height: 500px;
  overflow-y: auto;
  padding: 10px;
}

.project-items-section {
  margin-bottom: 20px;
}

.project-items-section h4 {
  margin: 0;
  margin-bottom: 10px;
  color: #303133;
}

.items-checkbox-group {
  display: flex;
  flex-direction: column;
}

.item-checkbox-row {
  margin-bottom: 8px;
}

.empty-items {
  padding: 30px 0;
}
</style>
