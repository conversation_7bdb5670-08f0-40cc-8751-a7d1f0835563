import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';
import { ElMessage } from 'element-plus';
import router from '../router';

// 创建一个自定义的axios实例，用于调用面试服务接口
const http = axios.create({
    baseURL: 'https://ceping.bimowo.com/interview',  // 直接指向后端服务地址
    timeout: 120000, // 120秒
    withCredentials: true  // 添加跨域请求时发送cookie
});

// 创建一个自定义的axios实例，用于调用统一认证服务
export const unifiedHttp = axios.create({
    baseURL: 'https://ceping.bimowo.com/unified',  // 统一认证服务地址
    timeout: 120000, // 120秒
    withCredentials: true  // 添加跨域请求时发送cookie
});

// 配置请求拦截器的工厂函数
const createRequestInterceptor = (instance: AxiosInstance) => {
    instance.interceptors.request.use(
        (config: InternalAxiosRequestConfig) => {
            if (config.url) {
                console.log('Request URL:', config.url);
                if (config.baseURL) {
                    console.log('Full URL:', config.baseURL + config.url);
                }
            }

            // 确保请求头中包含Referer和Origin
            if (config.headers) {
                if (!config.headers['Referer']) {
                    config.headers['Referer'] = window.location.href;
                }
                if (!config.headers['Origin']) {
                    config.headers['Origin'] = window.location.origin;
                }

                // 添加更多请求头
                config.headers['X-Requested-With'] = 'XMLHttpRequest';
                config.headers['Accept'] = 'application/json';

                // 从本地存储中获取token
                const token = localStorage.getItem('token');

                // 如果存在token，则在请求头中携带
                if (token) {
                    console.log('在请求头中添加token:', token);
                    config.headers['Authorization'] = `Bearer ${token}`;
                    // 也设置自定义头 'token'，确保与后端LoginFilter的getToken方法匹配
                    config.headers['token'] = token;
                } else {
                    console.log('本地存储中没有token');
                }
            }

            return config;
        },
        (error: AxiosError) => {
            console.error('请求错误：', error);
            return Promise.reject(error);
        }
    );
};

// 配置响应拦截器的工厂函数
const createResponseInterceptor = (instance: AxiosInstance) => {
    instance.interceptors.response.use(
        (response: AxiosResponse) => {
            console.log('Response received:', response);

            // 检查响应状态码
            if (response.status === 200) {
                // 检查业务状态码
                if (response.data && response.data.code) {
                    // 处理未授权情况
                    if (response.data.code === 401) {
                        console.log('检测到未授权响应，状态码401');

                        // 检查是否有重定向URL
                        if (response.data.data && response.data.data.redirectUrl) {
                            const redirectUrl = response.data.data.redirectUrl;
                            console.log('检测到重定向URL:', redirectUrl);

                            // 执行重定向
                            console.log('正在重定向到:', redirectUrl);
                            window.location.href = redirectUrl;
                            return Promise.reject(new Error('未授权，正在重定向到登录页面'));
                        }
                    } else if (response.data.code !== 0 && response.data.code !== 200) {
                        // 处理其他业务错误
                        const errorMsg = response.data.message || response.data.msg || '请求失败';
                        console.error('业务错误:', errorMsg);
                        ElMessage.error(errorMsg);
                    }
                }
            }

            // 正常响应，返回数据
            return response.data;
        },
        (error: AxiosError) => {
            console.error('响应错误:', error);

            // 检查是否有响应
            if (error.response) {
                console.log('错误响应状态码:', error.response.status);
                console.log('错误响应数据:', error.response.data);

                // 处理401未授权错误
                if (error.response.status === 401) {
                    console.log('检测到未授权响应，状态码401');

                    // 检查是否有重定向URL
                    if (error.response.data && error.response.data.data && error.response.data.data.redirectUrl) {
                        const redirectUrl = error.response.data.data.redirectUrl;
                        console.log('检测到重定向URL:', redirectUrl);

                        // 执行重定向
                        console.log('正在重定向到:', redirectUrl);
                        window.location.href = redirectUrl;
                        return Promise.reject(new Error('未授权，正在重定向到登录页面'));
                    } else {
                        // 没有重定向URL，等待后端处理
                        console.log('未检测到重定向URL，等待后端处理');
                        return Promise.reject(new Error('未授权，请重新登录'));
                    }
                } else if (error.response.status === 403) {
                    // 处理403禁止访问错误
                    console.log('检测到禁止访问响应，状态码403');
                    ElMessage.error('没有权限访问该资源');
                    return Promise.reject(new Error('没有权限访问该资源'));
                } else if (error.response.status === 500) {
                    // 处理500服务器错误
                    console.log('检测到服务器错误，状态码500');
                    ElMessage.error('服务器错误，请稍后重试');
                    return Promise.reject(new Error('服务器错误，请稍后重试'));
                }
            } else if (error.request) {
                // 请求已经发出，但没有收到响应
                console.log('没有收到响应:', error.request);
                ElMessage.error('网络错误，请检查您的网络连接');
                return Promise.reject(new Error('网络错误，请检查您的网络连接'));
            } else {
                // 发送请求时出了点问题
                console.log('请求配置错误:', error.message);
                ElMessage.error('请求错误，请稍后重试');
                return Promise.reject(new Error('请求错误，请稍后重试'));
            }

            // 默认错误处理
            ElMessage.error(error.message || '请求失败，请稍后重试');
            return Promise.reject(error);
        }
    );
};

// 应用拦截器
createRequestInterceptor(http);
createResponseInterceptor(http);
createRequestInterceptor(unifiedHttp);
createResponseInterceptor(unifiedHttp);

export default http;