// OSS直传工具类
// 注意: 此模块在Linux服务器构建时会被排除，因此需要做好降级处理

let OSS: any = null;
let isOssAvailable = false;

// 尝试加载ali-oss模块，如果失败则不会阻止应用其他功能运行
try {
  // 动态导入模块需要在异步环境中进行
  // 在此先标记为不可用，在实际使用前会再次检查
  isOssAvailable = false;
} catch (error) {
  console.warn('初始化OSS失败:', error);
  isOssAvailable = false;
}

/**
 * 检查OSS直传功能是否可用
 */
export function checkOssAvailability(): boolean {
  return isOssAvailable;
}

/**
 * 动态加载OSS模块
 * 在实际使用前调用此函数确保可用性
 */
export async function loadOssModule() {
  if (process.env.DISABLE_OSS === 'true') {
    isOssAvailable = false;
    return false;
  }
  
  try {
    // 使用动态import加载模块
    const ossModule = await import('ali-oss');
    OSS = ossModule.default;
    isOssAvailable = true;
    return true;
  } catch (error) {
    console.warn('无法加载ali-oss模块，将使用服务器中转上传方式', error);
    isOssAvailable = false;
    return false;
  }
}


/**
 * 获取OSS临时授权
 * @returns STS临时授权信息
 */
export async function getOssToken() {
  // 确保OSS模块已加载
  await loadOssModule();
  
  if (!isOssAvailable) {
    throw new Error('OSS模块不可用，请使用服务器中转上传');
  }
  
  try {
    // 从localStorage获取token
    const token = localStorage.getItem('token');
    
    const response = await fetch('/interview/oss/sts', {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'token': token || '' // 在请求头中添加token
      }
    });
    
    if (!response.ok) {
      throw new Error(`获取OSS授权失败: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    if (result.code !== 0) {
      throw new Error(result.message || '获取OSS授权失败');
    }
    
    return result.data;
  } catch (error) {
    console.error('获取OSS临时授权失败:', error);
    throw error;
  }
}

/**
 * 创建OSS客户端
 * @param stsToken STS临时授权信息
 * @returns OSS客户端实例
 */
export async function createOssClient(stsToken: any) {
  // 确保OSS模块已加载
  await loadOssModule();
  
  if (!isOssAvailable || !OSS) {
    throw new Error('OSS模块不可用，请使用服务器中转上传');
  }

  // 修复OSS配置，确保正确的域名格式
  const ossConfig = {
    region: 'oss-cn-beijing',
    accessKeyId: stsToken.accessKeyId,
    accessKeySecret: stsToken.accessKeySecret,
    stsToken: stsToken.securityToken,
    bucket: stsToken.bucket,
    secure: true, // 使用HTTPS
    timeout: 120000, // 设置超时时间为2分钟
    cname: false, // 不使用自定义域名
    internal: true, // 不使用内网endpoint
    useFetch: true, // 在浏览器环境中使用fetch
    retries: 3 ,// 失败重试次数
    endpoint: stsToken.endpoint
  };
  
  console.log('OSS客户端配置:', JSON.stringify({
    region: ossConfig.region,
    bucket: ossConfig.bucket
  }));
  
  return new OSS(ossConfig);
}

/**
 * OSS直传文件
 * @param file 要上传的文件
 * @param directory 存储目录
 * @param onProgress 进度回调
 * @returns 上传结果，包含文件URL
 */
export async function uploadToOss(
  file: File, 
  directory: string = 'interview',
  onProgress?: (percent: number, checkpoint: any) => void
) {
  // 确保OSS模块已加载
  await loadOssModule();
  
  if (!isOssAvailable || !OSS) {
    throw new Error('OSS模块不可用，请使用服务器中转上传');
  }
  
  try {
    // 1. 获取临时授权
    const stsToken = await getOssToken();
    console.log('获取到STS授权:', JSON.stringify({
      region: stsToken.region,
      bucket: stsToken.bucket,
      endpoint: stsToken.endpoint,
      accessKeyId: stsToken.accessKeyId,
      accessKeySecret: stsToken.accessKeySecret
    }));

    // 2. 创建OSS客户端
    const ossClient = await createOssClient(stsToken);
    
    // 3. 生成OSS存储路径
    const date = new Date();
    const dateStr = `${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}`;
    const fileExt = file.name.substring(file.name.lastIndexOf('.'));
    const fileName = `${directory}/${dateStr}/${Date.now()}${fileExt}`;
    
    console.log('开始上传文件:', fileName);
    
    // 4. 执行上传
    const result = await ossClient.multipartUpload(fileName, file, {
      progress: function(p: number, checkpoint: any) {
        // 上传进度回调
        onProgress && onProgress(Math.floor(p * 100), checkpoint);
        console.log('上传进度:', Math.floor(p * 100) + '%');
      },
      // 设置并发上传的分片数量
      parallel: 4,
      // 设置分片大小
      partSize: 1024 * 1024
    });
    
    // 5. 返回结果
    if (result.res && result.res.status === 200) {
      // 正确构建OSS URL
      // 格式: https://{bucketName}.oss-{region}.aliyuncs.com/{objectName}
      const fileUrl = `https://${stsToken.bucket}.oss-${stsToken.region}.aliyuncs.com/${fileName}`;
      
      console.log('上传成功，文件URL:', fileUrl);
      
      return {
        success: true,
        url: fileUrl,
        fileName: fileName,
        ossPath: fileName
      };
    } else {
      throw new Error('上传失败，请重试');
    }
  } catch (error) {
    console.error('OSS上传失败:', error);
    throw error;
  }
} 