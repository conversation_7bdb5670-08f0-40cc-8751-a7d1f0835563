package com.bimowu.resume.filter;

import com.bimowu.resume.base.BaseResponse;
import com.bimowu.resume.base.Constant;
import com.bimowu.resume.entity.KsUser;
import com.bimowu.resume.utils.CookieUtils;
import com.bimowu.resume.utils.RedisUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.tomcat.util.http.MimeHeaders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.Field;
import java.net.MalformedURLException;
import java.net.URLEncoder;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 *  判断用户是否登录，若未登录则返回重定向地址给前端.
 * <AUTHOR>
 *
 */
@Slf4j
@Configuration
public class LoginFilter implements Filter {
    private Logger logger = LoggerFactory.getLogger(LoginFilter.class);


	@Value("${sso.login.loginUrl}")
    private String loginUrl;
	@Value("${sso.clientId}")
	private String clientId;
	@Value("${sso.validate.url}")
	private String validateTokenUrl;
	@Value("${token.expire.seconds}")
	private Long tokenExpireSeconds;

	@Autowired
	private RedisUtil redisUtil;
	@Autowired
	private RestTemplate restTemplate;
	@Autowired
	private ObjectMapper objectMapper;

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response,
						 FilterChain chain) throws IOException, ServletException {
		HttpServletRequest httpRequest = (HttpServletRequest) request;
		HttpServletResponse httpResponse = (HttpServletResponse) response;

		// 对OPTIONS预检请求直接放行
		if ("OPTIONS".equalsIgnoreCase(httpRequest.getMethod())) {
			chain.doFilter(httpRequest, httpResponse);
			return;
		}

		String url = httpRequest.getRequestURI();
		// login judge
		log.info("请求url="+url);
		if(url.contains("/api/debug/font") || url.contains("/api/debug/template")){
			chain.doFilter(httpRequest, httpResponse);
			return;
		}
		//需要验证登录的情况
		String token = getToken(httpRequest);
		log.info("token="+token);
		if (token != null) {
			//从本地缓存中根据token获取用户信息，若不存在，从sso服务端获取用户信息
			Object userId = redisUtil.get(String.format(Constant.REDIS_KEY, token));
			if (userId == null) {
				//从sso服务端获取用户信息 地址： http://localhost:8091/unified/sso/validateToken header里携带token
				try {
					org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
					headers.set(Constant.TOKEN_KEY, token);
					HttpEntity<String> entity = new HttpEntity<>(null, headers);

					ResponseEntity<Map> response2 = restTemplate.exchange(
						validateTokenUrl,
						HttpMethod.GET,
						entity,
						Map.class
					);

					if (response2.getStatusCode().is2xxSuccessful()) {
						Map<String, Object> responseBody = response2.getBody();
						if (responseBody != null && (Integer)responseBody.get("code")==0) {
							Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
							if (data != null && data.containsKey("userId")) {
								userId = Long.parseLong(data.get("userId").toString());
								// 将用户信息存入RedisString userKey = String.format(Constant.REDIS_KEY, userId);
								redisUtil.set(String.format(Constant.REDIS_KEY, token), Long.parseLong(userId.toString()), tokenExpireSeconds);
								redisUtil.set(String.format(Constant.REDIS_KEY, userId), data.get("userInfo"), tokenExpireSeconds);
								// 将userId设置到请求头中
								Map<String, Long> headerMap = new HashMap<>();
								headerMap.put("userId", Long.parseLong(userId.toString()));
								addHeader(httpRequest, headerMap);
							} else {
								// 验证失败，返回重定向地址给前端
								String redirectUrl = URLEncoder.encode(getRequestUrl(httpRequest), "UTF-8");
								String loginRedirectUrl = loginUrl + "?clientId=" + clientId + "&redirectUrl=" + redirectUrl;
								sendRedirectResponse(httpResponse, httpRequest, loginRedirectUrl);
								return;
							}
						} else {
							// 验证失败，返回重定向地址给前端
							String redirectUrl = URLEncoder.encode(getRequestUrl(httpRequest), "UTF-8");
							String loginRedirectUrl = loginUrl + "?clientId=" + clientId + "&redirectUrl=" + redirectUrl;
							sendRedirectResponse(httpResponse, httpRequest, loginRedirectUrl);
							return;
						}
					} else {
						// 调用服务失败，返回重定向地址给前端
						String redirectUrl = URLEncoder.encode(getRequestUrl(httpRequest), "UTF-8");
						String loginRedirectUrl = loginUrl + "?clientId=" + clientId + "&redirectUrl=" + redirectUrl;
						sendRedirectResponse(httpResponse, httpRequest, loginRedirectUrl);
						return;
					}
				} catch (Exception e) {
					log.error("验证token异常", e);
					// 验证失败，返回重定向地址给前端
					String redirectUrl = URLEncoder.encode(getRequestUrl(httpRequest), "UTF-8");
					String loginRedirectUrl = loginUrl + "?clientId=" + clientId + "&redirectUrl=" + redirectUrl;
					sendRedirectResponse(httpResponse, httpRequest, loginRedirectUrl);
					return;
				}
			} else {
				// 用户已登录，将userId设置到请求头中
				Map<String, Long> headerMap = new HashMap<>();
				headerMap.put("userId", Long.parseLong(userId.toString()));
				addHeader(httpRequest, headerMap);
			}
		} else {
			log.info("用户未登录");
			// 若token为空，返回重定向地址给前端
			String redirectUrl = URLEncoder.encode(getRequestUrl(httpRequest), "UTF-8");
			String loginRedirectUrl = loginUrl + "?clientId=" + clientId + "&redirectUrl=" + redirectUrl;
			sendRedirectResponse(httpResponse, httpRequest, loginRedirectUrl);
			log.info("用户未登录，返回重定向地址给前端");
			return;
		}
		log.info("filter 处理完成");
		chain.doFilter(httpRequest, httpResponse);
	}
	
	/**
	 * 返回重定向响应给前端
	 */
	private void sendRedirectResponse(HttpServletResponse response, HttpServletRequest request, String redirectUrl) throws IOException {
		response.setContentType(MediaType.APPLICATION_JSON_VALUE);
		response.setCharacterEncoding("UTF-8");
		
		// 添加CORS头，根据请求的Origin头来设置
		String origin = request.getHeader("Origin");
		if (origin != null) {
			response.setHeader("Access-Control-Allow-Origin", origin);
		} else {
			// 如果没有Origin头，则使用默认值
			response.setHeader("Access-Control-Allow-Origin", "http://localhost:3000");
		}
		response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
		response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, token");
		response.setHeader("Access-Control-Allow-Credentials", "true");
		response.setHeader("Access-Control-Max-Age", "3600");
		
		// 创建响应对象
		Map<String, String> data = new HashMap<>();
		data.put("redirectUrl", redirectUrl);
		BaseResponse<Map<String, String>> baseResponse = new BaseResponse<>(401,"用户未登录或登录已过期",data);
		log.info("响应参数：{}",baseResponse.toString());
		
		// 将响应写入输出流
		try (PrintWriter writer = response.getWriter()) {
			String jsonResponse = objectMapper.writeValueAsString(baseResponse);
			log.info("JSON响应：{}", jsonResponse);
			writer.write(jsonResponse);
			writer.flush();
		} catch (Exception e) {
			log.error("生成JSON响应时发生错误", e);
		}
	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub
	}
	private static String getToken(HttpServletRequest request) {
		
		// 然后尝试从token头中获取
		String tokenHeader = request.getHeader(Constant.TOKEN_KEY);
		if (org.apache.commons.lang3.StringUtils.isNotBlank(tokenHeader)) {
			log.info("从token头中获取到token: {}", tokenHeader);
			return tokenHeader;
		}
		// 最后尝试从Cookie中获取
//		String tokenCookie = CookieUtils.getValue(request, Constant.TOKEN_KEY);
//		if (org.apache.commons.lang3.StringUtils.isNotBlank(tokenCookie)) {
//			log.info("从Cookie中获取到token: {}", tokenCookie);
//			return tokenCookie;
//		}
		log.info("未找到token");
		return null;
	}
	private String getRequestUrl(HttpServletRequest request) throws IOException {
		// 使用前端地址，而不是后端API地址
		// 从请求头中获取Referer，这通常包含了前端页面的完整URL
		String frontendUrl = request.getHeader("Referer");
		logger.info("从请求头获取的Referer: {}", frontendUrl);
		
		// 如果Referer不存在，尝试从Origin获取基础URL
		if (frontendUrl == null || frontendUrl.isEmpty()) {
			frontendUrl = request.getHeader("Origin");
			logger.info("从请求头获取的Origin: {}", frontendUrl);
			
			// 如果Origin也不存在，使用配置的host
//			if (frontendUrl == null || frontendUrl.isEmpty()) {
//				frontendUrl = host;
//				logger.info("使用配置的host作为前端URL: {}", frontendUrl);
//
//				// 如果使用host，则添加默认的登录路径
//				return frontendUrl + "/login";
//			}
			
			// 如果使用Origin，它只包含基础URL，需要添加登录路径
			return frontendUrl + "/login";
		}
		
		// 如果有Referer，它通常包含完整的前端URL路径
		// 我们可以直接返回它，或者提取出其中的路径部分
		
		// 选项1：直接返回Referer（保留用户当前所在的前端页面）
		logger.info("直接使用Referer作为重定向URL: {}", frontendUrl);
		return frontendUrl;
		
		// 选项2：从Referer中提取基础URL，然后添加登录路径
		// 这里我们注释掉这个选项，使用选项1
		/*
		try {
			URL url = new URL(frontendUrl);
			String baseUrl = url.getProtocol() + "://" + url.getHost();
			if (url.getPort() != -1) {
				baseUrl += ":" + url.getPort();
			}
			String redirectUrl = baseUrl + "/login";
			logger.info("从Referer提取基础URL并添加/login: {}", redirectUrl);
			return redirectUrl;
		} catch (MalformedURLException e) {
			logger.error("无法解析Referer URL: {}", frontendUrl, e);
			return host + "/login";
		}
		*/
	}
	private void addHeader(HttpServletRequest request, Map<String, Long> headerMap) {
		if (headerMap==null||headerMap.isEmpty()){
			log.info("headerMap为空");
			return;
		}

		Class<? extends HttpServletRequest> c=request.getClass();
		//System.out.println(c.getName());
		log.info("request实现类="+c.getName());
		try{
			Field requestField=c.getDeclaredField("request");
			requestField.setAccessible(true);

			Object o=requestField.get(request);
			Field coyoteRequest=o.getClass().getDeclaredField("coyoteRequest");
			coyoteRequest.setAccessible(true);

			Object o2=coyoteRequest.get(o);
			Field headers=o2.getClass().getDeclaredField("headers");
			headers.setAccessible(true);

			MimeHeaders mimeHeaders=(MimeHeaders) headers.get(o2);
			for (Map.Entry<String,Long> entry:headerMap.entrySet()){
				mimeHeaders.removeHeader(entry.getKey());
				mimeHeaders.addValue(entry.getKey()).setLong(entry.getValue());
			}

		}catch (Exception e){
			log.info("headerMap<UNK>:{}",e.getMessage());
		}
	}

}
