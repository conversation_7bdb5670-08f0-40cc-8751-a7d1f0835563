<template>
  <div class="resume-template template-2">
    <div class="container">
      <!-- 简历内容区 - 改为单列布局 -->
      <div class="resume-body-single-column">
        <!-- 个人信息 -->
        <div class="profile-section">
          <div class="profile-header">
            <div class="profile-text">
              <div class="profile-name">{{
                  resume && resume.modules && resume.modules.basic ? (resume.modules.basic.name || '未填写姓名') : '未填写姓名'
                }}
              </div>
              <div class="profile-details">
                <span class="info-item">性别：{{
                    resume && resume.modules && resume.modules.basic ? (resume.modules.basic.gender || '未填写') : '未填写'
                  }}
                </span>
                <span class="info-item">年龄：{{ resume && resume.modules && resume.modules.basic ? (resume.modules.basic.age || '未填写') : '未填写' }}</span>
                <span class="info-item">电话：{{
                    resume && resume.modules && resume.modules.basic ? (resume.modules.basic.phone || '未填写') : '未填写'
                  }}
                </span>
                <span class="info-item">邮箱：{{
                    resume && resume.modules && resume.modules.basic ? (resume.modules.basic.email || '未填写') : '未填写'
                  }}
                </span>
              </div>
            </div>
            <div class="avatar-container">
              <Avatar 
                v-if="resume && resume.modules && resume.modules.basic && resume.modules.basic.avatar"
                :src="resume.modules.basic.avatar" 
                className="avatar" 
                alt="头像"
              />
              <Avatar 
                v-else 
                :src="'/images/default-avatar.svg'" 
                className="avatar" 
                alt="默认头像"
              />
            </div>
          </div>
        </div>

        <!-- 各模块 -->
        <!-- 教育背景 -->
        <div v-if="hasEducation" class="section education-section">
          <div class="section-title">
            <h2 style="border-bottom: 2px solid red;">教育经历 / EDUCATIONAL EXPERIENCE</h2>
          </div>
          <div class="section-content">
            <div v-for="(edu, index) in resume.modules.education" :key="index" class="education-item">
              <div class="education-item-header">
                <div class="edu-details-left">
                  <div class="edu-school">{{ edu.school }}（{{ edu.degree }}）</div>
                  <div class="edu-major">{{ edu.major }}</div>
                  <div class="edu-time">{{ formatDate(edu.time[0]) }} - {{ formatDate(edu.time[1]) || '至今' }}</div>
                </div>

              </div>
              <div v-if="edu.courses" class="edu-courses" style="white-space: normal; display: flex; align-items: baseline; flex-wrap: wrap;">
                <span class="courses-label">主修课程：</span>
                <span style="display: inline; white-space: normal;">{{ edu.courses }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 技能特长 -->
        <div v-if="hasSkills" class="section skills-section">
          <div class="section-title">
            <h2 style="border-bottom: 2px solid red;">专业技能 / PROFESSIONAL SKILLS</h2>
          </div>
          <div class="section-content">
            <div v-if="hasSkillsWithDescriptions" class="skill-descriptions">
              <div v-for="(skill, index) in resume.modules.skills" :key="'desc-'+index" class="skill-description-item"
                   v-show="skill.description">
                <div class="skill-description-body" v-html="formatContent(skill.description)"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 真实项目 -->
        <div v-if="hasProjects" class="section projects-section">
          <div class="section-title">
            <h2 style="border-bottom: 2px solid orange;">真实项目 / PROJECT EXPERIENCE</h2>
          </div>
          <div class="section-content">
            <div v-for="(project, index) in resume.modules.projects" :key="index" class="project-item">
              <div class="project-item-header">
                <div class="project-time">{{ formatDate(project.time[0]) }} - {{ formatDate(project.time[1]) || '至今' }}</div>
                <div class="project-name">{{ project.name }}</div>
                <div class="project-role">{{ project.role }}</div>
              </div>
              <div class="project-description">
                <div class="project-description" v-html="formatContent(project.description)"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 练手项目 -->
        <div v-if="hasPractices" class="section practices-section">
          <div class="section-title">
            <h2 style="border-bottom: 2px solid blue;">练手项目 / PROJECT EXPERIENCE</h2>
          </div>
          <div class="section-content">
            <div v-for="(practice, index) in resume.modules.practices" :key="index" class="project-item">
              <div class="project-item-header">
                <div class="project-role">{{ practice.role }}</div>
                <div class="project-name">{{ practice.name }}</div>
                <div class="project-time">{{ formatDate(practice.time[0]) }} - {{ formatDate(practice.time[1]) || '至今' }}</div>
              </div>
              <div v-if="practice.url" class="project-url">
                项目地址：<span style="color: #0066cc; text-decoration: underline;">{{ practice.url }}</span>
              </div>
              <div class="project-description">
                <div class="project-description" v-html="formatContent(practice.description)"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 实习经历 -->
        <div v-if="hasWork" class="section work-section">
          <div class="section-title">
            <h2 style="border-bottom: 2px solid green;">实习经历 / SELF ASSESSMENT</h2>
          </div>
          <div class="section-content">
            <div v-for="(work, index) in resume.modules.work" :key="index" class="work-item">
              <div class="work-header">
                <div class="work-position">{{ work.position }}</div>
                <div class="work-company">{{ work.company }}</div>
                <div class="work-time">{{ formatDate(work.time[0]) }} - {{ formatDate(work.time[1]) || '至今' }}</div>
              </div>
              <div class="work-description" v-html="formatContent(work.description)"></div>
            </div>
          </div>
        </div>

        <!-- 个人评价 -->
        <div v-if="hasEvaluation" class="section evaluation-section">
          <div class="section-title">
            <h2 style="border-bottom: 2px solid blueviolet;">个人评价 / SELF ASSESSMENT</h2>
          </div>
          <div class="section-content evaluation-content">
            <MdPreview :modelValue="formattedEvaluation"/>
          </div>
        </div>

        <!-- 荣誉证书 -->
        <div v-if="hasCertificates" class="section certificates-section">
          <div class="section-title">
            <h2 style="border-bottom: 2px solid brown;">荣誉证书 / CERTIFICATES</h2>
          </div>
          <div class="section-content certificate-content">
            <MdPreview :modelValue="typeof resume.modules.certificates === 'string' ? 
              resume.modules.certificates : 
              (typeof resume.modules.certificates === 'object' ? resume.modules.certificates.certificateName : '')"/>
          </div>
        </div>

        <!-- 校园经历 (如果之前在左侧栏，现在移到右侧栏下方或其他位置) -->
        <!-- 根据图片，校园经历在技能特长下方，属于左侧栏 -->
        <!-- 兴趣爱好 (如果之前在左侧栏，现在移到右侧栏下方或其他位置) -->
        <!-- 根据图片，兴趣爱好在校园经历下方，属于左侧栏 -->

      </div>
    </div>
  </div>
</template>

<script setup>
import {computed, toRefs, watch} from 'vue';
import {MdPreview} from 'md-editor-v3';
import 'md-editor-v3/lib/preview.css';
import Avatar from '../Avatar.vue'

const props = defineProps({
  resume: {
    type: Object,
    required: true
  }
});

// 格式化日期为 YYYY-MM
const formatDate = (date) => {
  if (!date) return '';
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      // If not a valid Date object, try to parse as YYYY-MM-DD or YYYY/MM/DD etc.
      const match = date.match(/^(\d{4}[-\.]\d{2})/);
      if (match && match[1]) {
        return match[1]; // Return YYYY-MM from string
      }
      return date; // Return original if parsing fails
    }
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  } catch (e) {
    console.error('Error formatting date:', e);
    return date; // Return original string in case of error
  }
};

// 使用toRefs增强响应式
const {resume} = toRefs(props);

// 添加监听来调试组件渲染
watch(() => resume.value, (newVal, oldVal) => {
  console.log('Template2 - resume prop changed');
}, {deep: true});

// 判断各模块是否有内容
const hasEducation = computed(() => props.resume.modules && props.resume.modules.education && props.resume.modules.education.length > 0);
const hasWork = computed(() => props.resume.modules && props.resume.modules.work && props.resume.modules.work.length > 0);
const hasProjects = computed(() => props.resume.modules && props.resume.modules.projects && props.resume.modules.projects.length > 0);
const hasSkills = computed(() => props.resume.modules && props.resume.modules.skills && props.resume.modules.skills.length > 0);
const hasSkillsWithDescriptions = computed(() => {
  if (!props.resume.modules || !props.resume.modules.skills) return false;
  return props.resume.modules.skills.some(skill => skill.description && skill.description.trim() !== '');
});
const hasCertificates = computed(() => {
  if (!props.resume.modules || !props.resume.modules.certificates) return false;
  const cert = props.resume.modules.certificates;
  return typeof cert === 'string' ? cert.trim() !== '' :
      (typeof cert === 'object' && cert.certificateName && cert.certificateName.trim() !== '');
});
const hasCampus = computed(() => {
  if (!props.resume.modules || !props.resume.modules.campus) return false;
  const campus = props.resume.modules.campus;
  return typeof campus === 'string' ? formatContent(campus) :
      (typeof campus === 'object' && campus.description ? formatContent(campus.description) : '');
});
const hasInterests = computed(() => {
  if (!props.resume.modules || !props.resume.modules.interests) return false;
  const interests = props.resume.modules.interests;
  return typeof interests === 'string' ? formatContent(interests) :
      (typeof interests === 'object' && interests.description ? formatContent(interests.description) : '');
});
const hasEvaluation = computed(() => {
  if (!props.resume.modules || !props.resume.modules.selfEvaluation) return false;
  const evaluation = props.resume.modules.selfEvaluation;
  return typeof evaluation === 'string' ? formatContent(evaluation) :
      (typeof evaluation === 'object' && evaluation.description ? formatContent(evaluation.description) : '');
});
const hasPractices = computed(() => props.resume.modules && props.resume.modules.practices && props.resume.modules.practices.length > 0);

// 格式化文本内容，保持段落格式，支持Markdown基本语法
const formatContent = (content) => {
  if (!content || typeof content === 'object') return '';

  // If content already contains HTML tags, return directly
  if (content.includes('<') && content.includes('>')) {
    return content;
  }

  let html = content;

  // Basic Markdown to HTML
  html = html
      .replace(/^###\s(.*?)$/gm, '<h3>$1</h3>') // H3
      .replace(/^##\s(.*?)$/gm, '<h2>$1</h2>') // H2
      .replace(/^#\s(.*?)$/gm, '<h1>$1</h1>');   // H1

  html = html
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
      .replace(/\*(.*?)\*/g, '<em>$1</em>');     // Italic

  // Handle lists (lines starting with - or *) - simple approach
  html = html.split('\n').map(line => {
    if (line.trim().startsWith('-') || line.trim().startsWith('*')) {
      return `<li>${line.trim().substring(1).trim()}</li>`;
    } else {
      return line;
    }
  }).join('\n');

  // Wrap list items in ul if list items exist and are not already in ul
  if (html.includes('<li>') && !html.includes('<ul>')) {
    html = `<ul>${html}</ul>`;
  }

  // Handle paragraphs for lines not part of a list (simplified)
  // This is tricky with simple regex; often requires more stateful parsing.
  // For simplicity, wrap blocks of non-list text in paragraphs, use <br> for single newlines within blocks.
  const blocks = html.split(/<li>.*?<\/li>|\n\s*\n/s); // Split by list items or double newlines

  html = blocks.map(block => {
    if (block.trim() === '' || block.trim().startsWith('<li')) {
      return block; // Keep empty blocks or already processed list items
    } else {
      // Wrap non-list block in paragraph, convert single newlines to br
      return '<p>' + block.trim().replace(/\n/g, '<br>') + '</p>';
    }
  }).join('');


  return html;
};

// 格式化内容
const formattedCampus = computed(() => {
  if (!props.resume.modules || !props.resume.modules.campus) return '';
  const campus = props.resume.modules.campus;
  return typeof campus === 'string' ? formatContent(campus) :
      (typeof campus === 'object' && campus.description ? formatContent(campus.description) : '');
});

const formattedInterests = computed(() => {
  if (!props.resume.modules || !props.resume.modules.interests) return '';
  const interests = props.resume.modules.interests;
  return typeof interests === 'string' ? formatContent(interests) :
      (typeof interests === 'object' && interests.description ? formatContent(interests.description) : '');
});

const formattedEvaluation = computed(() => {
  if (!props.resume.modules || !props.resume.modules.selfEvaluation) return '';
  const evaluation = props.resume.modules.selfEvaluation;
  return typeof evaluation === 'string' ? formatContent(evaluation) :
      (typeof evaluation === 'object' && evaluation.description ? formatContent(evaluation.description) : '');
});

// 根据技能等级获取文本描述
const getSkillLevelText = (level) => {
  switch (level) {
    case 1:
      return '了解';
    case 2:
      return '掌握';
    case 3:
      return '熟悉';
    case 4:
      return '精通';
    case 5:
      return '专家';
    default:
      return '了解';
  }
};


</script>

<style scoped>
.resume-template {
  width: 100%;
  background: white;
  color: #333;
  font-family: 'Microsoft YaHei', sans-serif;
}

.container {
  max-width: 210mm; /* A4 width */
  margin: 0 auto; /* Center the content */
  padding: 20px; /* Add some padding */
  box-sizing: border-box; /* Include padding in the element's total width and height */
}

.resume-body-single-column {
  /* No specific flex or grid needed for single column */
}

/* Personal Info Section */
.profile-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  /* border-bottom: 1px solid #d3d3d3;  // 去掉灰色分割线 */
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.profile-text {
  flex-grow: 1;
  margin-right: 20px;
}

.profile-name {
  font-size: 26px;
  font-weight: bold;
  color: #000;
  margin-bottom: 5px;
}

.profile-details {
  font-size: 14px;
  color: #333;
  line-height: 1.7;
}

.info-item {
  margin-right: 15px;
  white-space: nowrap;
}

.info-item:last-child {
  margin-right: 0;
}

.avatar-container {
  width: 90px;
  height: 110px;
  overflow: hidden;
  flex-shrink: 0;
  border: none;
  border-radius: 3px;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Section Titles */
.section-title {
  margin-top: 15px; /* Further reduced margin above section title */
  margin-bottom: 8px; /* Further reduced margin below title */
  padding-bottom: 0;
  border-bottom: none;
}

.section-title h2 {
  font-size: 18px;
  color: #000;
  margin: 0;
  font-weight: bold;
  text-align: left;
  padding-bottom: 4px; /* Further reduced padding below text for underline */
}

/* Module Content */
.section-content {
  margin-top: 6px;
  line-height: 1.7;
  color: #333;
  font-size: 14px;
  text-align: left;
}

.education-item,
.work-item,
.project-item,
.skill-description-item,
.certificate-content,
.campus-content,
.interests-content,
.evaluation-content,
.practices-content {
  margin-bottom: 12px;
}

.education-item:last-child,
.work-item:last-child,
.project-item:last-child,
.skill-description-item:last-child,
.certificate-content:last-child,
.campus-content:last-child,
.interests-content:last-child,
.evaluation-content:last-child,
.practices-content:last-child {
  margin-bottom: 0;
}

.education-item-header,
.work-item-header,
.project-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start; /* Align items to the start */
  margin-bottom: 4px;
  font-size: 15px;
  font-weight: bold;
}

.edu-details-left,
.work-details-left,
.project-details-left {
  display: flex;
  justify-content: space-between;
  flex-grow: 1;
  margin-right: 10px;
}

.edu-school,
.work-company,
.project-name {
  color: #000;
  margin-bottom: 2px; /* Space below school/company/name */
}

.edu-major,
.work-position,
.project-role {
  flex-shrink: 0;
  font-weight: bold;
  font-size: 14px;
  margin-left: 0; /* Remove left margin */
}

.edu-time,
.work-time,
.project-time {
  flex-shrink: 0;
  color: #555;
  font-weight: bold;
  font-size: 14px;
  margin-left: 10px; /* Space before time */
}

.edu-courses,
.work-description,
.project-description,
.skill-description-body {
  line-height: 1.7;
  text-align: justify;
  margin-top: 4px;
}

.edu-courses .courses-label {
  font-weight: bold;
  margin-right: 5px;
}

/* Markdown List Styles */
:deep(.md-preview-html ul) {
  list-style-type: disc;
  padding-left: 18px;
  margin: 0;
}

:deep(.md-preview-html li) {
  margin-bottom: 4px;
  line-height: 1.7;
}

:deep(.md-preview-html p) {
  margin: 0 0 4px 0;
  line-height: 1.7;
}

/* Adjust specific module content styles */
.skills-content .skill-description-item {
  display: flex;
  align-items: baseline;
  margin-bottom: 4px;
}

.skill-bullet {
  margin-right: 8px;
  font-weight: bold;
  color: #333;
}

.skill-description-body {
  flex-grow: 1;
  font-size: 14px;
  text-align: left;
}

/* Adjusted style for skill description body */
/* Keep this if you want to maintain the left alignment and font size */
/*
.skill-description-body {
    line-height: 1.6;
    text-align: left; 
    font-size: 14px;
}
*/

.certificate-content,
.campus-content,
.interests-content,
.evaluation-content {
  /* Specific styles already defined above */
  text-align: left;
}


.work-header,
.project-item-header  {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 4px;
  font-size: 15px;
  font-weight: bold;
}

.work-time,
.project-time  {
  flex-shrink: 0;
  font-weight: bold;
  font-size: 14px;
  margin-right: 10px;
}

.work-company,
.project-name {
  color: #000;
  margin-right: 10px;
}

.work-position,
.project-role {
  flex-shrink: 0;
  color: #333;
  font-weight: bold;
  font-size: 14px;
}
.section{
  padding: 0;
}
</style> 