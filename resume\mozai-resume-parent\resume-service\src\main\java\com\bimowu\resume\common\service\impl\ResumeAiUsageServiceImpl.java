package com.bimowu.resume.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bimowu.resume.common.dao.ResumeAiUsageMapper;
import com.bimowu.resume.common.service.ResumeAiUsageService;
import com.bimowu.resume.config.AIConfig;
import com.bimowu.resume.entity.ResumeAiUsage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户AI使用次数记录表 服务实现类
 */
@Service
public class ResumeAiUsageServiceImpl extends ServiceImpl<ResumeAiUsageMapper, ResumeAiUsage> implements ResumeAiUsageService {

    @Autowired
    private AIConfig aiConfig;

    @Override
    public boolean hasAvailableCount(Long userId) {
        ResumeAiUsage aiUsage = getUserAiUsage(userId);
        return aiUsage != null && aiUsage.getRemainingCount() > 0;
    }

    @Override
    @Transactional
    public boolean decreaseCount(Long userId) {
        ResumeAiUsage aiUsage = getUserAiUsage(userId);
        if (aiUsage == null || aiUsage.getRemainingCount() <= 0) {
            return false;
        }
        
        // 减少可用次数，增加已使用次数
        aiUsage.setRemainingCount(aiUsage.getRemainingCount() - 1);
        aiUsage.setTotalUsed(aiUsage.getTotalUsed() + 1);
        return updateById(aiUsage);
    }

    @Override
    public int getRemainingCount(Long userId) {
        ResumeAiUsage aiUsage = getUserAiUsage(userId);
        return aiUsage != null ? aiUsage.getRemainingCount() : 0;
    }

    @Override
    @Transactional
    public boolean initUserAiCount(Long userId, Integer initialCount) {
        // 查询用户是否已有记录
        ResumeAiUsage aiUsage = getUserAiUsage(userId);
        
        if (aiUsage == null) {
            // 创建新记录
            aiUsage = new ResumeAiUsage();
            aiUsage.setUserId(userId);
            aiUsage.setRemainingCount(initialCount != null ? initialCount : aiConfig.getUsageLimit().getDefault());
            aiUsage.setTotalUsed(0);
            aiUsage.setIsDelete(0);
            return save(aiUsage);
        }
        
        return true;
    }
    
    /**
     * 获取用户AI使用记录，如果不存在则返回null
     * @param userId 用户ID
     * @return 用户AI使用记录
     */
    private ResumeAiUsage getUserAiUsage(Long userId) {
        LambdaQueryWrapper<ResumeAiUsage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResumeAiUsage::getUserId, userId)
                .eq(ResumeAiUsage::getIsDelete, 0);
        return getOne(queryWrapper);
    }
} 