-- PDF模板生成系统初始化数据脚本
-- 用于开发环境初始化测试数据

-- 清理现有数据（谨慎使用）
-- DELETE FROM resume_style_sync_record;
-- DELETE FROM resume_template_style_config;

-- 插入模板样式配置测试数据
INSERT INTO `resume_template_style_config` (`template_id`, `css_content`, `font_config`, `color_palette`, `layout_config`, `version`, `enabled`, `create_by`, `remark`) VALUES
(1, 
'/* 简约风格模板 */
body { 
    font-family: "Microsoft YaHei", "SimSun", serif; 
    font-size: 14px; 
    line-height: 1.6; 
    color: #333333; 
    background-color: #ffffff;
    margin: 0;
    padding: 20px;
}
.header { 
    background-color: #f8f9fa; 
    padding: 20px; 
    border-bottom: 2px solid #007bff;
    margin-bottom: 20px;
}
.header h1 {
    color: #007bff;
    font-size: 24px;
    margin: 0;
}
.content { 
    padding: 0 20px; 
}
.section {
    margin-bottom: 25px;
}
.section-title {
    font-size: 18px;
    font-weight: bold;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 5px;
    margin-bottom: 15px;
}',
'[{"family":"Microsoft YaHei","style":"normal","weight":400,"size":"14px","lineHeight":"1.6","isWebFont":false},{"family":"SimSun","style":"normal","weight":400,"size":"14px","lineHeight":"1.6","isWebFont":false}]',
'{"primary":"#007bff","secondary":"#6c757d","background":"#ffffff","text":"#333333","border":"#dee2e6","customColors":{"headerBg":"#f8f9fa","titleColor":"#495057"}}',
'{"width":800,"height":1200,"display":"block","position":"static","margins":{"top":"20px","right":"20px","bottom":"20px","left":"20px"},"padding":{"top":"0","right":"20px","bottom":"0","left":"20px"}}',
'1.0.0', 1, 'system', '简约风格模板样式配置'),

(2, 
'/* 商务风格模板 */
body { 
    font-family: "SimSun", "Times New Roman", serif; 
    font-size: 13px; 
    line-height: 1.5; 
    color: #2c3e50; 
    background-color: #ffffff;
    margin: 0;
    padding: 25px;
}
.header { 
    background-color: #34495e; 
    color: #ffffff;
    padding: 25px; 
    text-align: center;
    margin-bottom: 25px;
}
.header h1 {
    color: #ffffff;
    font-size: 26px;
    margin: 0;
    font-weight: bold;
}
.content { 
    padding: 0 25px; 
}
.section {
    margin-bottom: 30px;
}
.section-title {
    font-size: 16px;
    font-weight: bold;
    color: #34495e;
    background-color: #ecf0f1;
    padding: 8px 15px;
    margin-bottom: 15px;
}',
'[{"family":"SimSun","style":"normal","weight":400,"size":"13px","lineHeight":"1.5","isWebFont":false},{"family":"Times New Roman","style":"normal","weight":400,"size":"13px","lineHeight":"1.5","isWebFont":false}]',
'{"primary":"#34495e","secondary":"#7f8c8d","background":"#ffffff","text":"#2c3e50","border":"#bdc3c7","customColors":{"headerBg":"#34495e","sectionBg":"#ecf0f1"}}',
'{"width":800,"height":1200,"display":"block","position":"static","margins":{"top":"25px","right":"25px","bottom":"25px","left":"25px"},"padding":{"top":"0","right":"25px","bottom":"0","left":"25px"}}',
'1.0.0', 1, 'system', '商务风格模板样式配置'),

(3, 
'/* 创意风格模板 */
body { 
    font-family: "Microsoft YaHei", "Arial", sans-serif; 
    font-size: 14px; 
    line-height: 1.7; 
    color: #444444; 
    background-color: #fafafa;
    margin: 0;
    padding: 15px;
}
.header { 
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    padding: 30px; 
    border-radius: 10px;
    margin-bottom: 20px;
}
.header h1 {
    color: #ffffff;
    font-size: 28px;
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}
.content { 
    background-color: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
.section {
    margin-bottom: 25px;
}
.section-title {
    font-size: 18px;
    font-weight: bold;
    color: #667eea;
    border-left: 4px solid #667eea;
    padding-left: 15px;
    margin-bottom: 15px;
}',
'[{"family":"Microsoft YaHei","style":"normal","weight":400,"size":"14px","lineHeight":"1.7","isWebFont":false},{"family":"Arial","style":"normal","weight":400,"size":"14px","lineHeight":"1.7","isWebFont":false}]',
'{"primary":"#667eea","secondary":"#764ba2","background":"#fafafa","text":"#444444","border":"#e1e5e9","customColors":{"gradientStart":"#667eea","gradientEnd":"#764ba2","contentBg":"#ffffff"}}',
'{"width":800,"height":1200,"display":"block","position":"static","margins":{"top":"15px","right":"15px","bottom":"15px","left":"15px"},"padding":{"top":"20px","right":"20px","bottom":"20px","left":"20px"}}',
'1.0.0', 1, 'system', '创意风格模板样式配置');

-- 插入样式同步记录测试数据
INSERT INTO `resume_style_sync_record` (`template_id`, `sync_type`, `status`, `sync_time`, `change_log`, `before_version`, `after_version`, `duration`, `changed_files`, `trigger_type`, `operator`, `remark`) VALUES
(1, 'FULL', 'SUCCESS', NOW() - INTERVAL 1 DAY, '初始化模板1样式配置', NULL, '1.0.0', 1200, 1, 'MANUAL', 'system', '系统初始化'),
(2, 'FULL', 'SUCCESS', NOW() - INTERVAL 1 DAY, '初始化模板2样式配置', NULL, '1.0.0', 1100, 1, 'MANUAL', 'system', '系统初始化'),
(3, 'FULL', 'SUCCESS', NOW() - INTERVAL 1 DAY, '初始化模板3样式配置', NULL, '1.0.0', 1300, 1, 'MANUAL', 'system', '系统初始化'),
(1, 'INCREMENTAL', 'SUCCESS', NOW() - INTERVAL 2 HOUR, '更新字体配置', '1.0.0', '1.0.1', 800, 1, 'AUTO', 'system', '自动同步'),
(2, 'INCREMENTAL', 'FAILED', NOW() - INTERVAL 1 HOUR, '同步失败', '1.0.0', NULL, 500, 0, 'AUTO', 'system', '网络超时导致同步失败');

-- 更新模板样式配置的最后同步时间
UPDATE `resume_template_style_config` SET 
    `last_sync_time` = NOW() - INTERVAL 2 HOUR,
    `version` = '1.0.1'
WHERE `template_id` = 1;

UPDATE `resume_template_style_config` SET 
    `last_sync_time` = NOW() - INTERVAL 1 DAY
WHERE `template_id` IN (2, 3);