package com.bimowu.interview.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bimowu.interview.dao.ResumeProgressMapper;
import com.bimowu.interview.model.ResumeProgress;
import com.bimowu.interview.service.ResumeProgressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 简历进度管理服务实现类
 */
@Service
@Slf4j
public class ResumeProgressServiceImpl extends ServiceImpl<ResumeProgressMapper, ResumeProgress>
        implements ResumeProgressService {

    @Autowired
    private ResumeProgressMapper resumeProgressMapper;

    @Override
    @Transactional
    public boolean createResumeProgress(ResumeProgress resumeProgress) {
        log.info("创建进度信息: userId={}, resumeId={}, currentStage={}", 
                resumeProgress.getUserId(), resumeProgress.getResumeId(), resumeProgress.getCurrentStage());
        
        // 设置默认值
        if (resumeProgress.getIsDeleted() == null) {
            resumeProgress.setIsDeleted(0);
        }
        
        // 设置时间
        Date now = new Date();
        resumeProgress.setCreateTime(now);
        resumeProgress.setUpdateTime(now);
        
        try {
            int result = resumeProgressMapper.insert(resumeProgress);
            return result > 0;
        } catch (Exception e) {
            log.error("创建进度信息失败", e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean updateResumeProgress(ResumeProgress resumeProgress) {
        log.info("更新进度信息: id={}, userId={}, resumeId={}, currentStage={}", 
                resumeProgress.getId(), resumeProgress.getUserId(), resumeProgress.getResumeId(), resumeProgress.getCurrentStage());
        
        // 设置更新时间
        resumeProgress.setUpdateTime(new Date());
        
        try {
            int result = resumeProgressMapper.update(resumeProgress);
            return result > 0;
        } catch (Exception e) {
            log.error("更新进度信息失败", e);
            throw e;
        }
    }

    @Override
    public ResumeProgress getResumeProgressById(Long id) {
        return resumeProgressMapper.selectById(id);
    }

    @Override
    public ResumeProgress getResumeProgressByUserId(Integer userId) {
        return resumeProgressMapper.selectByUserId(userId);
    }

    @Override
    public ResumeProgress getResumeProgressByResumeId(Long resumeId) {
        return resumeProgressMapper.selectByResumeId(resumeId);
    }

    @Override
    public List<ResumeProgress> getResumeProgressByCondition(Map<String, Object> params) {
        return resumeProgressMapper.selectByCondition(params);
    }

    @Override
    @Transactional
    public boolean updateResumeProgressStage(Long id, String currentStage) {
        log.info("更新进度阶段: id={}, currentStage={}", id, currentStage);
        
        try {
            int result = resumeProgressMapper.updateStage(id, currentStage);
            return result > 0;
        } catch (Exception e) {
            log.error("更新进度阶段失败", e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean deleteResumeProgress(Long id) {
        log.info("删除进度信息: id={}", id);
        
        try {
            int result = resumeProgressMapper.deleteById(id);
            return result > 0;
        } catch (Exception e) {
            log.error("删除进度信息失败", e);
            throw e;
        }
    }
} 