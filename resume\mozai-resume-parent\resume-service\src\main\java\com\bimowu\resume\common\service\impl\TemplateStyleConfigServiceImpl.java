package com.bimowu.resume.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bimowu.resume.common.service.TemplateStyleConfigService;
import com.bimowu.resume.config.PDFGenerationConfig;
import com.bimowu.resume.dto.ExtractedStyles;
import com.bimowu.resume.entity.TemplateStyleConfig;
import com.bimowu.resume.common.dao.TemplateStyleConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 模板样式配置服务实现类
 */
@Slf4j
@Service
public class TemplateStyleConfigServiceImpl extends ServiceImpl<TemplateStyleConfigMapper, TemplateStyleConfig> 
        implements TemplateStyleConfigService {
    
    @Autowired
    private PDFGenerationConfig pdfConfig;
    
    @Override
    public TemplateStyleConfig getByTemplateId(Integer templateId) {
        return baseMapper.selectByTemplateId(templateId);
    }
    
    @Override
    @Transactional
    public boolean saveOrUpdateStyleConfig(Integer templateId, ExtractedStyles extractedStyles) {
        try {
            TemplateStyleConfig existingConfig = getByTemplateId(templateId);
            
            if (existingConfig != null) {
                // 更新现有配置
                updateStyleConfig(existingConfig, extractedStyles);
                return updateById(existingConfig);
            } else {
                // 创建新配置
                TemplateStyleConfig newConfig = createStyleConfig(templateId, extractedStyles);
                return save(newConfig);
            }
        } catch (Exception e) {
            log.error("保存样式配置失败，模板ID: {}", templateId, e);
            return false;
        }
    }
    
    @Override
    public List<TemplateStyleConfig> getEnabledConfigs() {
        return baseMapper.selectEnabledConfigs();
    }
    
    @Override
    @Transactional
    public boolean batchUpdateSyncTime(List<Integer> templateIds) {
        try {
            return baseMapper.batchUpdateSyncTime(templateIds) > 0;
        } catch (Exception e) {
            log.error("批量更新同步时间失败", e);
            return false;
        }
    }
    

    
    @Override
    @Transactional
    public boolean enableStyleConfig(Integer templateId, boolean enabled) {
        try {
            TemplateStyleConfig config = getByTemplateId(templateId);
            if (config != null) {
                config.setEnabled(enabled);
                config.setUpdateTime(new Date());
                return updateById(config);
            }
            return false;
        } catch (Exception e) {
            log.error("启用/禁用样式配置失败，模板ID: {}", templateId, e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean deleteByTemplateId(Integer templateId) {
        try {
            LambdaQueryWrapper<TemplateStyleConfig> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TemplateStyleConfig::getTemplateId, templateId);
            return remove(wrapper);
        } catch (Exception e) {
            log.error("删除样式配置失败，模板ID: {}", templateId, e);
            return false;
        }
    }
    
    @Override
    public boolean existsByTemplateId(Integer templateId) {
        LambdaQueryWrapper<TemplateStyleConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TemplateStyleConfig::getTemplateId, templateId);
        return count(wrapper) > 0;
    }
    
    @Override
    public String getVersionByTemplateId(Integer templateId) {
        TemplateStyleConfig config = getByTemplateId(templateId);
        return config != null ? config.getVersion() : null;
    }
    
    /**
     * 创建新的样式配置
     */
    private TemplateStyleConfig createStyleConfig(Integer templateId, ExtractedStyles extractedStyles) {
        TemplateStyleConfig config = new TemplateStyleConfig();
        config.setTemplateId(templateId);
        config.setEnabled(true);
        config.setCreateTime(new Date());
        config.setUpdateTime(new Date());
        
        updateStyleConfig(config, extractedStyles);
        
        return config;
    }
    
    /**
     * 更新样式配置内容
     */
    private void updateStyleConfig(TemplateStyleConfig config, ExtractedStyles extractedStyles) {
        config.setCssContent(extractedStyles.getCss());
        config.setFontConfig(JSON.toJSONString(extractedStyles.getFonts()));
        config.setColorPalette(JSON.toJSONString(extractedStyles.getColors()));
        config.setLayoutConfig(JSON.toJSONString(extractedStyles.getLayout()));
        config.setResponsiveRules(JSON.toJSONString(extractedStyles.getResponsive()));
        config.setVersion(extractedStyles.getVersion());
        config.setLastSyncTime(new Date());
        config.setUpdateTime(new Date());
    }
}