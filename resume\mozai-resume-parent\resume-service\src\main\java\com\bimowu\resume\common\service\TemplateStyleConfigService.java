package com.bimowu.resume.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bimowu.resume.dto.ExtractedStyles;
import com.bimowu.resume.entity.TemplateStyleConfig;

import java.util.List;

/**
 * 模板样式配置服务接口
 */
public interface TemplateStyleConfigService extends IService<TemplateStyleConfig> {
    
    /**
     * 根据模板ID获取样式配置
     */
    TemplateStyleConfig getByTemplateId(Integer templateId);
    
    /**
     * 保存或更新样式配置
     */
    boolean saveOrUpdateStyleConfig(Integer templateId, ExtractedStyles extractedStyles);
    
    /**
     * 获取所有启用的样式配置
     */
    List<TemplateStyleConfig> getEnabledConfigs();
    
    /**
     * 批量更新同步时间
     */
    boolean batchUpdateSyncTime(List<Integer> templateIds);
    

    
    /**
     * 启用或禁用样式配置
     */
    boolean enableStyleConfig(Integer templateId, boolean enabled);
    
    /**
     * 删除样式配置
     */
    boolean deleteByTemplateId(Integer templateId);
    
    /**
     * 检查样式配置是否存在
     */
    boolean existsByTemplateId(Integer templateId);
    
    /**
     * 获取样式配置版本
     */
    String getVersionByTemplateId(Integer templateId);
}