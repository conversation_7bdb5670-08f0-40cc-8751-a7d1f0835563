import{S as g}from"./index-d8f61d92.js";let c=null,o=!1;try{o=!1}catch(e){console.warn("初始化OSS失败:",e),o=!1}function p(){return o}async function l(){if({}.DISABLE_OSS==="true")return o=!1,!1;try{return c=(await g(()=>import("./aliyun-oss-sdk-9440289b.js").then(t=>t.a),["assets/aliyun-oss-sdk-9440289b.js","assets/index-d8f61d92.js","assets/index-0ec35521.css"])).default,o=!0,!0}catch(e){return console.warn("无法加载ali-oss模块，将使用服务器中转上传方式",e),o=!1,!1}}async function h(){if(await l(),!o)throw new Error("OSS模块不可用，请使用服务器中转上传");try{const e=localStorage.getItem("token"),t=await fetch("/interview/oss/sts",{method:"GET",credentials:"include",headers:{"Content-Type":"application/json",token:e||""}});if(!t.ok)throw new Error(`获取OSS授权失败: ${t.statusText}`);const s=await t.json();if(s.code!==0)throw new Error(s.message||"获取OSS授权失败");return s.data}catch(e){throw console.error("获取OSS临时授权失败:",e),e}}async function y(e){if(await l(),!o||!c)throw new Error("OSS模块不可用，请使用服务器中转上传");const t={region:"oss-cn-beijing",accessKeyId:e.accessKeyId,accessKeySecret:e.accessKeySecret,stsToken:e.securityToken,bucket:e.bucket,secure:!0,timeout:12e4,cname:!1,internal:!0,useFetch:!0,retries:3,endpoint:e.endpoint};return console.log("OSS客户端配置:",JSON.stringify({region:t.region,bucket:t.bucket})),new c(t)}async function m(e,t="interview",s){if(await l(),!o||!c)throw new Error("OSS模块不可用，请使用服务器中转上传");try{const r=await h();console.log("获取到STS授权:",JSON.stringify({region:r.region,bucket:r.bucket,endpoint:r.endpoint,accessKeyId:r.accessKeyId,accessKeySecret:r.accessKeySecret}));const S=await y(r),i=new Date,f=`${i.getFullYear()}${(i.getMonth()+1).toString().padStart(2,"0")}${i.getDate().toString().padStart(2,"0")}`,w=e.name.substring(e.name.lastIndexOf(".")),n=`${t}/${f}/${Date.now()}${w}`;console.log("开始上传文件:",n);const u=await S.multipartUpload(n,e,{progress:function(a,d){s&&s(Math.floor(a*100),d),console.log("上传进度:",Math.floor(a*100)+"%")},parallel:4,partSize:1024*1024});if(u.res&&u.res.status===200){const a=`https://${r.bucket}.oss-${r.region}.aliyuncs.com/${n}`;return console.log("上传成功，文件URL:",a),{success:!0,url:a,fileName:n,ossPath:n}}else throw new Error("上传失败，请重试")}catch(r){throw console.error("OSS上传失败:",r),r}}export{p as checkOssAvailability,y as createOssClient,h as getOssToken,l as loadOssModule,m as uploadToOss};
