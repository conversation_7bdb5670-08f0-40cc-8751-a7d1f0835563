<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template1 优化预览</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: #f5f5f5;
        }
        
        .resume-template {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            background: #fafafa;
            color: #333;
            min-height: 100vh;
        }
        
        .resume-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 30px;
            background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
            color: white;
            min-height: 100px;
        }
        
        .resume-title h1 {
            font-size: 32px;
            margin: 0;
            font-weight: bold;
            letter-spacing: 2px;
        }
        
        .resume-contact {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: flex-end;
        }
        
        .contact-row {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: white;
        }
        
        .resume-content {
            padding: 25px 30px;
            background: #fafafa;
        }
        
        .resume-section {
            margin-bottom: 25px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-header {
            position: relative;
            border-bottom: 2px solid #4A90E2;
            margin-bottom: 15px;
            padding-bottom: 8px;
        }
        
        .section-header h2 {
            font-size: 18px;
            color: #4A90E2;
            margin: 0;
            font-weight: bold;
            letter-spacing: 1px;
        }
        
        .work-item, .education-item, .project-item {
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .work-item:last-child, .education-item:last-child, .project-item:last-child {
            border-bottom: none;
        }
        
        .work-header, .education-header, .project-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            align-items: center;
        }
        
        .work-date, .edu-date, .project-date {
            font-weight: bold;
            color: #666;
            font-size: 14px;
            min-width: 140px;
        }
        
        .work-company, .edu-school, .project-title {
            font-weight: bold;
            color: #333;
            font-size: 16px;
            flex: 1;
            text-align: center;
        }
        
        .work-position, .edu-degree, .project-role {
            font-weight: bold;
            color: #4A90E2;
            font-size: 14px;
            min-width: 120px;
            text-align: right;
        }
        
        .work-description, .project-description, .education-details {
            line-height: 1.8;
            text-align: justify;
            color: #555;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="resume-template">
        <!-- 简历头部 -->
        <div class="resume-header">
            <div class="resume-title">
                <h1>侯富文</h1>
            </div>
            <div class="resume-contact">
                <div class="contact-row">
                    <div class="contact-item">
                        <span>📱 20岁</span>
                    </div>
                    <div class="contact-item">
                        <span>📞 13037044263</span>
                    </div>
                </div>
                <div class="contact-row">
                    <div class="contact-item">
                        <span>✉️ <EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <span>📍 陕西省/西安</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 简历内容 -->
        <div class="resume-content">
            <!-- 教育经历 -->
            <div class="resume-section">
                <div class="section-header">
                    <h2>教育经历</h2>
                </div>
                <div class="education-content">
                    <div class="education-item">
                        <div class="education-header">
                            <div class="edu-date">2023-09 - 2027-07</div>
                            <div class="edu-school">山西农业大学</div>
                            <div class="edu-degree">软件工程（本科）</div>
                        </div>
                        <div class="education-details">
                            主修课程：Java程序设计、数据结构、算法分析与设计、计算机网络、软件工程
                        </div>
                    </div>
                </div>
            </div>

            <!-- 工作经历 -->
            <div class="resume-section">
                <div class="section-header">
                    <h2>工作经历</h2>
                </div>
                <div class="work-content">
                    <div class="work-item">
                        <div class="work-header">
                            <div class="work-date">2024-09 - 2025-06</div>
                            <div class="work-company">农大实验室运维实习生</div>
                            <div class="work-position">软件开发实习</div>
                        </div>
                        <div class="work-description">
                            1. 参与实验室实验室管理系统开发，后端采用SpringBoot框架，前端采用Vue；<br>
                            2. 负责实验数据库的维护与分析，优化sql查询语句，提升查询效率；<br>
                            3. 协助实验室日常bug，修复代码缺陷，优化代码架构和性能。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目经验 -->
            <div class="resume-section">
                <div class="section-header">
                    <h2>项目经验</h2>
                </div>
                <div class="section-content">
                    <div class="project-item">
                        <div class="project-header">
                            <div class="project-title">人力资源信息管理系统</div>
                            <div class="project-role">软件工程师</div>
                            <div class="project-date">2025-02 - 2025-07</div>
                        </div>
                        <div class="project-description">
                            工作职责：需求分析与设计开发，系统架构设计，系统代码编写，系统测试与部署，维护和优化系统上线。<br>
                            技术栈：Spring Boot + Mybatis + MySQL + Redis + Linux + Vue + Nginx
                        </div>
                    </div>
                </div>
            </div>

            <!-- 技能特长 -->
            <div class="resume-section">
                <div class="section-header">
                    <h2>技能特长</h2>
                </div>
                <div class="skills-content">
                    <div class="skill-description-item">
                        <div class="skill-description-body">
                            JAVA基础扎实：面向OO、多线程、集合等等，熟悉常用设计模式和数据结构；<br>
                            熟悉JVM虚拟机原理，对象生命周期，类加载机制，了解常见的JVM调优参数，掌握JVM性能调优工具；
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人评价 -->
            <div class="resume-section">
                <div class="section-header">
                    <h2>个人评价</h2>
                </div>
                <div class="evaluation-content">
                    有良好的沟通和协调能力，有良好的学习和适应能力；<br>
                    具备扎实的编程工作基础，有耐心、有责任心和执行能力；<br>
                    能够灵活使用Office办公软件，能够进行数据统计分析；<br>
                    富有团队协作精神和较强的集体荣誉感。
                </div>
            </div>
        </div>
    </div>
</body>
</html>
