import ElTree from 'element-plus/es/components/tree';
import type { Ref } from 'vue';
import type ElSelect from 'element-plus/es/components/select';
export declare const useTree: (props: any, { attrs, slots, emit }: {
    attrs: any;
    slots: any;
    emit: any;
}, { select, tree, key, }: {
    select: Ref<InstanceType<typeof ElSelect> | undefined>;
    tree: Ref<InstanceType<typeof ElTree> | undefined>;
    key: Ref<string>;
}) => any;
