import type { ComponentInternalInstance } from 'vue';
import type { CheckboxProps } from '../checkbox';
import type { CheckboxModel } from '../composables';
export declare const useCheckboxStatus: (props: CheckboxProps, slots: ComponentInternalInstance['slots'], { model }: Pick<CheckboxModel, 'model'>) => {
    checkboxButtonSize: import("vue").ComputedRef<"" | "default" | "small" | "large">;
    isChecked: import("vue").ComputedRef<boolean>;
    isFocused: import("vue").Ref<boolean>;
    checkboxSize: import("vue").ComputedRef<"" | "default" | "small" | "large">;
    hasOwnLabel: import("vue").ComputedRef<boolean>;
};
export declare type CheckboxStatus = ReturnType<typeof useCheckboxStatus>;
