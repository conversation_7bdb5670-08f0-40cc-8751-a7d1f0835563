<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bimowu.interview.dao.InterviewSpeechRecordMapper">
    
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.bimowu.interview.model.InterviewSpeechRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="interview_id" property="interviewId" jdbcType="VARCHAR"/>
        <result column="speaker_id" property="speakerId" jdbcType="INTEGER"/>
        <result column="start_time" property="startTime" jdbcType="INTEGER"/>
        <result column="end_time" property="endTime" jdbcType="INTEGER"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 所有列 -->
    <sql id="Base_Column_List">
        id, interview_id, speaker_id, start_time, end_time, content, create_time, update_time
    </sql>
    
    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.bimowu.interview.model.InterviewSpeechRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO interview_speech_record (
            interview_id, speaker_id, start_time, end_time, content, create_time, update_time
        ) VALUES (
            #{interviewId,jdbcType=VARCHAR},
            #{speakerId,jdbcType=INTEGER},
            #{startTime,jdbcType=INTEGER},
            #{endTime,jdbcType=INTEGER},
            #{content,jdbcType=VARCHAR},
            NOW(),
            NOW()
        )
    </insert>
    
    <!-- 更新记录 -->
    <update id="update" parameterType="com.bimowu.interview.model.InterviewSpeechRecord">
        UPDATE interview_speech_record
        <set>
            <if test="interviewId != null">
                interview_id = #{interviewId,jdbcType=VARCHAR},
            </if>
            <if test="speakerId != null">
                speaker_id = #{speakerId,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=INTEGER},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=INTEGER},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>
    
    <!-- 根据ID查询记录 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview_speech_record
        WHERE id = #{id,jdbcType=BIGINT}
    </select>
    
    <!-- 根据面试ID查询记录 -->
    <select id="selectByInterviewId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview_speech_record
        WHERE interview_id = #{interviewId,jdbcType=VARCHAR}
        ORDER BY start_time ASC
    </select>
    
    <!-- 根据面试ID和时间范围查询记录 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview_speech_record
        WHERE interview_id = #{interviewId,jdbcType=VARCHAR}
        AND start_time >= #{startTime,jdbcType=INTEGER}
        AND end_time &lt;= #{endTime,jdbcType=INTEGER}
        ORDER BY start_time ASC
    </select>
    
    <!-- 根据面试ID和发言人ID查询记录 -->
    <select id="selectBySpeaker" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview_speech_record
        WHERE interview_id = #{interviewId,jdbcType=VARCHAR}
        AND speaker_id = #{speakerId,jdbcType=INTEGER}
        ORDER BY start_time ASC
    </select>
    
    <!-- 根据条件查询记录 -->
    <select id="selectByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview_speech_record
        <where>
            <if test="interviewId != null">
                AND interview_id = #{interviewId,jdbcType=VARCHAR}
            </if>
            <if test="speakerId != null">
                AND speaker_id = #{speakerId,jdbcType=INTEGER}
            </if>
            <if test="startTime != null">
                AND start_time >= #{startTime,jdbcType=INTEGER}
            </if>
            <if test="endTime != null">
                AND end_time &lt;= #{endTime,jdbcType=INTEGER}
            </if>
            <if test="content != null">
                AND content LIKE CONCAT('%', #{content,jdbcType=VARCHAR}, '%')
            </if>
            <if test="createTimeStart != null">
                AND create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="createTimeEnd != null">
                AND create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY start_time ASC
    </select>
    
    <!-- 根据ID删除记录 -->
    <delete id="deleteById">
        DELETE FROM interview_speech_record
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>
    
    <!-- 根据面试ID删除所有记录 -->
    <delete id="deleteByInterviewId">
        DELETE FROM interview_speech_record
        WHERE interview_id = #{interviewId,jdbcType=VARCHAR}
    </delete>
</mapper> 