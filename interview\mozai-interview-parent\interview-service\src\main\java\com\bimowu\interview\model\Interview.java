package com.bimowu.interview.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 面试信息实体类
 */
@Data
public class Interview {
    
    /**
     * 面试ID
     */
    private String id;

    private Integer userId;
    
    /**
     * 面试类型(mock:模拟面试,formal:正式面试)
     */
    private String type;
    
    /**
     * 应聘者姓名
     */
    private String candidateName;
    
    /**
     * 面试公司(正式面试)
     */
    private String company;
    
    /**
     * 面试职位
     */
    private String position;
    
    /**
     * 面试阶段 (hr/tech)
     */
    private String stage;
    
    /**
     * 工作经验 (fresh/1-3/3-5/5+)
     */
    private String experience;

    
    /**
     * 面试状态 (0:进行中, 1:等待结果, 2:已完成)
     */
    private Integer status;
    
    /**
     * 面试结果 (0:未通过, 1:通过, null:未评定)
     */
    private Integer result;
    
    /**
     * 总体评分 (0-100)
     */
    private Integer overallScore;
    
    /**
     * 面试反馈
     */
    private String feedback;
    
    /**
     * 优势表现(JSON格式)
     */
    private String strengths;
    
    /**
     * 改进建议(JSON格式)
     */
    private String improvements;
    
    /**
     * 面试视频文件URL
     */
    private String videoUrl;
    
    /**
     * 面试时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date interviewTime;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
} 