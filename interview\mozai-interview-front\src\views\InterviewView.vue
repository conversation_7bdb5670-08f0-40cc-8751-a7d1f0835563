<template>
  <div class="interview-container">
    <div class="interview-header">
      <h1>AI面试进行中</h1>
      <el-progress :percentage="progress" :format="progressFormat" />
    </div>

    <div class="interview-main">
      <div class="digital-human-container">
        <div class="digital-human">
          <!-- 数字人视频/图像区域 -->
          <div class="digital-human-content">
            <!-- 3D数字人容器 (替换静态图片) -->
            <div ref="digitalHumanContainer" class="digital-human-3d-container"></div>

            <!-- 数字人加载状态 -->
            <div v-if="digitalHumanLoading" class="digital-human-loading">
              <el-progress type="dashboard" :percentage="50" status="warning" :indeterminate="true" />
              <p>正在加载数字人模型...</p>
            </div>

            <!-- 数字人错误提示 -->
            <div v-if="digitalHumanError" class="digital-human-error">
              <el-alert type="error" :title="digitalHumanError" :closable="false" />
              <img src="@/assets/digital-human.png" alt="数字人" class="digital-human-image" />
            </div>

            <!-- 问题字幕区域 -->
            <div class="question-subtitle" v-if="isAskingQuestion">
              <p>{{ typedQuestion }}</p>
            </div>
          </div>
        </div>

        <div class="question-container">
          <h3>当前问题:</h3>
          <div class="question">{{ currentQuestion }}</div>
        </div>
      </div>

      <div class="user-video-container">
        <!-- 添加顶部状态提示条 -->
        <div class="status-bar" v-if="cameraActive && !isAskingQuestion">
          <div class="status-item" :class="{'active': micMuted}">
            <el-icon><Mute /></el-icon>
            麦克风已静音
          </div>
          <div v-if="silenceDetected" class="countdown-timer">
            {{ Math.ceil((autoNextTimeout - autoNextCounter) / 1000) }}秒后自动下一题
          </div>
          <el-button
              v-if="cameraActive && !isAskingQuestion && !isLastQuestion"
              type="primary"
              size="small"
              @click="skipToNextQuestion"
          >
            下一题
          </el-button>
        </div>

        <div class="video-wrapper">
          <video ref="userVideo" autoplay muted></video>
          <div v-if="!cameraActive" class="video-placeholder">
            <el-button type="primary" @click="startInterview">开始面试</el-button>
          </div>
          <div class="recording-indicator" v-if="isRecording">
            <span class="recording-dot"></span> 正在录制
          </div>
        </div>

        <div class="controls">
          <div class="current-status" v-if="cameraActive">
            <span v-if="isAskingQuestion" class="status-text">AI正在提问...</span>
            <span v-else-if="isRecording && micMuted" class="status-text warning-text">麦克风已静音</span>
            <span v-else-if="isRecording" class="status-text">请回答问题...</span>
            <span v-else class="status-text">准备中...</span>
          </div>

          <div class="auto-next-indicator" v-if="silenceDetected">
            <el-progress type="circle" :percentage="autoNextProgress" :width="36" />
            <span>{{ Math.ceil((autoNextTimeout - autoNextCounter) / 1000) }}秒后自动下一题</span>
          </div>

          <el-button
              v-if="cameraActive && !isAskingQuestion"
              :type="micMuted ? 'danger' : 'warning'"
              circle
              @click="toggleMicrophone"
              :class="{'mic-toggle-btn': true, 'is-muted': micMuted}"
              :title="micMuted ? '已静音，将自动进入下一题' : '点击静音麦克风'"
          >
            <el-icon>
              <Microphone v-if="!micMuted" />
              <Mute v-else />
            </el-icon>
          </el-button>

          <el-button
              v-if="cameraActive && isLastQuestion && hasAnsweredAll"
              type="success"
              @click="finishInterview"
          >
            结束面试
          </el-button>

          <el-button
              v-if="cameraActive && !isAskingQuestion && !isLastQuestion"
              type="primary"
              @click="skipToNextQuestion"
          >
            下一题
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { Microphone, Mute } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useInterviewStore } from '@/store'
import RecordRTC from 'recordrtc'
import { DigitalHuman, createDigitalHuman } from '@/utils/digitalHuman'
import api, {  } from '@/api'
import axios from 'axios'
import httpClient from '@/utils/http'

const router = useRouter()
const interviewStore = useInterviewStore()

// 视频相关
const userVideo = ref<HTMLVideoElement | null>(null)
const cameraActive = ref(false)
const isRecording = ref(false)
const videoRecorder = ref<any>(null)
const audioRecorder = ref<any>(null)
const answeredQuestions = ref<Set<number>>(new Set())
const isAskingQuestion = ref(false) // 控制是否显示问题字幕
const typedQuestion = ref('') // 用于打字机效果的问题文本
const typingInterval = ref<number | null>(null) // 打字机效果的定时器
const speechSynthesis = window.speechSynthesis // 语音合成API
const micMuted = ref(false) // 控制麦克风是否静音

// 数字人相关
const digitalHumanContainer = ref<HTMLElement | null>(null)
const digitalHuman = ref<DigitalHuman | null>(null)
const digitalHumanLoading = ref(false)
const digitalHumanError = ref<string | null>(null)

// 自动进入下一题相关
const silenceDetected = ref(false)
const autoNextCounter = ref(0)
const autoNextTimeout = 5000 // 5秒静音后自动下一题
const autoNextInterval = ref<number | null>(null)
const autoNextProgress = computed(() => {
  return Math.min(100, (autoNextCounter.value / autoNextTimeout) * 100)
})

// 音频分析相关
const audioContext = ref<AudioContext | null>(null)
const audioAnalyser = ref<AnalyserNode | null>(null)
const audioDataArray = ref<Uint8Array | null>(null)
const silenceCheckInterval = ref<number | null>(null)
const userMediaStream = ref<MediaStream | null>(null)
const silenceCounter = ref(0) // 连续静音帧计数器
const minimumSilenceFrames = 15 // 需要连续多少帧静音才触发（约5秒）

// 新增每个问题的单独录音
const questionAudioRecorders = ref<Record<number, RecordRTC>>({})
const questionAudioBlobs = ref<Record<number, Blob>>({})
const isQuestionRecording = ref(false) // 控制当前问题是否正在录音

// 计算属性
const currentQuestion = computed(() => interviewStore.currentQuestion)
const isLastQuestion = computed(() => interviewStore.isLastQuestion)
const isFirstQuestion = computed(() => interviewStore.currentQuestionIndex === 0)
const progress = computed(() => interviewStore.progress)
const hasAnsweredAll = computed(() =>
    answeredQuestions.value.size === interviewStore.questions.length
)
const currentQuestionIndex = computed(() => interviewStore.currentQuestionIndex)

// 格式化进度条文本
const progressFormat = (percentage: number) => {
  return `${interviewStore.currentQuestionIndex + 1}/${interviewStore.questions.length}`
}

// 定义转写处理状态
interface ProcessingState {
  isProcessing: boolean
  status: string
  isCompleted: boolean
  hasError: boolean
}

const questionProcessingStates = ref<Record<number, ProcessingState>>({})

// 问题转写结果
const questionTranscriptions = ref<Record<number, string>>({})

// 是否自动前进到下一个问题
const isAutoAdvanceEnabled = ref(true)

// 移动到指定问题
const moveToQuestion = (index: number) => {
  if (index >= 0 && index < interviewStore.questions.length) {
    interviewStore.setCurrentQuestionIndex(index)
  }
}

// 开始面试流程
const startInterview = async () => {
  await startCamera()
  if (cameraActive.value) {
    // 重置麦克风状态
    micMuted.value = false

    // 开始录制
    startFullRecording()
    // 开始第一个问题
    playCurrentQuestion()
  }
}

// 播放当前问题
const playCurrentQuestion = () => {
  if (!cameraActive.value) return

  // 如果正在检测静音，先停止
  stopSilenceDetection()

  // 重置静音计数器
  silenceCounter.value = 0
  silenceDetected.value = false

  // 显示AI提问
  isAskingQuestion.value = true

  // 播放数字人提问
  playDigitalHumanQuestion()
}

// 开启摄像头
const startCamera = async () => {
  try {
    console.log('正在请求媒体访问权限...')
    const stream = await navigator.mediaDevices.getUserMedia({
      video: { width: 640, height: 480 },
      audio: true
    })

    console.log('成功获取媒体流', '视频轨道:', stream.getVideoTracks().length, '音频轨道:', stream.getAudioTracks().length)

    // 保存媒体流用于录制
    userMediaStream.value = stream

    if (userVideo.value) {
      userVideo.value.srcObject = stream
      cameraActive.value = true

      // 初始化音频分析器
      console.log('开始初始化音频分析器...')
      setupAudioAnalyser(stream)

      // 检查音频分析器是否成功初始化
      if (!audioAnalyser.value || !audioDataArray.value) {
        console.warn('音频分析器初始化失败，静音检测将不可用')
        ElMessage.warning('麦克风初始化异常，静音检测可能不工作')
      } else {
        console.log('音频分析器初始化成功')
      }

      return true
    }
  } catch (error) {
    console.error('无法访问摄像头/麦克风:', error)
    ElMessage.error('无法访问摄像头或麦克风，请确保已授予权限')
  }

  return false
}

// 人脸检测功能已移除

// 设置音频分析器
const setupAudioAnalyser = (stream: MediaStream) => {
  try {
    if (!stream) {
      console.error('设置音频分析器失败: 媒体流不可用')
      return
    }

    // 检查音频轨道
    const audioTracks = stream.getAudioTracks()
    if (audioTracks.length === 0) {
      console.error('设置音频分析器失败: 没有音频轨道')
      return
    }

    // 创建音频上下文和分析器
    audioContext.value = new AudioContext()
    audioAnalyser.value = audioContext.value.createAnalyser()
    audioAnalyser.value.fftSize = 2048

    const source = audioContext.value.createMediaStreamSource(stream)
    source.connect(audioAnalyser.value)

    const bufferLength = audioAnalyser.value.frequencyBinCount
    audioDataArray.value = new Uint8Array(bufferLength)

    console.log('音频分析器设置成功', '缓冲区长度:', bufferLength)
  } catch (error) {
    console.error('设置音频分析器失败:', error)

    // 重置音频分析器相关变量
    audioContext.value = null
    audioAnalyser.value = null
    audioDataArray.value = null
  }
}

// 检测静音 - 完全重写
const checkForSilence = () => {
  try {
    // 必要条件检查
    if (!audioAnalyser.value || !audioDataArray.value) {
      console.error('音频分析器未初始化')
      stopSilenceDetection() // 停止检测避免持续报错
      return
    }

    // 只在AI不在提问时进行静音检测
    if (isAskingQuestion.value) {
      // console.log('AI正在提问，暂停静音检测')
      return
    }

    // 如果麦克风已被用户主动静音，直接触发自动下一题
    if (micMuted.value) {
      if (!silenceDetected.value) {
        console.log('麦克风已静音，准备自动进入下一题')
        silenceDetected.value = true
        startAutoNextTimer()
      }
      return
    }

    // 获取音频数据
    audioAnalyser.value.getByteFrequencyData(audioDataArray.value)

    // 计算平均音量 - 只考虑人声频率范围(85-255Hz)
    let sum = 0
    let count = 0

    // 使用一个更合适的频率范围来判断人声
    // 在48kHz采样率下，索引10-30大约对应86-258Hz
    const minIndex = 10
    const maxIndex = 50

    for (let i = minIndex; i < Math.min(maxIndex, audioDataArray.value.length); i++) {
      sum += audioDataArray.value[i]
      count++
    }

    const average = count > 0 ? sum / count : 0

    // 极低的静音阈值
    const silenceThreshold = 100

    // 记录音量
    if (isRecording.value) {
      console.log(`当前音量: ${average.toFixed(2)}, 静音阈值: ${silenceThreshold}, 静音计数: ${silenceCounter.value}/${minimumSilenceFrames}`)
    }

    // 静音检测逻辑
    if (average < silenceThreshold) {
      // 检测到静音，增加计数器
      silenceCounter.value++

      // 如果连续静音达到阈值，触发自动下一题
      if (silenceCounter.value >= minimumSilenceFrames && !silenceDetected.value) {
        console.log(`检测到连续${minimumSilenceFrames}帧静音，准备自动进入下一题`)
        silenceDetected.value = true

        // 启动自动下一题计时器
        startAutoNextTimer()
      }
    } else {
      // 检测到声音，重置计数器和状态
      if (silenceCounter.value > 0) {
        console.log('检测到声音，重置静音计数')
        silenceCounter.value = 0

        // 如果检测到声音，停止自动下一题计时器
        if (silenceDetected.value) {
          silenceDetected.value = false
          stopAutoNextTimer()
        }
      }
    }
  } catch (error) {
    console.error('静音检测过程中发生错误:', error)
    // 出错时停止静音检测
    stopSilenceDetection()
  }
}

// 开始静音检测 - 完全重写
const startSilenceDetection = () => {
  // 先确保音频分析器已经初始化
  if (!audioAnalyser.value || !audioDataArray.value) {
    console.log('音频分析器未初始化，无法启动静音检测')
    return
  }

  // 先停止之前的检测
  stopSilenceDetection()

  // 重置静音计数器
  silenceCounter.value = 0
  silenceDetected.value = false

  console.log('开始静音检测')
  silenceCheckInterval.value = window.setInterval(() => {
    checkForSilence()
  }, 300) // 每300毫秒检测一次
}

// 停止静音检测
const stopSilenceDetection = () => {
  if (silenceCheckInterval.value !== null) {
    clearInterval(silenceCheckInterval.value)
    silenceCheckInterval.value = null
  }

  silenceCounter.value = 0
  silenceDetected.value = false
  console.log('停止静音检测')
}

// 开始全程录制
const startFullRecording = () => {
  // 如果已经在录制则不重复开始
  if (isRecording.value) return;

  if (!userMediaStream.value) {
    ElMessage.warning('无法获取媒体流，请重新开始')
    return
  }

  // 录制视频
  videoRecorder.value = new RecordRTC(userMediaStream.value, {
    type: 'video',
    mimeType: 'video/webm',
    recorderType: RecordRTC.MediaStreamRecorder
  })

  // 录制音频 - 使用符合阿里云语音识别要求的配置
  audioRecorder.value = new RecordRTC(userMediaStream.value, {
    type: 'audio',
    mimeType: 'audio/wav', // 使用WAV格式，广泛支持
    recorderType: RecordRTC.StereoAudioRecorder,
    numberOfAudioChannels: 1, // 使用单声道
    desiredSampRate: 16000, // 使用16kHz采样率，符合语音识别要求
    disableLogs: false
  })

  videoRecorder.value.startRecording()
  audioRecorder.value.startRecording()
  isRecording.value = true

  console.log('开始全程录制，使用16kHz采样率的WAV格式')
}

// 停止录制
const stopRecording = () => {
  if (!videoRecorder.value || !audioRecorder.value) return

  videoRecorder.value.stopRecording(() => {
    const videoBlob = videoRecorder.value.getBlob()
    interviewStore.setVideoRecording(videoBlob)

    // 上传视频文件
    if (interviewStore.interviewId) {
      const videoFile = new File([videoBlob], `interview-${interviewStore.interviewId}.mp4`, { type: 'video/mp4' })

      // 使用OSS直传上传视频
      import('../utils/ossUploader').then(module => {
        const { uploadToOss } = module;
        uploadToOss(videoFile, 'interview/video', (percent) => {
          console.log(`视频上传进度: ${percent}%`)
        }).then(result => {
          // 上传成功后，更新面试记录中的视频URL
          api.updateInterviewVideoUrl(interviewStore.interviewId, result.url)
              .then((response: any) => {
                if (response.code === 0) {
                  console.log('视频URL更新成功:', result.url)
                } else {
                  console.error('视频URL更新失败:', response.message)
                }
              })
              .catch((error: any) => {
                console.error('视频URL更新出错:', error)
              })
        }).catch(error => {
          console.error('视频上传到OSS失败:', error)
          // 不再回退到传统上传方式，只显示错误
          ElMessage.error('视频上传失败，请重试')
        })
      }).catch(error => {
        console.error('加载OSS上传工具失败:', error)
        // 不再使用传统上传方式，只显示错误
        ElMessage.error('视频上传功能加载失败，请刷新页面重试')
      })
    }
  })

  audioRecorder.value.stopRecording(() => {
    const audioBlob = audioRecorder.value.getBlob()
    interviewStore.setAudioRecording(audioBlob)

    // 处理录制的数据
    const handleRecordedData = async () => {
      try {
        // 获取所有转写结果
        fetchAllTranscriptions()

        console.log('录制数据处理完成')
      } catch (error) {
        console.error('处理录制数据失败:', error)
      }
    }

    // 调用处理函数
    handleRecordedData()
  })

  // 停止所有问题的单独录音
  Object.keys(questionAudioRecorders.value).forEach(index => {
    const recorder = questionAudioRecorders.value[Number(index)]
    if (recorder) {
      recorder.stopRecording(() => {
        const audioBlob = recorder.getBlob()
        questionAudioBlobs.value[Number(index)] = audioBlob
      })
    }
  })

  isRecording.value = false
  console.log('停止录制')
}

// 开始录制当前问题的音频
const startQuestionRecording = () => {
  if (!userMediaStream.value) {
    console.error('无法开始录音：媒体流不可用')
    return
  }

  const index = currentQuestionIndex.value
  console.log(`开始录制问题 ${index} 的回答`)

  // 如果已经有录制器，先停止
  if (questionAudioRecorders.value[index]) {
    questionAudioRecorders.value[index].stopRecording()
  }

  // 创建新的录制器 - 使用符合阿里云语音识别要求的配置
  const audioRecorderOptions = {
    type: 'audio' as 'audio',
    mimeType: 'audio/wav' as 'audio/wav', // 使用WAV格式，阿里云支持
    recorderType: RecordRTC.StereoAudioRecorder,
    numberOfAudioChannels: 1 as 1, // 使用单声道，这对于语音识别非常重要
    desiredSampRate: 16000, // 使用16kHz采样率，符合语音识别要求
    bufferSize: 4096 as 4096, // 缓冲区大小
    disableLogs: false
  }

  try {
    // 创建一个不包含视频轨道的媒体流，只包含音频
    const audioOnlyStream = new MediaStream(userMediaStream.value.getAudioTracks())

    // 创建录音机
    questionAudioRecorders.value[index] = new RecordRTC(audioOnlyStream, audioRecorderOptions)

    // 开始录制
    questionAudioRecorders.value[index].startRecording()

    isQuestionRecording.value = true
    console.log(`问题 ${index} 录制已开始，使用配置:`, audioRecorderOptions)
  } catch (error) {
    console.error(`问题 ${index} 录制启动失败:`, error)
    ElMessage.error('录音启动失败，请刷新页面重试')
  }
}

// 停止录制当前问题的回答
const stopQuestionRecording = async () => {
  const index = currentQuestionIndex.value
  console.log(`停止录制问题 ${index} 的回答`)

  if (!questionAudioRecorders.value[index]) {
    console.warn(`问题 ${index} 没有正在进行的录制`)
    return
  }

  return new Promise<void>((resolve) => {
    questionAudioRecorders.value[index].stopRecording(() => {
      // 获取录制的音频blob
      questionAudioBlobs.value[index] = questionAudioRecorders.value[index].getBlob()
      console.log(`问题 ${index} 录制完成，音频大小: ${questionAudioBlobs.value[index].size} 字节`)

      // 上传到后端进行语音转写
      uploadAudioForTranscription(index, questionAudioBlobs.value[index])

      isQuestionRecording.value = false
      resolve()
    })
  })
}

// 上传音频到后端进行语音转写
const uploadAudioForTranscription = async (questionIndex: number, audioBlob: Blob) => {
  console.log(`准备上传问题 ${questionIndex} 的音频进行转写`)

  // 确保有面试ID
  if (!interviewStore.interviewId) {
    console.error('缺少面试ID，无法关联转写结果')
    return null
  }

  // 检查Blob的MIME类型
  const blobType = audioBlob.type
  console.log(`音频Blob的MIME类型：${blobType}`)

  // 添加音频格式检测和调试信息
  console.log(`音频信息 - 大小：${audioBlob.size} 字节，类型：${audioBlob.type}`);

  // 检查音频格式是否被支持
  const supportedFormats = ['audio/wav', 'audio/mpeg', 'audio/mp4', 'audio/x-m4a', 'audio/wma', 'audio/aac', 'audio/ogg', 'audio/amr', 'audio/flac'];
  if (!supportedFormats.includes(audioBlob.type)) {
    console.warn(`警告：音频格式 ${audioBlob.type} 可能不被阿里云语音识别支持`);
  } else {
    console.log(`音频格式 ${audioBlob.type} 被阿里云语音识别支持`);
  }

  // 显示加载状态
  const questionProcessingState = questionProcessingStates.value[questionIndex]
  if (questionProcessingState) {
    questionProcessingState.isProcessing = true
    questionProcessingState.status = '正在转写音频...'
  }

  try {
    // 将Blob转换为带有适当文件扩展名的File对象
    let fileExtension = '.wav' // 默认扩展名
    let contentType = 'audio/wav' // 默认MIME类型

    // 根据MIME类型确定正确的扩展名
    if (blobType.includes('webm')) {
      fileExtension = '.webm'
      contentType = 'audio/webm'
    } else if (blobType.includes('mp3')) {
      fileExtension = '.mp3'
      contentType = 'audio/mpeg'
    } else if (blobType.includes('m4a')) {
      fileExtension = '.m4a'
      contentType = 'audio/m4a'
    } else if (blobType.includes('ogg')) {
      fileExtension = '.ogg'
      contentType = 'audio/ogg'
    } else if (blobType.includes('flac')) {
      fileExtension = '.flac'
      contentType = 'audio/flac'
    }

    // 创建文件对象
    const audioFile = new File(
        [audioBlob],
        `question_${questionIndex}_${Date.now()}${fileExtension}`,
        { type: contentType }
    )

    console.log(`已创建文件: ${audioFile.name}, 大小: ${audioFile.size} 字节, 类型: ${audioFile.type}`)

    // 获取当前问题内容
    const currentQuestionText = interviewStore.questions[questionIndex]

    // 使用API调用上传音频进行转写
    console.log(`使用面试ID ${interviewStore.interviewId} 发送转写请求...`)
    const response = await api.transcribeInterview(
        audioFile,
        questionIndex,
        interviewStore.interviewId,
        currentQuestionText // 传递问题内容
    )

    console.log('转写结果:', response)

    // 检查响应
    if (response && typeof response === 'object' && response.code === 0) {
      // 保存转写结果
      const transcriptionText = response.data.text
      questionTranscriptions.value[questionIndex] = transcriptionText

      // 保存到 store 中
      interviewStore.setTranscription(questionIndex, transcriptionText)

      // 更新状态
      if (questionProcessingState) {
        questionProcessingState.isProcessing = false
        questionProcessingState.status = '音频转写完成'
        questionProcessingState.isCompleted = true
      }

      // 检查是否需要进入下一个问题
      if (isAutoAdvanceEnabled.value && questionIndex < interviewStore.questions.length - 1) {
        // 延迟一下，让用户看到完成状态
        setTimeout(() => {
          moveToQuestion(questionIndex + 1)
        }, 1000)
      }

      return transcriptionText
    } else {
      // 处理错误
      const errorMsg = response && typeof response === 'object' ? response.message : '转写失败，未知错误'
      console.error('转写失败:', errorMsg)

      // 更新状态
      if (questionProcessingState) {
        questionProcessingState.isProcessing = false
        questionProcessingState.status = `转写失败: ${errorMsg}`
        questionProcessingState.hasError = true
      }

      return null
    }
  } catch (error) {
    console.error('转写请求失败:', error)

    // 更新状态
    if (questionProcessingState) {
      questionProcessingState.isProcessing = false
      questionProcessingState.status = `转写请求失败: ${error}`
      questionProcessingState.hasError = true
    }

    return null
  }
}

// 获取面试的所有转写结果
const fetchAllTranscriptions = async () => {
  if (!interviewStore.interviewId) return

  try {
    // console.log(`获取面试ID ${interviewStore.interviewId} 的所有转写结果`)

    // 兼容不同的开发环境
    const baseUrl = process.env.NODE_ENV === 'development'
        ? 'https://ceping.bimowo.com/interview'
        : '';

    const response = await fetch(`${baseUrl}/interview/speechToText/transcriptions/${interviewStore.interviewId}`, {
      headers: {
        'Accept': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      if (data.code === 200 && data.data) {
        // 将转写结果保存到store中
        interviewStore.setAllTranscriptions(data.data)
        console.log('成功获取转写结果', data.data)
      } else {
        console.warn('获取转写结果异常', data)
      }
    } else {
      console.error('获取转写结果失败', await response.text())
    }
  } catch (error) {
    console.error('获取转写结果失败', error)
  }
}

// 播放数字人提问
const playDigitalHumanQuestion = () => {
  // 如果数字人未初始化或处于错误状态，跳过
  if (!digitalHuman.value || digitalHumanError.value) {
    console.warn('数字人未准备好，无法播放提问')
    isAskingQuestion.value = false
    return
  }

  // 获取当前问题
  const question = currentQuestion.value
  if (!question) {
    console.warn('没有可用的问题')
    isAskingQuestion.value = false
    return
  }

  // 设置打字机效果
  typedQuestion.value = ''
  if (typingInterval.value) {
    clearInterval(typingInterval.value)
  }

  // 开始执行打字机效果
  let i = 0
  typingInterval.value = window.setInterval(() => {
    if (i <= question.length) {
      typedQuestion.value = question.substring(0, i)
      i++
    } else {
      if (typingInterval.value) {
        clearInterval(typingInterval.value)
      }
    }
  }, 50)

  // 让数字人开始说话动画
  digitalHuman.value.startTalking()

  // 使用语音合成API播放问题
  const utterance = new SpeechSynthesisUtterance(question)
  utterance.lang = 'zh-CN'
  utterance.rate = 1.0
  utterance.pitch = 1.0

  // 设置语音完成回调
  utterance.onend = () => {
    // 结束数字人说话动画
    if (digitalHuman.value) {
      digitalHuman.value.stopTalking()
    }

    // 停止提问状态
    isAskingQuestion.value = false

    // 开始录制当前问题的回答
    startQuestionRecording()

    // 开始检测静音
    startSilenceDetection()

    // 设置状态为正在录制
    isRecording.value = true
  }

  // 开始播放
  speechSynthesis.speak(utterance)
}

// 下一个问题
const nextQuestion = () => {
  // 停止语音合成
  if (speechSynthesis.speaking) {
    speechSynthesis.cancel()
  }

  // 停止当前问题的录制
  stopQuestionRecording()

  // 停止当前问题的静音检测
  stopSilenceDetection()

  // 停止自动下一题计时器
  stopAutoNextTimer()

  // 重置麦克风状态
  if (micMuted.value && userMediaStream.value) {
    const audioTracks = userMediaStream.value.getAudioTracks()
    audioTracks.forEach(track => {
      track.enabled = true
    })
    micMuted.value = false
  }

  // 进入下一个问题
  interviewStore.nextQuestion()
  playCurrentQuestion()
}

// 完成面试
const finishInterview = async () => {
  // 如果正在提问，不允许结束
  if (isAskingQuestion.value) return

  // 停止当前问题的录音
  if (isQuestionRecording.value) {
    await stopQuestionRecording()
  }

  // 停止全局录制
  stopRecording()

  // 获取所有转写结果
  // await fetchAllTranscriptions()

  // 其他结束面试的逻辑

  // 标记当前问题已回答（如果还未标记）
  if (currentQuestionIndex.value !== null) {
    answeredQuestions.value.add(currentQuestionIndex.value)
  }

  // 生成面试结果
  // generateInterviewResults()

  // 更新面试状态为已完成
  updateInterviewStatus(interviewStore.interviewId)

  // 跳转到结果页面
  router.push('/end')
}

async function updateInterviewStatus(interviewId: string) {
  try {
    const response = await httpClient.get(`/resume/status/${interviewId}`);
    const data = response.data;

    if (data.code === 0) {
      console.log('面试状态更新成功');
      return true;
    } else {
      console.error('面试状态更新失败:', data.message);
      return false;
    }
  } catch (error) {
    console.error('请求出错:', error);
    return false;
  }
}

// 生成面试结果
const generateInterviewResults = () => {
  // 生成模拟的面试结果
  const mockResults = {
    answers: interviewStore.questions.map((_, index) =>
        `这是问题${index + 1}的回答分析: 回答流畅，主要观点明确，但可以进一步加强案例支持。`
    ),
    scores: {
      content: 85,
      expression: 80,
      lighting: 95,
      noise: 90,
      overall: 85
    },
    feedback: '整体表现良好，回答问题时条理清晰，观点明确。面部表情自然，但可以适当增加肢体语言的表达。简历与回答一致性较高，展现了专业能力和团队协作精神。建议进一步增强案例细节的阐述，并在回答时更加突出个人在项目中的贡献和影响。'
  }

  // 设置面试结果
  interviewStore.setInterviewResults(mockResults)
}

// 确保语音合成API可用
const ensureSpeechSynthesis = () => {
  if (!speechSynthesis) {
    console.error('您的浏览器不支持语音合成API')
    return false
  }

  // 预加载语音
  speechSynthesis.getVoices()

  return true
}

// 添加直接跳转下一题的按钮功能
const skipToNextQuestion = async () => {
  // 如果正在提问，不允许跳转
  if (isAskingQuestion.value) return

  // 停止当前问题的录音
  if (isQuestionRecording.value) {
    await stopQuestionRecording()
  }

  // 如果正在检测静音，先停止
  stopSilenceDetection()

  // 重置静音计数器和状态
  silenceCounter.value = 0
  silenceDetected.value = false

  // 标记当前问题已回答
  answeredQuestions.value.add(currentQuestionIndex.value)

  // 前进到下一题
  interviewStore.nextQuestion()

  // 播放下一个问题
  playCurrentQuestion()
}

// 初始化数字人
const initDigitalHuman = () => {
  // 如果已经初始化则返回
  if (digitalHuman.value) return;

  // 确保容器已准备好
  if (!digitalHumanContainer.value) {
    console.error('数字人容器未找到');
    digitalHumanError.value = '数字人初始化失败：容器未找到';
    return;
  }

  digitalHumanLoading.value = true;
  console.log('开始初始化数字人模型...');

  try {
    // 从store获取设置
    const { model, voice } = interviewStore.digitalHuman;
    const gender = voice === 'female' ? 'female' : 'male';

    // 创建数字人实例，使用简化模式
    digitalHuman.value = createDigitalHuman(digitalHumanContainer.value, {
      gender,
      onLoad: () => {
        console.log('数字人模型加载成功');
        digitalHumanLoading.value = false;
      },
      onError: (error) => {
        console.error('数字人模型加载失败:', error);
        // 不要立即将错误状态设置为true，因为我们已经有了备用立方体模型
        // 但显示警告信息
        ElMessage.warning('3D数字人加载失败，已使用备用模式');
        digitalHumanLoading.value = false;
      }
    });
  } catch (error) {
    console.error('初始化数字人时出错:', error);
    digitalHumanError.value = '数字人初始化失败，请刷新页面重试';
    digitalHumanLoading.value = false;

    // 显示错误提示
    ElMessage.error('数字人初始化出错，已使用静态图片');
  }
}

// 销毁数字人
const disposeDigitalHuman = () => {
  if (digitalHuman.value) {
    digitalHuman.value.dispose();
    digitalHuman.value = null;
  }
}

// 切换麦克风静音状态
const toggleMicrophone = () => {
  if (!userMediaStream.value) return

  const audioTracks = userMediaStream.value.getAudioTracks()
  if (audioTracks.length === 0) {
    ElMessage.warning('未检测到麦克风')
    return
  }

  // 切换所有音频轨道的启用状态
  audioTracks.forEach(track => {
    track.enabled = !track.enabled
  })

  // 更新状态
  micMuted.value = !micMuted.value

  // 显示提示
  ElMessage.success(micMuted.value ? '麦克风已静音' : '麦克风已开启')

  // 如果静音，则启动静音检测以触发自动进入下一题
  if (micMuted.value && !isAskingQuestion.value) {
    // 设置静音计数器为较高值，以便更快触发自动进入下一题
    silenceCounter.value = Math.max(silenceCounter.value, Math.floor(minimumSilenceFrames / 2))
    silenceDetected.value = true

    // 启动自动下一题计时器
    startAutoNextTimer()
  } else {
    // 如果取消静音，则停止自动下一题计时器
    stopAutoNextTimer()
    silenceDetected.value = false
  }
}

// 启动自动下一题计时器
const startAutoNextTimer = () => {
  // 先停止之前的计时器
  stopAutoNextTimer()

  // 重置计数器
  autoNextCounter.value = 0

  // 启动新的计时器
  autoNextInterval.value = window.setInterval(() => {
    autoNextCounter.value += 100

    // 达到设定时间后自动进入下一题
    if (autoNextCounter.value >= autoNextTimeout) {
      // 标记当前问题已回答
      answeredQuestions.value.add(interviewStore.currentQuestionIndex)

      // 清除计时器
      stopAutoNextTimer()

      // 根据当前是否为最后一题决定行为
      if (!isLastQuestion.value) {
        nextQuestion()
      } else if (hasAnsweredAll.value) {
        finishInterview()
      }
    }
  }, 100)
}

// 停止自动下一题计时器
const stopAutoNextTimer = () => {
  if (autoNextInterval.value !== null) {
    clearInterval(autoNextInterval.value)
    autoNextInterval.value = null
  }
  autoNextCounter.value = 0
}

// 生命周期钩子
onMounted(async () => {
  // 检查是否有面试问题
  if (!interviewStore.questions.length) {
    ElMessage.warning('没有面试问题，请先在简历系统创建对应简历')
    router.push('/')
    return
  }

  // 检查是否有面试ID，如果没有，表示未正确创建面试
  if (!interviewStore.interviewId) {
    ElMessage.warning('简历未创建，请先在简历系统创建对应简历')
    router.push('/')
    return
  }

  // 确保语音合成API可用
  ensureSpeechSynthesis()

  // 预加载语音
  window.addEventListener('load', () => {
    speechSynthesis.getVoices()
  })

  // 初始化数字人
  nextTick(() => {
    initDigitalHuman()
  })

  // 添加调试日志
  setInterval(() => {
    if (cameraActive.value && !isAskingQuestion.value) {
      console.log('当前状态检查 - 录制中:', isRecording.value, '静音检测:', silenceCheckInterval.value !== null, '静音状态:', silenceDetected.value)
    }
  }, 5000)
})

onUnmounted(() => {
  // 清理资源
  if (videoRecorder.value) {
    videoRecorder.value.destroy()
  }

  if (audioRecorder.value) {
    audioRecorder.value.destroy()
  }

  // 关闭摄像头
  if (userMediaStream.value) {
    userMediaStream.value.getTracks().forEach(track => track.stop())
  }

  // 清除定时器
  if (typingInterval.value !== null) {
    clearInterval(typingInterval.value)
  }

  if (silenceCheckInterval.value !== null) {
    clearInterval(silenceCheckInterval.value)
  }

  if (autoNextInterval.value !== null) {
    clearInterval(autoNextInterval.value)
  }

  // 停止语音合成
  if (speechSynthesis.speaking) {
    speechSynthesis.cancel()
  }

  // 关闭音频上下文
  if (audioContext.value && audioContext.value.state !== 'closed') {
    audioContext.value.close()
  }

  // 销毁数字人
  disposeDigitalHuman()

  // 销毁问题录音机
  Object.values(questionAudioRecorders.value).forEach(recorder => {
    if (recorder) {
      recorder.destroy()
    }
  })
})
</script>

<style scoped>
.interview-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
}

.interview-header {
  text-align: center;
  margin-bottom: 20px;
}

.interview-header h1 {
  color: #409EFF;
  margin-bottom: 15px;
}

.interview-main {
  display: flex;
  flex: 1;
  gap: 20px;
  height: calc(100vh - 150px);
}

.digital-human-container,
.user-video-container {
  flex: 1;
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.digital-human {
  flex: 1;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.digital-human-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.digital-human-3d-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: #000;
}

.digital-human-image {
  max-width: 100%;
  max-height: 70%;
  object-fit: contain;
  display: block; /* 显示静态图片 */
  margin: 0 auto;
}

.question-subtitle {
  position: absolute;
  bottom: 30px;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 15px;
  text-align: center;
  font-size: 1.2rem;
  animation: fadeIn 0.5s;
  z-index: 10;
  min-height: 60px;
}

.question-subtitle p {
  margin: 0;
  line-height: 1.5;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.question-container {
  margin-top: 20px;
}

.question {
  background-color: #f1f5f9;
  padding: 15px;
  border-radius: 8px;
  font-size: 1.1rem;
  margin-top: 10px;
}

.video-wrapper {
  flex: 1;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.video-wrapper video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
}

.controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

.current-status {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 14px;
  margin-right: 10px;
  color: #606266;
  position: relative;
}

.status-text::after {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #67C23A;
  margin-left: 8px;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

.warning-text {
  color: #F56C6C;
  font-weight: bold;
}

.warning-text::after {
  background-color: #F56C6C;
}

.auto-next-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #606266;
  font-size: 0.9rem;
}

.recording-indicator {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.recording-dot {
  width: 12px;
  height: 12px;
  background-color: red;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .interview-main {
    flex-direction: column;
  }
}

.digital-human-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 20;
  color: white;
}

.digital-human-loading p {
  margin-top: 15px;
  font-size: 1.2rem;
}

.digital-human-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 25;
  padding: 20px;
}

.digital-human-error .el-alert {
  width: 100%;
  margin-bottom: 20px;
}

.digital-human-error .digital-human-image {
  display: block;
  max-width: 50%;
  max-height: 50%;
}

.mic-toggle-btn {
  transition: all 0.3s ease;
}

.mic-toggle-btn.is-muted {
  background-color: #F56C6C;
  border-color: #F56C6C;
  color: #fff;
}

.mic-toggle-btn:not(.is-muted) {
  background-color: #409EFF;
  border-color: #409EFF;
  color: #fff;
}

.status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f0f2f5;
  padding: 8px 15px;
  border-radius: 8px;
  margin-bottom: 10px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #909399;
  font-size: 14px;
  opacity: 0.6;
}

.status-item.active {
  color: #F56C6C;
  font-weight: 500;
  opacity: 1;
}

.countdown-timer {
  background-color: #409EFF;
  color: white;
  border-radius: 15px;
  padding: 5px 12px;
  font-size: 13px;
  font-weight: 500;
}
</style> 