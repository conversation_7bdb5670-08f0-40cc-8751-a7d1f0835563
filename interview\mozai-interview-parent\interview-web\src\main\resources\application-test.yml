spring:
  datasource:
    url: **************************************************************************************************************************************************
    username: deploy
    password: d123456d
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:  # Redis数据库索引（默认为0）
    database: '0'
    host: ************  #Redis服务器地址
    port: 6379  # Redis服务器连接端口
    password: HuAxIad0e37EE6054667bd9745d5400ecabc
    jedis:
      pool:
        max-active: 10000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1  # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10000 # 连接池中的最大空闲连接
        min-idle: 0  # 连接池中的最小空闲连接
    timeout: 120000 # 连接超时时间（毫秒）
  # 添加文件上传配置
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
      enabled: true
swagger:
  enable: true
test:
  env: test
sso:
  clientId: interview
  login:
    loginUrl: https://tt.bimowo.com/unified/sso/auth
  validate:
    url: https://tt.bimowo.com/unified/sso/validateToken
  logout:
    url: https://tt.bimowo.com/unified/sso/logout
token:
  expire:
    seconds: 7200