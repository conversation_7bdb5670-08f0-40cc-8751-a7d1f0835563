{"name": "interview-vue", "version": "1.0.0", "description": "简历制作系统", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "keywords": ["resume", "vue", "简历"], "author": "", "license": "ISC", "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.5.0", "element-plus": "^2.3.12", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "md-editor-v3": "^5.5.1", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-router": "^4.2.4", "vuedraggable": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "eslint": "^8.48.0", "eslint-plugin-vue": "^9.17.0", "sass": "^1.66.1", "terser": "^5.19.2", "vite": "^4.4.9"}}