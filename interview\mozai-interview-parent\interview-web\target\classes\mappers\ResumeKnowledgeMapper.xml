<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bimowu.interview.dao.ResumeKnowledgeMapper">
    
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.bimowu.interview.model.ResumeKnowledge">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="position_type" property="positionType" jdbcType="INTEGER"/>
        <result column="knowledge_catalog" property="knowledgeCatalog" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
    </resultMap>
    
    <!-- 所有列 -->
    <sql id="Base_Column_List">
        id, position_type, knowledge_catalog, create_time, update_time, is_deleted
    </sql>
    
    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.bimowu.interview.model.ResumeKnowledge" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO resume_knowledge (
            position_type, knowledge_catalog, create_time, update_time, is_deleted
        ) VALUES (
            #{positionType,jdbcType=INTEGER},
            #{knowledgeCatalog,jdbcType=VARCHAR},
            NOW(),
            NOW(),
            0
        )
    </insert>
    
    <!-- 更新记录 -->
    <update id="update" parameterType="com.bimowu.interview.model.ResumeKnowledge">
        UPDATE resume_knowledge
        <set>
            <if test="positionType != null">
                position_type = #{positionType,jdbcType=INTEGER},
            </if>
            <if test="knowledgeCatalog != null">
                knowledge_catalog = #{knowledgeCatalog,jdbcType=VARCHAR},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT} AND is_deleted = 0
    </update>
    
    <!-- 逻辑删除 -->
    <update id="deleteById">
        UPDATE resume_knowledge
        SET is_deleted = 1,
            update_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT} AND is_deleted = 0
    </update>
    
    <!-- 根据ID查询记录 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM resume_knowledge
        WHERE id = #{id,jdbcType=BIGINT} AND is_deleted = 0
    </select>
    
    <!-- 根据职位类型查询记录 -->
    <select id="selectByPositionType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM resume_knowledge
        WHERE position_type = #{positionType,jdbcType=INTEGER} AND is_deleted = 0
        ORDER BY knowledge_catalog ASC
    </select>
    
    <!-- 根据知识目录查询记录 -->
    <select id="selectByKnowledgeCatalog" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM resume_knowledge
        WHERE knowledge_catalog = #{knowledgeCatalog,jdbcType=VARCHAR} AND is_deleted = 0
        LIMIT 1
    </select>
    
    <!-- 根据条件查询记录 -->
    <select id="selectByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM resume_knowledge
        <where>
            is_deleted = 0
            <if test="positionType != null">
                AND position_type = #{positionType,jdbcType=INTEGER}
            </if>
            <if test="knowledgeCatalog != null">
                AND knowledge_catalog = #{knowledgeCatalog,jdbcType=VARCHAR}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY knowledge_catalog ASC
    </select>
</mapper> 