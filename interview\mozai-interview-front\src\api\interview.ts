import axios from 'axios'
import type {
  Interview,
  MockInterview,
  FormalInterview,
  InterviewEvaluation,
  InterviewType,
  ApiResponse,
  ApiInterview
} from '@/types/interview'
import { InterviewType as InterviewTypeEnum, convertApiInterviewToModel } from '@/types/interview'
import { formatDate } from '@/utils/dateFormat'

// API基础URL
const API_BASE_URL = '/interview/interviews'

// 创建axios实例
const axiosInstance = axios.create({
  timeout: 120000
})

// 请求拦截器
axiosInstance.interceptors.request.use(
    config => {
      // 自动携带token
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.token = token;
      }
      // 可以在这里添加token等认证信息
      return config
    },
    error => {
      return Promise.reject(error)
    }
)

// 获取面试列表
export const getInterviewList = async (): Promise<Interview[]> => {
  try {
    const response = await axiosInstance.get<ApiResponse<ApiInterview[]>>(`${API_BASE_URL}`)

    if (response.data.code === 0 && response.data.data) {
      // 将API返回的数据转换为前端模型
      return response.data.data.map(interview => convertApiInterviewToModel(interview))
    }

    return []
  } catch (error) {
    console.error('获取面试列表失败:', error)
    return []
  }
}

// 根据类型获取面试列表
export const getInterviewsByType = async (type: InterviewType): Promise<Interview[]> => {
  try {
    const response = await axiosInstance.get<ApiResponse<ApiInterview[]>>(`${API_BASE_URL}/type/${type}`)

    if (response.data.code === 0 && response.data.data) {
      // 将API返回的数据转换为前端模型
      return response.data.data.map(interview => convertApiInterviewToModel(interview))
    }

    return []
  } catch (error) {
    console.error(`获取${type}面试列表失败:`, error)
    return []
  }
}

// 获取面试详情
export const getInterviewDetail = async (id: string): Promise<Interview | null> => {
  try {
    const response = await axiosInstance.get<ApiResponse<ApiInterview>>(`${API_BASE_URL}/${id}`)

    if (response.data.code === 0 && response.data.data) {
      // 将API返回的数据转换为前端模型
      return convertApiInterviewToModel(response.data.data)
    }

    return null
  } catch (error) {
    console.error('获取面试详情失败:', error)
    return null
  }
}

// 创建模拟面试
export const createMockInterview = async (data: Partial<MockInterview>): Promise<string | null> => {
  try {
    // 确保日期格式正确
    let interviewTime = data.interviewTime;
    if (interviewTime && (interviewTime.includes('yyyy') || !interviewTime.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/))) {
      // 如果日期格式不正确，使用当前时间
      const now = new Date();
      interviewTime = formatDate(now.toString());
      console.log('修正日期格式:', interviewTime);
    }

    const apiData = {
      type: InterviewTypeEnum.MOCK,
      position: data.position,
      interviewTime: interviewTime, // 使用修正后的日期格式
      status: 0 // 进行中
    }

    console.log('提交模拟面试数据:', apiData);

    const response = await axiosInstance.post<ApiResponse<{id: string}>>(`${API_BASE_URL}`, apiData)

    if (response.data.code === 0 && response.data.data) {
      return response.data.data.id
    }

    return null
  } catch (error) {
    console.error('创建模拟面试失败:', error)
    return null
  }
}

// 创建正式面试
export const createFormalInterview = async (data: Partial<FormalInterview>): Promise<string | null> => {
  try {
    // 确保日期格式正确
    let interviewTime = data.interviewTime;
    if (interviewTime && (interviewTime.includes('yyyy') || !interviewTime.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/))) {
      // 如果日期格式不正确，使用当前时间
      const now = new Date();
      interviewTime = formatDate(now.toString());
      console.log('修正日期格式:', interviewTime);
    }

    const apiData = {
      type: InterviewTypeEnum.FORMAL,
      company: data.company,
      position: data.position,
      interviewTime: interviewTime, // 使用修正后的日期格式
      status: data.status === 'in_progress' ? 0 :
          data.status === 'waiting_result' ? 1 :
              data.status === 'completed' ? 2 : 0, // 将状态转换为数字
      videoUrl: data.videoUrl // 传递视频URL
    }

    console.log('提交正式面试数据:', apiData);

    const response = await axiosInstance.post<ApiResponse<{interviewId: string}>>(`${API_BASE_URL}`, apiData)

    console.log('服务器响应:', response.data);

    if (response.data.code === 0 && response.data.data && response.data.data.interviewId) {
      return response.data.data.interviewId
    } else {
      console.error('创建正式面试失败:', response.data.message || '未知错误');
      return null
    }
  } catch (error) {
    console.error('创建正式面试失败:', error)
    return null
  }
}

// 更新面试评价
export const updateInterviewEvaluation = async (
    id: string,
    evaluation: InterviewEvaluation
): Promise<boolean> => {
  try {
    // 将前端评价数据转换为API格式
    const apiData = {
      overallScore: evaluation.score,
      feedback: evaluation.feedback,
      strengths: JSON.stringify(evaluation.strengths),
      improvements: JSON.stringify(evaluation.improvements)
    }

    const response = await axiosInstance.put<ApiResponse<boolean>>(`${API_BASE_URL}/${id}/result`, apiData)

    return response.data.code === 0
  } catch (error) {
    console.error('更新面试评价失败:', error)
    return false
  }
}

/**
 * 上传面试视频（传统方式，通过服务器中转）
 * @param formData 包含文件的表单数据
 * @param onProgress 上传进度回调
 * @returns 响应结果
 */
export const uploadInterviewVideo = async (
    formData: FormData,
    onProgress?: (progressEvent: any) => void
) => {
  const response = await axios.post('/api/interview/upload/video', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: onProgress
  });
  return response.data;
};

// 使用OSS直传上传面试视频
export const uploadInterviewVideoOss = async (id: string, file: File, onProgress?: (percent: number) => void): Promise<boolean> => {
  try {
    console.log('尝试使用OSS直传上传视频...');

    // 动态导入OSS上传工具
    const { uploadToOss, loadOssModule } = await import('../utils/ossUploader');

    // 先加载OSS模块确认可用性
    const ossAvailable = await loadOssModule();
    if (!ossAvailable) {
      throw new Error('OSS模块不可用，无法使用直传功能');
    }

    // 上传文件到OSS，指定上传目录为interview/video
    const result = await uploadToOss(file, 'interview/video', onProgress);

    if (!result || !result.url) {
      throw new Error('OSS上传失败，请稍后重试');
    }

    // 文件上传成功后，将URL更新到面试记录
    const response = await axiosInstance.post<ApiResponse<boolean>>(`${API_BASE_URL}/${id}/video-url`, {
      videoUrl: result.url
    });

    if (response.data.code !== 0) {
      throw new Error(`更新视频URL失败: ${response.data.message || '未知错误'}`);
    }

    return true;
  } catch (error) {
    console.error('OSS直传失败:', error);
    // 直接抛出错误，不降级到传统上传
    throw error;
  }
}

// 删除面试记录
export const deleteInterview = async (id: string): Promise<boolean> => {
  try {
    const response = await axiosInstance.delete<ApiResponse<boolean>>(`${API_BASE_URL}/${id}`)

    return response.data.code === 0
  } catch (error) {
    console.error('删除面试记录失败:', error)
    return false
  }
}

// 创建面试（通用函数）
export const createInterview = async (data: Partial<Interview>): Promise<string | null> => {
  try {
    if (data.type === InterviewTypeEnum.MOCK) {
      return createMockInterview(data as Partial<MockInterview>)
    } else if (data.type === InterviewTypeEnum.FORMAL) {
      return createFormalInterview(data as Partial<FormalInterview>)
    } else {
      console.error('未知的面试类型')
      return null
    }
  } catch (error) {
    console.error('创建面试失败:', error)
    return null
  }
} 