import type { ExtractPropTypes } from 'vue';
import type RadioButton from './radio-button.vue';
export declare const radioButtonProps: {
    readonly name: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly size: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly disabled: BooleanConstructor;
    readonly label: import("element-plus/es/utils").EpPropFinalized<(NumberConstructor | BooleanConstructor | StringConstructor)[], unknown, unknown, string, boolean>;
};
export declare type RadioButtonProps = ExtractPropTypes<typeof radioButtonProps>;
export declare type RadioButtonInstance = InstanceType<typeof RadioButton>;
