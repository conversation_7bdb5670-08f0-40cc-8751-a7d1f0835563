package com.bimowu.resume.utils;

import com.bimowu.resume.dto.ResumeFullSaveDto;
import com.bimowu.resume.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 简历数据映射工具类
 * 专门用于将简历数据映射为HTML模板所需的格式
 */
@Component
@Slf4j
public class ResumeDataMapper {

    @Autowired
    private PDFGenerationLogger pdfLogger;

    /**
     * 映射所有模块数据
     */
    public Map<String, String> mapAllModules(ResumeFullSaveDto resume) {
        return mapAllModules(resume, null);
    }

    /**
     * 映射所有模块数据（支持模板特定逻辑）
     */
    public Map<String, String> mapAllModules(ResumeFullSaveDto resume, Long templateId) {
        String resumeId = resume != null && resume.getResumeVo() != null ?
                String.valueOf(resume.getResumeVo().getResumeId()) : "unknown";

        log.info("开始映射所有模块数据 - resumeId: {}, templateId: {}, talentList: {}", resumeId, templateId,
                resume != null && resume.getTalentList() != null ? resume.getTalentList().size() : "null");
        Map<String, String> templateData = new HashMap<>();

        // 基本信息映射
        if (templateId != null && templateId == 5L) {
            mapBasicInfoForTemplate5(resume, templateData, resumeId);
        } else {
            mapBasicInfo(resume, templateData, resumeId);
        }

        // 教育经历映射
        if (templateId != null && templateId == 4L) {
            mapEducationForTemplate4(resume, templateData, resumeId);
        } else if (templateId != null && templateId == 5L) {
            mapEducationForTemplate5(resume, templateData, resumeId);
        } else {
            mapEducation(resume, templateData, resumeId);
        }

        // 工作经验映射（支持模板特定逻辑）
        if (templateId != null && templateId == 4L) {
            mapWorkExperienceForTemplate4(resume, templateData, resumeId);
        } else if (templateId != null && templateId == 5L) {
            mapWorkExperienceForTemplate5(resume, templateData, resumeId);
        } else {
            mapWorkExperience(resume, templateData, resumeId);
        }

        // 项目经验映射
        if (templateId != null && templateId == 4L) {
            mapProjectsForTemplate4(resume, templateData, resumeId);
        } else if (templateId != null && templateId == 5L) {
            mapProjectsForTemplate5(resume, templateData, resumeId);
        } else {
            mapProjects(resume, templateData, resumeId);
        }

        // 练手项目映射
        if (templateId != null && templateId == 4L) {
            mapPracticesForTemplate4(resume, templateData, resumeId);
        } else if (templateId != null && templateId == 5L) {
            mapPracticesForTemplate5(resume, templateData, resumeId);
        } else {
            mapPractices(resume, templateData, resumeId);
        }

        // 技能特长映射
        log.error("准备调用mapSkills - talentList: {}", resume.getTalentList() != null ? resume.getTalentList().size() : "null");
        if (templateId != null && templateId == 4L) {
            mapSkillsForTemplate4(resume, templateData, resumeId);
        } else if (templateId != null && templateId == 5L) {
            mapSkillsForTemplate5(resume, templateData, resumeId);
        } else {
            mapSkills(resume, templateData, resumeId);
        }
        log.error("mapSkills调用完成");

        // 证书奖项映射
        if (templateId != null && templateId == 4L) {
            mapCertificatesForTemplate4(resume, templateData, resumeId);
        } else if (templateId != null && templateId == 5L) {
            mapCertificatesForTemplate5(resume, templateData, resumeId);
        } else {
            mapCertificates(resume, templateData, resumeId);
        }

        // 校园经历映射
        if (templateId != null && templateId == 4L) {
            mapCampusForTemplate4(resume, templateData, resumeId);
        } else {
            mapCampus(resume, templateData, resumeId);
        }

        // 兴趣爱好映射
        if (templateId != null && templateId == 4L) {
            mapInterestsForTemplate4(resume, templateData, resumeId);
        } else if (templateId != null && templateId == 5L) {
            mapInterestsForTemplate5(resume, templateData, resumeId);
        } else {
            mapInterests(resume, templateData, resumeId);
        }

        // 自我评价映射（支持模板特定逻辑）
        if (templateId != null && templateId == 4L) {
            mapSelfEvaluationForTemplate4(resume, templateData, resumeId);
        } else if (templateId != null && templateId == 5L) {
            mapSelfEvaluationForTemplate5(resume, templateData, resumeId);
        } else {
            mapSelfEvaluation(resume, templateData, resumeId);
        }

        log.info("所有模块数据映射完成，共生成 {} 个模板变量 - resumeId: {}, templateId: {}", templateData.size(), resumeId, templateId);
        return templateData;
    }

    /**
     * 基本信息映射
     */
    private void mapBasicInfo(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        if (resume.getInformation() != null) {
            ResumeInformationVo info = resume.getInformation();

            pdfLogger.logDataMappingTrace(resumeId, "基本信息", "开始映射", info, null);

            templateData.put("name", escapeHtml(info.getName() != null ? info.getName() : "求职者"));
            templateData.put("age", info.getAge() != null ? info.getAge().toString() : "");
            templateData.put("gender", escapeHtml(info.getGender() != null ? info.getGender() : ""));
            templateData.put("phone", escapeHtml(info.getPhone() != null ? info.getPhone() : ""));
            templateData.put("email", escapeHtml(info.getEmail() != null ? info.getEmail() : ""));
            templateData.put("hometown", escapeHtml(info.getHometown() != null ? info.getHometown() : ""));
            templateData.put("jobObjective", escapeHtml(info.getJobObjective() != null ? info.getJobObjective() : ""));

            // 头像处理
            if (info.getAvatar() != null && !info.getAvatar().isEmpty()) {
                templateData.put("avatar", "<img src=\"" + info.getAvatar() + "\" alt=\"头像\" class=\"photo\" />");
            } else {
                templateData.put("avatar", "");
            }

            pdfLogger.logConditionalDisplay(resumeId, "基本信息", true, "包含完整基本信息");
            log.info("基本信息映射完成 - 姓名: {}, 电话: {}", info.getName(), info.getPhone());
        } else {
            // 设置默认值
            templateData.put("name", "求职者");
            templateData.put("age", "");
            templateData.put("gender", "");
            templateData.put("phone", "");
            templateData.put("email", "");
            templateData.put("hometown", "");
            templateData.put("jobObjective", "");
            templateData.put("avatar", "");

            pdfLogger.logConditionalDisplay(resumeId, "基本信息", false, "基本信息为空，使用默认值");
            log.info("基本信息为空，使用默认值");
        }
    }

    /**
     * 教育经历映射
     */
    private void mapEducation(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeEducationalVo> educationList = resume.getEducationList();
        if (educationList != null && !educationList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "教育经历", "开始映射", educationList, null);
            StringBuilder educationHtml = new StringBuilder();
            educationHtml.append("<div class=\"resume-section\">");
            educationHtml.append("<div class=\"section-header\">");
            educationHtml.append("<h2>教育经历</h2>");
            educationHtml.append("</div>");
            educationHtml.append("<div class=\"education-content\">");

            for (ResumeEducationalVo edu : educationList) {
                educationHtml.append("<div class=\"education-item\">");
                educationHtml.append("<div class=\"edu-header\">");

                // 时间
                educationHtml.append("<span class=\"edu-date\">")
                        .append(escapeHtml(edu.getTimePeriod() != null ? edu.getTimePeriod() : ""))
                        .append("</span>");

                // 学校
                educationHtml.append("<span class=\"edu-school\">")
                        .append(escapeHtml(edu.getSchool() != null ? edu.getSchool() : ""))
                        .append("</span>");

                // 学历和专业
                String degreeAndMajor = "";
                if (edu.getEducation() != null && !edu.getEducation().isEmpty()) {
                    degreeAndMajor += escapeHtml(edu.getEducation());
                }
                if (edu.getMajor() != null && !edu.getMajor().isEmpty()) {
                    if (!degreeAndMajor.isEmpty()) {
                        degreeAndMajor += "，";
                    }
                    degreeAndMajor += escapeHtml(edu.getMajor());
                }
                educationHtml.append("<span class=\"edu-degree\">").append(degreeAndMajor).append("</span>");

                educationHtml.append("</div>"); // End of edu-header

                // 主修课程
                if (edu.getMainCourses() != null && !edu.getMainCourses().trim().isEmpty()) {
                    educationHtml.append("<div class=\"edu-info\">");
                    educationHtml.append("<div class=\"edu-courses\">");
                    educationHtml.append("<span class=\"courses-label\">主修课程：</span>");
                    educationHtml.append(formatContent(edu.getMainCourses(), resumeId));
                    educationHtml.append("</div>");
                    educationHtml.append("</div>");
                }

                educationHtml.append("</div>"); // End of education-item
            }

            educationHtml.append("</div>"); // End of education-content
            educationHtml.append("</div>"); // End of resume-section

            String finalHtml = educationHtml.toString();
            templateData.put("education", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "教育经历", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "教育经历", true, educationList.size() + "条记录");
            log.debug("教育经历映射完成 - {} 条记录", educationList.size());
        } else {
            templateData.put("education", "");
            pdfLogger.logConditionalDisplay(resumeId, "教育经历", false, "教育经历列表为空");
            log.debug("教育经历为空");
        }
    }

    /**
     * 模板4专用的教育经历映射
     */
    private void mapEducationForTemplate4(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeEducationalVo> educationList = resume.getEducationList();
        log.info("mapEducationForTemplate4被调用，educationList: {}", educationList != null ? educationList.size() + "条记录" : "null");
        if (educationList != null && !educationList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "教育背景(模板4)", "开始映射", educationList, null);
            StringBuilder educationHtml = new StringBuilder();
            educationHtml.append("<div class=\"resume-section\">");

            // 标题部分 - 与其他模块相同的样式
            educationHtml.append("<div class=\"section-header\" style=\"position: relative; margin-bottom: 5px;\">");
            educationHtml.append("<h2 style=\"font-family: 'SimHei', 'Microsoft YaHei', '黑体', sans-serif !important; font-weight: bold; font-size: 18px; color: #333; background-color: #f0f0f0; padding: 5px 15px; border-radius: 5px 5px 0 0; margin: 0; display: inline-block;\">教育背景</h2>");
            educationHtml.append("<div style=\"position: absolute; bottom: -2px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            educationHtml.append("<div style=\"position: absolute; bottom: -5px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            educationHtml.append("</div>");

            educationHtml.append("<div class=\"education-content\" style=\"padding: 5px 0;\">");

            for (ResumeEducationalVo edu : educationList) {
                educationHtml.append("<div class=\"education-item\" style=\"margin-bottom: 15px;\">");

                // 教育背景头部 - 三列布局：学校(学历)、专业、时间
                educationHtml.append("<div class=\"education-header\" style=\"display: table; width: 100%; margin-bottom: 8px; table-layout: fixed;\">");

                // 左侧学校和学历（加粗）
                educationHtml.append("<div class=\"school-degree\" style=\"display: table-cell; color: #333; font-size: 14px; font-weight: bold; width: 40%; vertical-align: middle;\">");
                String schoolInfo = "";
                if (edu.getSchool() != null && !edu.getSchool().trim().isEmpty()) {
                    schoolInfo = escapeHtml(edu.getSchool());
                    if (edu.getEducation() != null && !edu.getEducation().trim().isEmpty()) {
                        schoolInfo += "(" + escapeHtml(edu.getEducation()) + ")";
                    }
                } else if (edu.getEducation() != null && !edu.getEducation().trim().isEmpty()) {
                    schoolInfo = escapeHtml(edu.getEducation());
                }
                educationHtml.append(schoolInfo);
                educationHtml.append("</div>");

                // 中间专业
                educationHtml.append("<div class=\"education-major\" style=\"display: table-cell; color: #333; font-size: 14px; width: 30%; text-align: center; vertical-align: middle;\">");
                educationHtml.append(escapeHtml(edu.getMajor() != null ? edu.getMajor() : ""));
                educationHtml.append("</div>");

                // 右侧时间
                educationHtml.append("<div class=\"education-time\" style=\"display: table-cell; color: #333; font-size: 14px; width: 30%; text-align: right; vertical-align: middle;\">");
                educationHtml.append(escapeHtml(edu.getTimePeriod() != null ? edu.getTimePeriod() : ""));
                educationHtml.append("</div>");

                educationHtml.append("</div>"); // End of education-header

                // 主修课程
                if (edu.getMainCourses() != null && !edu.getMainCourses().trim().isEmpty()) {
                    log.info("处理主修课程(模板4): {}", edu.getMainCourses());
                    educationHtml.append("<div class=\"education-courses\" style=\"line-height: 1.6; font-size: 14px; color: rgb(68, 84, 106);\">");
                    educationHtml.append(formatContentForTemplate4(edu.getMainCourses(), resumeId));
                    educationHtml.append("</div>");
                } else {
                    log.info("主修课程为空或null(模板4)");
                }

                educationHtml.append("</div>"); // End of education-item
            }

            educationHtml.append("</div>"); // End of education-content
            educationHtml.append("</div>"); // End of resume-section

            String finalHtml = educationHtml.toString();
            templateData.put("education", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "教育背景(模板4)", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "教育背景(模板4)", true, educationList.size() + "条记录，使用模板4特殊样式");
            log.debug("教育背景映射完成(模板4) - {} 条记录", educationList.size());
        } else {
            templateData.put("education", "");
            pdfLogger.logConditionalDisplay(resumeId, "教育背景(模板4)", false, "教育经历列表为空");
            log.debug("教育背景为空(模板4)");
        }
    }

    /**
     * 工作经验映射
     */
    private void mapWorkExperience(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeWorkVo> workList = resume.getWorkList();
        log.info("mapWorkExperience被调用，workList: {}", workList != null ? workList.size() + "条记录" : "null");
        if (workList != null && !workList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "工作经验", "开始映射", workList, null);
            StringBuilder workHtml = new StringBuilder();
            workHtml.append("<div class=\"resume-section\">");
            workHtml.append("<div class=\"section-header\">");
            workHtml.append("<h2>工作经验</h2>");
            workHtml.append("</div>");
            workHtml.append("<div class=\"work-content\">");

            for (ResumeWorkVo work : workList) {
                workHtml.append("<div class=\"work-item\">");
                workHtml.append("<div class=\"work-header\">");

                // 时间
                workHtml.append("<div class=\"work-time\">")
                        .append(escapeHtml(work.getTimePeriod() != null ? work.getTimePeriod() : ""))
                        .append("</div>");

                // 公司
                workHtml.append("<div class=\"work-company\">")
                        .append(escapeHtml(work.getCompany() != null ? work.getCompany() : ""))
                        .append("</div>");

                // 职位
                workHtml.append("<div class=\"work-position\">")
                        .append(escapeHtml(work.getPosition() != null ? work.getPosition() : ""))
                        .append("</div>");

                workHtml.append("</div>"); // End of work-header

                // 工作描述
                if (work.getWorkDescription() != null && !work.getWorkDescription().trim().isEmpty()) {
                    log.info("处理工作描述: {}", work.getWorkDescription());
                    workHtml.append("<div class=\"work-description\">");
                    workHtml.append(formatContent(work.getWorkDescription(), resumeId));
                    workHtml.append("</div>");
                } else {
                    log.info("工作描述为空或null");
                }

                workHtml.append("</div>"); // End of work-item
            }

            workHtml.append("</div>"); // End of work-content
            workHtml.append("</div>"); // End of resume-section

            String finalHtml = workHtml.toString();
            templateData.put("work", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "工作经验", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "工作经验", true, workList.size() + "条记录");
            log.debug("工作经验映射完成 - {} 条记录", workList.size());
        } else {
            templateData.put("work", "");
            pdfLogger.logConditionalDisplay(resumeId, "工作经验", false, "工作经验列表为空");
            log.debug("工作经验为空");
        }
    }

    /**
     * 模板4专用的工作经验映射
     */
    private void mapWorkExperienceForTemplate4(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeWorkVo> workList = resume.getWorkList();
        log.info("mapWorkExperienceForTemplate4被调用，workList: {}", workList != null ? workList.size() + "条记录" : "null");
        if (workList != null && !workList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "工作经验(模板4)", "开始映射", workList, null);
            StringBuilder workHtml = new StringBuilder();
            workHtml.append("<div class=\"resume-section\">");

            // 标题部分 - 与自我评价相同的样式
            workHtml.append("<div class=\"section-header\" style=\"position: relative; margin-bottom: 5px;\">");
            workHtml.append("<h2 style=\"font-family: 'SimHei', 'Microsoft YaHei', '黑体', sans-serif !important; font-weight: bold; font-size: 18px; color: #333; background-color: #f0f0f0; padding: 5px 15px; border-radius: 5px 5px 0 0; margin: 0; display: inline-block;\">工作经验</h2>");
            workHtml.append("<div style=\"position: absolute; bottom: -2px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            workHtml.append("<div style=\"position: absolute; bottom: -5px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            workHtml.append("</div>");

            workHtml.append("<div class=\"work-content\" style=\"padding: 5px 0;\">");

            for (ResumeWorkVo work : workList) {
                workHtml.append("<div class=\"work-item\" style=\"margin-bottom: 15px;\">");

                // 工作经验头部 - 使用table布局确保在同一行
                workHtml.append("<div class=\"work-header\" style=\"display: table; width: 100%; margin-bottom: 8px; table-layout: fixed;\">");

                // 左侧时间
                workHtml.append("<div class=\"work-time\" style=\"display: table-cell; color: #333; font-size: 14px; font-weight: bold; width: 30%; vertical-align: middle;\">");
                workHtml.append(escapeHtml(work.getTimePeriod() != null ? work.getTimePeriod() : ""));
                workHtml.append("</div>");

                // 右侧公司和职位
                workHtml.append("<div class=\"work-company-position\" style=\"display: table-cell; color: #333; font-size: 14px; width: 70%; text-align: right; vertical-align: middle;\">");
                workHtml.append(escapeHtml(work.getCompany() != null ? work.getCompany() : ""));
                if (work.getPosition() != null && !work.getPosition().trim().isEmpty()) {
                    workHtml.append(" | ");
                    workHtml.append(escapeHtml(work.getPosition()));
                }
                workHtml.append("</div>");

                workHtml.append("</div>"); // End of work-header

                // 工作描述
                if (work.getWorkDescription() != null && !work.getWorkDescription().trim().isEmpty()) {
                    log.info("处理工作描述(模板4): {}", work.getWorkDescription());
                    workHtml.append("<div class=\"work-description\" style=\"line-height: 1.6; font-size: 14px; color: rgb(68, 84, 106);\">");
                    workHtml.append(formatContentForTemplate4(work.getWorkDescription(), resumeId));
                    workHtml.append("</div>");
                } else {
                    log.info("工作描述为空或null(模板4)");
                }

                workHtml.append("</div>"); // End of work-item
            }

            workHtml.append("</div>"); // End of work-content
            workHtml.append("</div>"); // End of resume-section

            String finalHtml = workHtml.toString();
            templateData.put("work", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "工作经验(模板4)", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "工作经验(模板4)", true, workList.size() + "条记录，使用模板4特殊样式");
            log.debug("工作经验映射完成(模板4) - {} 条记录", workList.size());
        } else {
            templateData.put("work", "");
            pdfLogger.logConditionalDisplay(resumeId, "工作经验(模板4)", false, "工作经验列表为空");
            log.debug("工作经验为空(模板4)");
        }
    }

    /**
     * 项目经验映射
     */
    private void mapProjects(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeProjectExperienceVo> projectList = resume.getProjectList();
        if (projectList != null && !projectList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "项目经验", "开始映射", projectList, null);
            StringBuilder projectHtml = new StringBuilder();
            projectHtml.append("<div class=\"resume-section\">");
            projectHtml.append("<div class=\"section-header\">");
            projectHtml.append("<h2>项目经验</h2>");
            projectHtml.append("</div>");
            projectHtml.append("<div class=\"work-content\">");

            for (ResumeProjectExperienceVo project : projectList) {
                projectHtml.append("<div class=\"project-item\">");
                projectHtml.append("<div class=\"work-header\">");

                // 时间
                projectHtml.append("<div class=\"project-date\">")
                        .append(escapeHtml(project.getTimePeriod() != null ? project.getTimePeriod() : ""))
                        .append("</div>");

                // 项目名称
                projectHtml.append("<div class=\"project-name\">")
                        .append(escapeHtml(project.getProjectName() != null ? project.getProjectName() : ""))
                        .append("</div>");

                // 角色
                projectHtml.append("<div class=\"project-role\">")
                        .append(escapeHtml(project.getRole() != null ? project.getRole() : ""))
                        .append("</div>");

                projectHtml.append("</div>"); // End of work-header

                // 项目描述
                if (project.getProjectDescription() != null && !project.getProjectDescription().trim().isEmpty()) {
                    projectHtml.append("<div class=\"project-description\">");
                    projectHtml.append(formatContent(project.getProjectDescription(), resumeId));
                    projectHtml.append("</div>");
                }

                projectHtml.append("</div>"); // End of project-item
            }

            projectHtml.append("</div>"); // End of work-content
            projectHtml.append("</div>"); // End of resume-section

            String finalHtml = projectHtml.toString();
            templateData.put("projects", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "项目经验", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "项目经验", true, projectList.size() + "条记录");
            log.debug("项目经验映射完成 - {} 条记录", projectList.size());
        } else {
            templateData.put("projects", "");
            pdfLogger.logConditionalDisplay(resumeId, "项目经验", false, "项目经验列表为空");
            log.debug("项目经验为空");
        }
    }

    /**
     * 模板4专用的项目经验映射
     */
    private void mapProjectsForTemplate4(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeProjectExperienceVo> projectList = resume.getProjectList();
        log.info("mapProjectsForTemplate4被调用，projectList: {}", projectList != null ? projectList.size() + "条记录" : "null");
        if (projectList != null && !projectList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "项目经验(模板4)", "开始映射", projectList, null);
            StringBuilder projectHtml = new StringBuilder();
            projectHtml.append("<div class=\"resume-section\">");

            // 标题部分 - 与工作经验相同的样式
            projectHtml.append("<div class=\"section-header\" style=\"position: relative; margin-bottom: 5px;\">");
            projectHtml.append("<h2 style=\"font-family: 'SimHei', 'Microsoft YaHei', '黑体', sans-serif !important; font-weight: bold; font-size: 18px; color: #333; background-color: #f0f0f0; padding: 5px 15px; border-radius: 5px 5px 0 0; margin: 0; display: inline-block;\">项目经验</h2>");
            projectHtml.append("<div style=\"position: absolute; bottom: -2px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            projectHtml.append("<div style=\"position: absolute; bottom: -5px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            projectHtml.append("</div>");

            projectHtml.append("<div class=\"work-content\" style=\"padding: 5px 0;\">");

            for (ResumeProjectExperienceVo project : projectList) {
                projectHtml.append("<div class=\"project-item\" style=\"margin-bottom: 15px;\">");

                // 项目经验头部 - 三列布局：项目名称、角色、时间
                projectHtml.append("<div class=\"project-header\" style=\"display: table; width: 100%; margin-bottom: 8px; table-layout: fixed;\">");

                // 左侧项目名称（加粗）
                projectHtml.append("<div class=\"project-name\" style=\"display: table-cell; color: #333; font-size: 14px; font-weight: bold; width: 40%; vertical-align: middle;\">");
                projectHtml.append(escapeHtml(project.getProjectName() != null ? project.getProjectName() : ""));
                projectHtml.append("</div>");

                // 中间角色
                projectHtml.append("<div class=\"project-role\" style=\"display: table-cell; color: #333; font-size: 14px; width: 30%; text-align: center; vertical-align: middle;\">");
                projectHtml.append(escapeHtml(project.getRole() != null ? project.getRole() : ""));
                projectHtml.append("</div>");

                // 右侧时间
                projectHtml.append("<div class=\"project-time\" style=\"display: table-cell; color: #333; font-size: 14px; width: 30%; text-align: right; vertical-align: middle;\">");
                projectHtml.append(escapeHtml(project.getTimePeriod() != null ? project.getTimePeriod() : ""));
                projectHtml.append("</div>");

                projectHtml.append("</div>"); // End of project-header

                // 项目描述
                if (project.getProjectDescription() != null && !project.getProjectDescription().trim().isEmpty()) {
                    log.info("处理项目描述(模板4): {}", project.getProjectDescription());
                    projectHtml.append("<div class=\"project-description\" style=\"line-height: 1.6; font-size: 14px; color: rgb(68, 84, 106);\">");
                    projectHtml.append(formatContentForTemplate4(project.getProjectDescription(), resumeId));
                    projectHtml.append("</div>");
                } else {
                    log.info("项目描述为空或null(模板4)");
                }

                projectHtml.append("</div>"); // End of project-item
            }

            projectHtml.append("</div>"); // End of work-content
            projectHtml.append("</div>"); // End of resume-section

            String finalHtml = projectHtml.toString();
            templateData.put("projects", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "项目经验(模板4)", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "项目经验(模板4)", true, projectList.size() + "条记录，使用模板4特殊样式");
            log.debug("项目经验映射完成(模板4) - {} 条记录", projectList.size());
        } else {
            templateData.put("projects", "");
            pdfLogger.logConditionalDisplay(resumeId, "项目经验(模板4)", false, "项目经验列表为空");
            log.debug("项目经验为空(模板4)");
        }
    }

    /**
     * 练手项目映射
     */
    private void mapPractices(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumePracticeVo> practiceList = resume.getPracticeList();
        if (practiceList != null && !practiceList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "练手项目", "开始映射", practiceList, null);
            StringBuilder practiceHtml = new StringBuilder();
            practiceHtml.append("<div class=\"resume-section\">");
            practiceHtml.append("<div class=\"section-header\">");
            practiceHtml.append("<h2>练手项目</h2>");
            practiceHtml.append("</div>");
            practiceHtml.append("<div class=\"work-content\">");

            for (ResumePracticeVo practice : practiceList) {
                practiceHtml.append("<div class=\"project-item\">");
                practiceHtml.append("<div class=\"work-header\">");

                // 时间
                practiceHtml.append("<div class=\"project-date\">")
                        .append(escapeHtml(practice.getTimePeriod() != null ? practice.getTimePeriod() : ""))
                        .append("</div>");

                // 项目名称
                practiceHtml.append("<div class=\"project-name\">")
                        .append(escapeHtml(practice.getProjectName() != null ? practice.getProjectName() : ""))
                        .append("</div>");

                // 角色
                practiceHtml.append("<div class=\"project-role\">")
                        .append(escapeHtml(practice.getRole() != null ? practice.getRole() : ""))
                        .append("</div>");

                practiceHtml.append("</div>"); // End of work-header

                // 项目URL
                if (practice.getProjectUrl() != null && !practice.getProjectUrl().trim().isEmpty()) {
                    practiceHtml.append("<div class=\"project-url\">");
                    practiceHtml.append("项目地址：");
                    practiceHtml.append("<span style=\"color: #0066cc; text-decoration: underline;\">");
                    practiceHtml.append(escapeHtml(practice.getProjectUrl()));
                    practiceHtml.append("</span>");
                    practiceHtml.append("</div>");
                }

                // 项目描述
                if (practice.getProjectDescription() != null && !practice.getProjectDescription().trim().isEmpty()) {
                    practiceHtml.append("<div class=\"project-description\">");
                    practiceHtml.append(formatContent(practice.getProjectDescription(), resumeId));
                    practiceHtml.append("</div>");
                }

                practiceHtml.append("</div>"); // End of project-item
            }

            practiceHtml.append("</div>"); // End of work-content
            practiceHtml.append("</div>"); // End of resume-section

            String finalHtml = practiceHtml.toString();
            templateData.put("practices", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "练手项目", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "练手项目", true, practiceList.size() + "条记录");
            log.debug("练手项目映射完成 - {} 条记录", practiceList.size());
        } else {
            templateData.put("practices", "");
            pdfLogger.logConditionalDisplay(resumeId, "练手项目", false, "练手项目列表为空");
            log.debug("练手项目为空");
        }
    }

    /**
     * 模板4专用的练手项目映射
     */
    private void mapPracticesForTemplate4(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumePracticeVo> practiceList = resume.getPracticeList();
        log.info("mapPracticesForTemplate4被调用，practiceList: {}", practiceList != null ? practiceList.size() + "条记录" : "null");
        if (practiceList != null && !practiceList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "练手项目(模板4)", "开始映射", practiceList, null);
            StringBuilder practiceHtml = new StringBuilder();
            practiceHtml.append("<div class=\"resume-section\">");

            // 标题部分 - 与项目经验相同的样式
            practiceHtml.append("<div class=\"section-header\" style=\"position: relative; margin-bottom: 5px;\">");
            practiceHtml.append("<h2 style=\"font-family: 'SimHei', 'Microsoft YaHei', '黑体', sans-serif !important; font-weight: bold; font-size: 18px; color: #333; background-color: #f0f0f0; padding: 5px 15px; border-radius: 5px 5px 0 0; margin: 0; display: inline-block;\">练手项目</h2>");
            practiceHtml.append("<div style=\"position: absolute; bottom: -2px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            practiceHtml.append("<div style=\"position: absolute; bottom: -5px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            practiceHtml.append("</div>");

            practiceHtml.append("<div class=\"work-content\" style=\"padding: 5px 0;\">");

            for (ResumePracticeVo practice : practiceList) {
                practiceHtml.append("<div class=\"project-item\" style=\"margin-bottom: 15px;\">");

                // 练手项目头部 - 三列布局：项目名称、角色、时间
                practiceHtml.append("<div class=\"project-header\" style=\"display: table; width: 100%; margin-bottom: 8px; table-layout: fixed;\">");

                // 左侧项目名称（加粗）
                practiceHtml.append("<div class=\"project-name\" style=\"display: table-cell; color: #333; font-size: 14px; font-weight: bold; width: 40%; vertical-align: middle;\">");
                practiceHtml.append(escapeHtml(practice.getProjectName() != null ? practice.getProjectName() : ""));
                practiceHtml.append("</div>");

                // 中间角色
                practiceHtml.append("<div class=\"project-role\" style=\"display: table-cell; color: #333; font-size: 14px; width: 30%; text-align: center; vertical-align: middle;\">");
                practiceHtml.append(escapeHtml(practice.getRole() != null ? practice.getRole() : ""));
                practiceHtml.append("</div>");

                // 右侧时间
                practiceHtml.append("<div class=\"project-time\" style=\"display: table-cell; color: #333; font-size: 14px; width: 30%; text-align: right; vertical-align: middle;\">");
                practiceHtml.append(escapeHtml(practice.getTimePeriod() != null ? practice.getTimePeriod() : ""));
                practiceHtml.append("</div>");

                practiceHtml.append("</div>"); // End of project-header

                // 项目URL - 参考模板3的链接样式：蓝色字体 + 下划线
                if (practice.getProjectUrl() != null && !practice.getProjectUrl().trim().isEmpty()) {
                    practiceHtml.append("<div class=\"project-url\" style=\"line-height: 1.6; font-size: 14px; margin-bottom: 5px;\">");
                    practiceHtml.append("<span style=\"color: rgb(68, 84, 106);\">项目地址：</span>");
                    practiceHtml.append("<span style=\"color: #0066cc; text-decoration: underline; font-weight: normal;\">");
                    practiceHtml.append(escapeHtml(practice.getProjectUrl()));
                    practiceHtml.append("</span>");
                    practiceHtml.append("</div>");
                }

                // 项目描述
                if (practice.getProjectDescription() != null && !practice.getProjectDescription().trim().isEmpty()) {
                    log.info("处理练手项目描述(模板4): {}", practice.getProjectDescription());
                    practiceHtml.append("<div class=\"project-description\" style=\"line-height: 1.6; font-size: 14px; color: rgb(68, 84, 106);\">");
                    practiceHtml.append(formatContentForTemplate4(practice.getProjectDescription(), resumeId));
                    practiceHtml.append("</div>");
                } else {
                    log.info("练手项目描述为空或null(模板4)");
                }

                practiceHtml.append("</div>"); // End of project-item
            }

            practiceHtml.append("</div>"); // End of work-content
            practiceHtml.append("</div>"); // End of resume-section

            String finalHtml = practiceHtml.toString();
            templateData.put("practices", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "练手项目(模板4)", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "练手项目(模板4)", true, practiceList.size() + "条记录，使用模板4特殊样式");
            log.debug("练手项目映射完成(模板4) - {} 条记录", practiceList.size());
        } else {
            templateData.put("practices", "");
            pdfLogger.logConditionalDisplay(resumeId, "练手项目(模板4)", false, "练手项目列表为空");
            log.debug("练手项目为空(模板4)");
        }
    }

    /**
     * 技能特长映射
     */
    private void mapSkills(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeTalentVo> skillList = resume.getTalentList();
        log.error("mapSkills被调用 - skillList: {}", skillList != null ? skillList.size() : "null");
        if (skillList != null && !skillList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "技能特长", "开始映射", skillList, null);
            StringBuilder skillHtml = new StringBuilder();
            skillHtml.append("<div class=\"resume-section\">");
            skillHtml.append("<div class=\"section-header\">");
            skillHtml.append("<h2>技能特长</h2>");
            skillHtml.append("</div>");
            skillHtml.append("<div class=\"skills-content\">");
            skillHtml.append("<div class=\"skills-description\">");

            for (ResumeTalentVo skill : skillList) {
                if (skill.getSkillDescription() != null && !skill.getSkillDescription().trim().isEmpty()) {
                    skillHtml.append("<div class=\"skill-description-item\">");
                    skillHtml.append("<div class=\"skill-description-body\">");
                    skillHtml.append(formatContent(skill.getSkillDescription(), resumeId));
                    skillHtml.append("</div>");
                    skillHtml.append("</div>");
                }
            }

            skillHtml.append("</div>"); // End of skills-description
            skillHtml.append("</div>"); // End of skills-content
            skillHtml.append("</div>"); // End of resume-section

            String finalHtml = skillHtml.toString();
            templateData.put("skills", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "技能特长", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "技能特长", true, skillList.size() + "条记录");
            log.debug("技能特长映射完成 - {} 条记录", skillList.size());
        } else {
            templateData.put("skills", "");
            pdfLogger.logConditionalDisplay(resumeId, "技能特长", false, "技能特长列表为空");
            log.debug("技能特长为空");
        }
    }

    /**
     * 模板4专用的技能特长映射
     */
    private void mapSkillsForTemplate4(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeTalentVo> skillList = resume.getTalentList();
        log.info("mapSkillsForTemplate4被调用，skillList: {}", skillList != null ? skillList.size() + "条记录" : "null");
        if (skillList != null && !skillList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "相关技能(模板4)", "开始映射", skillList, null);
            StringBuilder skillHtml = new StringBuilder();
            skillHtml.append("<div class=\"resume-section\">");

            // 标题部分 - 与其他模块相同的样式
            skillHtml.append("<div class=\"section-header\" style=\"position: relative; margin-bottom: 5px;\">");
            skillHtml.append("<h2 style=\"font-family: 'SimHei', 'Microsoft YaHei', '黑体', sans-serif !important; font-weight: bold; font-size: 18px; color: #333; background-color: #f0f0f0; padding: 5px 15px; border-radius: 5px 5px 0 0; margin: 0; display: inline-block;\">相关技能</h2>");
            skillHtml.append("<div style=\"position: absolute; bottom: -2px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            skillHtml.append("<div style=\"position: absolute; bottom: -5px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            skillHtml.append("</div>");

            skillHtml.append("<div class=\"skills-content\" style=\"padding: 5px 0;\">");

            // 将所有技能描述合并为连续的段落
            for (int i = 0; i < skillList.size(); i++) {
                ResumeTalentVo skill = skillList.get(i);
                if (skill.getSkillDescription() != null && !skill.getSkillDescription().trim().isEmpty()) {
                    log.info("处理技能描述(模板4): {}", skill.getSkillDescription());

                    // 每个技能描述作为一个段落，不添加额外的分隔
                    if (i > 0) {
                        skillHtml.append("<br>"); // 技能之间添加换行
                    }

                    skillHtml.append("<div class=\"skill-paragraph\" style=\"line-height: 1.6; font-size: 14px; color: rgb(68, 84, 106); margin-bottom: 8px;\">");
                    skillHtml.append(formatContentForTemplate4(skill.getSkillDescription(), resumeId));
                    skillHtml.append("</div>");
                } else {
                    log.info("技能描述为空或null(模板4)");
                }
            }

            skillHtml.append("</div>"); // End of skills-content
            skillHtml.append("</div>"); // End of resume-section

            String finalHtml = skillHtml.toString();
            templateData.put("skills", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "相关技能(模板4)", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "相关技能(模板4)", true, skillList.size() + "条记录，使用模板4特殊样式");
            log.debug("相关技能映射完成(模板4) - {} 条记录", skillList.size());
        } else {
            templateData.put("skills", "");
            pdfLogger.logConditionalDisplay(resumeId, "相关技能(模板4)", false, "技能列表为空");
            log.debug("相关技能为空(模板4)");
        }
    }

    /**
     * 证书奖项映射
     */
    private void mapCertificates(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeCertificateVo certificate = resume.getCertificate();
        if (certificate != null && certificate.getCertificateName() != null && !certificate.getCertificateName().trim().isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "证书奖项", "开始映射", certificate, null);
            StringBuilder certHtml = new StringBuilder();
            certHtml.append("<div class=\"resume-section\">");
            certHtml.append("<div class=\"section-header\">");
            certHtml.append("<h2>荣誉证书</h2>");
            certHtml.append("</div>");
            certHtml.append("<div class=\"certificate-content\">");
            certHtml.append(formatContent(certificate.getCertificateName(), resumeId));
            certHtml.append("</div>"); // End of certificate-content
            certHtml.append("</div>"); // End of resume-section

            String finalHtml = certHtml.toString();
            templateData.put("certificates", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "证书奖项", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "证书奖项", true, "包含证书信息");
            log.debug("证书奖项映射完成");
        } else {
            templateData.put("certificates", "");
            pdfLogger.logConditionalDisplay(resumeId, "证书奖项", false, "证书奖项为空");
            log.debug("证书奖项为空");
        }
    }

    /**
     * 模板4专用的证书奖项映射
     */
    private void mapCertificatesForTemplate4(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeCertificateVo certificate = resume.getCertificate();
        log.info("mapCertificatesForTemplate4被调用，certificate: {}", certificate != null ? "有数据" : "null");
        if (certificate != null && certificate.getCertificateName() != null && !certificate.getCertificateName().trim().isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "证书奖项(模板4)", "开始映射", certificate, null);
            StringBuilder certHtml = new StringBuilder();
            certHtml.append("<div class=\"resume-section\">");

            // 标题部分 - 与其他模块相同的样式
            certHtml.append("<div class=\"section-header\" style=\"position: relative; margin-bottom: 5px;\">");
            certHtml.append("<h2 style=\"font-family: 'SimHei', 'Microsoft YaHei', '黑体', sans-serif !important; font-weight: bold; font-size: 18px; color: #333; background-color: #f0f0f0; padding: 5px 15px; border-radius: 5px 5px 0 0; margin: 0; display: inline-block;\">证书奖项</h2>");
            certHtml.append("<div style=\"position: absolute; bottom: -2px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            certHtml.append("<div style=\"position: absolute; bottom: -5px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            certHtml.append("</div>");

            certHtml.append("<div class=\"certificate-content\" style=\"padding: 5px 0; line-height: 1.6; font-size: 14px; color: rgb(68, 84, 106);\">");
            log.info("处理证书奖项内容(模板4): {}", certificate.getCertificateName());
            certHtml.append(formatContentForTemplate4(certificate.getCertificateName(), resumeId));
            certHtml.append("</div>"); // End of certificate-content
            certHtml.append("</div>"); // End of resume-section

            String finalHtml = certHtml.toString();
            templateData.put("certificates", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "证书奖项(模板4)", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "证书奖项(模板4)", true, "包含证书信息，使用模板4特殊样式");
            log.debug("证书奖项映射完成(模板4)");
        } else {
            templateData.put("certificates", "");
            pdfLogger.logConditionalDisplay(resumeId, "证书奖项(模板4)", false, "证书奖项为空");
            log.debug("证书奖项为空(模板4)");
        }
    }

    /**
     * 校园经历映射
     */
    private void mapCampus(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeCampusVo campus = resume.getCampus();
        if (campus != null && campus.getCampusExperience() != null && !campus.getCampusExperience().trim().isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "校园经历", "开始映射", campus, null);
            StringBuilder campusHtml = new StringBuilder();
            campusHtml.append("<div class=\"resume-section\">");
            campusHtml.append("<div class=\"section-header\">");
            campusHtml.append("<h2>校园经历</h2>");
            campusHtml.append("</div>");
            campusHtml.append("<div class=\"campus-content\">");
            campusHtml.append(formatContent(campus.getCampusExperience(), resumeId));
            campusHtml.append("</div>"); // End of campus-content
            campusHtml.append("</div>"); // End of resume-section

            String finalHtml = campusHtml.toString();
            templateData.put("campus", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "校园经历", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "校园经历", true, "包含校园经历信息");
            log.debug("校园经历映射完成");
        } else {
            templateData.put("campus", "");
            pdfLogger.logConditionalDisplay(resumeId, "校园经历", false, "校园经历为空");
            log.debug("校园经历为空");
        }
    }

    /**
     * 模板4专用的校园经历映射
     */
    private void mapCampusForTemplate4(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeCampusVo campus = resume.getCampus();
        log.info("mapCampusForTemplate4被调用，campus: {}", campus != null ? "有数据" : "null");
        if (campus != null && campus.getCampusExperience() != null && !campus.getCampusExperience().trim().isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "校园经历(模板4)", "开始映射", campus, null);
            StringBuilder campusHtml = new StringBuilder();
            campusHtml.append("<div class=\"resume-section\">");

            // 标题部分 - 与其他模块相同的样式
            campusHtml.append("<div class=\"section-header\" style=\"position: relative; margin-bottom: 5px;\">");
            campusHtml.append("<h2 style=\"font-family: 'SimHei', 'Microsoft YaHei', '黑体', sans-serif !important; font-weight: bold; font-size: 18px; color: #333; background-color: #f0f0f0; padding: 5px 15px; border-radius: 5px 5px 0 0; margin: 0; display: inline-block;\">校园经历</h2>");
            campusHtml.append("<div style=\"position: absolute; bottom: -2px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            campusHtml.append("<div style=\"position: absolute; bottom: -5px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            campusHtml.append("</div>");

            campusHtml.append("<div class=\"campus-content\" style=\"padding: 5px 0; line-height: 1.6; font-size: 14px; color: rgb(68, 84, 106);\">");
            log.info("处理校园经历内容(模板4): {}", campus.getCampusExperience());
            campusHtml.append(formatContentForTemplate4(campus.getCampusExperience(), resumeId));
            campusHtml.append("</div>"); // End of campus-content
            campusHtml.append("</div>"); // End of resume-section

            String finalHtml = campusHtml.toString();
            templateData.put("campus", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "校园经历(模板4)", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "校园经历(模板4)", true, "包含校园经历信息，使用模板4特殊样式");
            log.debug("校园经历映射完成(模板4)");
        } else {
            templateData.put("campus", "");
            pdfLogger.logConditionalDisplay(resumeId, "校园经历(模板4)", false, "校园经历为空");
            log.debug("校园经历为空(模板4)");
        }
    }

    /**
     * 兴趣爱好映射
     */
    private void mapInterests(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeInterestVo interest = resume.getInterest();
        if (interest != null && interest.getInterest() != null && !interest.getInterest().trim().isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "兴趣爱好", "开始映射", interest, null);
            StringBuilder interestsHtml = new StringBuilder();
            interestsHtml.append("<div class=\"resume-section\">");
            interestsHtml.append("<div class=\"section-header\">");
            interestsHtml.append("<h2>兴趣爱好</h2>");
            interestsHtml.append("</div>");
            interestsHtml.append("<div class=\"interests-content\">");
            interestsHtml.append(formatContent(interest.getInterest(), resumeId));
            interestsHtml.append("</div>"); // End of interests-content
            interestsHtml.append("</div>"); // End of resume-section

            String finalHtml = interestsHtml.toString();
            templateData.put("interests", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "兴趣爱好", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "兴趣爱好", true, "包含兴趣爱好信息");
            log.debug("兴趣爱好映射完成");
        } else {
            templateData.put("interests", "");
            pdfLogger.logConditionalDisplay(resumeId, "兴趣爱好", false, "兴趣爱好为空");
            log.debug("兴趣爱好为空");
        }
    }

    /**
     * 模板4专用的兴趣爱好映射
     */
    private void mapInterestsForTemplate4(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeInterestVo interest = resume.getInterest();
        log.info("mapInterestsForTemplate4被调用，interest: {}", interest != null ? "有数据" : "null");
        if (interest != null && interest.getInterest() != null && !interest.getInterest().trim().isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "兴趣爱好(模板4)", "开始映射", interest, null);
            StringBuilder interestsHtml = new StringBuilder();
            interestsHtml.append("<div class=\"resume-section\">");

            // 标题部分 - 与其他模块相同的样式
            interestsHtml.append("<div class=\"section-header\" style=\"position: relative; margin-bottom: 5px;\">");
            interestsHtml.append("<h2 style=\"font-family: 'SimHei', 'Microsoft YaHei', '黑体', sans-serif !important; font-weight: bold; font-size: 18px; color: #333; background-color: #f0f0f0; padding: 5px 15px; border-radius: 5px 5px 0 0; margin: 0; display: inline-block;\">兴趣爱好</h2>");
            interestsHtml.append("<div style=\"position: absolute; bottom: -2px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            interestsHtml.append("<div style=\"position: absolute; bottom: -5px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            interestsHtml.append("</div>");

            interestsHtml.append("<div class=\"interests-content\" style=\"padding: 5px 0; line-height: 1.6; font-size: 14px; color: rgb(68, 84, 106);\">");
            log.info("处理兴趣爱好内容(模板4): {}", interest.getInterest());
            interestsHtml.append(formatContentForTemplate4(interest.getInterest(), resumeId));
            interestsHtml.append("</div>"); // End of interests-content
            interestsHtml.append("</div>"); // End of resume-section

            String finalHtml = interestsHtml.toString();
            templateData.put("interests", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "兴趣爱好(模板4)", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "兴趣爱好(模板4)", true, "包含兴趣爱好信息，使用模板4特殊样式");
            log.debug("兴趣爱好映射完成(模板4)");
        } else {
            templateData.put("interests", "");
            pdfLogger.logConditionalDisplay(resumeId, "兴趣爱好(模板4)", false, "兴趣爱好为空");
            log.debug("兴趣爱好为空(模板4)");
        }
    }

    /**
     * 自我评价映射
     */
    private void mapSelfEvaluation(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeEvaluateVo evaluate = resume.getEvaluate();
        if (evaluate != null && evaluate.getSelfEvaluation() != null && !evaluate.getSelfEvaluation().trim().isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "自我评价", "开始映射", evaluate, null);
            StringBuilder evalHtml = new StringBuilder();
            evalHtml.append("<div class=\"resume-section\">");
            evalHtml.append("<div class=\"section-header\">");
            evalHtml.append("<h2>个人评价</h2>");
            evalHtml.append("</div>");
            evalHtml.append("<div class=\"evaluation-content\">");
            evalHtml.append(formatContent(evaluate.getSelfEvaluation(), resumeId));
            evalHtml.append("</div>"); // End of evaluation-content
            evalHtml.append("</div>"); // End of resume-section

            String finalHtml = evalHtml.toString();
            templateData.put("selfEvaluation", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "自我评价", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "自我评价", true, "包含自我评价信息");
            log.debug("自我评价映射完成");
        } else {
            templateData.put("selfEvaluation", "");
            pdfLogger.logConditionalDisplay(resumeId, "自我评价", false, "自我评价为空");
            log.debug("自我评价为空");
        }
    }

    /**
     * 模板4专用的自我评价映射
     */
    private void mapSelfEvaluationForTemplate4(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeEvaluateVo evaluate = resume.getEvaluate();
        if (evaluate != null && evaluate.getSelfEvaluation() != null && !evaluate.getSelfEvaluation().trim().isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "自我评价(模板4)", "开始映射", evaluate, null);
            StringBuilder evalHtml = new StringBuilder();
            evalHtml.append("<div class=\"resume-section\">");
            evalHtml.append("<div class=\"section-header\" style=\"position: relative; margin-bottom: 5px;\">");
            evalHtml.append("<h2 style=\"font-family: 'SimHei', 'Microsoft YaHei', '黑体', sans-serif !important; font-weight: bold; font-size: 18px; color: #333; background-color: #f0f0f0; padding: 5px 15px; border-radius: 5px 5px 0 0; margin: 0; display: inline-block;\">自我评价</h2>");
            evalHtml.append("<div style=\"position: absolute; bottom: -2px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            evalHtml.append("<div style=\"position: absolute; bottom: -5px; left: 0; right: 0; height: 1px; background-color: #333;\"></div>");
            evalHtml.append("</div>");
            evalHtml.append("<div class=\"evaluation-content\" style=\"padding: 5px 0; line-height: 1.6; font-size: 14px; color: rgb(68, 84, 106);\">");
            log.info("处理自我评价内容(模板4): {}", evaluate.getSelfEvaluation());
            evalHtml.append(formatContentForTemplate4(evaluate.getSelfEvaluation(), resumeId));
            evalHtml.append("</div>"); // End of evaluation-content
            evalHtml.append("</div>"); // End of resume-section

            String finalHtml = evalHtml.toString();
            templateData.put("selfEvaluation", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "自我评价(模板4)", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "自我评价(模板4)", true, "包含自我评价信息，使用模板4特殊样式");
            log.debug("自我评价映射完成(模板4)");
        } else {
            templateData.put("selfEvaluation", "");
            pdfLogger.logConditionalDisplay(resumeId, "自我评价(模板4)", false, "自我评价为空");
            log.debug("自我评价为空，跳过映射(模板4)");
        }
    }

    /**
     * 内容格式化方法
     */
    private String formatContent(String content) {
        return formatContent(content, "unknown");
    }

    /**
     * 内容格式化方法（带调试日志）
     */
    private String formatContent(String content, String resumeId) {
        log.info("=== formatContent方法被调用 ===");
        if (content == null || content.trim().isEmpty()) {
            return "";
        }

        String originalContent = content;
        System.out.println("formatContent处理前: " + content);

        // 首先处理Markdown格式
        // 处理加粗 **text** -> <strong>text</strong>
        content = content.replaceAll("\\*\\*(.*?)\\*\\*", "<strong>$1</strong>");
        log.info("加粗处理后: " + content);

        // 处理斜体 *text* -> <em>text</em>
        content = content.replaceAll("\\*(.*?)\\*", "<em>$1</em>");

        // 处理换行符，转换为HTML格式
        content = content.replace("\n", "<br/>");

        // 处理列表项
        if (content.contains("•") || content.contains("-")) {
            String[] lines = content.split("<br/>");
            StringBuilder formatted = new StringBuilder();
            boolean inList = false;

            for (String line : lines) {
                line = line.trim();
                if (line.startsWith("•") || line.startsWith("-")) {
                    if (!inList) {
                        formatted.append("<ul>");
                        inList = true;
                    }
                    formatted.append("<li>").append(line.substring(1).trim()).append("</li>");
                } else {
                    if (inList) {
                        formatted.append("</ul>");
                        inList = false;
                    }
                    if (!line.isEmpty()) {
                        formatted.append("<p>").append(line).append("</p>");
                    }
                }
            }

            if (inList) {
                formatted.append("</ul>");
            }

            return formatted.toString();
        }

        String formattedContent = "<p>" + content + "</p>";
        log.info("formatContent最终结果: {}", formattedContent);

        // 记录内容格式化过程
        if (pdfLogger != null) {
            pdfLogger.logContentFormatting(resumeId, "内容格式化", originalContent, formattedContent);
        }

        return formattedContent;
    }

    /**
     * 转义HTML特殊字符
     */
    private String escapeHtml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#39;");
    }

    /**
     * 日期格式化
     */
    private String formatDate(Date date) {
        if (date == null) return "至今";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        return sdf.format(date);
    }

    /**
     * 模板4专用的内容格式化方法
     */
    private String formatContentForTemplate4(String content, String resumeId) {
        log.info("=== formatContentForTemplate4方法被调用 ===");
        if (content == null || content.trim().isEmpty()) {
            return "";
        }

        String originalContent = content;
        System.out.println("formatContentForTemplate4处理前: " + content);

        // 首先处理Markdown格式的加粗文本 **text** -> <strong>text</strong>
        // 参考模板3的样式：加粗文本使用纯黑色(#000000)，普通文本使用灰色(rgb(68, 84, 106))
        content = content.replaceAll("\\*\\*(.*?)\\*\\*", "<strong style=\"color: #000000; font-weight: bold;\">$1</strong>");
        log.info("加粗处理后: " + content);

        // 处理换行符，转换为HTML格式
        content = content.replace("\n", "<br/>");

        // 处理有序列表 - 检测以数字开头的行
        String[] lines = content.split("<br/>");
        StringBuilder formattedContent = new StringBuilder();

        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) {
                continue;
            }

            // 检测是否是有序列表项（以数字.开头）
            if (line.matches("^\\d+\\.\\s*.*")) {
                formattedContent.append("<div style=\"margin-bottom: 3px;\">").append(line).append("</div>");
            }
            // 普通文本（已经包含了加粗处理）
            else {
                formattedContent.append("<div style=\"margin-bottom: 3px;\">").append(line).append("</div>");
            }
        }

        String finalContent = formattedContent.toString();
        log.info("formatContentForTemplate4最终结果: {}", finalContent);

        // 记录内容格式化过程
        if (pdfLogger != null) {
            pdfLogger.logContentFormatting(resumeId, "内容格式化(模板4)", originalContent, finalContent);
        }

        return finalContent;
    }

    /**
     * 模板5专用的教育背景映射
     */
    private void mapEducationForTemplate5(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeEducationalVo> educationList = resume.getEducationList();
        log.info("mapEducationForTemplate5被调用，educationList: {}", educationList != null ? educationList.size() + "条记录" : "null");
        if (educationList != null && !educationList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "教育背景(模板5)", "开始映射", educationList, null);
            StringBuilder educationHtml = new StringBuilder();
            educationHtml.append("<div class=\"section education-section\">");
            educationHtml.append("<div class=\"section-title\">");
            educationHtml.append("<h2>教育背景 / EDUCATIONAL EXPERIENCE</h2>");
            educationHtml.append("</div>");
            educationHtml.append("<div class=\"section-content\">");

            for (ResumeEducationalVo education : educationList) {
                educationHtml.append("<div class=\"education-item\">");

                // 教育信息头部 - 三列布局
                educationHtml.append("<div class=\"edu-header\">");

                // 学校和学位信息
                String schoolInfo = education.getSchool();
                if (education.getEducation() != null && !education.getEducation().trim().isEmpty()) {
                    schoolInfo += "（" + education.getEducation() + "）";
                }
                educationHtml.append("<div class=\"edu-school\">").append(schoolInfo).append("</div>");

                // 专业
                String major = "";
                if (education.getMajor() != null && !education.getMajor().trim().isEmpty()) {
                    major = education.getMajor();
                }
                educationHtml.append("<div class=\"edu-major\">").append(major).append("</div>");

                // 时间 - 右对齐
                String timeStr = formatTimeForTemplate5(education.getTimePeriod());
                educationHtml.append("<div class=\"edu-time\">").append(timeStr).append("</div>");

                educationHtml.append("</div>");

                // 主修课程
                if (education.getMainCourses() != null && !education.getMainCourses().trim().isEmpty()) {
                    educationHtml.append("<div class=\"edu-courses\">");
                    educationHtml.append("<span class=\"courses-label\">主修课程：</span>");
                    educationHtml.append("<span class=\"courses-content\">").append(formatContentForTemplate5(education.getMainCourses(), resumeId)).append("</span>");
                    educationHtml.append("</div>");
                }

                educationHtml.append("</div>");
            }

            educationHtml.append("</div>");
            educationHtml.append("</div>");

            templateData.put("education", educationHtml.toString());
            pdfLogger.logConditionalDisplay(resumeId, "教育背景(模板5)", true, "包含" + educationList.size() + "条教育记录");
            log.info("教育背景映射完成(模板5) - 共{}条记录", educationList.size());
        } else {
            templateData.put("education", "");
            pdfLogger.logConditionalDisplay(resumeId, "教育背景(模板5)", false, "教育背景为空");
            log.info("教育背景为空(模板5)");
        }
    }

    /**
     * 模板5专用的工作经验映射
     */
    private void mapWorkExperienceForTemplate5(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeWorkVo> workList = resume.getWorkList();
        log.info("mapWorkExperienceForTemplate5被调用，workList: {}", workList != null ? workList.size() + "条记录" : "null");
        if (workList != null && !workList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "实习经历(模板5)", "开始映射", workList, null);
            StringBuilder workHtml = new StringBuilder();
            workHtml.append("<div class=\"section work-section\">");
            workHtml.append("<div class=\"section-title\">");
            workHtml.append("<h2>实习经历 / INTERNSHIP EXPERIENCE</h2>");
            workHtml.append("</div>");
            workHtml.append("<div class=\"section-content work-content\">");

            for (ResumeWorkVo work : workList) {
                workHtml.append("<div class=\"work-item\">");
                workHtml.append("<div class=\"work-header\">");

                // 时间 - 使用点分隔格式
                String timeStr = formatTimeForTemplate5(work.getTimePeriod());
                workHtml.append("<div class=\"work-time\">").append(timeStr).append("</div>");

                // 公司
                if (work.getCompany() != null && !work.getCompany().trim().isEmpty()) {
                    workHtml.append("<div class=\"work-company\">").append(work.getCompany()).append("</div>");
                }

                // 职位
                if (work.getPosition() != null && !work.getPosition().trim().isEmpty()) {
                    workHtml.append("<div class=\"work-position\">").append(work.getPosition()).append("</div>");
                }

                workHtml.append("</div>");

                // 工作描述
                if (work.getWorkDescription() != null && !work.getWorkDescription().trim().isEmpty()) {
                    workHtml.append("<div class=\"work-description\">");
                    workHtml.append("<span class=\"work-label\">工作职责：</span>");
                    workHtml.append("<div class=\"work-content\">").append(formatContentForTemplate5(work.getWorkDescription(), resumeId)).append("</div>");
                    workHtml.append("</div>");
                }

                workHtml.append("</div>");
            }

            workHtml.append("</div>");
            workHtml.append("</div>");

            templateData.put("work", workHtml.toString());
            pdfLogger.logConditionalDisplay(resumeId, "实习经历(模板5)", true, "包含" + workList.size() + "条工作记录");
            log.info("实习经历映射完成(模板5) - 共{}条记录", workList.size());
        } else {
            templateData.put("work", "");
            pdfLogger.logConditionalDisplay(resumeId, "实习经历(模板5)", false, "实习经历为空");
            log.info("实习经历为空(模板5)");
        }
    }

    /**
     * 模板5专用的项目经验映射
     */
    private void mapProjectsForTemplate5(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeProjectExperienceVo> projectList = resume.getProjectList();
        log.info("mapProjectsForTemplate5被调用，projectList: {}", projectList != null ? projectList.size() + "条记录" : "null");
        if (projectList != null && !projectList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "项目经历(模板5)", "开始映射", projectList, null);
            StringBuilder projectHtml = new StringBuilder();
            projectHtml.append("<div class=\"section projects-section\">");
            projectHtml.append("<div class=\"section-title\">");
            projectHtml.append("<h2>项目经历 / PROJECT EXPERIENCE</h2>");
            projectHtml.append("</div>");
            projectHtml.append("<div class=\"section-content\">");

            for (ResumeProjectExperienceVo project : projectList) {
                projectHtml.append("<div class=\"project-item\">");

                // 使用与实习经历相同的三列布局
                projectHtml.append("<div class=\"work-header\">");

                // 时间（左侧）
                String timeStr = formatTimeForTemplate5(project.getTimePeriod());
                projectHtml.append("<div class=\"work-time\">").append(timeStr).append("</div>");

                // 项目名称（中间，加粗）
                projectHtml.append("<div class=\"work-company\">")
                        .append(escapeHtml(project.getProjectName() != null ? project.getProjectName() : ""))
                        .append("</div>");

                // 角色（右侧）
                projectHtml.append("<div class=\"work-position\">")
                        .append(escapeHtml(project.getRole() != null ? project.getRole() : ""))
                        .append("</div>");

                projectHtml.append("</div>"); // End of work-header

                // 项目描述
                if (project.getProjectDescription() != null && !project.getProjectDescription().trim().isEmpty()) {
                    projectHtml.append("<div class=\"work-description\">");
                    projectHtml.append("项目描述：");
                    projectHtml.append("<div>").append(formatContentForTemplate5(project.getProjectDescription(), resumeId)).append("</div>");
                    projectHtml.append("</div>");
                }

                projectHtml.append("</div>"); // End of project-item
            }

            projectHtml.append("</div>");
            projectHtml.append("</div>");

            templateData.put("projects", projectHtml.toString());
            pdfLogger.logConditionalDisplay(resumeId, "项目经历(模板5)", true, "包含" + projectList.size() + "条项目记录");
            log.info("项目经历映射完成(模板5) - 共{}条记录", projectList.size());
        } else {
            templateData.put("projects", "");
            pdfLogger.logConditionalDisplay(resumeId, "项目经历(模板5)", false, "项目经历为空");
            log.info("项目经历为空(模板5)");
        }
    }

    /**
     * 模板5专用的练手项目映射
     */
    private void mapPracticesForTemplate5(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumePracticeVo> practiceList = resume.getPracticeList();
        log.info("mapPracticesForTemplate5被调用，practiceList: {}", practiceList != null ? practiceList.size() + "条记录" : "null");
        if (practiceList != null && !practiceList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "练手项目(模板5)", "开始映射", practiceList, null);
            StringBuilder practiceHtml = new StringBuilder();
            practiceHtml.append("<div class=\"section practices-section\">");
            practiceHtml.append("<div class=\"section-title\">");
            practiceHtml.append("<h2>练手项目 / PRACTICE PROJECT</h2>");
            practiceHtml.append("</div>");
            practiceHtml.append("<div class=\"section-content\">");

            for (ResumePracticeVo practice : practiceList) {
                practiceHtml.append("<div class=\"project-item\">");

                // 使用与实习经历相同的三列布局
                practiceHtml.append("<div class=\"work-header\">");

                // 时间（左侧）
                String timeStr = formatTimeForTemplate5(practice.getTimePeriod());
                practiceHtml.append("<div class=\"work-time\">").append(timeStr).append("</div>");

                // 项目名称（中间，加粗）
                practiceHtml.append("<div class=\"work-company\">")
                        .append(escapeHtml(practice.getProjectName() != null ? practice.getProjectName() : ""))
                        .append("</div>");

                // 角色（右侧）- 练手项目通常没有角色，可以留空或显示"个人项目"
                practiceHtml.append("<div class=\"work-position\">")
                        .append(escapeHtml(practice.getRole() != null ? practice.getRole() : "个人项目"))
                        .append("</div>");

                practiceHtml.append("</div>"); // End of work-header

                // 项目地址
                if (practice.getProjectUrl() != null && !practice.getProjectUrl().trim().isEmpty()) {
                    practiceHtml.append("<div class=\"project-url\" style=\"line-height: 1.6; font-size: 14px; margin-bottom: 5px;\">");
                    practiceHtml.append("<span style=\"color: rgb(68, 84, 106);\">项目地址：</span>");
                    practiceHtml.append("<span style=\"color: #0066cc; text-decoration: underline; font-weight: normal;\">");
                    practiceHtml.append(escapeHtml(practice.getProjectUrl()));
                    practiceHtml.append("</span>");
                    practiceHtml.append("</div>");
                }

                // 项目描述
                if (practice.getProjectDescription() != null && !practice.getProjectDescription().trim().isEmpty()) {
                    practiceHtml.append("<div class=\"work-description\">");
                    practiceHtml.append("项目描述：");
                    practiceHtml.append("<div>").append(formatContentForTemplate5(practice.getProjectDescription(), resumeId)).append("</div>");
                    practiceHtml.append("</div>");
                }

                practiceHtml.append("</div>"); // End of project-item
            }

            practiceHtml.append("</div>");
            practiceHtml.append("</div>");

            templateData.put("practices", practiceHtml.toString());
            pdfLogger.logConditionalDisplay(resumeId, "练手项目(模板5)", true, "包含" + practiceList.size() + "条练手项目记录");
            log.info("练手项目映射完成(模板5) - 共{}条记录", practiceList.size());
        } else {
            templateData.put("practices", "");
            pdfLogger.logConditionalDisplay(resumeId, "练手项目(模板5)", false, "练手项目为空");
            log.info("练手项目为空(模板5)");
        }
    }

    /**
     * 模板5专用的技能特长映射
     */
    private void mapSkillsForTemplate5(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeTalentVo> skillList = resume.getTalentList();
        log.info("mapSkillsForTemplate5被调用，skillList: {}", skillList != null ? skillList.size() + "条记录" : "null");
        if (skillList != null && !skillList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "专业技能(模板5)", "开始映射", skillList, null);
            StringBuilder skillHtml = new StringBuilder();
            skillHtml.append("<div class=\"section skills-section\">");
            skillHtml.append("<div class=\"section-title\">");
            skillHtml.append("<h2>专业技能 / PROFESSIONAL SKILLS</h2>");
            skillHtml.append("</div>");
            skillHtml.append("<div class=\"section-content\">");

            for (ResumeTalentVo skill : skillList) {
                if (skill.getSkillDescription() != null && !skill.getSkillDescription().trim().isEmpty()) {
                    skillHtml.append("<div class=\"skill-item\">");
                    skillHtml.append("<div class=\"skill-description-item\">");
                    skillHtml.append("<div class=\"skill-description-body\">");
                    skillHtml.append(formatContentForTemplate5(skill.getSkillDescription(), resumeId));
                    skillHtml.append("</div>");
                    skillHtml.append("</div>");
                    skillHtml.append("</div>");
                }
            }

            skillHtml.append("</div>");
            skillHtml.append("</div>");

            templateData.put("skills", skillHtml.toString());
            pdfLogger.logConditionalDisplay(resumeId, "专业技能(模板5)", true, "包含" + skillList.size() + "条技能记录");
            log.info("专业技能映射完成(模板5) - 共{}条记录", skillList.size());
        } else {
            templateData.put("skills", "");
            pdfLogger.logConditionalDisplay(resumeId, "专业技能(模板5)", false, "专业技能为空");
            log.info("专业技能为空(模板5)");
        }
    }

    /**
     * 模板5专用的证书奖项映射
     */
    private void mapCertificatesForTemplate5(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeCertificateVo certificate = resume.getCertificate();
        log.info("mapCertificatesForTemplate5被调用，certificateList: {}", certificate);
        if (certificate != null) {
            pdfLogger.logDataMappingTrace(resumeId, "证书奖项(模板5)", "开始映射", certificate, null);
            StringBuilder certificateHtml = new StringBuilder();
            certificateHtml.append("<div class=\"section certificates-section\">");
            certificateHtml.append("<div class=\"section-title\">");
            certificateHtml.append("<h2>证书奖项 / CERTIFICATES AND AWARDS</h2>");
            certificateHtml.append("</div>");
            certificateHtml.append("<div class=\"section-content\">");
            if (certificate.getCertificateName() != null && !certificate.getCertificateName().trim().isEmpty()) {
                certificateHtml.append("<div class=\"certificate-item\">");
                certificateHtml.append(formatContentForTemplate5(certificate.getCertificateName(), resumeId));
                certificateHtml.append("</div>");
            }


            certificateHtml.append("</div>");
            certificateHtml.append("</div>");

            templateData.put("certificates", certificateHtml.toString());
        } else {
            templateData.put("certificates", "");
            pdfLogger.logConditionalDisplay(resumeId, "证书奖项(模板5)", false, "证书奖项为空");
            log.info("证书奖项为空(模板5)");
        }
    }

    /**
     * 模板5专用的兴趣爱好映射
     */
    private void mapInterestsForTemplate5(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeInterestVo interest = resume.getInterest();
        log.info("mapInterestsForTemplate5被调用，interest: {}", interest);
        if (interest != null) {
            StringBuilder interestHtml = new StringBuilder();
            interestHtml.append("<div class=\"section interests-section\">");
            interestHtml.append("<div class=\"section-title\">");
            interestHtml.append("<h2>兴趣爱好 / INTERESTS</h2>");
            interestHtml.append("</div>");
            interestHtml.append("<div class=\"section-content\">");

            if (interest.getInterest() != null && !interest.getInterest().trim().isEmpty()) {
                interestHtml.append("<div class=\"interest-item\">");
                interestHtml.append(formatContentForTemplate5(interest.getInterest(), resumeId));
                interestHtml.append("</div>");
            }
            interestHtml.append("</div>");
            interestHtml.append("</div>");
            templateData.put("interests", interestHtml.toString());
        } else {
            templateData.put("interests", "");
            pdfLogger.logConditionalDisplay(resumeId, "兴趣爱好(模板5)", false, "兴趣爱好为空");
            log.info("兴趣爱好为空(模板5)");
        }
    }

    /**
     * 模板5专用的自我评价映射
     */
    private void mapSelfEvaluationForTemplate5(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeEvaluateVo selfEvaluation = resume.getEvaluate();
        log.info("mapSelfEvaluationForTemplate5被调用，selfEvaluation: {}", selfEvaluation != null ? "有内容" : "null");
        if (selfEvaluation != null && selfEvaluation.getSelfEvaluation() != null && !selfEvaluation.getSelfEvaluation().trim().isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "个人总结(模板5)", "开始映射", selfEvaluation, null);
            StringBuilder evaluationHtml = new StringBuilder();
            evaluationHtml.append("<div class=\"section evaluation-section\">");
            evaluationHtml.append("<div class=\"section-title\">");
            evaluationHtml.append("<h2>个人总结 / SELF EVALUATION</h2>");
            evaluationHtml.append("</div>");
            evaluationHtml.append("<div class=\"section-content\">");
            evaluationHtml.append("<div class=\"evaluation-content\">");
            evaluationHtml.append(formatContentForTemplate5(selfEvaluation.getSelfEvaluation(), resumeId));
            evaluationHtml.append("</div>");
            evaluationHtml.append("</div>");
            evaluationHtml.append("</div>");

            templateData.put("selfEvaluation", evaluationHtml.toString());
            pdfLogger.logConditionalDisplay(resumeId, "个人总结(模板5)", true, "包含自我评价内容");
            log.info("个人总结映射完成(模板5)");
        } else {
            templateData.put("selfEvaluation", "");
            pdfLogger.logConditionalDisplay(resumeId, "个人总结(模板5)", false, "个人总结为空");
            log.info("个人总结为空(模板5)");
        }
    }

    /**
     * 模板5专用的时间格式化方法 - 使用点分隔格式
     */
    private String formatTimeForTemplate5(String timePeriod) {
        if (timePeriod == null || timePeriod.trim().isEmpty()) {
            return "";
        }

        // 将常见的时间分隔符转换为点分隔格式
        String formatted = timePeriod.trim();

        // 替换常见的分隔符
        formatted = formatted.replaceAll("年", ".");
        formatted = formatted.replaceAll("月", "");
        formatted = formatted.replaceAll("/", ".");
        formatted = formatted.replaceAll("-", " - ");

        // 处理"至今"的情况
        formatted = formatted.replaceAll("至今", "至今");

        return formatted;
    }

    /**
     * 模板5专用的内容格式化方法 - 简洁格式
     */
    private String formatContentForTemplate5(String content, String resumeId) {
        if (content == null || content.trim().isEmpty()) {
            return "";
        }

        String originalContent = content;
        log.info("formatContentForTemplate5开始处理: {}", originalContent);

        // 清理内容
        content = content.trim();

        // 处理加粗标记 - 参考模板3的样式：加粗文本使用纯黑色(#000000)和加粗字体
        content = content.replaceAll("\\*\\*(.*?)\\*\\*", "<strong style=\"color: #000000; font-weight: bold; font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;\">$1</strong>");

        // 处理换行符，转换为HTML格式
        content = content.replace("\n", "<br/>");

        // 处理列表项 - 简化处理
        String[] lines = content.split("<br/>");
        StringBuilder formattedContent = new StringBuilder();

        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) {
                continue;
            }

            // 检测是否是列表项（以数字.、-、•开头）
            if (line.matches("^[\\d]+\\.\\s*.*") || line.matches("^[-•]\\s*.*")) {
                formattedContent.append("<div style=\"margin-bottom: 3px;\">").append(line).append("</div>");
            } else {
                formattedContent.append("<div style=\"margin-bottom: 3px;\">").append(line).append("</div>");
            }
        }

        String finalContent = formattedContent.toString();
        log.info("formatContentForTemplate5最终结果: {}", finalContent);

        // 记录内容格式化过程
        if (pdfLogger != null) {
            pdfLogger.logContentFormatting(resumeId, "内容格式化(模板5)", originalContent, finalContent);
        }

        return finalContent;
    }

    /**
     * 模板5专用的基本信息映射
     */
    private void mapBasicInfoForTemplate5(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeInformationVo info = resume.getInformation();
        if (info != null) {
            // 基本信息字段
            templateData.put("name", info.getName() != null ? info.getName() : "求职者");
            templateData.put("age", info.getAge() != null ? info.getAge().toString() : "");
            templateData.put("gender", info.getGender() != null ? info.getGender() : "");
            templateData.put("phone", info.getPhone() != null ? info.getPhone() : "");
            templateData.put("email", info.getEmail() != null ? info.getEmail() : "");
            templateData.put("hometown", info.getHometown() != null ? info.getHometown() : "");
            templateData.put("jobObjective", info.getJobObjective() != null ? info.getJobObjective() : "");

            // 头像处理 - 使用img标签而不是class="photo"
            if (info.getAvatar() != null && !info.getAvatar().isEmpty()) {
                templateData.put("avatar", "<img src=\"" + info.getAvatar() + "\" alt=\"头像\" class=\"avatar\" />");
            } else {
                templateData.put("avatar", "");
            }

            pdfLogger.logConditionalDisplay(resumeId, "基本信息(模板5)", true, "包含完整基本信息");
            log.info("基本信息映射完成(模板5) - 姓名: {}, 电话: {}", info.getName(), info.getPhone());
        } else {
            // 设置默认值
            templateData.put("name", "求职者");
            templateData.put("age", "");
            templateData.put("gender", "");
            templateData.put("phone", "");
            templateData.put("email", "");
            templateData.put("hometown", "");
            templateData.put("jobObjective", "");
            templateData.put("avatar", "");

            pdfLogger.logConditionalDisplay(resumeId, "基本信息(模板5)", false, "基本信息为空，使用默认值");
            log.info("基本信息为空(模板5)，使用默认值");
        }
    }
}