package com.bimowu.interview.config;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云配置类
 */
@Configuration
public class AliyunConfig {

    @Value("${aliyun.accessKey}")
    private String accessKey;

    @Value("${aliyun.accessKeySecret}")
    private String accessKeySecret;

    @Value("${aliyun.region}")
    private String region;

    @Value("${aliyun.appKey:}")
    private String appKey;

    /**
     * 创建阿里云客户端
     */
    @Bean
    public IAcsClient acsClient() {
        DefaultProfile profile = DefaultProfile.getProfile(region, accessKey, accessKeySecret);
        return new DefaultAcsClient(profile);
    }

    public String getAppKey() {
        return appKey;
    }
} 