package com.bimowu.resume.controller;

import com.bimowu.resume.common.service.impl.PDFMetricsService;
import com.bimowu.resume.common.service.impl.PDFPerformanceMonitor;
import com.bimowu.resume.utils.PDFGenerationLogger;
import com.bimowu.resume.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * PDF生成监控指标控制器
 */
@RestController
@RequestMapping("/api/pdf/metrics")
@Slf4j
public class PDFMetricsController {
    
    @Autowired
    private PDFMetricsService metricsService;
    
    @Autowired
    private PDFPerformanceMonitor performanceMonitor;
    
    @Autowired
    private PDFGenerationLogger pdfLogger;
    
    /**
     * 获取PDF生成监控指标
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getMetrics() {
        try {
            // 检查自动重置
            metricsService.checkAutoReset();
            
            Map<String, Object> metrics = metricsService.getMetricsAsJson();
            return Result.success(metrics);
        } catch (Exception e) {
            log.error("获取PDF监控指标失败", e);
            return Result.error("获取监控指标失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取详细的监控报告
     */
    @GetMapping("/report")
    public Result<String> getDetailedReport() {
        try {
            String report = metricsService.getDetailedMetricsReport();
            return Result.success(report);
        } catch (Exception e) {
            log.error("获取PDF监控报告失败", e);
            return Result.error("获取监控报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取性能监控报告
     */
    @GetMapping("/performance")
    public Result<String> getPerformanceReport() {
        try {
            String report = performanceMonitor.getPerformanceReport();
            return Result.success(report);
        } catch (Exception e) {
            log.error("获取性能监控报告失败", e);
            return Result.error("获取性能监控报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取健康状态
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> getHealthStatus() {
        try {
            boolean isHealthy = metricsService.isHealthy();
            Map<String, Object> health = new HashMap<>();
            health.put("status", isHealthy ? "UP" : "DOWN");
            health.put("healthy", isHealthy);
            health.put("currentConcurrency", metricsService.getCurrentConcurrency());
            health.put("maxConcurrency", metricsService.getMaxConcurrency());
            health.put("successRate", metricsService.getSuccessRate());
            health.put("averageTime", metricsService.getAverageGenerationTime());
            return Result.success(health);
        } catch (Exception e) {
            log.error("获取健康状态失败", e);
            return Result.error("获取健康状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置监控指标
     */
    @PostMapping("/reset")
    public Result<String> resetMetrics() {
        try {
            metricsService.resetMetrics();
            performanceMonitor.resetStats();
            pdfLogger.resetStatistics();
            return Result.success("监控指标已重置");
        } catch (Exception e) {
            log.error("重置监控指标失败", e);
            return Result.error("重置监控指标失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取调试统计信息
     */
    @GetMapping("/debug")
    public Result<String> getDebugStatistics() {
        try {
            if (pdfLogger.isDebugEnabled()) {
                pdfLogger.logDebugStatistics();
                return Result.success("调试统计信息已输出到日志");
            } else {
                return Result.error("调试模式未启用");
            }
        } catch (Exception e) {
            log.error("获取调试统计失败", e);
            return Result.error("获取调试统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 动态设置调试模式
     */
    @PostMapping("/debug/enable")
    public Result<String> enableDebugMode(@RequestParam(defaultValue = "true") boolean enabled,
                                         @RequestParam(defaultValue = "false") boolean detailed) {
        try {
            pdfLogger.setDebugEnabled(enabled);
            pdfLogger.setDetailedDebugEnabled(detailed);
            
            String message = String.format("调试模式已%s，详细模式已%s", 
                enabled ? "启用" : "禁用", 
                detailed ? "启用" : "禁用");
            
            return Result.success(message);
        } catch (Exception e) {
            log.error("设置调试模式失败", e);
            return Result.error("设置调试模式失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前配置信息
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> getCurrentConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("debugEnabled", pdfLogger.isDebugEnabled());
            config.put("detailedDebugEnabled", pdfLogger.isDetailedDebugEnabled());
            config.put("performanceMonitoringEnabled", performanceMonitor.getPerformanceReport().contains("性能监控未启用") ? false : true);
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取配置信息失败", e);
            return Result.error("获取配置信息失败: " + e.getMessage());
        }
    }
}