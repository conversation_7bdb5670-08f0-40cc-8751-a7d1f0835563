const fs = require('fs');
const path = require('path');

// 修复 element-plus 模块引用问题
function fixElementPlusReferences() {
  const indexPath = path.join(__dirname, 'node_modules/element-plus/es/index.mjs');

  if (!fs.existsSync(indexPath)) {
    console.log('Element Plus index.mjs not found, skipping fix...');
    return;
  }

  try {
    let content = fs.readFileSync(indexPath, 'utf8');
    let fixed = false;

    // 修复可能的错误引用
    const fixes = [
      {
        wrong: './components/config-provider/src/hooks/use-globalThis-config.mjs',
        correct: './components/config-provider/src/hooks/use-global-config.mjs'
      },
      {
        wrong: './hooks/use-prevent-globalThis/index.mjs',
        correct: './hooks/use-prevent-global/index.mjs'
      }
    ];

    fixes.forEach(fix => {
      if (content.includes(fix.wrong)) {
        content = content.replace(new RegExp(fix.wrong.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), fix.correct);
        console.log(`✅ Fixed reference: ${fix.wrong} -> ${fix.correct}`);
        fixed = true;
      }
    });

    if (fixed) {
      fs.writeFileSync(indexPath, content, 'utf8');
      console.log('✅ Element Plus module references fixed');
    } else {
      console.log('✅ Element Plus references are already correct');
    }

    // 验证修复后的文件
    const hooksDir = path.join(__dirname, 'node_modules/element-plus/es/components/config-provider/src/hooks');
    if (fs.existsSync(hooksDir)) {
      const files = fs.readdirSync(hooksDir);
      console.log('📁 Available hook files:', files.join(', '));
    }

  } catch (error) {
    console.error('❌ Error fixing element-plus references:', error.message);
  }
}

fixElementPlusReferences();
