import{d as _,c as d,a as e,b as u,w as a,u as l,e as r,o as p,f as i,_ as c}from"./index-d8f61d92.js";const m={class:"not-found"},f={class:"not-found-content"},v=_({__name:"NotFoundView",setup(x){const t=l(),n=()=>{t.push("/")};return(N,o)=>{const s=r("el-button");return p(),d("div",m,[e("div",f,[o[1]||(o[1]=e("h1",null,"404",-1)),o[2]||(o[2]=e("h2",null,"页面未找到",-1)),o[3]||(o[3]=e("p",null,"您访问的页面不存在或已被移动",-1)),u(s,{type:"primary",onClick:n},{default:a(()=>o[0]||(o[0]=[i("返回首页",-1)])),_:1,__:[0]})])])}}});const w=c(v,[["__scopeId","data-v-2ec3506d"]]);export{w as default};
