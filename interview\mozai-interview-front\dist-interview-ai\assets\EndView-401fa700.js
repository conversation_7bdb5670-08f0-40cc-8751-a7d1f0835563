import{d as u,c as p,a as t,b as o,w as s,u as f,e as _,o as m,f as n,_ as v}from"./index-4df1abd4.js";const b={class:"end-container"},w={class:"end-content"},x={class:"result-info"},V={class:"action-buttons"},y=u({__name:"EndView",setup(C){const c=f(),l=()=>{c.push("/interviews")},i=()=>{c.push("/")};return(k,e)=>{const r=_("el-alert"),a=_("el-button"),d=_("el-result");return m(),p("div",b,[t("div",w,[o(d,{icon:"success",title:"面试已完成","sub-title":"感谢您完成本次AI面试"},{extra:s(()=>[t("div",x,[o(r,{title:"面试结果正在生成中",type:"info",closable:!1,"show-icon":""},{default:s(()=>e[0]||(e[0]=[t("p",{class:"alert-content"},[n(" 系统正在分析您的面试表现，生成详细的面试报告。"),t("br"),n(" 请稍后前往面试记录查看您的面试结果。 ")],-1)])),_:1,__:[0]})]),t("div",V,[o(a,{type:"primary",onClick:l},{default:s(()=>e[1]||(e[1]=[n(" 查看面试记录 ",-1)])),_:1,__:[1]}),o(a,{onClick:i},{default:s(()=>e[2]||(e[2]=[n("返回首页",-1)])),_:1,__:[2]})])]),_:1})])])}}});const g=v(y,[["__scopeId","data-v-fce6bf38"]]);export{g as default};
