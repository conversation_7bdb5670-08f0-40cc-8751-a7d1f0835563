<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.interview.dao.ResumeHrQuestionsMapper">

    <resultMap id="BaseResultMap" type="com.bimowu.interview.model.ResumeHrQuestions">
            <id property="queId" column="que_id" jdbcType="BIGINT"/>
            <result property="questionType" column="question_type" jdbcType="OTHER"/>
            <result property="question" column="question" jdbcType="VARCHAR"/>
            <result property="answer" column="answer" jdbcType="VARCHAR"/>
            <result property="createAt" column="create_at" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        que_id,question_type,question,
        answer,create_at,create_time,
        update_time
    </sql>
</mapper>
