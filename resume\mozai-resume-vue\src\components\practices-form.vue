<template>
  <div class="form-container">
    <div class="editor-header">
      <h3>校园经历</h3>
      <div class="editor-actions">
        <el-button type="success" size="small" @click="polishContent" :disabled="!formData.content">
          <el-icon class="icon"><magic-stick /></el-icon> AI润色
        </el-button>
      </div>
    </div>

    <div class="editor-content">
      <el-input
          v-model="formData.content"
          type="textarea"
          :rows="10"
          placeholder="请输入您的校园经历，如：学生组织、社团活动、志愿服务等，建议按时间倒序排列，描述清晰具体。"
      />
    </div>

    <!-- AI润色对话框 -->
    <el-dialog
      v-model="polishDialogVisible"
      title="AI润色"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="polishLoading" class="loading-container">
        <el-skeleton :rows="5" animated />
        <div class="loading-text">AI正在润色您的内容...</div>
      </div>
      <div v-else>
        <div class="polish-result">
          <div class="original-content">
            <h4>原始内容</h4>
            <div class="content-box">{{ formData.content }}</div>
          </div>
          <div class="polished-content">
            <h4>润色后内容</h4>
            <div class="content-box">{{ polishedContent }}</div>
          </div>
        </div>
        <div class="polish-options">
          <h4>请选择要使用的版本</h4>
          <el-radio-group v-model="selectedVersion">
            <el-radio :label="0">使用原始内容</el-radio>
            <el-radio :label="1">使用润色后内容</el-radio>
          </el-radio-group>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="polishDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyPolishedContent" :disabled="polishLoading">
            应用所选内容
          </el-button>
        </span>
      </template>
    </el-dialog>

    <div class="form-actions">
      <el-button type="primary" @click="saveForm">保存信息</el-button>
      <el-button @click="resetForm">重置</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { polishCampus } from '../api/resume'
import { MagicStick } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = ref({
  content: ''
})

// AI润色相关
const polishDialogVisible = ref(false)
const polishLoading = ref(false)
const polishedContent = ref('')
const selectedVersion = ref(1)

// 初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal) {
    // 确保content始终是字符串类型
    formData.value.content = typeof newVal === 'object' ? '' : newVal || ''
  } else {
    formData.value.content = ''
  }
}, { immediate: true, deep: true })

// 润色内容
const polishContent = () => {
  if (!formData.value.content) {
    ElMessage.warning('请先输入内容再使用AI润色功能')
    return
  }

  polishDialogVisible.value = true
  polishLoading.value = true

  // 调用后端AI润色API
  polishCampus(formData.value.content).then(res => {
    if (res.code === 0) {
      polishedContent.value = res.data
    } else {
      ElMessage.error(res.msg || '润色失败')
      polishedContent.value = formData.value.content
    }
    polishLoading.value = false
  }).catch(error => {
    console.error('润色请求失败', error)
    ElMessage.error('润色请求失败，请稍后重试')
    polishedContent.value = formData.value.content
    polishLoading.value = false
  })
}

// 应用润色后的内容
const applyPolishedContent = () => {
  if (selectedVersion.value === 1) {
    formData.value.content = polishedContent.value
  }
  polishDialogVisible.value = false
  ElMessage.success('内容已更新')
}

// 保存表单
const saveForm = () => {
  emit('update', formData.value.content)
  ElMessage.success('校园经历信息已保存')
}

// 重置表单
const resetForm = () => {
  formData.value.content = props.data || ''
  ElMessage.info('已重置为上次保存的内容')
}
</script>

<style scoped>
.form-container {
  padding: 20px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.editor-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.editor-content {
  margin-bottom: 20px;
}

.tips {
  margin-bottom: 20px;
}

.tips-content {
  font-size: 13px;
  line-height: 1.5;
}

.tips-content p {
  margin: 5px 0;
}

.loading-container {
  padding: 20px 0;
  text-align: center;
}

.loading-text {
  margin-top: 15px;
  color: #409eff;
  font-size: 14px;
}

.polish-result {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.original-content, .polished-content {
  flex: 1;
}

.content-box {
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  min-height: 200px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  font-size: 14px;
  line-height: 1.5;
}

.polish-options {
  margin-top: 20px;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.icon {
  margin-right: 5px;
}
</style>