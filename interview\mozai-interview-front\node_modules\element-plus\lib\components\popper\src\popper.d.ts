import type { ExtractPropTypes } from 'vue';
import type Popper from './popper.vue';
declare const effects: readonly ["light", "dark"];
declare const triggers: readonly ["click", "contextmenu", "hover", "focus"];
export declare const Effect: {
    readonly LIGHT: "light";
    readonly DARK: "dark";
};
export declare const roleTypes: readonly ["dialog", "grid", "group", "listbox", "menu", "navigation", "tooltip", "tree"];
export declare type PopperEffect = typeof effects[number];
export declare type PopperTrigger = typeof triggers[number];
export declare const popperProps: {
    readonly role: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "group" | "listbox" | "grid" | "menu" | "tooltip" | "dialog" | "navigation" | "tree", unknown, "tooltip", boolean>;
};
export declare type PopperProps = ExtractPropTypes<typeof popperProps>;
export declare type PopperInstance = InstanceType<typeof Popper>;
/** @deprecated use `popperProps` instead, and it will be deprecated in the next major version */
export declare const usePopperProps: {
    readonly role: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "group" | "listbox" | "grid" | "menu" | "tooltip" | "dialog" | "navigation" | "tree", unknown, "tooltip", boolean>;
};
/** @deprecated use `PopperProps` instead, and it will be deprecated in the next major version */
export declare type UsePopperProps = PopperProps;
export {};
