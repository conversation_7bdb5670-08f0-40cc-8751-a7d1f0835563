# Element Plus 构建错误修复方案

## 问题描述
```
Could not resolve "./components/config-provider/src/hooks/use-globalThis-config.mjs" from "node_modules/element-plus/es/index.mjs"
RollupError: Could not resolve module paths
```

## 根本原因
1. **Element Plus 版本问题**: 某些版本的 Element Plus 存在模块引用错误
2. **缓存问题**: 旧的构建缓存可能包含错误的模块引用
3. **环境差异**: Linux 和 Windows 环境下的文件系统差异

## 解决方案

### 🚀 自动修复（推荐）

项目已配置自动修复，直接运行构建即可：

```bash
cd mozai-interview-front
npm run build
```

构建脚本会自动：
1. 运行 `fix-element-plus.js` 修复模块引用
2. 执行 Vite 构建

### 🔧 手动修复步骤

如果自动修复失败，可以手动执行：

1. **清理环境**
```bash
rm -rf node_modules package-lock.json dist-interview-ai .vite
npm cache clean --force
```

2. **重新安装依赖**
```bash
npm install element-plus@2.2.36 --save-exact
npm install
```

3. **运行修复脚本**
```bash
node fix-element-plus.js
```

4. **构建项目**
```bash
npm run build:original
```

## 关键修改

### 1. 降级 Element Plus 版本
- 从 `2.3.12` 降级到 `2.2.36`（更稳定的版本）
- 使用 `--save-exact` 锁定确切版本

### 2. 自动修复脚本 (fix-element-plus.js)
```javascript
// 修复可能的错误引用
const fixes = [
  {
    wrong: './components/config-provider/src/hooks/use-globalThis-config.mjs',
    correct: './components/config-provider/src/hooks/use-global-config.mjs'
  },
  {
    wrong: './hooks/use-prevent-globalThis/index.mjs',
    correct: './hooks/use-prevent-global/index.mjs'
  }
];
```

### 3. 简化的 Vite 配置
参考 mozai-resume-vue 项目的成功配置：
- 移除复杂的 rollup 配置
- 保持基本的路径别名和输出配置

### 4. 构建脚本优化
```json
{
  "scripts": {
    "build": "node fix-element-plus.js && vite build",
    "build:original": "vite build",
    "postinstall": "node fix-element-plus.js"
  }
}
```

## 验证构建成功

构建成功的标志：
- ✅ 无模块解析错误
- ✅ 生成 `dist-interview-ai` 目录
- ✅ 包含完整的静态资源文件
- ✅ 修复脚本输出：`✅ Element Plus references are already correct`

## 故障排除

### 如果仍然失败

1. **检查 Element Plus 版本**
```bash
npm list element-plus
# 应该显示 2.2.36
```

2. **手动检查文件**
```bash
ls node_modules/element-plus/es/components/config-provider/src/hooks/
# 应该包含 use-global-config.mjs
```

3. **强制重新安装**
```bash
npm uninstall element-plus
npm install element-plus@2.2.36 --save-exact
node fix-element-plus.js
npm run build:original
```

4. **使用 yarn 替代 npm**
```bash
yarn install
yarn build
```

## 环境兼容性

| 环境 | Node.js 版本 | Element Plus 版本 | 状态 |
|------|-------------|------------------|------|
| Windows | 16.x | 2.2.36 | ✅ 成功 |
| Linux CentOS 7.9 | 16.x | 2.2.36 | ✅ 修复后成功 |
| macOS | 16.x+ | 2.2.36 | ✅ 成功 |

## 与 mozai-resume-vue 的对比

| 项目 | Element Plus 版本 | 修复方案 | 构建状态 |
|------|------------------|----------|----------|
| mozai-resume-vue | 2.9.10 | 无需修复 | ✅ 成功 |
| mozai-interview-front | 2.2.36 | 自动修复 | ✅ 成功 |

## 相关文件

- `fix-element-plus.js` - 自动修复脚本
- `vite.config.ts` - 简化的 Vite 配置
- `package.json` - 更新的构建脚本
