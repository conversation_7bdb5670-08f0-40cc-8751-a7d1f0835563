import { createRouter, createWebHashHistory } from 'vue-router'
import { useUserStore } from '../stores/user'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue')
  },
  {
    path: '/templates',
    name: 'Templates',
    component: () => import('@/views/Templates.vue')
  },
  {
    path: '/editor/:id?',
    name: 'ResumeEditor',
    component: () => import('@/views/ResumeEditor.vue')
  },
  {
    path: '/user',
    name: 'UserCenter',
    component: () => import('@/views/UserCenter.vue')
  },
  {
    path: '/preview/:id',
    name: 'Preview',
    component: () => import('@/views/Preview.vue')
  },
  {
    path: '/airesume',
    name: 'AIResume',
    component: () => import('@/views/AIResume.vue')
  },
  {
    path: '/sso-callback',
    name: 'SSOCallback',
    component: () => import('@/views/SSOCallback.vue'),
    // 路由元信息，标记为SSO回调路由
    meta: { 
      isSSOCallback: true 
    }
  },
  {
    path: '/pdf-system-manager',
    name: 'PDFSystemManager',
    component: () => import('@/components/PDFSystemManager.vue'),
    meta: { requiresAdmin: true }
  },
  {
    path: '/pdf-test',
    name: 'PDFTestPage',
    component: () => import('@/views/PDFTestPage.vue'),
    meta: { requiresAdmin: true }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory('/resume-ai/'),
  routes
})

// 路由守卫 - 处理SSO登录回调和token，以及获取用户信息
router.beforeEach(async (to, from, next) => {
  // 获取用户状态管理
  const userStore = useUserStore()
  
  // 检查URL中是否包含token参数（SSO登录成功后的重定向）
  const token = to.query.token
  
  if (token) {
    console.log('检测到URL中的token:', token)
    // 将token保存到本地存储
    userStore.setToken(token)
    
    // 如果是专门的SSO回调路由，直接进入处理页面
    if (to.meta.isSSOCallback) {
      next()
      return
    }
    
    // 否则，移除URL中的token参数并重定向到同一页面
    // 这样可以防止token在浏览器历史记录中保留
    const query = { ...to.query }
    delete query.token
    next({
      path: to.path,
      query: query,
      replace: true // 替换当前历史记录，而不是添加新记录
    })
    return
  }
  
  // 对于每个页面跳转都调用后端获取用户信息，触发SSO状态校验
  if (to.name !== 'SSOCallback') {
    try {
      // 不管有没有token，都调用后端获取用户信息
      console.log('路由守卫: 调用后端获取用户信息，触发SSO校验')
      await userStore.fetchUserInfo()
    } catch (error) {
      console.error('获取用户信息失败', error)
      // 即使获取失败也继续导航，由后端决定是否需要重定向
    }
  }
  
  // 对于其他所有路由，直接放行
  next()
})

export default router 