<template>
  <div class="interview-list-container">
    <div class="header">
      <h1 class="title">我的面试</h1>
      <div class="tabs">
        <button 
          :class="['tab-btn', { active: activeTab === 'mock' }]"
          @click="() => { activeTab = 'mock'; loadInterviewsByType(InterviewType.MOCK); }"
        >
          模拟面试
        </button>
        <button 
          :class="['tab-btn', { active: activeTab === 'formal' }]"
          @click="() => { activeTab = 'formal'; loadInterviewsByType(InterviewType.FORMAL); }"
        >
          正式面试
        </button>
      </div>
    </div>

    <!-- 模拟面试列表 -->
    <div v-if="activeTab === 'mock'" class="interview-section">
      <div v-if="loading" class="loading-state">
        <el-skeleton :rows="3" animated />
        <el-skeleton :rows="3" animated />
      </div>
      <div v-else-if="mockInterviews.length === 0" class="empty-state">
        <div class="empty-icon">📝</div>
        <p>暂无模拟面试记录</p>
      </div>
      <div v-else class="interview-grid">
        <div 
          v-for="interview in mockInterviews" 
          :key="interview.id"
          class="interview-card mock-card"
          :class="{ 'non-clickable': true }"
        >
          <div class="card-header">
            <span class="interview-type">模拟面试</span>
            <span :class="['status', interview.status]">
              {{ interview.status === 'completed' ? '已完成' : 
                 interview.status === 'in_progress' ? '进行中' :
                 interview.status === 'waiting_result' ? '等待结果' : '待处理' }}
            </span>
          </div>
          
          <div class="card-content">
            <div class="info-item">
              <span class="label">面试环节：</span>
              <span class="value">{{ formatStage(interview.stage) }}</span>
            </div>
            
            <div class="info-item">
              <span class="label">面试岗位：</span>
              <span class="value">{{ formatPosition(interview.position) }}</span>
            </div>
            
            <div class="info-item">
              <span class="label">面试时间：</span>
              <span class="value">{{ formatDate(interview.interviewTime) }}</span>
            </div>
            
            <div v-if="interview.result !== undefined && interview.status !== 'waiting_result'" class="info-item">
              <span class="label">面试结果：</span>
              <span class="value">
                <el-tag :type="getResultType(interview.result)" size="small">
                  {{ getResultText(interview.result) }}
                </el-tag>
              </span>
            </div>

            <div v-if="interview.status !== 'waiting_result'" class="info-item">
              <span class="label">面试评价：</span>
              <span class="value">
                <span v-if="interview.evaluation" class="score">
                  {{ interview.evaluation.score }}分
                </span>
                <span v-else class="no-evaluation">暂无评价</span>
              </span>
            </div>
          </div>
          
          <div class="card-actions">
            <button
              v-if="interview.evaluation && interview.status !== 'waiting_result'"
              @click.stop="viewEvaluation(interview.evaluation, interview)"
              class="btn btn-primary"
            >
              查看评价
            </button>
            <button v-else class="btn btn-disabled" disabled>
              暂无评价
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 正式面试列表 -->
    <div v-if="activeTab === 'formal'" class="interview-section">
      <div class="action-bar">
        <el-button type="primary" @click="uploadFormalInterview">
          <el-icon><Plus /></el-icon>上传正式面试
        </el-button>
      </div>
      
      <div v-if="loading" class="loading-state">
        <el-skeleton :rows="3" animated />
        <el-skeleton :rows="3" animated />
      </div>
      <div v-else-if="formalInterviews.length === 0" class="empty-state">
        <div class="empty-icon">💼</div>
        <p>暂无正式面试记录</p>
        <el-button type="primary" @click="uploadFormalInterview" style="margin-top: 20px;">
          上传正式面试
        </el-button>
      </div>
      <div v-else class="interview-grid">
        <div 
          v-for="interview in formalInterviews" 
          :key="interview.id"
          class="interview-card formal-card"
          :class="{ 'non-clickable': true }"
        >
          <div class="card-header">
            <span class="interview-type">正式面试</span>
            <span :class="['status', interview.status]">
              {{ interview.status === 'completed' ? '已完成' : 
                 interview.status === 'in_progress' ? '进行中' :
                 interview.status === 'waiting_result' ? '等待结果' : '待处理' }}
            </span>
          </div>
          
          <div class="card-content">
            <div class="info-item">
              <span class="label">面试公司：</span>
              <span class="value">{{ interview.company }}</span>
            </div>
            
            <div class="info-item">
              <span class="label">面试岗位：</span>
              <span class="value">{{ formatPosition(interview.position) }}</span>
            </div>
            
            <div class="info-item">
              <span class="label">面试时间：</span>
              <span class="value">{{ formatDate(interview.interviewTime) }}</span>
            </div>
            
            <div v-if="interview.result !== undefined && interview.status !== 'waiting_result'" class="info-item">
              <span class="label">面试结果：</span>
              <span class="value">
                <el-tag :type="getResultType(interview.result)" size="small">
                  {{ getResultText(interview.result) }}
                </el-tag>
              </span>
            </div>
            
            <div class="info-item">
              <span class="label">面试评价：</span>
              <span class="value">
                <span v-if="interview.evaluation" class="score">
                  {{ interview.evaluation.score }}分
                </span>
                <span v-else class="no-evaluation">暂无评价</span>
              </span>
            </div>
            
            <div v-if="interview.videoUrl" class="info-item">
              <span class="label">面试音视频：</span>
              <span class="value">
                <el-tag size="small" type="success" @click="playMedia(interview)" class="media-tag">
                  <el-icon><VideoPlay /></el-icon>
                  已上传 (点击播放)
                </el-tag>
              </span>
            </div>
          </div>
          
          <div class="card-actions">
            <button 
              v-if="interview.evaluation"
              @click.stop="viewEvaluation(interview.evaluation, interview)"
              class="btn btn-primary"
            >
              查看评价
            </button>
            <button v-else class="btn btn-disabled" disabled>
              暂无评价
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 评价详情弹窗 -->
    <div v-if="showEvaluationModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>面试评价详情</h3>
          <button @click="closeModal" class="close-btn">&times;</button>
        </div>
        
        <div class="modal-body" v-if="selectedEvaluation">
          <div class="evaluation-score">
            <span class="score-label">总分：</span>
            <span class="score-value">{{ selectedEvaluation.score }}分</span>
          </div>
          
          <div class="evaluation-section">
            <h4>整体反馈</h4>
            <p class="feedback-text">{{ selectedEvaluation.feedback }}</p>
          </div>
          
          <div class="evaluation-section">
            <h4>优势表现</h4>
            <p class="feedback-text">{{ selectedEvaluation.strengths }}</p>

          </div>
          
          <div class="evaluation-section">
            <h4>改进建议</h4>
            <p class="feedback-text">{{ selectedEvaluation.improvements }}</p>
          </div>
          
          <div class="evaluation-time">
            评价时间：{{ formatDate(new Date().toISOString()) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 媒体播放弹窗 -->
    <div v-if="showMediaModal" class="modal-overlay" @click="closeMediaModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ currentMediaTitle }}</h3>
          <button @click="closeMediaModal" class="close-btn">&times;</button>
        </div>
        
        <div class="modal-body">
          <video controls class="media-video">
            <source :src="currentMediaFile" type="video/mp4">
            Your browser does not support the video tag.
          </video>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import type { MockInterview, FormalInterview, InterviewEvaluation } from '@/types/interview'
import { InterviewType } from '@/types/interview'
import { getInterviewList, getInterviewsByType, getInterviewDetail } from '@/api/interview'
import { ElMessage } from 'element-plus'
import { Plus, VideoPlay } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useInterviewStore } from '@/store'
import api from '@/api/index'
import { formatDate } from '@/utils/dateFormat'

const router = useRouter()
const activeTab = ref<'mock' | 'formal'>('mock')
const showEvaluationModal = ref(false)
const selectedEvaluation = ref<InterviewEvaluation | null>(null)
const loading = ref(false)

// 媒体播放相关
const showMediaModal = ref(false)
const currentMediaFile = ref('')
const currentMediaTitle = ref('')

// 面试数据
const interviews = ref<(MockInterview | FormalInterview)[]>([])

// 计算属性
const mockInterviews = computed(() => 
  interviews.value.filter(interview => interview.type === InterviewType.MOCK) as MockInterview[]
)

const formalInterviews = computed(() => 
  interviews.value.filter(interview => interview.type === InterviewType.FORMAL) as FormalInterview[]
)

// 方法
const viewEvaluation = (evaluation: InterviewEvaluation, interview?: MockInterview | FormalInterview) => {
  selectedEvaluation.value = evaluation
  showEvaluationModal.value = true
  // 移除跳转逻辑，仅显示评价弹窗
}

// 关闭评价详情弹窗
const closeModal = () => {
  showEvaluationModal.value = false
  selectedEvaluation.value = null
}

// 上传正式面试
const uploadFormalInterview = () => {
  router.push('/interviews/formal/upload')
}

// 播放媒体文件
const playMedia = (interview: FormalInterview) => {
  if (!interview.videoUrl) return
  
  currentMediaFile.value = interview.videoUrl
  currentMediaTitle.value = `${interview.company} - ${interview.position}`
  showMediaModal.value = true
}

// 关闭媒体播放器
const closeMediaModal = () => {
  showMediaModal.value = false
  currentMediaFile.value = ''
  currentMediaTitle.value = ''
}

// 加载面试数据
const loadInterviews = async () => {
  loading.value = true
  try {
    const result = await getInterviewList()
    if (result.length > 0) {
      interviews.value = result
    } else {
      ElMessage.error('获取面试列表失败')
    }
  } catch (error) {
    console.error('获取面试列表失败:', error)
    ElMessage.error('获取面试列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 根据类型加载面试数据
const loadInterviewsByType = async (type: InterviewType) => {
  loading.value = true
  try {
    const result = await getInterviewsByType(type)
    interviews.value = result
    console.log(interviews.value)
  } catch (error) {
    console.error(`获取${type}面试列表失败:`, error)
    ElMessage.error(`获取${type}面试列表失败，请稍后重试`)
  } finally {
    loading.value = false
  }
}

// 监听标签页切换
watch(activeTab, (newTab) => {
  if (newTab === 'mock') {
    loadInterviewsByType(InterviewType.MOCK)
  } else {
    loadInterviewsByType(InterviewType.FORMAL)
  }
})

onMounted(() => {
  // 检查路由查询参数，如果有tab参数则切换到对应的标签页
  const tab = router.currentRoute.value.query.tab
  if (tab === 'formal') {
    activeTab.value = 'formal'
  }
  
  // 初始加载当前标签页的面试数据
  loadInterviewsByType(activeTab.value === 'mock' ? InterviewType.MOCK : InterviewType.FORMAL)
})

// 添加格式化函数
const formatPosition = (position: string | number) => {
  // 处理数字值
  if (typeof position === 'number') {
    const positionNumberMap: Record<number, string> = {
      1: '开发',
      2: '技术支持',
      3: '测试'
    }
    return positionNumberMap[position] || `未知岗位(${position})`
  }
  
  // 处理字符串数字值，如 "1", "2", "3"
  if (position && !isNaN(Number(position))) {
    const numPosition = Number(position);
    const positionNumberMap: Record<number, string> = {
      1: '开发',
      2: '技术支持',
      3: '测试'
    }
    return positionNumberMap[numPosition] || `未知岗位(${position})`
  }
  
  // 处理字符串值（兼容旧数据）
  const positionMap: Record<string, string> = {
    'dev': '开发岗',
    'support': '技术支持岗',
    'test': '测试岗',
    '开发': '开发',
    '技术支持': '技术支持',
    '测试': '测试'
  }
  return positionMap[position] || position
}

const formatStage = (stage: string) => {
  const stageMap: Record<string, string> = {
    'hr': 'HR面试',
    'tech': '技术面试'
  }
  return stageMap[stage] || stage
}

// 获取面试结果文本
const getResultText = (result: number | undefined | null) => {
  if (result === undefined || result === null) return '未评定'
  return result === 1 ? '面试通过' : '面试未通过'
}

// 获取面试结果标签类型
const getResultType = (result: number | undefined | null) => {
  if (result === undefined || result === null) return 'info'
  return result === 1 ? 'success' : 'danger'
}
</script>

<style scoped>
.interview-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 20px 0;
}

.tabs {
  display: flex;
  gap: 12px;
}

.tab-btn {
  padding: 12px 24px;
  border: 2px solid #e1e5e9;
  background: white;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
}

.tab-btn:hover {
  border-color: #4f46e5;
  color: #4f46e5;
}

.tab-btn.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.interview-section {
  margin-top: 24px;
}

.action-bar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.loading-state {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.interview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.interview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.non-clickable {
  cursor: default;
}

.interview-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.mock-card {
  border-left: 4px solid #10b981;
}

.formal-card {
  border-left: 4px solid #3b82f6;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.interview-type {
  font-size: 14px;
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 20px;
  background: #f3f4f6;
  color: #374151;
}

.mock-card .interview-type {
  background: #d1fae5;
  color: #065f46;
}

.formal-card .interview-type {
  background: #dbeafe;
  color: #1e40af;
}

.status {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
}

.status.completed {
  color: #67C23A;
}

.status.in_progress {
  color: #409EFF;
}

.status.waiting_result {
  color: #E6A23C;
}

.status.pending {
  color: #909399;
}

.card-content {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #6b7280;
  min-width: 80px;
  flex-shrink: 0;
}

.value {
  color: #1f2937;
  flex: 1;
}

.score {
  font-weight: 600;
  color: #059669;
}

.no-evaluation {
  color: #9ca3af;
  font-style: italic;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background: #4338ca;
}

.btn-disabled {
  background: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 0 24px 24px 24px;
}

.evaluation-score {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.score-label {
  font-weight: 500;
  color: #374151;
}

.score-value {
  font-size: 24px;
  font-weight: 700;
  color: #059669;
}

.evaluation-section {
  margin-bottom: 24px;
}

.evaluation-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.feedback-text {
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
}

.strengths-list,
.improvements-list {
  margin: 0;
  padding-left: 20px;
}

.strengths-list li,
.improvements-list li {
  color: #4b5563;
  margin-bottom: 8px;
  line-height: 1.5;
}

.strengths-list li:last-child,
.improvements-list li:last-child {
  margin-bottom: 0;
}

.evaluation-time {
  font-size: 14px;
  color: #9ca3af;
  text-align: right;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.media-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.media-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.media-video {
  width: 100%;
  max-height: 60vh;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .interview-list-container {
    padding: 16px;
  }
  
  .interview-grid {
    grid-template-columns: 1fr;
  }
  
  .header {
    padding: 20px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .tabs {
    flex-direction: column;
  }
  
  .tab-btn {
    text-align: center;
  }
  
  .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .info-item {
    flex-direction: column;
    gap: 4px;
  }
  
  .label {
    min-width: auto;
  }
}

.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}
</style> 