package com.bimowu.resume.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import com.bimowu.resume.config.PDFConfig;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * PDF生成调试日志系统
 * 提供详细的PDF生成过程跟踪和调试信息
 */
@Component
@Slf4j
public class PDFGenerationLogger {
    
    @Autowired
    private PDFConfig pdfConfig;
    
    // 统计信息
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong successfulRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);
    private final Map<String, AtomicLong> templateStats = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> errorStats = new ConcurrentHashMap<>();
    
    /**
     * 记录数据映射日志
     */
    public void logDataMapping(String module, Object data) {
        if (data != null) {
            log.debug("映射模块 [{}] 数据: {}", module, data.toString().length() > 100 ? 
                data.toString().substring(0, 100) + "..." : data.toString());
        } else {
            log.debug("映射模块 [{}] 数据: null", module);
        }
    }
    
    /**
     * 记录模板渲染日志
     */
    public void logTemplateRendering(String templateId, int dataSize, int templateLength) {
        log.info("渲染模板 [{}] - 数据项数量: {}, 模板长度: {}", templateId, dataSize, templateLength);
    }
    
    /**
     * 记录PDF生成日志
     */
    public void logPDFGeneration(String resumeId, String templateId, String htmlContent) {
        log.info("PDF生成 - 简历ID: {}, 模板ID: {}, HTML内容长度: {}", 
            resumeId, templateId, htmlContent.length());
        
        if (log.isTraceEnabled()) {
            log.trace("HTML内容预览: {}", 
                htmlContent.length() > 500 ? htmlContent.substring(0, 500) + "..." : htmlContent);
        }
    }
    
    /**
     * 记录PDF生成成功日志
     */
    public void logPDFGenerationSuccess(String resumeId, String templateId, long duration, int pdfSize) {
        log.info("PDF生成成功 - 简历ID: {}, 模板ID: {}, 耗时: {}ms, PDF大小: {} bytes", 
            resumeId, templateId, duration, pdfSize);
    }
    
    /**
     * 记录PDF生成失败日志
     */
    public void logPDFGenerationFailure(String resumeId, String templateId, String errorCode, 
                                       String errorMessage, Throwable cause) {
        log.error("PDF生成失败 - 简历ID: {}, 模板ID: {}, 错误代码: {}, 错误信息: {}", 
            resumeId, templateId, errorCode, errorMessage, cause);
    }
    
    /**
     * 记录数据验证日志
     */
    public void logDataValidation(String resumeId, String validationResult) {
        log.info("数据验证 - 简历ID: {}, 验证结果: {}", resumeId, validationResult);
    }
    
    /**
     * 记录模板加载日志
     */
    public void logTemplateLoading(String templateId, boolean success, String message) {
        if (success) {
            log.debug("模板加载成功 - 模板ID: {}, 信息: {}", templateId, message);
        } else {
            log.error("模板加载失败 - 模板ID: {}, 错误: {}", templateId, message);
        }
    }
    
    /**
     * 记录占位符替换日志
     */
    public void logPlaceholderReplacement(int totalPlaceholders, int replacedCount, int unreplacedCount) {
        log.info("占位符替换完成 - 总数: {}, 已替换: {}, 未替换: {}", 
            totalPlaceholders, replacedCount, unreplacedCount);
        
        if (unreplacedCount > 0) {
            log.warn("发现 {} 个未替换的占位符，可能影响PDF显示效果", unreplacedCount);
        }
    }
    
    /**
     * 记录性能统计日志
     */
    public void logPerformanceStats(String operation, long duration, String details) {
        log.info("性能统计 - 操作: {}, 耗时: {}ms, 详情: {}", operation, duration, details);
    }
    
    /**
     * 开始PDF生成流程跟踪
     */
    public void startPDFGenerationTrace(String resumeId, String templateId) {
        totalRequests.incrementAndGet();
        templateStats.computeIfAbsent(templateId, k -> new AtomicLong(0)).incrementAndGet();
        
        if (isDebugEnabled()) {
            log.debug("=== PDF生成流程开始 ===");
            log.debug("简历ID: {}, 模板ID: {}", resumeId, templateId);
            log.debug("当前总请求数: {}", totalRequests.get());
        }
    }
    
    /**
     * 记录数据获取阶段
     */
    public void logDataRetrievalPhase(String resumeId, String phase, Object data) {
        if (isDebugEnabled()) {
            log.debug("数据获取阶段 [{}] - 简历ID: {}", phase, resumeId);
            if (isDetailedDebugEnabled() && data != null) {
                log.debug("获取到的数据: {}", data.toString().length() > 200 ? 
                    data.toString().substring(0, 200) + "..." : data.toString());
            }
        }
    }
    
    /**
     * 记录数据映射过程的详细跟踪
     */
    public void logDataMappingTrace(String resumeId, String module, String operation, Object input, Object output) {
        if (isDebugEnabled()) {
            log.debug("数据映射跟踪 - 简历ID: {}, 模块: {}, 操作: {}", resumeId, module, operation);
            
            if (isDetailedDebugEnabled()) {
                if (input != null) {
                    String inputStr = input.toString();
                    log.debug("输入数据: {}", inputStr.length() > 100 ? inputStr.substring(0, 100) + "..." : inputStr);
                }
                if (output != null) {
                    String outputStr = output.toString();
                    log.debug("输出数据: {}", outputStr.length() > 200 ? outputStr.substring(0, 200) + "..." : outputStr);
                }
            }
        }
    }
    
    /**
     * 记录HTML内容生成的调试信息
     */
    public void logHtmlContentGeneration(String resumeId, String module, String htmlContent) {
        if (isDebugEnabled()) {
            log.debug("HTML内容生成 - 简历ID: {}, 模块: {}, 内容长度: {}", 
                resumeId, module, htmlContent != null ? htmlContent.length() : 0);
            
            if (isDetailedDebugEnabled() && htmlContent != null && !htmlContent.isEmpty()) {
                log.debug("HTML内容预览 [{}]: {}", module, 
                    htmlContent.length() > 300 ? htmlContent.substring(0, 300) + "..." : htmlContent);
            }
        }
    }
    
    /**
     * 记录条件显示逻辑的判断过程
     */
    public void logConditionalDisplay(String resumeId, String module, boolean hasData, String reason) {
        if (isDebugEnabled()) {
            log.debug("条件显示判断 - 简历ID: {}, 模块: {}, 是否显示: {}, 原因: {}", 
                resumeId, module, hasData, reason);
        }
    }
    
    /**
     * 记录模板占位符处理过程
     */
    public void logPlaceholderProcessing(String resumeId, String placeholder, String value, boolean replaced) {
        if (isDebugEnabled() && isDetailedDebugEnabled()) {
            log.debug("占位符处理 - 简历ID: {}, 占位符: {}, 是否替换: {}, 值长度: {}", 
                resumeId, placeholder, replaced, value != null ? value.length() : 0);
        }
    }
    
    /**
     * 记录内容格式化过程
     */
    public void logContentFormatting(String resumeId, String module, String originalContent, String formattedContent) {
        if (isDebugEnabled()) {
            log.debug("内容格式化 - 简历ID: {}, 模块: {}, 原始长度: {}, 格式化后长度: {}", 
                resumeId, module, 
                originalContent != null ? originalContent.length() : 0,
                formattedContent != null ? formattedContent.length() : 0);
            
            if (isDetailedDebugEnabled()) {
                if (originalContent != null && formattedContent != null && !originalContent.equals(formattedContent)) {
                    log.debug("格式化前: {}", originalContent.length() > 100 ? originalContent.substring(0, 100) + "..." : originalContent);
                    log.debug("格式化后: {}", formattedContent.length() > 100 ? formattedContent.substring(0, 100) + "..." : formattedContent);
                }
            }
        }
    }
    
    /**
     * 记录错误处理过程
     */
    public void logErrorHandling(String resumeId, String module, String errorType, String errorMessage, Throwable cause) {
        failedRequests.incrementAndGet();
        errorStats.computeIfAbsent(errorType, k -> new AtomicLong(0)).incrementAndGet();
        
        log.error("错误处理 - 简历ID: {}, 模块: {}, 错误类型: {}, 错误信息: {}", 
            resumeId, module, errorType, errorMessage, cause);
    }
    
    /**
     * 完成PDF生成流程跟踪
     */
    public void completePDFGenerationTrace(String resumeId, String templateId, boolean success, long totalDuration) {
        if (success) {
            successfulRequests.incrementAndGet();
        }
        
        if (isDebugEnabled()) {
            log.debug("=== PDF生成流程完成 ===");
            log.debug("简历ID: {}, 模板ID: {}, 成功: {}, 总耗时: {}ms", resumeId, templateId, success, totalDuration);
            log.debug("当前成功率: {:.2f}%", getSuccessRate());
        }
    }
    
    /**
     * 输出调试统计报告
     */
    public void logDebugStatistics() {
        if (isDebugEnabled()) {
            log.info("=== PDF生成调试统计报告 ===");
            log.info("总请求数: {}", totalRequests.get());
            log.info("成功请求数: {}", successfulRequests.get());
            log.info("失败请求数: {}", failedRequests.get());
            log.info("成功率: {:.2f}%", getSuccessRate());
            
            log.info("按模板统计:");
            templateStats.forEach((templateId, count) -> 
                log.info("  模板 {}: {} 次请求", templateId, count.get()));
            
            log.info("按错误类型统计:");
            errorStats.forEach((errorType, count) -> 
                log.info("  错误类型 {}: {} 次", errorType, count.get()));
        }
    }
    
    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        totalRequests.set(0);
        successfulRequests.set(0);
        failedRequests.set(0);
        templateStats.clear();
        errorStats.clear();
        
        if (isDebugEnabled()) {
            log.info("调试统计信息已重置");
        }
    }
    
    /**
     * 获取成功率
     */
    private double getSuccessRate() {
        long total = totalRequests.get();
        if (total == 0) return 0.0;
        return (successfulRequests.get() * 100.0) / total;
    }
    
    /**
     * 检查是否启用调试模式
     */
    public boolean isDebugEnabled() {
        return pdfConfig != null && pdfConfig.getDebug().isEnabled();
    }
    
    /**
     * 检查是否启用详细调试模式
     */
    public boolean isDetailedDebugEnabled() {
        return pdfConfig != null && pdfConfig.getDebug().isDetailed();
    }
    
    /**
     * 动态设置调试模式
     */
    public void setDebugEnabled(boolean enabled) {
        if (pdfConfig != null) {
            pdfConfig.getDebug().setEnabled(enabled);
            log.info("PDF调试模式已{}启用", enabled ? "" : "禁用");
        }
    }
    
    /**
     * 动态设置详细调试模式
     */
    public void setDetailedDebugEnabled(boolean enabled) {
        if (pdfConfig != null) {
            pdfConfig.getDebug().setDetailed(enabled);
            log.info("PDF详细调试模式已{}启用", enabled ? "" : "禁用");
        }
    }
}