package com.bimowu.resume.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * PDF生成性能配置
 */
@Configuration
@ConfigurationProperties(prefix = "pdf.performance")
@Data
public class PDFPerformanceConfig {
    
    /**
     * 是否启用模板缓存
     */
    private boolean enableTemplateCache = true;
    
    /**
     * 模板缓存过期时间（分钟）
     */
    private int templateCacheExpireMinutes = 30;
    
    /**
     * 是否启用HTML内容缓存
     */
    private boolean enableHtmlCache = false;
    
    /**
     * HTML缓存过期时间（分钟）
     */
    private int htmlCacheExpireMinutes = 5;
    
    /**
     * PDF生成超时时间（秒）
     */
    private int pdfGenerationTimeoutSeconds = 30;
    
    /**
     * 最大并发PDF生成数量
     */
    private int maxConcurrentGenerations = 10;
    
    /**
     * 是否启用性能监控
     */
    private boolean enablePerformanceMonitoring = true;
    
    /**
     * 慢查询阈值（毫秒）
     */
    private long slowQueryThresholdMs = 5000;
    
    /**
     * 是否启用内存优化
     */
    private boolean enableMemoryOptimization = true;
    
    /**
     * 大文件处理阈值（字节）
     */
    private long largeFileThresholdBytes = 1024 * 1024; // 1MB
}