<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.interview.dao.ResumeProjectQuestionMapper">

    <resultMap id="BaseResultMap" type="com.bimowu.interview.model.ResumeProjectQuestion">
            <id property="queId" column="que_id" jdbcType="BIGINT"/>
            <result property="projectId" column="project_id" jdbcType="BIGINT"/>
            <result property="conId" column="con_id" jdbcType="BIGINT"/>
            <result property="question" column="question" jdbcType="VARCHAR"/>
            <result property="answer" column="answer" jdbcType="VARCHAR"/>
            <result property="questionOrder" column="question_order" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        que_id,project_id,con_id,
        question,answer,question_order,
        create_time,update_time
    </sql>
</mapper>
