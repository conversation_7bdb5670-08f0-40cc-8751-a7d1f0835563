import { defineStore } from 'pinia'
import { getUserInfo, logout as apiLogout } from '../api/user'

// 定义API响应接口
interface ApiResponse {
  code: number;
  message: string;
  data: any;
}

export interface InterviewType {
  stage: string;
  position: string;
  experience: string;
}

export interface UserInfo {
  username?: string;
  role?: string;
  avatar?: string;
  userId?: number;
  [key: string]: any;
}

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: JSON.parse(localStorage.getItem('userInfo') || '{}') as UserInfo,
    isLoading: false
  }),
  
  actions: {
    setToken(token: string) {
      this.token = token
      localStorage.setItem('token', token)
    },
    
    setUserInfo(userInfo: UserInfo) {
      console.log('设置用户信息:', userInfo)
      this.userInfo = userInfo
      localStorage.setItem('userInfo', JSON.stringify(userInfo))
    },
    
    async fetchUserInfo() {
      // 即使没有token也调用后端API，让后端处理登录状态和SSO验证
      // 如果已登录，后端会返回用户信息
      // 如果未登录，后端filter会处理重定向到SSO登录
      
      try {
        this.isLoading = true
        console.log('开始获取用户信息')
        const res = await getUserInfo() as ApiResponse
        console.log('获取用户信息响应:', res)
        
        if (res.code === 0 && res.data) {
          console.log('获取用户信息成功:', res.data)
          this.setUserInfo(res.data)
          return res.data
        } else {
          console.warn('获取用户信息失败:', res.message)
          return null
        }
      } catch (error) {
        console.error('获取用户信息出错:', error)
        return null
      } finally {
        this.isLoading = false
      }
    },
    
    async logout() {
      console.log('开始执行退出登录')
      try {
        // 无论是否有token，都调用退出登录接口
        console.log('调用后端退出登录接口')
        const res = await apiLogout() as ApiResponse
        console.log('退出登录接口响应:', res)
        
        // 清除本地存储的token和用户信息
        console.log('清除本地token和用户信息')
        this.token = ''
        this.userInfo = {}
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        
        // 检查后端返回的登录地址
        if (res && res.code === 0 && res.data && res.data.redirectUrl) {
          console.log('后端返回重定向地址，准备跳转:', res.data.redirectUrl)
          // 使用后端返回的登录地址进行跳转
          window.location.href = res.data.redirectUrl
          return
        } else if (res && res.code === 0 && res.data && res.data.loginUrl) {
          console.log('后端返回登录地址，准备跳转:', res.data.loginUrl)
          // 使用后端返回的登录地址进行跳转
          window.location.href = res.data.loginUrl
          return
        } else if (res && res.code === 0) {
          console.log('后端退出登录成功，但未返回登录地址')
          // 如果没有返回登录地址，让后端处理重定向
          window.location.reload()
        } else {
          console.warn('后端退出登录返回非成功状态:', res)
          // 如果退出失败，也刷新页面，让后端处理重定向
          window.location.reload()
        }
      } catch (error) {
        console.error('调用退出登录接口出错:', error)
        // 如果出错，刷新页面，让后端处理重定向
        window.location.reload()
      }
    }
  },
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    nickname: (state) => {
      console.log('获取昵称，当前userInfo:', state.userInfo)
      const nick = state.userInfo?.nickname || state.userInfo?.username || '用户'
      console.log('计算得到的昵称:', nick)
      return nick
    }
  }
})

export const useInterviewStore = defineStore('interview', {
  state: () => ({
    resume: null as File | null,
    resumeText: '' as string,
    questions: [] as string[],
    videoRecording: null as Blob | null,
    audioRecording: null as Blob | null,
    transcriptions: {} as Record<number, string>,
    interviewId: '' as string,
    interviewType: {
      stage: '',
      position: '',
      experience: ''
    } as InterviewType,
    interviewResults: {
      answers: {} as Record<string, string>,
      questions: {} as Record<string, string>,
      transcriptions: {} as Record<string, string>,
      interview: {
        id: '',
        userId: 0,
        type: '',
        candidateName: '',
        company: null,
        position: '',
        stage: '',
        experience: '',
        status: 0,
        overallScore: 0,
        feedback: '',
        strengths: null,
        improvements: null,
        videoUrl: '',
        interviewTime: '',
        createTime: '',
        updateTime: ''
      }
    },
    digitalHuman: {
      model: 'default',
      voice: 'female'
    },
    currentQuestionIndex: 0
  }),
  
  actions: {
    setResume(file: File) {
      this.resume = file
    },
    
    setResumeText(text: string) {
      this.resumeText = text
    },
    
    setQuestions(questions: string[]) {
      this.questions = questions
    },
    
    setInterviewType(interviewType: InterviewType) {
      this.interviewType = interviewType
    },
    
    setVideoRecording(blob: Blob) {
      this.videoRecording = blob
    },
    
    setAudioRecording(blob: Blob) {
      this.audioRecording = blob
    },
    
    setInterviewId(id: string) {
      this.interviewId = id
    },
    
    setTranscription(questionIndex: number, text: string) {
      this.transcriptions[questionIndex] = text
    },
    
    setAllTranscriptions(transcriptions: Record<number, string>) {
      this.transcriptions = transcriptions
    },
    
    setInterviewResults(results: any) {
      this.interviewResults = results
    },
    
    setCurrentQuestionIndex(index: number) {
      this.currentQuestionIndex = index
    },
    
    nextQuestion() {
      if (this.currentQuestionIndex < this.questions.length - 1) {
        this.currentQuestionIndex++
      }
    },
    
    previousQuestion() {
      if (this.currentQuestionIndex > 0) {
        this.currentQuestionIndex--
      }
    },
    
    resetInterview() {
      this.questions = []
      this.videoRecording = null
      this.audioRecording = null
      this.transcriptions = {}
      this.interviewId = ''
      this.interviewResults = {
        answers: {} as Record<string, string>,
        questions: {} as Record<string, string>,
        transcriptions: {} as Record<string, string>,
        interview: {
          id: '',
          userId: 0,
          type: '',
          candidateName: '',
          company: null,
          position: '',
          stage: '',
          experience: '',
          status: 0,
          overallScore: 0,
          feedback: '',
          strengths: null,
          improvements: null,
          videoUrl: '',
          interviewTime: '',
          createTime: '',
          updateTime: ''
        }
      }
      this.currentQuestionIndex = 0
    }
  },
  
  getters: {
    currentQuestion: (state) => {
      return state.questions[state.currentQuestionIndex] || ''
    },
    
    isLastQuestion: (state) => {
      return state.currentQuestionIndex === state.questions.length - 1
    },
    
    progress: (state) => {
      return state.questions.length > 0 
        ? Math.round(((state.currentQuestionIndex + 1) / state.questions.length) * 100) 
        : 0
    }
  }
}) 