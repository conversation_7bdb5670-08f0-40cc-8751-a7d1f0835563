<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 14px;
            font-weight: normal;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 0;
            color: #333;
            line-height: 1.7;
        }

        .resume-template {
            width: 100%;
            font-family: inherit !important;
            color: #333;
            background-color: #fff;
            line-height: 1.7;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
            }
        }

        .resume-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px 30px;
        }

        /* Header Styles */
        .header {
            position: relative;
            min-height: 120px;
            padding-bottom: 15px;
            border-bottom: 1px solid #000;
            margin-bottom: 20px;
        }

        .header-info {
            margin-right: 105px;
        }

        .name {
            font-size: 32px;
            font-weight: bold;
            color: #000;
            margin-bottom: 12px;
            font-family: 'SimHei', '黑体', 'Microsoft YaHei', '微软雅黑', sans-serif;
        }

        .contact-info {
            font-size: 14px;
            color: #333;
            line-height: 1.8;
        }

        .contact-item {
            margin-bottom: 6px;
            display: inline-block;
            margin-right: 25px;
        }

        .contact-icon {
            font-size: 14px;
            margin-right: 6px;
            display: inline-block;
            width: 16px;
            text-align: center;
        }

        .contact-icon.phone {
            color: #e74c3c;
        }

        .contact-icon.email {
            color: #3498db;
        }

        .contact-icon.address {
            color: #f39c12;
        }

        .contact-icon.age {
            color: #9b59b6;
        }

        .avatar-container {
            position: absolute;
            top: 0;
            right: 0;
            width: 85px;
            height: 120px;
            overflow: hidden;
            border: 1px solid #ddd;
        }

        .avatar {
            width: 100%;
            height: 100%;
            object-fit: contain;
            object-position: center;
            display: block;
            background-color: #f9f9f9;
        }

        /* Section Title Styles - 模板5简洁风格 */
        .section-title {
            margin-top: 25px;
            margin-bottom: 15px;
            text-align: center;
        }

        .section-title h2 {
            font-size: 16px;
            color: #000;
            margin: 0;
            font-weight: bold;
            display: inline-block;
            border-bottom: 2px solid #8B0000;
            padding-bottom: 2px;
            text-transform: uppercase;
        }

        /* Section Content Styles */
        .section-content {
            margin-top: 8px;
            font-size: 14px;
            color: #333;
            line-height: 1.7;
        }

        /* Styles for individual items within sections */
        .education-item,
        .work-item,
        .project-item,
        .skill-item {
            margin-bottom: 15px;
        }

        .education-item:last-child,
        .work-item:last-child,
        .project-item:last-child,
        .skill-item:last-child {
            margin-bottom: 0;
        }

        .item-header {
            margin-bottom: 8px;
            font-size: 14px;
        }

        .item-header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .item-time, .work-time, .project-time {
            font-weight: bold;
            font-size: 14px;
            color: #000;
        }

        /* Education Specific Styles */
        .education-item {
            margin-bottom: 15px;
        }

        .edu-header {
            display: table;
            width: 100%;
            margin-bottom: 5px;
            table-layout: fixed;
        }

        .edu-school {
            display: table-cell;
            color: #000;
            font-weight: normal;
            font-size: 14px;
            width: 33.33%;
            text-align: left;
            vertical-align: middle;
        }

        .edu-major {
            display: table-cell;
            color: #000;
            font-weight: normal;
            font-size: 14px;
            width: 33.33%;
            text-align: center;
            vertical-align: middle;
        }

        .edu-time {
            display: table-cell;
            color: #000;
            font-weight: normal;
            font-size: 14px;
            width: 33.33%;
            text-align: right;
            vertical-align: middle;
        }

        .edu-courses {
            margin-top: 5px;
            font-size: 14px;
            line-height: 1.6;
            color: #777777; /* 参考模板3：非加粗文本使用浅灰色 */
        }

        .courses-label {
            font-weight: normal;
            margin-right: 0;
        }

        .courses-content {
            display: inline;
        }

        /* Work Specific Styles */
        .work-header {
            display: table;
            width: 100%;
            table-layout: fixed;
            margin-bottom: 5px;
        }

        .work-time {
            display: table-cell;
            color: #333;
            font-size: 14px;
            font-weight: normal;
            width: 25%;
            vertical-align: middle;
        }

        .work-company {
            display: table-cell;
            color: #000;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            width: 50%;
            vertical-align: middle;
        }

        .work-position {
            display: table-cell;
            color: #333;
            font-size: 14px;
            font-weight: normal;
            text-align: right;
            width: 25%;
            vertical-align: middle;
        }

        .work-description {
            margin-top: 5px;
            font-size: 14px;
            line-height: 1.6;
            color: #777777; /* 参考模板3：非加粗文本使用浅灰色 */
        }

        .work-label {
            font-weight: normal;
            margin-right: 0;
        }

        .work-content {
            margin-top: 3px;
        }

        .work-description {
            margin-top: 8px;
            font-size: 14px;
            line-height: 1.6;
            color: #777777; /* 参考模板3：非加粗文本使用浅灰色 */
        }

        /* Project Specific Styles */
        .project-title {
            color: #000;
            font-size: 14px;
            font-weight: normal;
        }

        .project-role {
            color: #000;
            font-size: 14px;
            font-weight: normal;
        }

        .project-tech {
            color: #000;
            font-size: 14px;
            margin-bottom: 5px;
            font-weight: normal;
        }

        .project-description {
            margin-top: 8px;
            font-size: 14px;
            line-height: 1.6;
            color: #777777; /* 参考模板3：非加粗文本使用浅灰色 */
        }

        /* Common styles for lists */
        ul {
            list-style-type: disc;
            padding-left: 20px;
            margin: 0;
        }

        li {
            margin-bottom: 5px;
            line-height: 1.7;
        }

        /* Common styles for paragraphs */
        p {
            margin: 0 0 5px 0;
            line-height: 1.7;
        }

        /* 通用加粗样式 - 参考模板3的样式：使用加粗字体，纯黑色突出显示 */
        strong, b,
        .work-description strong,
        .project-description strong,
        .edu-courses strong,
        .work-content strong,
        .section-content strong,
        .skills-description strong,
        .certificate-content strong,
        .interests-content strong,
        .evaluation-content strong {
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #000000 !important;
            font-weight: bold !important;
        }

        em {
            font-style: italic;
        }

        /* Styles for headers in markdown */
        h1, h2, h3 {
            margin-top: 10px;
            margin-bottom: 5px;
            font-weight: bold;
        }

        a {
            color: #4472c4;
            text-decoration: underline;
        }

        /* Skills section styles */
        .skill-description-item {
            margin-bottom: 10px;
        }

        .skill-description-body {
            line-height: 1.7;
            text-align: justify;
        }

        .skills-description {
            line-height: 1.6;
            text-align: justify;
            margin-left: 0;
            font-size: 14px;
            color: #777777; /* 参考模板3：非加粗文本使用浅灰色 */
        }

        .certificates-section,
        .interests-section {
            margin-top: 20px;
        }

        /* 专业技能、证书奖项、兴趣爱好、个人总结模块的非加粗文本颜色 */
        .skills-section .section-content,
        .certificates-section .section-content,
        .interests-section .section-content,
        .evaluation-section .section-content {
            color: #777777 !important; /* 使用浅灰色，参考模板3 */
        }

        /* Certificate, Interest, and Evaluation content styles */
        .certificate-content,
        .interests-content,
        .evaluation-content {
            color: #777777; /* 参考模板3：非加粗文本使用浅灰色 */
        }

        .certificate-content ul,
        .interests-content ul,
        .evaluation-content ul {
            margin: 3px 0;
            padding-left: 15px;
        }

        .certificate-content li,
        .interests-content li,
        .evaluation-content li {
            margin-bottom: 2px;
            font-size: 14px;
            color: #777777; /* 参考模板3：非加粗文本使用浅灰色 */
        }

        .certificate-content p,
        .interests-content p,
        .evaluation-content p {
            margin: 0 0 3px 0;
            font-size: 14px;
            line-height: 1.6;
            color: #777777; /* 参考模板3：非加粗文本使用浅灰色 */
        }

        .section-content ul {
            padding-left: 20px;
            margin: 0;
        }

        .section-content li {
            margin-bottom: 5px;
            line-height: 1.7;
            color: #777777; /* 参考模板3：非加粗文本使用浅灰色 */
        }

        .section-content p {
            margin: 0 0 5px 0;
            line-height: 1.7;
            color: #777777; /* 参考模板3：非加粗文本使用浅灰色 */
        }

        /* Skill description styles */
        .skill-item-description {
            margin-bottom: 10px;
            line-height: 1.7;
            color: #777777; /* 参考模板3：非加粗文本使用浅灰色 */
        }

        /* Evaluation content styles */
        .evaluation-content {
            line-height: 1.7;
            text-align: justify;
            color: #777777; /* 参考模板3：非加粗文本使用浅灰色 */
        }
    </style>
</head>
<body>
<div class="resume-template template-5">
    <div class="resume-container">
        <!-- 头部信息区域 -->
        <div class="header">
            <div class="header-info">
                <div class="name">${name}</div>
                <div class="contact-info">
                    <span class="contact-item"><span class="contact-icon phone">☎</span>${phone}</span>
                    <span class="contact-item"><span class="contact-icon email">@</span>${email}</span>
                    <span class="contact-item"><span class="contact-icon age">●</span>年龄：${age}岁</span>
                </div>
            </div>
            <div class="avatar-container">
                ${avatar}
            </div>
        </div>

        <!-- 教育背景 -->
        ${education}

        <!-- 工作经验 -->
        ${work}

        <!-- 项目经验 -->
        ${projects}

        <!-- 练手项目 -->
        ${practices}

        <!-- 技能特长 -->
        ${skills}

        <!-- 证书奖项 -->
        ${certificates}

        <!-- 兴趣爱好 -->
        ${interests}

        <!-- 个人总结/自我评价 -->
        ${selfEvaluation}
    </div>
</div>
</body>
</html> 