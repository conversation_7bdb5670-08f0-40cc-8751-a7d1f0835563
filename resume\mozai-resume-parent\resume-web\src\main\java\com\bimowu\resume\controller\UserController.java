package com.bimowu.resume.controller;

import com.bimowu.resume.base.BaseResponse;
import com.bimowu.resume.base.Constant;
import com.bimowu.resume.common.service.ResumeProgressService;
import com.bimowu.resume.common.service.SysTodoService;
import com.bimowu.resume.entity.KsUser;
import com.bimowu.resume.entity.ResumeProgress;
import com.bimowu.resume.entity.SysTodo;
import com.bimowu.resume.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private RedisUtil redisUtil;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private ResumeProgressService resumeProgressService;
    
    @Autowired
    private SysTodoService sysTodoService;
    
    @Value("${sso.logout.url}")
    private String ssoLogoutUrl;
    @Value("${sso.clientId}")
    private String ssoClientId;
    @Value("${resume.url}")
    private String resumeUrl;
    /**
     * 获取当前用户信息
     * 从请求头中获取userId，然后从Redis中获取用户信息
     *
     * @param request HTTP请求
     * @return 用户信息
     */
    @GetMapping("/info")
    public BaseResponse<Map<String, Object>> getUserInfo(HttpServletRequest request) {
        // 从请求头中获取userId
        String userIdStr = request.getHeader("userId");
        log.info("获取用户信息，userId: {}", userIdStr);
        
        if (userIdStr == null || userIdStr.isEmpty()) {
            log.warn("请求头中没有userId");
            return new BaseResponse<>(401, "未登录或登录已过期", null);
        }
        
        try {
            // 将userId转换为Long类型
            Long userId = Long.parseLong(userIdStr);
            
            // 构造Redis中用户信息的key
            String userKey = String.format(Constant.REDIS_KEY, userId);
            
            // 从Redis中获取用户信息
            Object userObj = redisUtil.get(userKey);
            log.info("从Redis获取到的用户信息: {}", userObj);
            
            if (userObj == null) {
                log.warn("Redis中没有找到用户信息，userId: {}", userId);
                return new BaseResponse<>(404, "用户信息不存在", null);
            }
            
            // 构建返回的用户信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", userId);
            
            // 如果userObj是Map类型，则直接使用
            if (userObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> userMap = (Map<String, Object>) userObj;
                
                // 从userMap中获取需要的字段
                if (userMap.containsKey("nickname")) {
                    userInfo.put("nickname", userMap.get("nickname"));
                }
                
                if (userMap.containsKey("username")) {
                    userInfo.put("username", userMap.get("username"));
                }
                
                if (userMap.containsKey("avatar")) {
                    userInfo.put("avatar", userMap.get("avatar"));
                }
            }
            
            log.info("成功获取用户信息: {}", userInfo);
            // 查询用户的待办信息
            List<SysTodo> list = sysTodoService.getTodosByUserId(userId);
            //首次使用系统，初始化用户待办
            // 如果不存在任何待办，则初始化
            if (CollectionUtils.isEmpty(list)) {
                log.info("用户进度信息不存在，创建新的进度信息，userId: {}", userId);

                //创建待办sys_todo,内容为：您有一份简历待创建，请及时处理！，标题为：简历创建；地址为：resumeUrl
                SysTodo todo = new SysTodo();
                todo.setUserId(userId);
                todo.setResumeId(null); // 初始情况下没有简历，设置为null
                todo.setTitle("简历创建");
                todo.setContent("您有一份简历待创建，请及时处理！");
                todo.setUrl(resumeUrl);
                todo.setTodoType(0); // 设置待办类型为0：创建简历
                todo.setStatus(0); // 未完成
                sysTodoService.createTodo(todo);
            }


            return new BaseResponse<>(0, "获取用户信息成功", userInfo);
        } catch (NumberFormatException e) {
            log.error("userId格式错误: {}", userIdStr, e);
            return new BaseResponse<>(400, "用户ID格式错误", null);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return new BaseResponse<>(500, "获取用户信息失败: " + e.getMessage(), null);
        }
    }
    
    /**
     * 退出登录
     * 清除本地Redis缓存，并调用SSO系统的退出登录接口
     *
     * @param request HTTP请求
     * @return 退出登录结果
     */
    @PostMapping("/logout")
    public BaseResponse<String> logout(HttpServletRequest request) {
        log.info("用户请求退出登录");

        // 从请求中获取token
        String token = getToken(request);
        if (token == null || token.isEmpty()) {
            log.warn("退出登录时，请求中没有token");
            return new BaseResponse<>(0, "退出登录成功", null);
        }

        try {
            // 1. 获取用户ID
            Object userId = redisUtil.get(String.format(Constant.REDIS_KEY, token));

            // 2. 清除本地Redis缓存
            if (userId != null) {
                // 删除用户信息缓存
                String userKey = String.format(Constant.REDIS_KEY, userId);
                redisUtil.del(userKey);
                log.info("已删除用户信息缓存, key: {}", userKey);
            }

            // 删除token对应的缓存
            String tokenKey = String.format(Constant.REDIS_KEY, token);
            redisUtil.del(tokenKey);
            log.info("已删除token缓存, key: {}", tokenKey);

            // 3. 调用SSO系统的退出登录接口
            try {
                // 设置请求头，将token添加到请求头中
                org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
                headers.set(Constant.TOKEN_KEY, token);
                HttpEntity<String> entity = new HttpEntity<>(null, headers);

                // 调用SSO系统的退出登录接口
                ResponseEntity<Map> response = restTemplate.exchange(
                        ssoLogoutUrl,
                        HttpMethod.POST,
                        entity,
                        Map.class
                );

                log.info("SSO退出登录接口响应: {}", response.getBody());
            } catch (Exception e) {
                // 即使SSO退出登录接口调用失败，也不影响本地退出登录
                log.error("调用SSO退出登录接口失败", e);
            }

            return new BaseResponse<>(0, "退出登录成功", null);
        } catch (Exception e) {
            log.error("退出登录失败", e);
            return new BaseResponse<>(500, "退出登录失败: " + e.getMessage(), null);
        }
    }
    /**
     * sso 回调退出 - 接收表单参数
     * 清除本地Redis缓存
     *
     * @return 退出登录结果
     */
//    @PostMapping(value = "/callback/logout", consumes = "application/x-www-form-urlencoded")
//    public BaseResponse<String> callbackLogout(
//            @RequestParam(value = "userId", required = false) Long userId,
//            @RequestParam(value = "clientId", required = false) String clientId,
//            @RequestParam(value = "token", required = false) String token) {
//        log.info("callback(表单参数):用户请求退出登录,token:{},userId:{},clientId:{}", token, userId, clientId);
//        return processLogoutCallback(userId, clientId, token);
//    }
//
    /**
     * sso 回调退出 - 接收JSON请求体
     * 清除本地Redis缓存
     *
     * @return 退出登录结果
     */
    @PostMapping(value = "/callback/logout", consumes = "application/json")
    public BaseResponse<String> callbackLogoutJson(@RequestBody LoginOutRequest request) {
        log.info("callback(JSON):用户请求退出登录,token:{},userId:{},clientId:{}", 
                request.getToken(), request.getUserId(), request.getClientId());
        return processLogoutCallback(request.getUserId(), request.getClientId(), request.getToken());
    }
    
    /**
     * 处理退出登录回调的通用方法
     */
    private BaseResponse<String> processLogoutCallback(Long userId, String clientId, String token) {
        if(!StringUtils.equals(clientId, ssoClientId)){
            log.warn("退出登录时，客户端错误");
            return new BaseResponse<>(500, "退出登录失败，客户端错误", null);
        }
        if (token == null || token.isEmpty()) {
            log.warn("退出登录时，请求中没有token");
            return new BaseResponse<>(0, "退出登录成功", null);
        }
        try {
            // 1. 获取用户ID
            Object token_userId = redisUtil.get(String.format(Constant.REDIS_KEY, token));
            // 2. 清除本地Redis缓存
            if (token_userId != null) {
                if(!StringUtils.equals(token_userId.toString(),userId.toString())){
                    log.warn("退出登录时，用户不一致");
                    return new BaseResponse<>(500, "退出登录失败，用户不一致", null);
                }
                // 删除用户信息缓存
                String userKey = String.format(Constant.REDIS_KEY, token_userId);
                redisUtil.del(userKey);
                log.info("已删除用户信息缓存, key: {}", userKey);
            }
            // 删除token对应的缓存
            String tokenKey = String.format(Constant.REDIS_KEY, token);
            redisUtil.del(tokenKey);
            log.info("已删除token缓存, key: {}", tokenKey);
            return new BaseResponse<>(0, "退出登录成功", null);
        } catch (Exception e) {
            log.error("退出登录失败", e);
            return new BaseResponse<>(500, "退出登录失败: " + e.getMessage(), null);
        }
    }
    
    /**
     * 从请求中获取token
     */
    private String getToken(HttpServletRequest request) {
        // 首先尝试从Authorization头中获取Bearer token
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7); // 去掉"Bearer "前缀
        }
        
        // 然后尝试从token头中获取
        String tokenHeader = request.getHeader(Constant.TOKEN_KEY);
        if (tokenHeader != null && !tokenHeader.isEmpty()) {
            return tokenHeader;
        }
        
        // 然后尝试从请求参数中获取
        String tokenParam = request.getParameter(Constant.TOKEN_KEY);
        if (tokenParam != null && !tokenParam.isEmpty()) {
            return tokenParam;
        }
        
        // 最后尝试从Cookie中获取
        return com.bimowu.resume.utils.CookieUtils.getValue(request, Constant.TOKEN_KEY);
    }
    public static class LoginOutRequest {
        public String token;
        public Long userId;
        public String clientId;
        // set  get 方法
        public String getToken() {
            return token;
        }
        public void setToken(String token) {
            this.token = token;
        }
        public Long getUserId() {
            return userId;
        }
        public void setUserId(Long userId) {
            this.userId = userId;
        }
        public String getClientId() {
            return clientId;
        }
        public void setClientId(String clientId) {
            this.clientId = clientId;
        }
    }
} 
 