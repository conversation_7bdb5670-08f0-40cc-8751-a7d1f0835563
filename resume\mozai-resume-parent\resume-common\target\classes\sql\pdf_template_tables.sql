-- PDF模板生成系统相关表结构

-- 1. 模板样式配置表
CREATE TABLE `resume_template_style_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_id` int(11) NOT NULL COMMENT '模板ID',
  `css_content` longtext COMMENT 'CSS样式内容',
  `font_config` text COMMENT '字体配置JSON',
  `color_palette` text COMMENT '颜色调色板JSON',
  `layout_config` text COMMENT '布局配置JSON',
  `responsive_rules` text COMMENT '响应式规则JSON',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `version` varchar(50) DEFAULT NULL COMMENT '样式版本',
  `enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_id` (`template_id`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_last_sync_time` (`last_sync_time`),
  KEY `idx_version` (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板样式配置表';

-- 2. 样式同步记录表
CREATE TABLE `resume_style_sync_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_id` int(11) NOT NULL COMMENT '模板ID',
  `sync_type` varchar(20) NOT NULL DEFAULT 'INCREMENTAL' COMMENT '同步类型（FULL-全量同步, INCREMENTAL-增量同步）',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '同步状态（SUCCESS-成功, FAILED-失败, PENDING-进行中）',
  `error_message` text COMMENT '错误消息',
  `sync_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '同步时间',
  `change_log` text COMMENT '变更日志',
  `before_version` varchar(50) DEFAULT NULL COMMENT '同步前版本',
  `after_version` varchar(50) DEFAULT NULL COMMENT '同步后版本',
  `duration` bigint(20) DEFAULT NULL COMMENT '同步耗时（毫秒）',
  `changed_files` int(11) DEFAULT '0' COMMENT '变更文件数量',
  `trigger_type` varchar(20) DEFAULT 'MANUAL' COMMENT '同步触发方式（AUTO-自动, MANUAL-手动）',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sync_time` (`sync_time`),
  KEY `idx_trigger_type` (`trigger_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='样式同步记录表';

-- 创建索引以提高查询性能
CREATE INDEX `idx_template_status` ON `resume_style_sync_record` (`template_id`, `status`);
CREATE INDEX `idx_sync_time_status` ON `resume_style_sync_record` (`sync_time`, `status`);

-- 插入一些初始数据（可选）
INSERT INTO `resume_template_style_config` (`template_id`, `css_content`, `version`, `enabled`, `create_by`, `remark`) VALUES
(1, '/* 默认模板1样式 */', '1.0.0', 1, 'system', '默认模板1样式配置'),
(2, '/* 默认模板2样式 */', '1.0.0', 1, 'system', '默认模板2样式配置'),
(3, '/* 默认模板3样式 */', '1.0.0', 1, 'system', '默认模板3样式配置');