import type { ExtractPropTypes } from 'vue';
import type Progress from './progress.vue';
export declare type ProgressColor = {
    color: string;
    percentage: number;
};
export declare type ProgressFn = (percentage: number) => string;
export declare const progressProps: {
    readonly type: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "circle" | "line" | "dashboard", unknown, "line", boolean>;
    readonly percentage: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly status: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "" | "success" | "warning" | "exception", unknown, "", boolean>;
    readonly indeterminate: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly duration: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 3, boolean>;
    readonly strokeWidth: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 6, boolean>;
    readonly strokeLinecap: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square") | ((new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square"))[], unknown, unknown, "round", boolean>;
    readonly textInside: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly width: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 126, boolean>;
    readonly showText: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    readonly color: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (string | ProgressFn | ProgressColor[]) & {}) | (() => string | ProgressFn | ProgressColor[]) | ((new (...args: any[]) => (string | ProgressFn | ProgressColor[]) & {}) | (() => string | ProgressFn | ProgressColor[]))[], unknown, unknown, "", boolean>;
    readonly striped: BooleanConstructor;
    readonly stripedFlow: BooleanConstructor;
    readonly format: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ProgressFn) | (() => ProgressFn) | {
        (): ProgressFn;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => ProgressFn) | (() => ProgressFn) | {
        (): ProgressFn;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, (percentage: number) => string, boolean>;
};
export declare type ProgressProps = ExtractPropTypes<typeof progressProps>;
export declare type ProgressInstance = InstanceType<typeof Progress>;
