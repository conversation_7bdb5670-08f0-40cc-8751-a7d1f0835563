package com.bimowu.interview.service.impl;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.bimowu.interview.config.AliyunConfig;
import com.bimowu.interview.model.InterviewSpeechChapter;
import com.bimowu.interview.model.InterviewSpeechRecord;
import com.bimowu.interview.model.InterviewTranscription;
import com.bimowu.interview.service.InterviewSpeechChapterService;
import com.bimowu.interview.service.InterviewSpeechRecordService;
import com.bimowu.interview.service.TranscriptionService;
import com.bimowu.interview.utils.OssUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 面试转写服务实现类
 */
@Service
@Slf4j
public class TranscriptionServiceImpl implements TranscriptionService {

    @Autowired
    private IAcsClient acsClient;
    
    @Autowired
    private AliyunConfig aliyunConfig;
    
    @Autowired(required = false)
    private OssUtils ossUtils;



    @Value("${aliyun.oss.endpoint:}")
    private String ossEndpoint;
    
    @Value("${aliyun.oss.accessKeyId:}")
    private String ossAccessKeyId;
    
    @Value("${aliyun.oss.accessKeySecret:}")
    private String ossAccessKeySecret;
    
    @Value("${aliyun.oss.bucketName:}")
    private String ossBucketName;
    @Value("${aliyun.appKey}")
    private String appKey;

    @Value("${interview.retryTimes}")
    private Integer retryTimes;

    /**
     * 用于跟踪上次上传的音频文件URL，用于反馈给调用方
     */
    private String lastUploadedFileUrl;

    /**
     * 将音频文件转为文本
     * @return 转写后的文本内容
     */
    @Override
    public String speechToText(MultipartFile audioFile) {
        log.info("处理语音转文字请求，文件大小: {} 字节", audioFile.getSize());
        try {
            if (audioFile == null || audioFile.isEmpty()) {
                log.error("上传的音频文件为空");
                return "语音识别失败: 上传的音频文件为空";
            }
            
            log.info("音频文件名: {}, 内容类型: {}", audioFile.getOriginalFilename(), audioFile.getContentType());
            
            // 上传文件到OSS并获取可访问URL
            String ossUrl;
            try {
                if (ossUtils != null) {
                    // 使用OssUtils上传
                    ossUrl = ossUtils.uploadFile(audioFile);
                    log.info("使用OssUtils上传文件成功，URL: {}", ossUrl);
                } else {
                    // 如果没有配置OssUtils，则返回错误
                    log.error("OssUtils未配置，无法上传文件");
                    return "语音识别失败: OSS工具未配置";
                }
            } catch (Exception e) {
                log.error("上传文件到OSS失败", e);
                return "语音识别失败: 上传文件到OSS时出错 - " + e.getMessage();
            }
            // 保存上传的文件URL
            this.lastUploadedFileUrl = ossUrl;
            // 提交转写任务并获取任务ID
            String taskId;
            try {
                taskId = translate(ossUrl);
                log.info("提交转写任务成功，taskId: {}", taskId);
            } catch (Exception e) {
                log.error("提交转写任务失败", e);
                return "语音识别失败: 提交转写任务时出错 - " + e.getMessage();
            }
            
            // 等待并获取转写结果
            try {
                String result = waitForTranscriptionResult(taskId);
                log.info("获取转写结果成功: {}", result);
                return result;
            } catch (Exception e) {
                log.error("获取转写结果失败", e);
                return "语音识别失败: 获取转写结果时出错 - " + e.getMessage();
            }
        } catch (Exception e) {
            log.error("语音识别过程中发生异常", e);
            return "语音识别失败: " + e.getMessage();
        }
    }

    /**
     * 获取最后上传的音频文件URL
     * @return 音频文件的访问URL
     */
    public String getLastUploadedFileUrl() {
        return this.lastUploadedFileUrl;
    }

    /**
     * 上传文件到阿里云OSS
     */
    private String uploadToOss(File file, String objectName) {
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(ossEndpoint, ossAccessKeyId, ossAccessKeySecret);
        try {
            // 上传文件
            ossClient.putObject(ossBucketName, objectName, file);
            
            // 设置URL过期时间为1小时
            Date expirationTime = new Date(System.currentTimeMillis() + 3600 * 1000);
            
            // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容
            return ossClient.generatePresignedUrl(ossBucketName, objectName, expirationTime).toString();
        } catch (Exception e) {
            log.error("上传文件到OSS失败", e);
            throw new RuntimeException("上传文件到OSS失败: " + e.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    @Override
    public String saveTranscription(MultipartFile audioFile, Integer questionIndex, String interviewId) {
        log.info("保存面试问题{}的转写结果，面试ID: {}", questionIndex, interviewId);
        try {
            // 调用语音识别
            String transcription = speechToText(audioFile);
            // 获取上传的音频文件URL
            String audioUrl = getLastUploadedFileUrl();
            
            log.info("已保存面试问题{}的转写结果，音频URL: {}", questionIndex, audioUrl);
            return transcription;
        } catch (Exception e) {
            log.error("保存面试转写结果失败", e);
            return "转写失败: " + e.getMessage();
        }
    }

    @Override
    public Map<Integer, String> getInterviewTranscriptions(String interviewId) {
        log.info("获取面试ID: {}的所有转写结果", interviewId);
//        return transcriptionStore.getOrDefault(interviewId, new HashMap<>());
        return null;
    }

    public static CommonRequest createCommonRequest(String domain, String version, ProtocolType protocolType, MethodType method, String uri) {
        // 创建API请求并设置参数
        CommonRequest request = new CommonRequest();
        request.setSysDomain(domain);
        request.setSysVersion(version);
        request.setSysProtocol(protocolType);
        request.setSysMethod(method);
        request.setSysUriPattern(uri);
        request.setHttpContentType(FormatType.JSON);
        return request;
    }

    public CommonResponse getTaskInfo(String taskId)  {
        String queryUrl = String.format("/openapi/tingwu/v2/tasks" + "/%s", taskId);

        CommonRequest request = createCommonRequest("tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS, MethodType.GET, queryUrl);
        CommonResponse response = null;
        try {
            response = acsClient.getCommonResponse(request);
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }
        log.info("查询转义：响应："+response.getData());
        return response;
    }
    private String translate(String videoUrl) {
        CommonRequest request = createCommonRequest("tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS, MethodType.PUT, "/openapi/tingwu/v2/tasks");
        request.putQueryParameter("type", "offline");

        JSONObject root = new JSONObject();
        root.put("AppKey", appKey);

        JSONObject input = new JSONObject();
        input.fluentPut("FileUrl", videoUrl)
                .fluentPut("SourceLanguage", "cn")
                .fluentPut("TaskKey", "task" + System.currentTimeMillis());
        root.put("Input", input);

        JSONObject parameters = new JSONObject();

        parameters.put("DiarizationEnabled", true);
        parameters.put("AutoChaptersEnabled", true);

        root.put("Parameters", parameters);
        request.setHttpContent(root.toJSONString().getBytes(), "utf-8", FormatType.JSON);

        CommonResponse response = null;
        try {
            response = acsClient.getCommonResponse(request);
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }

        log.info("转义响应："+response.getData());
        JSONObject result = new JSONObject();
        result.put("code", 0);
        result.put("message", "处理成功");
        result.put("data", JSONObject.parse(response.getData()));
        JSONObject responseData = JSONObject.parseObject(response.getData());
        JSONObject data =  responseData.getJSONObject("Data");
        String taskId = data.getString("TaskId");
        return taskId;
    }
    //语音对话转写
    @Override
    public String dialogTranslate(String videoUrl) {
        CommonRequest request = createCommonRequest("tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS, MethodType.PUT, "/openapi/tingwu/v2/tasks");
        request.putQueryParameter("type", "offline");

        JSONObject root = new JSONObject();
        root.put("AppKey", appKey);

        JSONObject input = new JSONObject();
        input.fluentPut("FileUrl", videoUrl)
                .fluentPut("SourceLanguage", "cn")
                .fluentPut("TaskKey", "task" + System.currentTimeMillis());
        root.put("Input", input);

        JSONObject parameters = new JSONObject();
        JSONObject transcription = new JSONObject();
        transcription.put("DiarizationEnabled", true);
        JSONObject speakerCount = new JSONObject();
        speakerCount.put("SpeakerCount", 2);
        transcription.put("Diarization", speakerCount);
        parameters.put("Transcription", transcription);
        root.put("Parameters", parameters);

        root.put("Parameters", parameters);
        request.setHttpContent(root.toJSONString().getBytes(), "utf-8", FormatType.JSON);

        CommonResponse response = null;
        try {
            response = acsClient.getCommonResponse(request);
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }

        log.info("语音对话转写：转义响应："+response.getData());
        JSONObject result = new JSONObject();
        result.put("code", 0);
        result.put("message", "处理成功");
        result.put("data", JSONObject.parse(response.getData()));
        JSONObject responseData = JSONObject.parseObject(response.getData());
        JSONObject data =  responseData.getJSONObject("Data");
        String taskId = data.getString("TaskId");
        return taskId;
    }
    //章节速览转写
    @Override
    public String chapterTranslate(String videoUrl) {
        CommonRequest request = createCommonRequest("tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS, MethodType.PUT, "/openapi/tingwu/v2/tasks");
        request.putQueryParameter("type", "offline");

        JSONObject root = new JSONObject();
        root.put("AppKey", appKey);

        JSONObject input = new JSONObject();
        input.fluentPut("FileUrl", videoUrl)
                .fluentPut("SourceLanguage", "cn")
                .fluentPut("TaskKey", "task" + System.currentTimeMillis());
        root.put("Input", input);
        JSONObject parameters = new JSONObject();
        parameters.put("AutoChaptersEnabled", true);
        JSONObject autoChapters = new JSONObject();
        autoChapters.put("ChapterGranularity", "Meticulous");
        autoChapters.put("TitleLengthLevel", "Long");
        parameters.put("AutoChapters", autoChapters);
        root.put("Parameters", parameters);
        request.setHttpContent(root.toJSONString().getBytes(), "utf-8", FormatType.JSON);

        CommonResponse response = null;
        try {
            response = acsClient.getCommonResponse(request);
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }

        log.info("章节速览转写：转义响应："+response.getData());
        JSONObject result = new JSONObject();
        result.put("code", 0);
        result.put("message", "处理成功");
        result.put("data", JSONObject.parse(response.getData()));
        JSONObject responseData = JSONObject.parseObject(response.getData());
        JSONObject data =  responseData.getJSONObject("Data");
        String taskId = data.getString("TaskId");
        return taskId;
    }

    @Override
    public String questionsAnsweringTranslate(String videoUrl) {
        CommonRequest request = createCommonRequest("tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS, MethodType.PUT, "/openapi/tingwu/v2/tasks");
        request.putQueryParameter("type", "offline");

        JSONObject root = new JSONObject();
        root.put("AppKey", appKey);

        JSONObject input = new JSONObject();
        input.fluentPut("FileUrl", videoUrl)
                .fluentPut("SourceLanguage", "cn")
                .fluentPut("TaskKey", "task" + System.currentTimeMillis());
        root.put("Input", input);
        JSONObject parameters = new JSONObject();
        parameters.put("SummarizationEnabled", true);
        JSONObject summarization = new JSONObject();
        // 全文摘要、发言人总结摘要、问答摘要(问答回顾)
        JSONArray types = new JSONArray()
                .fluentAdd("QuestionsAnswering");
        summarization.put("Types", types);
        parameters.put("Summarization", summarization);
        root.put("Parameters", parameters);
        request.setHttpContent(root.toJSONString().getBytes(), "utf-8", FormatType.JSON);

        CommonResponse response = null;
        try {
            response = acsClient.getCommonResponse(request);
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }

        log.info("章节速览转写：转义响应："+response.getData());
        JSONObject result = new JSONObject();
        result.put("code", 0);
        result.put("message", "处理成功");
        result.put("data", JSONObject.parse(response.getData()));
        JSONObject responseData = JSONObject.parseObject(response.getData());
        JSONObject data =  responseData.getJSONObject("Data");
        String taskId = data.getString("TaskId");
        return taskId;
    }

    /**
     * 等待并获取语音识别结果
     */
    private String waitForTranscriptionResult(String taskId) throws ClientException, InterruptedException {
        /**
         * 创建CommonRequest，设置任务ID。
         */
        CommonRequest getRequest = new CommonRequest();
        getRequest.setDomain("filetrans.cn-shanghai.aliyuncs.com");   // 设置域名，固定值。
        getRequest.setVersion("2018-08-17");         // 设置中国站的版本号。
        getRequest.setAction("GetTaskResult");           // 设置action，固定值。
        getRequest.setProduct("nls-filetrans");          // 设置产品名称，固定值。
        getRequest.putQueryParameter("TaskId", taskId);  // 设置任务ID为查询参数。
        getRequest.setMethod(MethodType.GET);            // 设置为GET方式的请求。
        /**
         * 提交录音文件识别结果查询请求
         * 以轮询的方式进行识别结果的查询，直到服务端返回的状态描述为"SUCCESS"、"SUCCESS_WITH_NO_VALID_FRAGMENT"，或者为错误描述，则结束轮询。
         */
        String statusText = "";
        String recognizedText = ""; // 用于存储识别出的文本结果
         int retryCount = 0;
        
        while (retryCount < retryTimes) {
            CommonResponse getResponse = getTaskInfo(taskId);
            if (getResponse.getHttpStatus() != 200) {
                log.error("识别结果查询请求失败，Http错误码： {}", getResponse.getHttpStatus());
                log.error("识别结果查询请求失败： {}", getResponse.getData());
                break;
            }
            
            JSONObject result = JSONObject.parseObject(getResponse.getData());
            log.info("识别查询结果：{}", result.toJSONString());
            // 从 Data 对象中获取 TaskStatus
            JSONObject data = result.getJSONObject("Data");
            if (data != null) {
                statusText = data.getString("TaskStatus");
            } else {
                log.error("获取任务状态失败，Data 对象为空");
                statusText = "FAILED";
            }
            
            if ("ONGOING".equals(statusText) || "FAILED".equals(statusText)) {
                // 继续轮询
                log.info("任务{}正在{}中，等待3秒后重试...", taskId, statusText);
                Thread.sleep(3000);
                retryCount++;
            } else {
                // 任务完成或失败
                if ("COMPLETED".equals(statusText)) {
                    log.info("录音文件识别成功！");
                    
                    // 获取 Result 对象
                    JSONObject resultJsonObj = data.getJSONObject("Result");
                    if (resultJsonObj != null) {
                        // 获取 Transcription URL
                            String transcriptionUrl = resultJsonObj.getString("Transcription");
                        if (transcriptionUrl != null && !transcriptionUrl.isEmpty()) {
                            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                                HttpGet request = new HttpGet(transcriptionUrl);
                                
                                // 执行请求
                                org.apache.http.HttpResponse response = httpClient.execute(request);
                                
                                if (response.getStatusLine().getStatusCode() == 200) {
                                    // 获取响应内容
                                    String responseBody = EntityUtils.toString(response.getEntity());
                                    
                                    // 解析 Transcription JSON
                                    JSONObject transcriptionJson = JSONObject.parseObject(responseBody);
                                    JSONObject transcription = transcriptionJson.getJSONObject("Transcription");
                                    if (transcription != null) {
                                        JSONArray paragraphs = transcription.getJSONArray("Paragraphs");
                                        
                                        if (paragraphs != null && !paragraphs.isEmpty()) {
                                            StringBuilder fullText = new StringBuilder();
                                            // 遍历所有段落
                                            for (int i = 0; i < paragraphs.size(); i++) {
                                                JSONObject paragraph = paragraphs.getJSONObject(i);
                                                JSONArray words = paragraph.getJSONArray("Words");
                                                
                                                if (words != null && !words.isEmpty()) {
                                                    // 遍历段落中的所有词
                                                    for (int j = 0; j < words.size(); j++) {
                                                        JSONObject word = words.getJSONObject(j);
                                                        String text = word.getString("Text");
                                                        if (text != null && !text.trim().isEmpty()) {
                                                            fullText.append(text);
                                                        }
                                                    }
                                                    fullText.append(" "); // 段落之间添加空格
                                                }
                                            }
                                            recognizedText = fullText.toString().trim();
                                            log.info("转写文本内容: {}", recognizedText);
                                        } else {
                                            log.warn("未找到有效的转写文本段落");
                                            recognizedText = "未能识别有效内容";
                                        }
                                    } else {
                                        log.warn("未找到 Transcription 对象");
                                        recognizedText = "转写结果格式错误";
                                    }
                                } else {
                                    log.error("获取转写内容失败，HTTP状态码: {}", response.getStatusLine().getStatusCode());
                                    recognizedText = "获取转写结果失败";
                                }
                            } catch (Exception e) {
                                log.error("处理转写内容时发生错误", e);
                                recognizedText = "处理转写结果时出错: " + e.getMessage();
                            }
                        } else {
                            log.warn("Transcription URL 为空");
                            recognizedText = "未找到转写结果URL";
                        }
                    } else {
                        log.warn("识别结果中没有 Result 字段");
                        recognizedText = "语音识别结果处理失败";
                    }
                } else {
                    log.error("录音文件识别失败: {}", statusText);
                    recognizedText = "语音识别失败: " + statusText;
                }
                break;
            }
        }
        
        // 如果达到最大重试次数仍未完成
        if (retryCount >= retryTimes) {
            log.warn("达到最大重试次数{}，任务{}可能仍在处理中", retryTimes, taskId);
            recognizedText = "语音识别超时，请稍后查看结果";
        }
        
        // 如果未能获取到有效文本，返回状态信息
        if (recognizedText == null || recognizedText.trim().isEmpty()) {
            recognizedText = "语音识别状态: " + statusText;
        }
        
        return recognizedText;
    }

    /**
     *
     * @param taskId
     * @param type dialog:对话取Transcription/chapter：章节取AutoChapters
     * @return
     * @throws ClientException
     * @throws InterruptedException
     */
    @Override
    public List<Object> transcriptionResult(String taskId, String type,String interviewId){
        List<Object> resultList = new ArrayList<>();
        /**
         * 创建CommonRequest，设置任务ID。
         */
        CommonRequest getRequest = new CommonRequest();
        getRequest.setDomain("filetrans.cn-shanghai.aliyuncs.com");   // 设置域名，固定值。
        getRequest.setVersion("2018-08-17");         // 设置中国站的版本号。
        getRequest.setAction("GetTaskResult");           // 设置action，固定值。
        getRequest.setProduct("nls-filetrans");          // 设置产品名称，固定值。
        getRequest.putQueryParameter("TaskId", taskId);  // 设置任务ID为查询参数。
        getRequest.setMethod(MethodType.GET);            // 设置为GET方式的请求。
        /**
         * 提交录音文件识别结果查询请求
         * 以轮询的方式进行识别结果的查询，直到服务端返回的状态描述为"SUCCESS"、"SUCCESS_WITH_NO_VALID_FRAGMENT"，或者为错误描述，则结束轮询。
         */
        String statusText = "";
        String recognizedText = ""; // 用于存储识别出的文本结果
         int retryCount = 0;

        while (retryCount < retryTimes) {
            CommonResponse getResponse = getTaskInfo(taskId);
            if (getResponse.getHttpStatus() != 200) {
                log.error("识别结果查询请求失败，Http错误码： {}", getResponse.getHttpStatus());
                log.error("识别结果查询请求失败： {}", getResponse.getData());
                break;
            }
            JSONObject result = JSONObject.parseObject(getResponse.getData());
            log.info("识别查询结果：{}", result.toJSONString());
            // 从 Data 对象中获取 TaskStatus
            JSONObject data = result.getJSONObject("Data");
            if (data != null) {
                statusText = data.getString("TaskStatus");
            } else {
                log.error("获取任务状态失败，Data 对象为空");
                statusText = "FAILED";
            }

            if ("ONGOING".equals(statusText) || "FAILED".equals(statusText)) {
                // 继续轮询
                log.info("任务{}正在{}中，等待3秒后重试...", taskId, statusText);
                try {
                    Thread.sleep(3000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                retryCount++;
            } else {
                // 任务完成或失败
                if ("COMPLETED".equals(statusText)) {
                    log.info("录音文件识别成功！");
                    // 获取 Result 对象
                    JSONObject resultJsonObj = data.getJSONObject("Result");
                    if (resultJsonObj != null) {
                        //dialog:对话取Transcription/chapter：章节取AutoChapters
                        if(StringUtils.equals(type,"dialog")){
                            // 获取 Transcription URL
                            String transcriptionUrl = resultJsonObj.getString("Transcription");
                            if (transcriptionUrl != null && !transcriptionUrl.isEmpty()) {
                                try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                                    HttpGet request = new HttpGet(transcriptionUrl);
                                    // 执行请求
                                    org.apache.http.HttpResponse response = httpClient.execute(request);
                                    if (response.getStatusLine().getStatusCode() == 200) {
                                        // 获取响应内容
                                        String responseBody = EntityUtils.toString(response.getEntity());
                                        // 解析 Transcription JSON
                                        JSONObject transcriptionJson = JSONObject.parseObject(responseBody);
                                        JSONObject transcription = transcriptionJson.getJSONObject("Transcription");
                                        if (transcription != null) {
                                            JSONArray paragraphs = transcription.getJSONArray("Paragraphs");

                                            if (paragraphs != null && !paragraphs.isEmpty()) {
                                                // 遍历所有段落
                                                for (int i = 0; i < paragraphs.size(); i++) {
                                                    JSONObject paragraph = paragraphs.getJSONObject(i);
                                                    JSONArray words = paragraph.getJSONArray("Words");
                                                    String speakerId = paragraph.getString("SpeakerId");
                                                    StringBuilder fullText = new StringBuilder();
                                                    Long start=null;
                                                    Long end=null;
                                                    if (words != null && !words.isEmpty()) {
                                                        // 遍历段落中的所有词
                                                        for (int j = 0; j < words.size(); j++) {
                                                            JSONObject word = words.getJSONObject(j);
                                                            if(j==0){
                                                                start = word.getLong("Start");
                                                            }
                                                            if(j==words.size()-1){
                                                                end = word.getLong("End");
                                                            }
                                                            String text = word.getString("Text");
                                                            if (text != null && !text.trim().isEmpty()) {
                                                                fullText.append(text);
                                                            }
                                                        }
                                                        InterviewSpeechRecord interviewSpeechRecord = new InterviewSpeechRecord();
                                                        interviewSpeechRecord.setContent(fullText.toString());
                                                        interviewSpeechRecord.setInterviewId(interviewId);
                                                        interviewSpeechRecord.setSpeakerId(Integer.parseInt(speakerId));
                                                        interviewSpeechRecord.setStartTime(start);
                                                        interviewSpeechRecord.setEndTime(end);
                                                        resultList.add(interviewSpeechRecord);
                                                    }
                                                }
                                            } else {
                                                log.info("未找到有效的转写文本段落");
                                                return null;
                                            }
                                        } else {
                                            log.info("未找到 Transcription 对象");
                                            return null;
                                        }
                                    } else {
                                        log.info("获取转写内容失败，HTTP状态码: {}", response.getStatusLine().getStatusCode());
                                        return null;
                                    }
                                } catch (Exception e) {
                                    log.error("处理转写内容时发生错误", e);
                                    return null;
                                }
                            } else {
                                log.info("Transcription URL 为空");
                                return null;
                            }
                        }else if(StringUtils.equals(type,"chapter")){
                            // 获取 Transcription URL
                            String transcriptionUrl = resultJsonObj.getString("AutoChapters");
                            if (transcriptionUrl != null && !transcriptionUrl.isEmpty()) {
                                try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                                    HttpGet request = new HttpGet(transcriptionUrl);
                                    // 执行请求
                                    org.apache.http.HttpResponse response = httpClient.execute(request);
                                    if (response.getStatusLine().getStatusCode() == 200) {
                                        // 获取响应内容
                                        String responseBody = EntityUtils.toString(response.getEntity());
                                        // 解析 Transcription JSON
                                        JSONObject transcriptionJson = JSONObject.parseObject(responseBody);
                                        JSONArray transcription = transcriptionJson.getJSONArray("AutoChapters");
                                        if (transcription != null) {
                                                // 遍历所有段落
                                            for (int i = 0; i < transcription.size(); i++) {
                                                InterviewSpeechChapter interviewSpeechChapter = new InterviewSpeechChapter();
                                                JSONObject chapters = transcription.getJSONObject(i);
                                                Long start = chapters.getLong("Start");
                                                Long End = chapters.getLong("End");
                                                String headline = chapters.getString("Headline");
                                                String summary = chapters.getString("Summary");
                                                interviewSpeechChapter.setStartTime(start);
                                                interviewSpeechChapter.setEndTime(End);
                                                interviewSpeechChapter.setChapterTitle(headline);
                                                interviewSpeechChapter.setChapterSummary(summary);
                                                resultList.add(interviewSpeechChapter);
                                            }
                                        } else {
                                            log.info("未找到 AutoChapters 对象");
                                            return null;
                                        }
                                    } else {
                                        log.info("获取转写内容失败，HTTP状态码: {}", response.getStatusLine().getStatusCode());
                                        return null;
                                    }
                                } catch (Exception e) {
                                    log.error("处理转写内容时发生错误", e);
                                    return null;
                                }
                            } else {
                                log.info("Transcription URL 为空");
                                return null;
                            }
                        }else{
                            // 获取 Transcription URL
                            String transcriptionUrl = resultJsonObj.getString("Summarization");
                            if (transcriptionUrl != null && !transcriptionUrl.isEmpty()) {
                                try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                                    HttpGet request = new HttpGet(transcriptionUrl);
                                    // 执行请求
                                    org.apache.http.HttpResponse response = httpClient.execute(request);
                                    if (response.getStatusLine().getStatusCode() == 200) {
                                        // 获取响应内容
                                        String responseBody = EntityUtils.toString(response.getEntity());
                                        // 解析 Transcription JSON
                                        JSONObject transcriptionJson = JSONObject.parseObject(responseBody);
                                        JSONObject summarization = transcriptionJson.getJSONObject("Summarization");
                                        if (summarization != null) {
                                            JSONArray questionsAnsweringSummary = summarization.getJSONArray("QuestionsAnsweringSummary");
                                            if(questionsAnsweringSummary!=null && questionsAnsweringSummary.size()>0){
                                                for (int i = 0; i < questionsAnsweringSummary.size(); i++) {
                                                    JSONObject questionsAnswering = questionsAnsweringSummary.getJSONObject(i);
                                                    String question = questionsAnswering.getString("Question");
                                                    String answer = questionsAnswering.getString("Answer");
                                                    InterviewTranscription interviewTranscription = new InterviewTranscription();
                                                    interviewTranscription.setInterviewId(interviewId);
                                                    interviewTranscription.setQuestionIndex(i);
                                                    interviewTranscription.setQuestion(question);
                                                    interviewTranscription.setTranscription(answer);
                                                    resultList.add(interviewTranscription);
                                                }
                                            }
                                        } else {
                                            log.info("未找到 Summarization 对象");
                                            return null;
                                        }
                                    } else {
                                        log.info("获取转写内容失败，HTTP状态码: {}", response.getStatusLine().getStatusCode());
                                        return null;
                                    }
                                } catch (Exception e) {
                                    log.error("处理转写内容时发生错误", e);
                                    return null;
                                }
                            } else {
                                log.info("Transcription URL 为空");
                                return null;
                            }
                        }
                    } else {
                        log.info("识别结果中没有 Result 字段");
                        return null;
                    }
                } else {
                    log.info("录音文件识别失败: {}", statusText);
                    return null;
                }
                break;
            }
        }
        // 如果达到最大重试次数仍未完成
        if (retryCount >= retryTimes) {
            log.info("达到最大重试次数{}，任务{}可能仍在处理中", retryTimes, taskId);
            return null;
        }
        return resultList;
    }


} 