package com.bimowu.resume.common.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.resume.entity.ResumeEvaluate;
import com.bimowu.resume.vo.ResumeEvaluateVo;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * <p>
 * 自我评价表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface ResumeEvaluateMapper extends BaseMapper<ResumeEvaluate> {

    ResumeEvaluateVo selectByResumeId(@Param("resumeId") Long resumeId);


}
