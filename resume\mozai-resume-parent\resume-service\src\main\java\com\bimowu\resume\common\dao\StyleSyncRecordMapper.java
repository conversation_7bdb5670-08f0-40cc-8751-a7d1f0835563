package com.bimowu.resume.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.resume.entity.StyleSyncRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 样式同步记录Mapper
 */
@Mapper
public interface StyleSyncRecordMapper extends BaseMapper<StyleSyncRecord> {
    
    /**
     * 根据模板ID查询同步记录
     */
    List<StyleSyncRecord> selectByTemplateId(@Param("templateId") Integer templateId);
    
    /**
     * 根据模板ID查询同步记录（带限制）
     */
    List<StyleSyncRecord> selectByTemplateIdWithLimit(@Param("templateId") Integer templateId, 
                                                     @Param("limit") int limit);
    
    /**
     * 查询最近的同步记录
     */
    StyleSyncRecord selectLatestByTemplateId(@Param("templateId") Integer templateId);
    
    /**
     * 根据状态查询同步记录
     */
    List<StyleSyncRecord> selectByStatus(@Param("status") String status);
    
    /**
     * 查询指定时间范围内的同步记录
     */
    List<StyleSyncRecord> selectByTimeRange(@Param("startTime") Date startTime, 
                                           @Param("endTime") Date endTime);
    
    /**
     * 统计同步成功率
     */
    Double selectSuccessRate(@Param("templateId") Integer templateId, 
                            @Param("days") Integer days);
    
    /**
     * 清理过期的同步记录
     */
    int deleteExpiredRecords(@Param("expireDays") Integer expireDays);
}