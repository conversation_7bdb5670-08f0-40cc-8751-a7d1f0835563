<template>
  <div class="oss-upload-demo">
    <h2>OSS直传示例</h2>
    <div class="upload-container">
      <input type="file" @change="handleFileChange" />
      <button @click="uploadFile" :disabled="!selectedFile || uploading">
        {{ uploading ? '上传中...' : '上传到OSS' }}
      </button>
    </div>
    
    <div v-if="uploading" class="progress-container">
      <div class="progress-bar">
        <div class="progress" :style="{ width: `${uploadProgress}%` }"></div>
      </div>
      <span>{{ uploadProgress }}%</span>
    </div>
    
    <div v-if="fileUrl" class="result-container">
      <h3>上传成功</h3>
      <p>文件URL: <a :href="fileUrl" target="_blank">{{ fileUrl }}</a></p>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import ossUploader from '../utils/ossUploader';

export default defineComponent({
  name: 'OssUploadDemo',
  setup() {
    const selectedFile = ref<File | null>(null);
    const uploading = ref(false);
    const uploadProgress = ref(0);
    const fileUrl = ref('');
    
    // 处理文件选择事件
    const handleFileChange = (event: Event) => {
      const input = event.target as HTMLInputElement;
      if (input.files && input.files.length > 0) {
        selectedFile.value = input.files[0];
        // 清除之前的上传结果
        fileUrl.value = '';
      }
    };
    
    // 上传文件到OSS
    const uploadFile = async () => {
      if (!selectedFile.value) {
        alert('请先选择文件');
        return;
      }
      
      try {
        uploading.value = true;
        uploadProgress.value = 0;
        
        // 调用OSS上传工具类，上传文件
        fileUrl.value = await ossUploader.uploadFile(
          selectedFile.value,
          'demo/uploads', // 上传目录
          (percent) => {
            uploadProgress.value = percent;
          }
        );
        
        alert('文件上传成功');
      } catch (error) {
        console.error('文件上传失败:', error);
        alert(`文件上传失败: ${error instanceof Error ? error.message : String(error)}`);
      } finally {
        uploading.value = false;
      }
    };
    
    return {
      selectedFile,
      uploading,
      uploadProgress,
      fileUrl,
      handleFileChange,
      uploadFile
    };
  }
});
</script>

<style scoped>
.oss-upload-demo {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.upload-container {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

button {
  padding: 8px 16px;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background-color: #a0cfff;
  cursor: not-allowed;
}

.progress-container {
  margin-bottom: 20px;
}

.progress-bar {
  height: 10px;
  background-color: #ebeef5;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress {
  height: 100%;
  background-color: #409eff;
  transition: width 0.3s ease;
}

.result-container {
  padding: 10px;
  background-color: #f0f9eb;
  border-radius: 4px;
  border-left: 4px solid #67c23a;
}

a {
  color: #409eff;
  word-break: break-all;
}
</style> 