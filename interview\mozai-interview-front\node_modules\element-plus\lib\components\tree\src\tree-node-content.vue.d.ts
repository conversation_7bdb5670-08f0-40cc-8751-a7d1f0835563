declare const _default: import("vue").DefineComponent<{
    node: {
        type: ObjectConstructor;
        required: true;
    };
    renderContent: FunctionConstructor;
}, () => any, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    node: {
        type: ObjectConstructor;
        required: true;
    };
    renderContent: FunctionConstructor;
}>>, {}>;
export default _default;
