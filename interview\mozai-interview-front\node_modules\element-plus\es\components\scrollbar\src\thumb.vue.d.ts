declare const _default: import("vue").DefineComponent<{
    readonly vertical: BooleanConstructor;
    readonly size: StringConstructor;
    readonly move: NumberConstructor;
    readonly ratio: {
        readonly type: import("vue").PropType<number>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly always: BooleanConstructor;
}, {
    COMPONENT_NAME: string;
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly vertical: BooleanConstructor;
        readonly size: StringConstructor;
        readonly move: NumberConstructor;
        readonly ratio: {
            readonly type: import("vue").PropType<number>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly always: BooleanConstructor;
    }>> & {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
    }>>;
    scrollbar: import("./constants").ScrollbarContext;
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    instance: import("vue").Ref<HTMLDivElement | undefined>;
    thumb: import("vue").Ref<HTMLDivElement | undefined>;
    thumbState: import("vue").Ref<{
        X?: number | undefined;
        Y?: number | undefined;
    }>;
    visible: import("vue").Ref<boolean>;
    cursorDown: boolean;
    cursorLeave: boolean;
    originalOnSelectStart: ((this: GlobalEventHandlers, ev: Event) => any) | null;
    bar: import("vue").ComputedRef<{
        readonly offset: "offsetHeight";
        readonly scroll: "scrollTop";
        readonly scrollSize: "scrollHeight";
        readonly size: "height";
        readonly key: "vertical";
        readonly axis: "Y";
        readonly client: "clientY";
        readonly direction: "top";
    } | {
        readonly offset: "offsetWidth";
        readonly scroll: "scrollLeft";
        readonly scrollSize: "scrollWidth";
        readonly size: "width";
        readonly key: "horizontal";
        readonly axis: "X";
        readonly client: "clientX";
        readonly direction: "left";
    }>;
    thumbStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
    offsetRatio: import("vue").ComputedRef<number>;
    clickThumbHandler: (e: MouseEvent) => void;
    clickTrackHandler: (e: MouseEvent) => void;
    startDrag: (e: MouseEvent) => void;
    mouseMoveDocumentHandler: (e: MouseEvent) => void;
    mouseUpDocumentHandler: () => void;
    mouseMoveScrollbarHandler: () => void;
    mouseLeaveScrollbarHandler: () => void;
    restoreOnselectstart: () => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly vertical: BooleanConstructor;
    readonly size: StringConstructor;
    readonly move: NumberConstructor;
    readonly ratio: {
        readonly type: import("vue").PropType<number>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly always: BooleanConstructor;
}>>, {
    readonly vertical: boolean;
    readonly always: boolean;
}>;
export default _default;
