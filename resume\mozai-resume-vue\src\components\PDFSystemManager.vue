<template>
  <div class="pdf-system-manager">
    <el-card class="system-status-card">
      <template #header>
        <div class="clearfix">
          <span>PDF生成系统管理</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="refreshStatus">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
      </template>
      
      <!-- 系统状态显示 -->
      <div class="status-section" v-if="systemStatus">
        <h4>系统状态</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="status-item">
              <span class="label">当前引擎:</span>
              <el-tag :type="systemStatus.newSystemEnabled ? 'success' : 'warning'">
                {{ systemStatus.newSystemEnabled ? 'Flying Saucer' : 'XMLWorker' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="status-item">
              <span class="label">系统健康:</span>
              <el-tag :type="systemHealth.healthy ? 'success' : 'danger'">
                {{ systemHealth.healthy ? '正常' : '异常' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="status-item">
              <span class="label">降级模式:</span>
              <el-tag :type="systemStatus.inFallbackMode ? 'danger' : 'success'">
                {{ systemStatus.inFallbackMode ? '是' : '否' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 15px;">
          <el-col :span="8">
            <div class="status-item">
              <span class="label">A/B测试:</span>
              <el-tag :type="systemStatus.abTestEnabled ? 'primary' : 'info'">
                {{ systemStatus.abTestEnabled ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="status-item">
              <span class="label">流量比例:</span>
              <span>{{ systemStatus.trafficPercent }}%</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="status-item">
              <span class="label">失败次数:</span>
              <el-tag :type="systemStatus.failureCount > 0 ? 'warning' : 'success'">
                {{ systemStatus.failureCount }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 系统控制 -->
      <div class="control-section" style="margin-top: 20px;">
        <h4>系统控制</h4>
        <el-button-group>
          <el-button 
            type="success" 
            @click="switchSystem('new')"
            :loading="switching"
            :disabled="systemStatus && systemStatus.newSystemEnabled && !systemStatus.inFallbackMode">
            切换到新系统
          </el-button>
          <el-button 
            type="warning" 
            @click="switchSystem('legacy')"
            :loading="switching"
            :disabled="systemStatus && !systemStatus.newSystemEnabled">
            切换到旧系统
          </el-button>
          <el-button 
            type="primary" 
            @click="testPdfGeneration"
            :loading="testing">
            测试生成
          </el-button>
        </el-button-group>
      </div>
      
      <!-- 性能监控 -->
      <div class="monitor-section" style="margin-top: 20px;" v-if="performanceData">
        <h4>性能监控</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="metric-item">
              <div class="metric-value">{{ performanceData.successRate }}%</div>
              <div class="metric-label">成功率</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-item">
              <div class="metric-value">{{ performanceData.avgDuration }}ms</div>
              <div class="metric-label">平均耗时</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-item">
              <div class="metric-value">{{ performanceData.totalRequests }}</div>
              <div class="metric-label">总请求数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-item">
              <div class="metric-value">{{ performanceData.errorCount }}</div>
              <div class="metric-label">错误次数</div>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 日志显示 -->
      <div class="log-section" style="margin-top: 20px;">
        <h4>操作日志</h4>
        <el-table :data="logs" size="mini" max-height="200">
          <el-table-column prop="timestamp" label="时间" width="160">
            <template #default="scope">
              {{ formatTime(scope.row.timestamp) }}
            </template>
          </el-table-column>
          <el-table-column prop="action" label="操作" width="120"></el-table-column>
          <el-table-column prop="result" label="结果" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.success ? 'success' : 'danger'" size="mini">
                {{ scope.row.success ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="消息"></el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
import { 
  getPdfSystemStatus, 
  switchPdfSystem, 
  getPdfSystemHealth 
} from '@/api/resume'

export default {
  name: 'PDFSystemManager',
  data() {
    return {
      systemStatus: null,
      systemHealth: { healthy: false },
      switching: false,
      testing: false,
      performanceData: {
        successRate: 95.5,
        avgDuration: 2340,
        totalRequests: 1250,
        errorCount: 56
      },
      logs: [],
      refreshTimer: null
    }
  },
  mounted() {
    this.refreshStatus()
    // 每30秒自动刷新状态
    this.refreshTimer = setInterval(() => {
      this.refreshStatus()
    }, 30000)
  },
  beforeUnmount() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  },
  methods: {
    // 刷新系统状态
    async refreshStatus() {
      try {
        const [statusResponse, healthResponse] = await Promise.all([
          getPdfSystemStatus(),
          getPdfSystemHealth()
        ])
        
        if (statusResponse.success) {
          this.systemStatus = statusResponse.data
        }
        
        this.systemHealth = healthResponse
        
      } catch (error) {
        console.error('刷新系统状态失败:', error)
        this.$message.error('获取系统状态失败')
      }
    },
    
    // 切换系统
    async switchSystem(systemType) {
      this.switching = true
      try {
        const response = await switchPdfSystem(systemType)
        if (response.success) {
          this.$message.success(response.message)
          this.addLog('系统切换', true, response.message)
          // 刷新状态
          setTimeout(() => {
            this.refreshStatus()
          }, 1000)
        } else {
          this.$message.error(response.message)
          this.addLog('系统切换', false, response.message)
        }
      } catch (error) {
        console.error('切换系统失败:', error)
        this.$message.error('切换系统失败')
        this.addLog('系统切换', false, error.message)
      } finally {
        this.switching = false
      }
    },
    
    // 测试PDF生成
    async testPdfGeneration() {
      this.testing = true
      try {
        // 这里可以调用测试接口
        await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟测试
        this.$message.success('PDF生成测试通过')
        this.addLog('测试生成', true, 'PDF生成功能正常')
      } catch (error) {
        this.$message.error('PDF生成测试失败')
        this.addLog('测试生成', false, error.message)
      } finally {
        this.testing = false
      }
    },
    
    // 添加日志
    addLog(action, success, message) {
      this.logs.unshift({
        timestamp: new Date(),
        action,
        success,
        message
      })
      
      // 只保留最近50条日志
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
    },
    
    // 格式化时间
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString()
    }
  }
}
</script>

<style scoped>
.pdf-system-manager {
  padding: 20px;
}

.system-status-card {
  margin-bottom: 20px;
}

.status-section h4,
.control-section h4,
.monitor-section h4,
.log-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.status-item .label {
  margin-right: 10px;
  color: #606266;
  font-weight: 500;
  min-width: 80px;
}

.metric-item {
  text-align: center;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 12px;
  color: #909399;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>