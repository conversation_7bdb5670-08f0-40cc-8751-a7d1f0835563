package com.bimowu.resume.vo;

import cn.hutool.db.DaoTemplate;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class ResumeVo {
    /**
     * 简历ID
     */
    private Long resumeId;

    /**
     * 简历标题
     */
    private String title;

    /**
     * 简历模板ID
     */
    private Long templateId;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 简历状态(0-待审核,1-已通过)
     */
    private Integer status;

    /**
     * 简历类型
     */
    private Long category;
    
    /**
     * 审核意见
     */
    private String auditOpinion;
}
