import type { HTMLAttributes } from 'vue';
declare type Orientation = HTMLAttributes['aria-orientation'];
declare type Direction = 'ltr' | 'rtl';
declare type FocusIntent = 'first' | 'last' | 'prev' | 'next';
export declare const getFocusIntent: (event: KeyboardEvent, orientation?: Orientation, dir?: Direction | undefined) => FocusIntent | undefined;
export declare const reorderArray: <T>(array: T[], atIdx: number) => T[];
export declare const focusFirst: (elements: HTMLElement[]) => void;
export {};
