package com.bimowu.resume.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * PDF生成配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "pdf")
public class PDFConfig {
    
    /**
     * 调试配置
     */
    private Debug debug = new Debug();
    
    /**
     * 性能监控配置
     */
    private Performance performance = new Performance();
    
    /**
     * 生成配置
     */
    private Generation generation = new Generation();
    
    /**
     * 模板配置
     */
    private Template template = new Template();
    
    /**
     * 字体配置
     */
    private Font font = new Font();
    
    /**
     * 页面配置
     */
    private Page page = new Page();
    
    /**
     * 图片处理配置
     */
    private Image image = new Image();
    
    /**
     * 错误处理配置
     */
    private Error error = new Error();
    
    @Data
    public static class Debug {
        private boolean enabled = false;
        private boolean detailed = false;
        private String logLevel = "INFO";
        private boolean outputHtml = false;
        private String outputPath = "/tmp/pdf-debug";
    }
    
    @Data
    public static class Performance {
        private boolean enableMonitoring = true;
        private int maxConcurrentGenerations = 10;
        private long slowQueryThresholdMs = 5000L;
        private boolean enableStatistics = true;
        private long statisticsResetInterval = 3600L;
    }
    
    @Data
    public static class Generation {
        private long timeoutMs = 30000L;
        private long maxHtmlSize = 10485760L; // 10MB
        private boolean enableFallback = true;
        private int retryCount = 2;
        private boolean enableCache = true;
        private long cacheTtl = 3600L;
    }
    
    @Data
    public static class Template {
        private String basePath = "classpath:templates/resume";
        private String encoding = "UTF-8";
        private boolean enableValidation = true;
        private boolean preloadTemplates = true;
    }
    
    @Data
    public static class Font {
        private String defaultFamily = "SimSun";
        private boolean enableFontEmbedding = true;
        private String fontPath = "classpath:fonts";
        private String[] fallbackFonts = {"SimHei", "Microsoft YaHei", "Arial"};
    }
    
    @Data
    public static class Page {
        private String format = "A4";
        private String orientation = "PORTRAIT";
        private int marginTop = 20;
        private int marginBottom = 20;
        private int marginLeft = 20;
        private int marginRight = 20;
    }
    
    @Data
    public static class Image {
        private int maxWidth = 800;
        private int maxHeight = 600;
        private double quality = 0.8;
        private boolean enableCompression = true;
    }
    
    @Data
    public static class Error {
        private boolean enableDetailedMessages = true;
        private boolean logStackTrace = true;
        private boolean enableErrorRecovery = true;
        private int maxErrorCount = 5;
    }
}