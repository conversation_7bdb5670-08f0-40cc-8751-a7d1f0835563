package com.bimowu.interview.base;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <Description>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/07/29
 */
public class PageReq implements Serializable {
    @ApiModelProperty(value = "当前页数", example = "1")
    @NotNull(message = "page 不能为空")
    private Integer page;
    @ApiModelProperty(value = "总页数", example = "20")
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;


    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

}
