import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// 检查环境变量是否跳过可选依赖
const skipOptionalDeps = process.env.SKIP_OPTIONAL_DEPENDENCIES === 'true'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  base: '/interview-ai/',
  build: {
    outDir: 'dist-interview-ai',
    assetsDir: 'assets',
    assetsInlineLimit: 4096,
    // 简化 rollup 配置以避免模块解析问题
    rollupOptions: {
      output: {
        manualChunks: {
          // 将 element-plus 单独打包
          'element-plus': ['element-plus'],
          // 将 vue 相关库单独打包
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          // 将其他第三方库打包
          'vendor': ['axios', 'uuid']
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      },
      // 如果设置了跳过可选依赖，则外部化ali-oss
      external: skipOptionalDeps ? ['ali-oss'] : []
    },
    // 减少构建警告
    chunkSizeWarningLimit: 1000,
    // 优化大型依赖项
    commonjsOptions: {
      include: [/node_modules/],
      transformMixedEsModules: true
    }
  },
  server: {
    port: 3000,
    open: true,
    cors: true
  },
  // 优化依赖预构建
  optimizeDeps: {
    include: ['element-plus'],
    exclude: skipOptionalDeps ? ['ali-oss'] : []
  }
})