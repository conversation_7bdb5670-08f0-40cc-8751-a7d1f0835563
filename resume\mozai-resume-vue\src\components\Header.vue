<template>
  <header class="app-header">
    <div class="container">
      <div class="logo" @click="navigateTo('/')">
        <el-icon class="logo-icon"><Monitor /></el-icon>
        <span class="logo-text">墨崽简历</span>
      </div>

      <nav class="nav-menu" v-if="isLoggedIn">
        <el-menu mode="horizontal" :ellipsis="false" router :default-active="activeIndex">
          <el-menu-item index="/">首页</el-menu-item>
          <el-menu-item index="/templates">简历模板</el-menu-item>
          <el-menu-item index="/user">我的简历</el-menu-item>
          
          <!-- 管理员菜单 -->
          <el-sub-menu index="admin" v-if="isAdmin">
            <template #title>系统管理</template>
            <el-menu-item index="/pdf-system-manager">PDF系统管理</el-menu-item>
            <el-menu-item index="/pdf-test">PDF测试</el-menu-item>
          </el-sub-menu>
        </el-menu>
      </nav>

      <div class="user-actions">
        <template v-if="isLoggedIn">
          <el-dropdown trigger="click" @command="handleCommand">
            <div class="user-avatar">
              <el-avatar :size="32" :src="userAvatar">{{ userInitials }}</el-avatar>
              <span class="username">{{ displayNickname }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout" divided>
                  <el-icon><Switch /></el-icon>退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        <template v-else>
          <el-button type="primary" @click="handleLogin">登录</el-button>
        </template>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Monitor, ArrowDown, User, Switch } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { getUserInfo } from '@/api/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const activeIndex = computed(() => route.path)
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 检查是否为管理员
const isAdmin = computed(() => {
  // 这里可以根据实际的权限系统来判断
  // 例如检查用户角色、权限等
  const user = userStore.userInfo
  return user && (user.role === 'admin' || user.isAdmin === true)
})

const displayNickname = computed(() => {
  if (userStore.userInfo && userStore.userInfo.nickname) {
    return userStore.userInfo.nickname
  }
  return userStore.nickname || '用户'
})

const userAvatar = computed(() => {
  return userStore.userInfo?.avatar || ''
})

const userInitials = computed(() => {
  return displayNickname.value.slice(0, 1).toUpperCase()
})

const navigateTo = (path) => {
  router.push(path)
}

const handleCommand = (command) => {
  if (command === 'logout') {
    handleLogout()
  }
}

const handleLogout = async () => {
  try {
    await userStore.logout()
    ElMessage.success('已退出登录')
    window.location.reload()
  } catch (error) {
    ElMessage.error('退出登录失败，请重试')
    window.location.reload()
  }
}

const handleLogin = async () => {
  try {
    await getUserInfo()
  } catch (error) {
    // 登录失败也没关系，后端会处理重定向
  }
}

onMounted(async () => {
  if (userStore.token && !userStore.userInfo.id) {
    try {
      await userStore.fetchUserInfo()
    } catch (error) {
      // 获取用户信息失败
    }
  }
})
</script>

<style scoped>
.app-header {
  background-color: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.container {
  max-width: 1200px;
  height: 60px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.logo-icon {
  font-size: 24px;
  color: #409EFF;
  margin-right: 8px;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.nav-menu {
  flex: 1;
  margin-left: 40px;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin: 0 8px;
  font-size: 14px;
  color: #606266;
}
</style> 