package com.bimowu.resume.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 字体工具类
 * 用于处理PDF生成中的中文字体支持
 */
@Component
@Slf4j
public class FontUtil {
    
    private static final Map<String, String> FONT_BASE64_CACHE = new HashMap<>();
    private static final Map<String, String> FONT_PATHS = new HashMap<String, String>() {{
        put("SimSun", "fonts/simsun.ttf");
        put("Microsoft YaHei", "fonts/simhei.ttf");
        put("SimHei", "fonts/simhei.ttf");
    }};
    
    /**
     * 读取输入流的所有字节内容
     * Java 8兼容版本，替代Java 9的InputStream.readAllBytes()
     * 
     * @param inputStream 输入流
     * @return 字节数组
     * @throws Exception 读取异常
     */
    private static byte[] readAllBytes(InputStream inputStream) throws Exception {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        int nRead;
        byte[] data = new byte[1024];
        while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        return buffer.toByteArray();
    }
    
    /**
     * 检查字体文件是否存在
     */
    public static boolean isFontAvailable(String fontName) {
        String fontPath = FONT_PATHS.get(fontName);
        if (fontPath == null) {
            return false;
        }
        
        try {
            ClassPathResource resource = new ClassPathResource(fontPath);
            return resource.exists();
        } catch (Exception e) {
            log.warn("检查字体文件失败: {}", fontName, e);
            return false;
        }
    }
    
    /**
     * 获取可用的中文字体CSS
     */
    public static String getChineseFontCSS() {
        StringBuilder css = new StringBuilder();
        
        // 尝试使用base64编码的字体（推荐方式）
        String base64Font = getBase64Font("SimSun");
        if (base64Font != null) {
            css.append("@font-face { ")
               .append("font-family: 'SimSun'; ")
               .append("src: url(data:font/truetype;charset=utf-8;base64,").append(base64Font).append(") format('truetype'); ")
               .append("font-weight: normal; ")
               .append("font-style: normal; ")
               .append("} ");
        } else {
            // 降级方案：使用文件路径
            if (isFontAvailable("SimSun")) {
                css.append("@font-face { ")
                   .append("font-family: 'SimSun'; ")
                   .append("src: url('classpath:fonts/simsun.ttf') format('truetype'); ")
                   .append("font-weight: normal; ")
                   .append("font-style: normal; ")
                   .append("} ");
            }
        }
        
        // 添加通用字体样式，使用更强的CSS选择器
        css.append("* { ")
           .append("font-family: ");
        
        // 按优先级添加字体
        if (base64Font != null || isFontAvailable("SimSun")) {
            css.append("'SimSun', ");
        }
        
        css.append("'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important; ")
           .append("font-size: 14px; ")
           .append("} ");
        
        // 添加更具体的选择器确保中文字体应用
        css.append("body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th { ")
           .append("font-family: ");
        
        if (base64Font != null || isFontAvailable("SimSun")) {
            css.append("'SimSun', ");
        }
        
        css.append("'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important; ")
           .append("} ");
        
        return css.toString();
    }
    
    /**
     * 获取字体的base64编码（禁用大字体文件的base64编码以避免内存溢出）
     */
    public static String getBase64Font(String fontName) {
        // 检查缓存
        if (FONT_BASE64_CACHE.containsKey(fontName)) {
            return FONT_BASE64_CACHE.get(fontName);
        }
        
        try {
            InputStream fontStream = getFontStream(fontName);
            if (fontStream == null) {
                return null;
            }
            
            // 先检查文件大小，避免读取过大的文件
            int availableBytes = fontStream.available();
            if (availableBytes > 1024 * 1024) { // 1MB限制，避免内存溢出
                log.warn("字体文件过大，不使用base64编码: {} bytes，将使用文件路径方式", availableBytes);
                fontStream.close();
                FONT_BASE64_CACHE.put(fontName, null); // 缓存null结果
                return null;
            }
            
            // 读取字体文件并转换为base64
            byte[] fontBytes = readAllBytes(fontStream);
            fontStream.close();
            
            String base64 = java.util.Base64.getEncoder().encodeToString(fontBytes);
            log.info("成功生成字体base64编码: {}, 原始大小: {} bytes, base64长度: {} 字符", 
                fontName, fontBytes.length, base64.length());
            
            // 缓存结果
            FONT_BASE64_CACHE.put(fontName, base64);
            return base64;
            
        } catch (OutOfMemoryError e) {
            log.error("字体base64编码时内存不足: {}，建议增加JVM内存或使用文件路径方式", fontName, e);
            FONT_BASE64_CACHE.put(fontName, null); // 缓存null结果
            return null;
        } catch (Exception e) {
            log.error("生成字体base64编码失败: {}", fontName, e);
            FONT_BASE64_CACHE.put(fontName, null); // 缓存null结果
            return null;
        }
    }
    
    /**
     * 获取字体文件输入流
     */
    public static InputStream getFontStream(String fontName) {
        String fontPath = FONT_PATHS.get(fontName);
        if (fontPath == null) {
            log.warn("未找到字体配置: {}", fontName);
            return null;
        }
        
        try {
            ClassPathResource resource = new ClassPathResource(fontPath);
            if (resource.exists()) {
                return resource.getInputStream();
            } else {
                log.warn("字体文件不存在: {}", fontPath);
                return null;
            }
        } catch (Exception e) {
            log.error("获取字体文件流失败: {}", fontName, e);
            return null;
        }
    }
    
    /**
     * 获取系统默认中文字体
     */
    public static String getDefaultChineseFont() {
        // 按优先级检查可用字体
        if (isFontAvailable("SimSun")) {
            return "SimSun";
        }
        if (isFontAvailable("Microsoft YaHei")) {
            return "Microsoft YaHei";
        }
        if (isFontAvailable("SimHei")) {
            return "SimHei";
        }
        
        // 如果没有找到字体文件，返回系统字体
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("windows")) {
            return "Microsoft YaHei, 微软雅黑, SimSun, 宋体";
        } else if (os.contains("mac")) {
            return "PingFang SC, Hiragino Sans GB, STSong";
        } else {
            return "WenQuanYi Micro Hei, Noto Sans CJK SC, Source Han Sans CN";
        }
    }
    
    /**
     * 生成字体回退CSS
     */
    public static String getFallbackFontCSS() {
        return "* { " +
               "font-family: " + getDefaultChineseFont() + ", Arial, sans-serif !important; " +
               "font-size: 14px; " +
               "}";
    }
    
    /**
     * 测试字体加载并输出调试信息
     */
    public static void testFontLoading() {
        log.info("=== 字体加载测试开始 ===");
        
        // 测试字体文件是否存在
        for (String fontName : FONT_PATHS.keySet()) {
            boolean available = isFontAvailable(fontName);
            log.info("字体 {} 是否可用: {}", fontName, available);
            
            if (available) {
                try {
                    InputStream stream = getFontStream(fontName);
                    if (stream != null) {
                        int available_bytes = stream.available();
                        stream.close();
                        log.info("字体 {} 文件大小: {} bytes", fontName, available_bytes);
                    }
                } catch (Exception e) {
                    log.error("读取字体 {} 失败", fontName, e);
                }
            }
        }
        
        // 测试base64编码
        String base64Font = getBase64Font("SimSun");
        if (base64Font != null) {
            log.info("SimSun字体base64编码成功，长度: {}", base64Font.length());
        } else {
            log.warn("SimSun字体base64编码失败");
        }
        
        // 输出生成的CSS
        String css = getChineseFontCSS();
        log.info("生成的中文字体CSS长度: {}", css.length());
        log.debug("生成的中文字体CSS内容: {}", css.length() > 500 ? css.substring(0, 500) + "..." : css);
        
        log.info("=== 字体加载测试完成 ===");
    }
}