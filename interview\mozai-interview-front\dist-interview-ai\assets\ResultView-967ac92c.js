import{d as U,n as V,r as a,H as y,m as j,E as c,c as r,a as e,b as u,w as d,k as m,j as b,s as x,u as B,P as N,e as v,o,g as O,Z as E,F as q,i as A,f as I,_ as F}from"./index-d8f61d92.js";const M={class:"result-container"},T={class:"result-content"},H={key:0,class:"waiting-result"},P={class:"waiting-icon"},Z={key:1,class:"result-summary"},z={class:"overall-score"},G={class:"score-circle"},J={class:"score-number"},K={class:"feedback-box"},Q={key:2,class:"interview-records"},W={key:0,class:"loading-container"},X={class:"answer-content"},Y={class:"transcription-section"},ee={class:"transcription-details"},te={class:"transcription-detail"},se={class:"answer-section"},ne={class:"answer-details"},oe={class:"answer-detail"},ie={class:"actions"},le=U({__name:"ResultView",setup(ae){const f=B(),i=V(),$=a("");a("");const g=a(!1),R=a([]);a(null),a(!1),a(0);const s=y(()=>i.interviewResults);y(()=>i.questions),y(()=>i.transcriptions);const C=async()=>{if(!i.interviewId){console.error("没有面试ID，无法获取转写记录");return}g.value=!0;try{console.log(`获取面试ID: ${i.interviewId} 的转写详情`);const n=await N.get(`/api/speech/transcriptions/${i.interviewId}`);n.data.success?(console.log("成功获取转写详情:",n.data.transcriptions),R.value=n.data.transcriptions||[]):(console.error("获取转写详情失败:",n.data.message),c.warning("获取转写详情失败: "+n.data.message))}catch(n){console.error("获取转写详情异常:",n),c.error("获取转写详情异常，请稍后再试")}finally{g.value=!1}},S=()=>{const n=`
AI面试评估报告
====================

总体评分: ${s.value.interview.overallScore}分

AI评价反馈:
${s.value.interview.feedback}

面试问答记录:
${Object.entries(s.value.questions).map(([p,k])=>`
问题${parseInt(p)+1}: ${k}
转写文本: ${s.value.transcriptions[p]||"未转写"}
`).join(`
`)}

报告生成时间: ${new Date().toLocaleString()}
  `,t=new Blob([n],{type:"text/plain"}),_=URL.createObjectURL(t),l=document.createElement("a");l.href=_,l.download=`面试报告_${new Date().toISOString().slice(0,10)}.txt`,document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(_),c.success("面试报告已下载")},D=()=>{i.resetInterview(),f.push("/")};return j(async()=>{if(!s.value.interview.id){c.warning("没有面试记录，请先完成面试"),f.push("/");return}if(s.value.interview.status!==1&&!s.value.interview.overallScore){c.warning("没有面试结果，请先完成面试"),f.push("/");return}s.value.interview.videoUrl&&($.value=s.value.interview.videoUrl),s.value.interview.status!==1&&await C()}),(n,t)=>{const _=v("el-icon"),l=v("el-skeleton"),p=v("el-collapse-item"),k=v("el-collapse"),h=v("el-button");return o(),r("div",M,[e("div",T,[t[9]||(t[9]=e("h1",null,"面试评估结果",-1)),s.value.interview.status===1?(o(),r("div",H,[e("div",P,[u(_,{class:"is-loading"},{default:d(()=>[u(O(E))]),_:1})]),t[0]||(t[0]=e("h3",null,"面试结果处理中",-1)),t[1]||(t[1]=e("p",null,"您的面试已完成，我们正在分析您的表现，请耐心等待结果...",-1))])):(o(),r("div",Z,[e("div",z,[e("div",G,[e("span",J,m(s.value.interview.overallScore),1)]),t[2]||(t[2]=e("h3",null,"总体评分",-1))]),e("div",K,[t[3]||(t[3]=e("h3",null,"AI 评价反馈",-1)),e("p",null,m(s.value.interview.feedback),1)])])),s.value.interview.status!==1?(o(),r("div",Q,[t[6]||(t[6]=e("h3",null,"面试问答记录",-1)),g.value?(o(),r("div",W,[u(l,{rows:5,animated:""})])):(o(),b(k,{key:1},{default:d(()=>[(o(!0),r(q,null,A(Object.entries(s.value.questions),(L,w)=>(o(),b(p,{key:w,title:`问题 ${w+1}: ${L[1]}`},{default:d(()=>[e("div",X,[e("div",Y,[t[4]||(t[4]=e("h4",null,"我的回答",-1)),e("div",ee,[e("div",te,[e("p",null,m(s.value.transcriptions[w]||"无回答内容"),1)])])]),e("div",se,[t[5]||(t[5]=e("h4",null,"参考答案",-1)),e("div",ne,[e("div",oe,[e("p",null,m(s.value.answers[w]||"无参考答案"),1)])])])])]),_:2},1032,["title"]))),128))]),_:1}))])):x("",!0),e("div",ie,[u(h,{onClick:S},{default:d(()=>t[7]||(t[7]=[I("下载面试报告",-1)])),_:1,__:[7]}),u(h,{type:"primary",onClick:D},{default:d(()=>t[8]||(t[8]=[I("开始新的面试",-1)])),_:1,__:[8]})])])])}}});const ce=F(le,[["__scopeId","data-v-07780768"]]);export{ce as default};
