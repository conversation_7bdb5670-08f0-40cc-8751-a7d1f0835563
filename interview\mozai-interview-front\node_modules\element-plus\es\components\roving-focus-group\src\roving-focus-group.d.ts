import type { ExtractPropTypes, StyleValue } from 'vue';
export declare const rovingFocusGroupProps: {
    style: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => StyleValue & {}) | (() => StyleValue) | ((new (...args: any[]) => StyleValue & {}) | (() => StyleValue))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    currentTabId: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => string & {}) | (() => string | null) | ((new (...args: any[]) => string & {}) | (() => string | null))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    defaultCurrentTabId: StringConstructor;
    loop: BooleanConstructor;
    dir: import("element-plus/es/utils").EpPropFinalized<StringConstructor, string, unknown, string, boolean>;
    orientation: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => ("horizontal" | "vertical" | undefined) & {}) | (() => "horizontal" | "vertical" | undefined) | ((new (...args: any[]) => ("horizontal" | "vertical" | undefined) & {}) | (() => "horizontal" | "vertical" | undefined))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    onBlur: FunctionConstructor;
    onFocus: FunctionConstructor;
    onMousedown: FunctionConstructor;
};
export declare type ElRovingFocusGroupProps = ExtractPropTypes<typeof rovingFocusGroupProps>;
declare const ElCollection: {
    name: string;
    setup(): void;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
    template?: string | object | undefined;
    render?: Function | undefined;
    components?: Record<string, import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>> | undefined;
    directives?: Record<string, import("vue").Directive<any, any>> | undefined;
    inheritAttrs?: boolean | undefined;
    emits?: (import("vue").EmitsOptions & ThisType<void>) | undefined;
    expose?: string[] | undefined;
    serverPrefetch?(): Promise<any>;
    compilerOptions?: import("vue").RuntimeCompilerOptions | undefined;
    call: (<T, A extends any[]>(this: new (...args: A) => T, thisArg: T, ...args: A) => void) & ((this: unknown, ...args: unknown[]) => never);
    __defaults?: {} | undefined;
    compatConfig?: (Partial<Record<import("vue").DeprecationTypes, boolean | "suppress-warning">> & {
        MODE?: 2 | 3 | ((comp: import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions> | null) => 2 | 3) | undefined;
    }) | undefined;
    data?: ((this: import("vue").CreateComponentPublicInstance<Readonly<ExtractPropTypes<{}>>, {}, {}, {}, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, Readonly<ExtractPropTypes<{}>>, {}, false, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<ExtractPropTypes<{}>>, {}, {}, {}, import("vue").MethodOptions, {}>, vm: import("vue").CreateComponentPublicInstance<Readonly<ExtractPropTypes<{}>>, {}, {}, {}, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, Readonly<ExtractPropTypes<{}>>, {}, false, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<ExtractPropTypes<{}>>, {}, {}, {}, import("vue").MethodOptions, {}>) => {}) | undefined;
    computed?: {} | undefined;
    methods?: {} | undefined;
    watch?: {
        [x: string]: (string | import("vue").WatchCallback<any, any> | ({
            handler: string | import("vue").WatchCallback<any, any>;
        } & import("vue").WatchOptions<boolean>)) | (string | import("vue").WatchCallback<any, any> | ({
            handler: string | import("vue").WatchCallback<any, any>;
        } & import("vue").WatchOptions<boolean>))[];
    } | undefined;
    provide?: import("vue").ComponentProvideOptions | undefined;
    inject?: (string[] | {
        [x: string]: string | symbol | {
            from?: string | symbol | undefined;
            default?: unknown;
        };
        [x: symbol]: string | symbol | {
            from?: string | symbol | undefined;
            default?: unknown;
        };
    }) | undefined;
    filters?: Record<string, Function> | undefined;
    mixins?: import("vue").ComponentOptionsMixin[] | undefined;
    extends?: import("vue").ComponentOptionsMixin | undefined;
    beforeCreate?(): void;
    created?(): void;
    beforeMount?(): void;
    mounted?(): void;
    beforeUpdate?(): void;
    updated?(): void;
    activated?(): void;
    deactivated?(): void;
    beforeDestroy?(): void;
    beforeUnmount?(): void;
    destroyed?(): void;
    unmounted?(): void;
    renderTracked?: ((e: import("vue").DebuggerEvent) => void) | undefined;
    renderTriggered?: ((e: import("vue").DebuggerEvent) => void) | undefined;
    errorCaptured?: ((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void) | undefined;
    delimiters?: [string, string] | undefined;
    __differentiator?: undefined;
    __isBuiltIn?: boolean | undefined;
    __file?: string | undefined;
    __name?: string | undefined;
    beforeRouteEnter?: import("vue-router").NavigationGuardWithThis<undefined> | undefined;
    beforeRouteUpdate?: import("vue-router").NavigationGuard | undefined;
    beforeRouteLeave?: import("vue-router").NavigationGuard | undefined;
    key?: string | number | symbol | undefined;
    ref?: import("vue").VNodeRef | undefined;
    ref_for?: boolean | undefined;
    ref_key?: string | undefined;
    onVnodeBeforeMount?: ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void) | ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void)[] | undefined;
    onVnodeMounted?: ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void) | ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void)[] | undefined;
    onVnodeBeforeUpdate?: ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, oldVNode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void) | ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, oldVNode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void)[] | undefined;
    onVnodeUpdated?: ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, oldVNode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void) | ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, oldVNode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void)[] | undefined;
    onVnodeBeforeUnmount?: ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void) | ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void)[] | undefined;
    onVnodeUnmounted?: ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void) | ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void)[] | undefined;
    class?: unknown;
    style?: unknown;
}, ElCollectionItem: {
    name: string;
    setup(_: unknown, { attrs }: import("vue").SetupContext<import("vue").EmitsOptions>): void;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
    template?: string | object | undefined;
    render?: Function | undefined;
    components?: Record<string, import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>> | undefined;
    directives?: Record<string, import("vue").Directive<any, any>> | undefined;
    inheritAttrs?: boolean | undefined;
    emits?: (import("vue").EmitsOptions & ThisType<void>) | undefined;
    expose?: string[] | undefined;
    serverPrefetch?(): Promise<any>;
    compilerOptions?: import("vue").RuntimeCompilerOptions | undefined;
    call: (<T, A extends any[]>(this: new (...args: A) => T, thisArg: T, ...args: A) => void) & ((this: unknown, ...args: unknown[]) => never);
    __defaults?: {} | undefined;
    compatConfig?: (Partial<Record<import("vue").DeprecationTypes, boolean | "suppress-warning">> & {
        MODE?: 2 | 3 | ((comp: import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions> | null) => 2 | 3) | undefined;
    }) | undefined;
    data?: ((this: import("vue").CreateComponentPublicInstance<Readonly<ExtractPropTypes<{}>>, {}, {}, {}, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, Readonly<ExtractPropTypes<{}>>, {}, false, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<ExtractPropTypes<{}>>, {}, {}, {}, import("vue").MethodOptions, {}>, vm: import("vue").CreateComponentPublicInstance<Readonly<ExtractPropTypes<{}>>, {}, {}, {}, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, Readonly<ExtractPropTypes<{}>>, {}, false, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<ExtractPropTypes<{}>>, {}, {}, {}, import("vue").MethodOptions, {}>) => {}) | undefined;
    computed?: {} | undefined;
    methods?: {} | undefined;
    watch?: {
        [x: string]: (string | import("vue").WatchCallback<any, any> | ({
            handler: string | import("vue").WatchCallback<any, any>;
        } & import("vue").WatchOptions<boolean>)) | (string | import("vue").WatchCallback<any, any> | ({
            handler: string | import("vue").WatchCallback<any, any>;
        } & import("vue").WatchOptions<boolean>))[];
    } | undefined;
    provide?: import("vue").ComponentProvideOptions | undefined;
    inject?: (string[] | {
        [x: string]: string | symbol | {
            from?: string | symbol | undefined;
            default?: unknown;
        };
        [x: symbol]: string | symbol | {
            from?: string | symbol | undefined;
            default?: unknown;
        };
    }) | undefined;
    filters?: Record<string, Function> | undefined;
    mixins?: import("vue").ComponentOptionsMixin[] | undefined;
    extends?: import("vue").ComponentOptionsMixin | undefined;
    beforeCreate?(): void;
    created?(): void;
    beforeMount?(): void;
    mounted?(): void;
    beforeUpdate?(): void;
    updated?(): void;
    activated?(): void;
    deactivated?(): void;
    beforeDestroy?(): void;
    beforeUnmount?(): void;
    destroyed?(): void;
    unmounted?(): void;
    renderTracked?: ((e: import("vue").DebuggerEvent) => void) | undefined;
    renderTriggered?: ((e: import("vue").DebuggerEvent) => void) | undefined;
    errorCaptured?: ((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void) | undefined;
    delimiters?: [string, string] | undefined;
    __differentiator?: undefined;
    __isBuiltIn?: boolean | undefined;
    __file?: string | undefined;
    __name?: string | undefined;
    beforeRouteEnter?: import("vue-router").NavigationGuardWithThis<undefined> | undefined;
    beforeRouteUpdate?: import("vue-router").NavigationGuard | undefined;
    beforeRouteLeave?: import("vue-router").NavigationGuard | undefined;
    key?: string | number | symbol | undefined;
    ref?: import("vue").VNodeRef | undefined;
    ref_for?: boolean | undefined;
    ref_key?: string | undefined;
    onVnodeBeforeMount?: ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void) | ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void)[] | undefined;
    onVnodeMounted?: ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void) | ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void)[] | undefined;
    onVnodeBeforeUpdate?: ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, oldVNode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void) | ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, oldVNode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void)[] | undefined;
    onVnodeUpdated?: ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, oldVNode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void) | ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, oldVNode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void)[] | undefined;
    onVnodeBeforeUnmount?: ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void) | ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void)[] | undefined;
    onVnodeUnmounted?: ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void) | ((vnode: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>) => void)[] | undefined;
    class?: unknown;
    style?: unknown;
}, COLLECTION_INJECTION_KEY: import("vue").InjectionKey<import("element-plus/es/components/collection").ElCollectionInjectionContext>, COLLECTION_ITEM_INJECTION_KEY: import("vue").InjectionKey<import("element-plus/es/components/collection").ElCollectionItemInjectionContext>;
export { ElCollection, ElCollectionItem, COLLECTION_INJECTION_KEY as ROVING_FOCUS_COLLECTION_INJECTION_KEY, COLLECTION_ITEM_INJECTION_KEY as ROVING_FOCUS_ITEM_COLLECTION_INJECTION_KEY, };
