<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 12px;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 20px;
            color: #777777;
            line-height: 1.5;
            font-size: 12px;
            background: white;
        }

        .resume-template {
            width: 100%;
            max-width: 800px;
            background: white;
            color: #777777;
            font-family: inherit !important;
            margin: 0 auto;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif !important;
            }
        }

        .header-content {
            margin-bottom: 15px;
            padding-bottom: 8px;
            background: white;
            /* border-bottom: 1px solid #e0e0e0;  // 去掉分割线 */
            padding-bottom: 15px;
        }

        .resume-title {
            font-size: 24px;
            font-weight: bold;
            color: #000000;
            margin-bottom: 15px;
            text-align: left;
        }

        /* 头部区域整体布局 */
        .header-content {
            position: relative;
            min-height: 133px;
            /* border-bottom: 1px solid #e0e0e0;  // 去掉分割线 */
        }

        /* 个人信息区域 - 不再需要与头像并排 */
        .info-photo-section {
            position: relative;
            width: 100%;
        }
        .basic-info-section {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
        }
        .info-item {
            margin-bottom: 8px;
            white-space: nowrap;
            font-size: 14px;
            line-height: 1.4;
            display: inline-block;
            margin-right: 20px;
        }

        .info-label {
            font-weight: normal;
            margin-right: 3px;
            color: #000000;
        }

        .info-value {
            color: #000000;
        }

        /* 头像容器 - 绝对定位到右上角 */
        .photo-container {
            width: 100px;
            min-width: 100px;
            text-align: center;
            border: none;
            padding: 0;
            overflow: visible;
            position: absolute;
            top: 0;
            right: 0;
            display: flex;
            align-items: flex-start;
            justify-content: flex-end;
            margin-top: 0;
            margin-right: 0;
            margin-bottom: 0;
        }
        .photo {
            width: 100px;
            height: 133px;
            display: block;
            object-fit: cover;
            object-position: center;
            border-radius: 3px;
            border: 1px solid #e0e0e0;
        }

        .resume-section {
            margin-bottom: 18px;
        }

        .section-header {
            margin-bottom: 12px;
            background-color: #ffffff;
            padding: 0;
            display: flex;
            align-items: center;
        }

        .section-header h2 {
            font-size: 14px;
            color: #FF0000;
            margin: 0;
            font-weight: bold;
            flex-grow: 1;
            border-bottom: 1px solid #FF0000;
            padding-bottom: 2px;
        }

        .evaluation-content,
        .certificate-content,
        .campus-content,
        .interests-content,
        .work-content,
        .project-content,
        .skills-content,
        .education-content {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-left: 0;
        }

        .work-item,
        .education-item,
        .project-item,
        .practice-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
            margin-bottom: 8px;
        }

        .project-header,
        .work-header,
        .edu-header,
        .practice-header {
            display: table;
            width: 100%;
            margin-bottom: 5px;
            table-layout: fixed;
        }

        .edu-date,
        .work-time,
        .project-time,
        .practice-time {
            display: table-cell;
            color: #333;
            font-size: 12px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 33.33%;
            text-align: left;
            vertical-align: middle;
        }

        .edu-school,
        .work-company,
        .project-name,
        .practice-name {
            display: table-cell;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #333;
            font-size: 12px;
            width: 33.33%;
            text-align: center;
            vertical-align: middle;
        }

        .edu-degree,
        .work-position,
        .project-role,
        .practice-role {
            display: table-cell;
            color: #333;
            font-size: 12px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 33.33%;
            text-align: right;
            vertical-align: middle;
        }

        .edu-info {
            color: #777777;
            font-size: 12px;
            margin-top: 2px;
        }

        .edu-courses {
            margin-top: 0;
            line-height: 1.4;
        }

        .edu-courses p {
            margin: 0 !important;
            padding: 0 !important;
            display: inline;
        }

        .courses-label {
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #333;
            font-size: 12px;
            margin-right: 0;
            display: inline;
        }

        .skills-description,
        .project-description,
        .work-description {
            line-height: 1.6;
            text-align: justify;
            margin-left: 0;
            font-size: 12px;
            color: #777777;
        }

        .work-description ul,
        .project-description ul {
            margin: 3px 0;
            padding-left: 15px;
        }

        .work-description li,
        .project-description li {
            margin-bottom: 2px;
            font-size: 12px;
        }

        .project-item {
            margin-bottom: 12px;
        }

        .project-url {
            font-weight: normal;
            color: #0066cc;
            text-decoration: underline;
        }

        .skill-item {
            margin-bottom: 10px;
        }

        .skill-name {
            font-weight: bold;
            margin-bottom: 2px;
            font-size: 12px;
        }

        .skill-description {
            color: #777777;
            line-height: 1.6;
            font-size: 12px;
        }

        .certificate-content ul,
        .campus-content ul,
        .interests-content ul,
        .evaluation-content ul {
            margin: 3px 0;
            padding-left: 15px;
        }

        .certificate-content li,
        .campus-content li,
        .interests-content li,
        .evaluation-content li {
            margin-bottom: 2px;
            font-size: 12px;
        }

        .certificate-content p,
        .campus-content p,
        .interests-content p,
        .evaluation-content p {
            margin: 0 0 3px 0;
            font-size: 12px;
            line-height: 1.6;
            color: #777777;
        }

        /* 加粗文本样式 - 支持**文本**格式 */
        .bold-text {
            font-weight: bold !important;
        }

        /* 通用加粗样式 - 使用SourceHanSerifSC-Bold字体，纯黑色突出显示 */
        strong, b,
        .work-description strong,
        .project-description strong,
        .campus-content strong,
        .interests-content strong,
        .self-evaluation-content strong {
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #000000 !important;
        }
        /* 教育经历标题样式 */
        .education-section-title {
            font-size: 14px;
            font-weight: bold;
            color: #111;
            margin: 0 0 8px 0;
            padding: 0;
            line-height: 1.2;
        }
        .education-section-title-bar {
            height: 1px;
            background: #ff2222;
            border: none;
            margin: 0 0 10px 0;
            width: 100%;
        }
        .education-item-header {
            display: table;
            width: 100%;
            margin-bottom: 5px;
            table-layout: fixed;
        }
        .education-school {
            display: table-cell;
            font-size: 12px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #333;
            width: 33.33%;
            text-align: left;
            vertical-align: middle;
        }
        .education-major {
            display: table-cell;
            font-size: 12px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #333;
            width: 33.33%;
            text-align: center;
            vertical-align: middle;
        }
        .education-time {
            display: table-cell;
            font-size: 12px;
            color: #333;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 33.33%;
            text-align: right;
            vertical-align: middle;
        }
        .edu-courses {
            margin-top: 0;
            font-size: 14px;
            color: #444;
            line-height: 1.6;
            white-space: normal;
        }
        .edu-courses .courses-label {
            font-weight: bold;
            color: #222;
            margin-right: 4px;
            display: inline;
        }
        .edu-courses p {
            display: inline;
            margin: 0;
            padding: 0;
        }
        /* 专业技能标题样式 */
        .skills-section-title {
            font-size: 14px;
            font-weight: bold;
            color: #111;
            margin: 0 0 8px 0;
            padding: 0;
            line-height: 1.2;
        }
        .skills-section {
            margin-top: 18px; /* 1.5倍行距，12px * 1.5 = 18px */
        }
        .skills-section-title-bar {
            height: 1px;
            background: #ff2222;
            border: none;
            margin: 0 0 10px 0;
            width: 100%;
        }
        .skills-content {
            margin-top: 0;
            font-size: 12px;
            color: #777777;
            line-height: 1.6;
        }
        .skill-item {
            margin-bottom: 8px;
            line-height: 1.6;
        }
        .skill-item:last-child {
            margin-bottom: 0;
        }
        /* 项目经验标题样式 */
        .projects-section-title {
            font-size: 14px;
            font-weight: bold;
            color: #111;
            margin: 0 0 8px 0;
            padding: 0;
            line-height: 1.2;
        }
        .projects-section-title-bar {
            height: 1px;
            background: #ff9900;
            border: none;
            margin: 0 0 10px 0;
            width: 100%;
        }
        .projects-content {
            margin-top: 0;
        }
        .project-item-header {
            display: table;
            width: 100%;
            margin-bottom: 5px;
            table-layout: fixed;
        }
        .project-item-header .project-time {
            display: table-cell;
            color: #333;
            font-size: 12px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 33.33%;
            text-align: left;
            vertical-align: middle;
        }
        .project-item-header .project-name {
            display: table-cell;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #333;
            font-size: 12px;
            width: 33.33%;
            text-align: center;
            vertical-align: middle;
        }
        .project-item-header .project-role {
            display: table-cell;
            color: #333;
            font-size: 12px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 33.33%;
            text-align: right;
            vertical-align: middle;
        }
        .project-description {
            line-height: 1.6;
            text-align: justify;
            margin-left: 0;
            font-size: 12px;
            color: #777777;
        }
        .project-description ul {
            margin: 3px 0;
            padding-left: 15px;
        }
        .project-description li {
            margin-bottom: 2px;
            font-size: 12px;
        }
        .project-description strong {
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #000000 !important;
        }
        /* 练手项目标题样式 */
        .practices-section-title {
            font-size: 14px;
            font-weight: bold;
            color: #111;
            margin: 0 0 8px 0;
            padding: 0;
            line-height: 1.2;
        }
        .practices-section-title-bar {
            height: 1px;
            background: #0066cc;
            border: none;
            margin: 0 0 10px 0;
            width: 100%;
        }
        .practices-content {
            margin-top: 0;
        }
        .practice-item-header {
            display: table;
            width: 100%;
            margin-bottom: 5px;
            table-layout: fixed;
        }
        .practice-item-header .practice-tech {
            display: table-cell;
            color: #333;
            font-size: 12px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 33.33%;
            text-align: left;
            vertical-align: middle;
        }
        .practice-item-header .practice-name {
            display: table-cell;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #333;
            font-size: 12px;
            width: 33.33%;
            text-align: center;
            vertical-align: middle;
        }
        .practice-item-header .practice-time {
            display: table-cell;
            color: #333;
            font-size: 12px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 33.33%;
            text-align: right;
            vertical-align: middle;
        }
        .practice-description {
            line-height: 1.6;
            text-align: justify;
            margin-left: 0;
            font-size: 12px;
            color: #777777;
        }
        .practice-description ul {
            margin: 3px 0;
            padding-left: 15px;
        }
        .practice-description li {
            margin-bottom: 2px;
            font-size: 12px;
        }
        .practice-description strong {
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #000000 !important;
        }
        /* 工作经验标题样式 */
        .work-section-title {
            font-size: 14px;
            font-weight: bold;
            color: #111;
            margin: 0 0 8px 0;
            padding: 0;
            line-height: 1.2;
        }
        .work-section-title-bar {
            height: 1px;
            background: #00cc66;
            border: none;
            margin: 0 0 10px 0;
            width: 100%;
        }
        .work-content {
            margin-top: 0;
        }
        .work-item-header {
            display: table;
            width: 100%;
            margin-bottom: 5px;
            table-layout: fixed;
        }
        .work-item-header .work-position {
            display: table-cell;
            color: #333;
            font-size: 12px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 33.33%;
            text-align: left;
            vertical-align: middle;
        }
        .work-item-header .work-company {
            display: table-cell;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #333;
            font-size: 12px;
            width: 33.33%;
            text-align: center;
            vertical-align: middle;
        }
        .work-item-header .work-time {
            display: table-cell;
            color: #333;
            font-size: 12px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 33.33%;
            text-align: right;
            vertical-align: middle;
        }
        .work-description {
            line-height: 1.6;
            text-align: justify;
            margin-left: 0;
            font-size: 12px;
            color: #777777;
        }
        .work-description ul {
            margin: 3px 0;
            padding-left: 15px;
        }
        .work-description li {
            margin-bottom: 2px;
            font-size: 12px;
        }
        .work-description strong {
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #000000 !important;
        }
        /* 个人评价标题样式 */
        .self-evaluation-section-title {
            font-size: 14px;
            font-weight: bold;
            color: #111;
            margin: 0 0 8px 0;
            padding: 0;
            line-height: 1.2;
        }
        .self-evaluation-section-title-bar {
            height: 1px;
            background: #9966cc;
            border: none;
            margin: 0 0 10px 0;
            width: 100%;
        }
        .self-evaluation-content {
            margin-top: 0;
            font-size: 12px;
            color: #777777;
            line-height: 1.6;
            text-align: justify;
        }
        .self-evaluation-content strong {
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #000000 !important;
        }
        /* 荣誉证书标题样式 */
        .certificates-section-title {
            font-size: 14px;
            font-weight: bold;
            color: #111;
            margin: 0 0 8px 0;
            padding: 0;
            line-height: 1.2;
        }
        .certificates-section-title-bar {
            height: 1px;
            background: #ff2222;
            border: none;
            margin: 0 0 10px 0;
            width: 100%;
        }
        .certificates-content {
            margin-top: 0;
            font-size: 12px;
            color: #777777;
            line-height: 1.6;
            text-align: justify;
        }
        .certificates-content strong {
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #000000 !important;
        }
    </style>
</head>
<body>
<div class="resume-template template-2">
    <div class="resume-section">
        <!-- 简历头部 -->
        <div class="header-content">
            <!-- 头像容器，绝对定位右上角 -->
            <div class="photo-container">
                ${avatar}
            </div>
            <!-- 姓名单独一行 -->
            <div class="resume-title">
                ${name}
            </div>
            <!-- 个人信息区域 -->
            <div class="info-photo-section">
                <div class="basic-info-section">
                    <div class="info-item">
                        <span class="info-label">性别:</span>
                        <span class="info-value">${gender}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">年龄:</span>
                        <span class="info-value">${age}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">电话:</span>
                        <span class="info-value">${phone}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">邮箱:</span>
                        <span class="info-value">${email}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 教育经历 -->
    ${education}

    <!-- 专业技能 -->
    ${skills}
    
    <!-- 项目经验 -->
    ${projects}
    <!-- 练手项目 -->
    ${practices}
    <!-- 工作经验 -->
    ${work}
    <!-- 自我评价 -->
    ${selfEvaluation}
    <!-- 证书奖项 -->
    ${certificates}

</div>
</body>
</html> 