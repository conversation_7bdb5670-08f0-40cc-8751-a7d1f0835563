package com.bimowu.resume.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bimowu.resume.common.dao.*;
import com.bimowu.resume.common.service.ResumeService;
import com.bimowu.resume.dto.ResumeFullSaveDto;
import com.bimowu.resume.entity.Resume;
import com.bimowu.resume.entity.ResumeCategoryRelation;
import com.bimowu.resume.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 简历主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
@Slf4j
public class ResumeServiceImpl extends ServiceImpl<ResumeMapper, Resume> implements ResumeService {

    @Autowired
    private ResumeMapper resumeMapper;
    @Autowired
    private ResumeInformationMapper informationMapper;
    @Autowired
    private ResumeEducationalMapper educationalMapper;
    @Autowired
    private ResumeWorkMapper workMapper;
    @Autowired
    private ResumeProjectExperienceMapper projectMapper;
    @Autowired
    private ResumePracticeMapper practiceMapper;
    @Autowired
    private ResumeTalentMapper talentMapper;
    @Autowired
    private ResumeCertificateMapper certificateMapper;
    @Autowired
    private ResumeCampusMapper campusMapper;
    @Autowired
    private ResumeInterestMapper interestMapper;
    @Autowired
    private ResumeEvaluateMapper evaluateMapper;
    @Autowired
    private ResumeCategoryRelationMapper categoryRelationMapper;

    public List<ResumeFullSaveDto> getFullResumeListByUserId(Long userId) {
        List<Resume> resumes = resumeMapper.selectList(
                new QueryWrapper<Resume>().eq("user_id", userId).eq("is_delete", 0)
        );
        List<ResumeFullSaveDto> result = new ArrayList<>();
        for (Resume resume : resumes) {
            ResumeFullSaveDto dto = new ResumeFullSaveDto();
            ResumeVo resumeVo = new ResumeVo();
            BeanUtils.copyProperties(resume,resumeVo);
            // 简历类型
            QueryWrapper<ResumeCategoryRelation> query = new QueryWrapper<>();
            query.eq("user_id", userId);
            query.eq("resume_id", resume.getResumeId());
            ResumeCategoryRelation resumeCategoryRelation = categoryRelationMapper.selectOne(query);
            if (resumeCategoryRelation != null) {
                resumeVo.setCategory(resumeCategoryRelation.getCatId());
                dto.setResumeVo(resumeVo);
            }
            // 基本信息
            ResumeInformationVo info = informationMapper.selectByResumeId(resume.getResumeId());
            dto.setInformation(info);
            // 教育经历
            dto.setEducationList(educationalMapper.selectByResumeId(resume.getResumeId()));
            // 工作经验
            dto.setWorkList(workMapper.selectByResumeId(resume.getResumeId()));
            // 项目经验
            dto.setProjectList(projectMapper.selectByResumeId(resume.getResumeId()));
            // 练手项目
            dto.setPracticeList(practiceMapper.selectByResumeId(resume.getResumeId()));
            // 技能特长
            dto.setTalentList(talentMapper.selectByResumeId(resume.getResumeId()));
            // 证书奖项
            dto.setCertificate(certificateMapper.selectByResumeId(resume.getResumeId()));
            // 校园经历
            dto.setCampus(campusMapper.selectByResumeId(resume.getResumeId()));
            // 兴趣爱好
            dto.setInterest(interestMapper.selectByResumeId(resume.getResumeId()));
            // 自我评价
            dto.setEvaluate(evaluateMapper.selectByResumeId(resume.getResumeId()));

            result.add(dto);
        }
        return result;
    }
    @Override
    public ResumeFullSaveDto getResumeDetail(Long resumeId, Long userId) {
        log.info("开始获取简历详情 - resumeId: {}, userId: {}", resumeId, userId);
        
        Resume resume = resumeMapper.selectOne(new QueryWrapper<Resume>().eq("user_id", userId).eq("resume_id",resumeId).eq("is_delete", 0));
        if (resume == null) {
            log.warn("未找到简历记录 - resumeId: {}, userId: {}", resumeId, userId);
            return null;
        }
        
        log.info("找到简历记录 - resumeId: {}, title: {}, templateId: {}", 
            resume.getResumeId(), resume.getTitle(), resume.getTemplateId());
        
        ResumeFullSaveDto dto = new ResumeFullSaveDto();
        ResumeVo resumeVo = new ResumeVo();
        BeanUtils.copyProperties(resume,resumeVo);
        
        // 确保templateId正确设置
        if (resume.getTemplateId() != null) {
            resumeVo.setTemplateId(resume.getTemplateId());
        } else {
            resumeVo.setTemplateId(1L); // 默认模板ID为1
        }
        
        // 简历类型
        QueryWrapper<ResumeCategoryRelation> query = new QueryWrapper<>();
        query.eq("user_id", userId);
        query.eq("resume_id", resumeId);
        ResumeCategoryRelation resumeCategoryRelation = categoryRelationMapper.selectOne(query);
        if (resumeCategoryRelation != null) {
            resumeVo.setCategory(resumeCategoryRelation.getCatId());
        }
        dto.setResumeVo(resumeVo);
        
        // 基本信息
        ResumeInformationVo info = informationMapper.selectByResumeId(resume.getResumeId());
        dto.setInformation(info);
        log.info("基本信息获取完成 - 姓名: {}, 电话: {}, 邮箱: {}", 
            info != null ? info.getName() : "null",
            info != null ? info.getPhone() : "null",
            info != null ? info.getEmail() : "null");
        
        // 教育经历
        List<ResumeEducationalVo> educationList = educationalMapper.selectByResumeId(resume.getResumeId());
        dto.setEducationList(educationList);
        log.info("教育经历获取完成 - 数量: {}", educationList != null ? educationList.size() : 0);
        if (educationList != null && !educationList.isEmpty()) {
            for (ResumeEducationalVo edu : educationList) {
                log.debug("教育经历 - 学校: {}, 专业: {}, 学历: {}", edu.getSchool(), edu.getMajor(), edu.getEducation());
            }
        }
        
        // 工作经验
        List<ResumeWorkVo> workList = workMapper.selectByResumeId(resume.getResumeId());
        dto.setWorkList(workList);
        log.info("工作经验获取完成 - 数量: {}", workList != null ? workList.size() : 0);
        if (workList != null && !workList.isEmpty()) {
            for (ResumeWorkVo work : workList) {
                log.debug("工作经验 - 公司: {}, 职位: {}", work.getCompany(), work.getPosition());
            }
        }
        
        // 项目经验
        List<ResumeProjectExperienceVo> projectList = projectMapper.selectByResumeId(resume.getResumeId());
        dto.setProjectList(projectList);
        log.info("项目经验获取完成 - 数量: {}", projectList != null ? projectList.size() : 0);
        if (projectList != null && !projectList.isEmpty()) {
            for (ResumeProjectExperienceVo project : projectList) {
                log.debug("项目经验 - 项目名: {}, 角色: {}", project.getProjectName(), project.getRole());
            }
        }
        
        // 练手项目
        List<ResumePracticeVo> practiceList = practiceMapper.selectByResumeId(resume.getResumeId());
        dto.setPracticeList(practiceList);
        log.info("练手项目获取完成 - 数量: {}", practiceList != null ? practiceList.size() : 0);
        
        // 技能特长
        List<ResumeTalentVo> talentList = talentMapper.selectByResumeId(resume.getResumeId());
        dto.setTalentList(talentList);
        log.info("技能特长获取完成 - 数量: {}", talentList != null ? talentList.size() : 0);
        if (talentList != null && !talentList.isEmpty()) {
            for (ResumeTalentVo talent : talentList) {
                log.debug("技能特长 - 技能名: {}, 描述长度: {}", 
                    talent.getSkillName(), 
                    talent.getSkillDescription() != null ? talent.getSkillDescription().length() : 0);
            }
        }
        
        // 证书奖项
        ResumeCertificateVo certificate = certificateMapper.selectByResumeId(resume.getResumeId());
        dto.setCertificate(certificate);
        log.info("证书奖项获取完成 - 内容长度: {}", 
            certificate != null && certificate.getCertificateName() != null ? certificate.getCertificateName().length() : 0);
        
        // 校园经历
        ResumeCampusVo campus = campusMapper.selectByResumeId(resume.getResumeId());
        dto.setCampus(campus);
        log.info("校园经历获取完成 - 内容长度: {}", 
            campus != null && campus.getCampusExperience() != null ? campus.getCampusExperience().length() : 0);
        
        // 兴趣爱好
        ResumeInterestVo interest = interestMapper.selectByResumeId(resume.getResumeId());
        dto.setInterest(interest);
        log.info("兴趣爱好获取完成 - 内容长度: {}", 
            interest != null && interest.getInterest() != null ? interest.getInterest().length() : 0);
        
        // 自我评价
        ResumeEvaluateVo evaluate = evaluateMapper.selectByResumeId(resume.getResumeId());
        dto.setEvaluate(evaluate);
        log.info("自我评价获取完成 - 内容长度: {}", 
            evaluate != null && evaluate.getSelfEvaluation() != null ? evaluate.getSelfEvaluation().length() : 0);

        log.info("简历详情获取完成 - resumeId: {}", resumeId);
        return dto;
    }
}
