<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.interview.dao.SysPropertyDao">

	<resultMap id="SysProperty" type="com.bimowu.interview.model.SysProperty" >
		<result column="id" property="id"/>
		<result column="sys_key" property="sysKey"/>
		<result column="sys_type" property="sysType"/>
		<result column="sys_value" property="sysValue"/>
		<result column="state" property="state"/>
		<result column="memo" property="memo"/>
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		id,
		sys_key,
		sys_type,
		sys_value,
		state,
		memo
	</sql>

	<!-- 查询（根据主键ID查询） -->
	<select id="selectByPrimaryKey" resultMap="SysProperty" parameterType="java.lang.Long">
		 SELECT
		 <include refid="Base_Column_List" />
		 FROM sys_property
		 WHERE id = #{id}
	</select>

	<!--删除：根据主键ID删除-->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		 DELETE FROM sys_property
		 WHERE id = #{id}
	</delete>

	<!-- 添加 -->
	<insert id="insert" parameterType="com.bimowu.interview.model.SysProperty">
		 INSERT INTO sys_property
 		(
			 id,
			 sys_key,
			 sys_type,
			 sys_value,
			 state,
			 memo
		) 
		 VALUES 
 		(
			 #{id},
			 #{sysKey},
			 #{sysType},
			 #{sysValue},
			 #{state},
			 #{memo}
 		) 
		 <selectKey keyProperty="id" resultType="Long" order="AFTER">
			 select LAST_INSERT_ID()
		 </selectKey>
	</insert>

	<!-- 修 改-->
	<update id="updateByPrimaryKeySelective" parameterType="com.bimowu.interview.model.SysProperty">
		 UPDATE sys_property
 		 <set> 
			<if test="sysKey != null and sysKey != ''">
				 sys_key = #{sysKey},
			</if>
			<if test="sysType != null and sysType != ''">
				 sys_type = #{sysType},
			</if>
			<if test="sysValue != null and sysValue != ''">
				 sys_value = #{sysValue},
			</if>
			<if test="state != null">
				 state = #{state},
			</if>
			<if test="memo != null and memo != ''">
				 memo = #{memo},
			</if>

 		 </set>
		 WHERE id = #{id}
	</update>

	<!-- list4Page 分页查询-->
	<select id="list4Page" resultMap="SysProperty">
		 SELECT 
		 <include refid="Base_Column_List" />
		 from sys_property
 		 where 1=1  
		<if test="record.id != null">
			 and id = #{record.id} 
		</if>
		<if test="record.sysKey != null and record.sysKey != ''">
			 and sys_key = #{record.sysKey} 
		</if>
		<if test="record.sysType != null and record.sysType != ''">
			 and sys_type = #{record.sysType} 
		</if>
		<if test="record.sysValue != null and record.sysValue != ''">
			 and sys_value = #{record.sysValue} 
		</if>
		<if test="record.state != null">
			 and state = #{record.state} 
		</if>
		<if test="record.memo != null and record.memo != ''">
			 and memo = #{record.memo} 
		</if>
		<if test="commonQueryParam != null">
			<if test="commonQueryParam.start != null  and commonQueryParam.pageSize != null">
				 limit #{commonQueryParam.start}, #{commonQueryParam.pageSize}
			</if>
		</if>
	</select>
	<!-- count 总数-->
	<select id="count" resultType="long">
		 SELECT 
		 count(1) 
		 from sys_property
 		 where 1=1  
		<if test="record.id != null">
			 and id = #{record.id} 
		</if>
		<if test="record.sysKey != null and record.sysKey != ''">
			 and sys_key = #{record.sysKey} 
		</if>
		<if test="record.sysType != null and record.sysType != ''">
			 and sys_type = #{record.sysType} 
		</if>
		<if test="record.sysValue != null and record.sysValue != ''">
			 and sys_value = #{record.sysValue} 
		</if>
		<if test="record.state != null">
			 and state = #{record.state} 
		</if>
		<if test="record.memo != null and record.memo != ''">
			 and memo = #{record.memo} 
		</if>
	</select>
	<!-- list 查询-->
	<select id="list" resultMap="SysProperty">
		 SELECT 
		 <include refid="Base_Column_List" />
		 from sys_property
 		 where 1=1  
		<if test="record.id != null">
			 and id = #{record.id} 
		</if>
		<if test="record.sysKey != null and record.sysKey != ''">
			 and sys_key = #{record.sysKey} 
		</if>
		<if test="record.sysType != null and record.sysType != ''">
			 and sys_type = #{record.sysType} 
		</if>
		<if test="record.sysValue != null and record.sysValue != ''">
			 and sys_value = #{record.sysValue} 
		</if>
		<if test="record.state != null">
			 and state = #{record.state} 
		</if>
		<if test="record.memo != null and record.memo != ''">
			 and memo = #{record.memo} 
		</if>
	</select>
</mapper>