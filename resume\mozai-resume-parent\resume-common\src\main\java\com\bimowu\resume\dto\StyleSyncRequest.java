package com.bimowu.resume.dto;

import lombok.Data;

import java.util.List;

/**
 * 样式同步请求DTO
 */
@Data
public class StyleSyncRequest {
    
    /**
     * 模板ID
     */
    private Integer templateId;
    
    /**
     * 批量模板ID列表
     */
    private List<Integer> templateIds;
    
    /**
     * 同步类型（FULL-全量同步, INCREMENTAL-增量同步）
     */
    private String syncType = "FULL";
    
    /**
     * 是否强制同步（忽略时间间隔限制）
     */
    private boolean forceSync = false;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 同步备注
     */
    private String remark;
    
    /**
     * 是否异步执行
     */
    private boolean async = false;
    
    /**
     * 回调URL（异步执行时使用）
     */
    private String callbackUrl;
}