server:
  port: 7093
  tomcat:
    max-connections: 10000
    max-threads: 10000
    connection-timeout: 120000 # 设置Tomcat连接超时为120秒
  servlet:
    context-path: /interview
spring:
  profiles:
    active: test
  aop:
    proxy-target-class: false  # 使用 JDK 动态代理而不是 CGLIB
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      location: ${java.io.tmpdir}   # 使用系统临时目录
mybatis-plus:
  mapper-locations: classpath:/mappers/**/*.xml
  type-aliases-package: com.bimowu.interview.common.model
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: is_delete # 全局逻辑删除字段名
      logic-delete-value: 1 # 逻辑已删除值
      logic-not-delete-value: 0 # 逻辑未删除值
oss:
  endpoint: oss-cn-beijing.aliyuncs.com
  accessKeyId: LTAI5tLmkL7ebnMBvdmZ1eF7
  accessKeySecret: ******************************
  bucketName: bmw-pic

# AI模型配置
ai:
  # 替换为自己的API密钥
  api-key: 5c8c15df-4179-4bc0-a7ff-7d1fe4b79d80
  model-name: deepseek-v3-250324
#  model:
#    # 替换为实际使用的大模型API地址
#    url: https://api.deepseek.com/v1
#    # 替换为自己的API密钥
#    key: ***********************************
#    # 模型类型
#    type: deepseek-reasoner

# 日志配置
logging:
  level:
    root: INFO
    com.bimowu.interview: DEBUG

# 阿里云配置
aliyun:
  accessKey: LTAI5tQ2bXYzSNY9DK2nkVRo
  accessKeySecret: ******************************
  region: cn-shanghai     # 修改为上海区域，与语音识别服务一致
  appKey: 5jH7n81ApChAapfJ
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com    # 修改为与region一致的端点
    accessKeyId: LTAI5tEm5vMpTW7AB2BjHtYD
    accessKeySecret: ******************************
    bucketName: interview-video1
    roleArn: acs:ram::1242406705498739:role/aliyunoss-front # 请替换为实际的角色ARN
    region: cn-beijing
# 语音识别配置
interview:
  retryTimes: 10
  maxQuestion: 15

