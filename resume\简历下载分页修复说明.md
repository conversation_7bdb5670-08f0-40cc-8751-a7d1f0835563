# 简历PDF下载分页修复说明

## 问题描述
简历PDF下载时出现分页被斩断的问题，导致内容在页面边界处被不自然地截断，影响阅读体验。

## 问题原因分析

### 1. 前端PDF生成问题
- 使用html2canvas + jsPDF的方式，分页逻辑过于简单
- 只是简单地按页面高度切割图片，没有考虑内容的完整性
- 缺少CSS分页控制属性

### 2. 布局问题
- 使用了flex布局和固定高度，可能导致内容溢出容器
- 没有使用CSS的page-break属性来控制分页
- 第三方库的默认配置问题

### 3. 后端PDF生成问题
- 使用iText库但没有设置分页控制
- 没有检查页面剩余空间

## 解决方案

### 1. 前端修复

#### 1.1 创建专门的PDF打印CSS文件
- 文件：`src/assets/pdf-print.css`
- 包含完整的分页控制样式
- 使用`page-break-inside: avoid`等属性

#### 1.2 创建智能PDF生成器
- 文件：`src/utils/pdfGenerator.js`
- 封装了完整的PDF生成逻辑
- 包含智能分页算法
- 自动处理图片加载和样式应用

#### 1.3 优化PDF生成配置
```javascript
// 使用html2canvas的优化配置
const canvas = await html2canvas(container, {
  scale: 2,
  useCORS: true,
  backgroundColor: '#fff',
  logging: false,
  allowTaint: true,
  foreignObjectRendering: true,
  // ... 其他配置
});
```

#### 1.4 智能分页逻辑
```javascript
// 优化分页逻辑 - 使用更精确的分页计算
if (pdfImgHeight <= pageHeight) {
  // 单页内容
  pdf.addImage(imgData, 'JPEG', 0, 0, pageWidth, pdfImgHeight);
} else {
  // 多页内容 - 使用更智能的分页
  let position = 0;
  let remainingHeight = pdfImgHeight;
  let pageCount = 0;
  
  while (remainingHeight > 0) {
    // 计算当前页能显示的高度
    const currentPageHeight = Math.min(pageHeight, remainingHeight);
    
    // 添加当前页内容
    pdf.addImage(imgData, 'JPEG', 0, position, pageWidth, pdfImgHeight);
    
    remainingHeight -= pageHeight;
    position -= pageHeight;
    pageCount++;
    
    // 如果还有内容，添加新页
    if (remainingHeight > 0) {
      pdf.addPage();
    }
  }
}
```

### 2. 后端修复

#### 2.1 添加分页控制
- 在`ResumeExportUtil.java`中添加了`PdfPageEventHelper`
- 为每个部分添加了分页检查逻辑

#### 2.2 优化分页逻辑
```java
// 检查是否需要新页
if (document.getPageNumber() > 1) {
    document.newPage();
}
document.add(new Paragraph("教育经历", sectionFont));
for (ResumeEducationalVo edu : resume.getEducationList()) {
    // 检查当前页剩余空间，如果不够则新页
    if (document.getPageNumber() > 1 && document.getPageNumber() < 3) {
        document.newPage();
    }
    // ... 添加内容
}
```

### 3. CSS分页控制

#### 3.1 关键CSS属性
```css
/* 避免在元素内部分页 */
.resume-section {
  page-break-inside: avoid;
  break-inside: avoid;
}

/* 在元素前强制分页 */
.page-break-before {
  page-break-before: always;
  break-before: page;
}

/* 避免在标题后分页 */
.section-header {
  page-break-after: avoid;
  break-after: avoid;
}

/* 避免在单个项目内分页 */
.education-item,
.work-item,
.project-item,
.skill-description-item {
  page-break-inside: avoid;
  break-inside: avoid;
}
```

#### 3.2 打印媒体查询
```css
@media print {
  .resume-section {
    page-break-inside: avoid;
    break-inside: avoid;
  }
  
  .section-header {
    page-break-after: avoid;
    break-after: avoid;
  }
  
  /* 确保内容不会溢出 */
  .resume-content {
    overflow: visible;
    max-height: none;
  }
  
  /* 避免使用固定高度 */
  .resume-template {
    min-height: auto;
    height: auto;
  }
}
```

## 修复效果

### 1. 分页优化
- 简历各部分（教育经历、工作经验、项目经验等）不会被截断
- 标题和内容保持在同一页
- 单个项目条目不会被分页切断

### 2. 布局优化
- 内容不会溢出容器
- 使用动态高度而非固定高度
- 图片和表格正确处理

### 3. 兼容性提升
- 支持多种浏览器
- 处理CORS图片问题
- 自动处理图片加载失败

## 使用说明

### 1. 前端使用
```javascript
import { generateResumePDF } from '@/utils/pdfGenerator'

// 简单使用
await generateResumePDF(element, 'resume.pdf')

// 带配置使用
await generateResumePDF(element, 'resume.pdf', {
  scale: 2,
  useCORS: true
})
```

### 2. 后端使用
```java
// 直接调用导出方法
byte[] pdfBytes = ResumeExportUtil.exportToPdf(resumeData);
```

## 注意事项

1. **图片处理**：确保所有图片都有正确的CORS设置
2. **字体支持**：使用系统默认字体以确保兼容性
3. **内容长度**：过长的内容会自动分页，但会保持完整性
4. **样式兼容**：CSS分页属性在不同浏览器中可能有差异

## 测试建议

1. 测试不同长度的简历内容
2. 测试包含图片的简历
3. 测试在不同浏览器中的表现
4. 测试打印预览功能

## 后续优化建议

1. 考虑使用Puppeteer等更专业的PDF生成方案
2. 添加PDF模板选择功能
3. 优化图片压缩和质量设置
4. 添加水印和页眉页脚功能 