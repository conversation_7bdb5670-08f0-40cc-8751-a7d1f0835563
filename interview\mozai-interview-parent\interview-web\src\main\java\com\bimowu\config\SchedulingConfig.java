package com.bimowu.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 定时任务配置类
 * 启用定时任务并配置Redisson客户端用于分布式锁
 */
@Configuration
@EnableScheduling
public class SchedulingConfig {

    @Value("${spring.redis.host}")
    private String redisHost;
    
    @Value("${spring.redis.port}")
    private int redisPort;
    
    @Value("${spring.redis.password:}")
    private String redisPassword;
    
    @Value("${spring.redis.database:0}")
    private int redisDatabase;
    
    /**
     * 配置Redisson客户端
     * 用于分布式锁
     *
     * @return RedissonClient实例
     */
    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        
        // 构建Redis连接URL
        String redisUrl = "redis://" + redisHost + ":" + redisPort;
        
        // 设置单节点模式
        if (redisPassword != null && !redisPassword.isEmpty()) {
            // 有密码的情况
            config.useSingleServer()
                  .setAddress(redisUrl)
                  .setPassword(redisPassword)
                  .setDatabase(redisDatabase)
                  .setConnectionMinimumIdleSize(1)
                  .setConnectionPoolSize(10)
                  .setConnectTimeout(10000)
                  .setRetryAttempts(3);
        } else {
            // 无密码的情况
            config.useSingleServer()
                  .setAddress(redisUrl)
                  .setDatabase(redisDatabase)
                  .setConnectionMinimumIdleSize(1)
                  .setConnectionPoolSize(10)
                  .setConnectTimeout(10000)
                  .setRetryAttempts(3);
        }
        
        return Redisson.create(config);
    }
} 