package com.bimowu.interview.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <Description>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/07/12
 */
@Data
@Accessors(chain = true)
public class PageResp<T> implements Serializable {
    @ApiModelProperty(value = "返回内容列表")
    private List<T> list;
    @ApiModelProperty(value = "总记录数", example = "100")
    private Long totalNum;
    @ApiModelProperty(value = "总页数", example = "20")
    private Long totalPage;
    @ApiModelProperty(value = "当前页数")
    private Long pageNum;
    @ApiModelProperty(value = "每页条数")
    private Long pageSize;

    public static <T> PageResp<T> EmptyObj(Long pageNum, Long pageSize) {
        return new PageResp().setPageNum(pageNum).setPageSize(pageSize);

    }
}
