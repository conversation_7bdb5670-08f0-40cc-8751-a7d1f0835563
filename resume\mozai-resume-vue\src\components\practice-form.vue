<template>
  <div class="form-container">
    <div class="project-select">
      <el-option
          v-for="project in projectList"
          :key="project.proId"
          :label="project.name"
          :value="project.proId"
      >
        <span>{{ project.name }}</span>
        <span class="project-date">{{ formatDate(project.startTime) }} - {{ formatDate(project.endTime) }}</span>
      </el-option>
    </div>

    <!--    <div v-if="showEditor">-->
    <!--      <div class="editor-header">-->
    <!--        <div class="editor-actions">-->
    <!--          <el-button type="primary" size="small" @click="$emit('select-content-items')">-->
    <!--            <el-icon class="icon"><document /></el-icon> 选择项目内容条款-->
    <!--          </el-button>-->
    <!--          &lt;!&ndash;          <el-button type="success" size="small" @click="$emit('polish-content')" :disabled="!formData.content">&ndash;&gt;-->
    <!--          &lt;!&ndash;            <el-icon class="icon"><magic-stick /></el-icon> AI润色&ndash;&gt;-->
    <!--          &lt;!&ndash;          </el-button>&ndash;&gt;-->
    <!--        </div>-->
    <!--      </div>-->

    <div class="editor-content">
      <MdEditor
          v-model="formData.content"
          height="300px"
          :toolbars="toolbars"
          :preview="true"
          :previewTheme="'default'"
          :showCodeRowNumber="true"
          :previewOnly="false"
          :previewWidth="'50%'"
          @onSave="saveForm"
          @change="handleContentChange"
          placeholder="请描述项目背景、职责、技术栈和成果..."
      />
    </div>

    <div class="tips">
      <el-alert
          title="撰写建议"
          type="info"
          :closable="false"
          show-icon
      >
        <div class="tips-content">
          <p>1. 描述项目背景和目标</p>
          <p>2. 说明您在项目中的角色和职责</p>
          <p>3. 列举使用的技术栈和工具</p>
          <p>4. 强调您的贡献和项目成果</p>
          <p>5. 可以使用加粗、斜体等富文本格式增强表现力</p>
        </div>
      </el-alert>
    </div>
  </div>
</template>

<script setup>
import {ref, defineProps, defineEmits, watch} from 'vue'
import {ElMessage} from 'element-plus'
import {MdEditor} from 'md-editor-v3'
import {MagicStick, Document} from '@element-plus/icons-vue'
import 'md-editor-v3/lib/style.css'

const props = defineProps({
  data: {
    type: String,
    required: true
  },
  projectName: {
    type: String,
    default: ''
  },
  projectCategory: {
    type: String,
    default: ''
  },
  showEditor: {
    type: Boolean,
    default: false
  },
  projectList: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update', 'select-content-items', 'polish-content', 'project-select'])

// 控制编辑器显示/隐藏
const showEditor = ref(props.showEditor)

// 监听props.showEditor的变化
watch(() => props.showEditor, (newVal) => {
  showEditor.value = newVal
}, {immediate: true})

// 暴露showEditor变量，让父组件可以访问
defineExpose({
  showEditor
})

// 富文本编辑器配置
const toolbars = [
  'bold',
  'italic',
  'strikethrough',
  'title',
  'sub',
  'sup',
  'quote',
  'unorderedList',
  'orderedList',
  'codeRow',
  'link',
  'save',
  'pageFullscreen',
  'fullscreen',
  'preview',
]

const preview = ref(true)

// 表单数据
const formData = ref({
  content: ''
})

// 初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal) {
    // 确保content始终是字符串类型
    formData.value.content = typeof newVal === 'object' ? '' : newVal || ''
  } else {
    formData.value.content = ''
  }
}, {immediate: true, deep: true})

// 监听内容变化，实时更新
watch(() => formData.value.content, (newVal) => {
  emit('update', newVal)
}, {immediate: false})

// 隐藏编辑器
const hideEditor = () => {
  showEditor.value = false
  saveForm()
}

// 保存表单
const saveForm = () => {
  emit('update', formData.value.content)
  ElMessage.success('项目描述信息已保存')
}

// 重置表单
const resetForm = () => {
  formData.value.content = props.data || ''
  ElMessage.info('已重置为上次保存的内容')
}

// 处理内容变化
const handleContentChange = (newContent) => {
  formData.value.content = newContent
  emit('update', newContent)
}

// 选中的项目
const selectedProject = ref('')

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
}

// 处理项目选择
const handleProjectSelect = (projectId) => {
  const project = props.projectList.find(p => p.proId === projectId)
  if (project) {
    emit('project-select', project)
    // 如果项目有内容，自动填充到编辑器中
    if (project.content) {
      formData.value.content = project.content
    }
  }
}
</script>

<style scoped>
.form-container {
  /* 删除padding属性，使宽度与项目特长模块一致 */
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.editor-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.editor-actions {
  display: flex;
  gap: 10px;
}

.icon {
  margin-right: 5px;
}

.editor-content {
  margin-bottom: 20px;
}

.tips {
  margin: 20px 0;
}

.tips-content p {
  margin: 5px 0;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.project-select {
  margin-bottom: 20px;
}

.project-date {
  float: right;
  color: #999;
  font-size: 12px;
}
</style>