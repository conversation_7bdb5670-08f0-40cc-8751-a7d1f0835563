import type { default as CascaderNode } from './node';
import type { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<{
    node: {
        type: PropType<CascaderNode>;
        required: true;
    };
    menuId: StringConstructor;
}, {
    panel: import("./types").ElCascaderPanelContext;
    isHoverMenu: import("vue").ComputedRef<boolean>;
    multiple: import("vue").ComputedRef<boolean>;
    checkStrictly: import("vue").ComputedRef<boolean>;
    checkedNodeId: import("vue").ComputedRef<number>;
    isDisabled: import("vue").ComputedRef<boolean>;
    isLeaf: import("vue").ComputedRef<boolean>;
    expandable: import("vue").ComputedRef<boolean>;
    inExpandingPath: import("vue").ComputedRef<boolean>;
    inCheckedPath: import("vue").ComputedRef<boolean>;
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    handleHoverExpand: (e: Event) => void;
    handleExpand: () => void;
    handleClick: () => void;
    handleCheck: (checked: boolean) => void;
    handleSelectCheck: (checked: boolean) => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "expand"[], "expand", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    node: {
        type: PropType<CascaderNode>;
        required: true;
    };
    menuId: StringConstructor;
}>> & {
    onExpand?: ((...args: any[]) => any) | undefined;
}, {}>;
export default _default;
