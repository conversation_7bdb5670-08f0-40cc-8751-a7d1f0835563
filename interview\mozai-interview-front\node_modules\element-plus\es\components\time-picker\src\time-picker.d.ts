declare const _default: import("vue").DefineComponent<{
    /**
     * @description whether to pick a time range
     */
    isRange: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabledDate: {
        readonly type: import("vue").PropType<Function>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    cellClassName: {
        readonly type: import("vue").PropType<Function>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    shortcuts: import("../../../utils").EpPropFinalized<ArrayConstructor, unknown, unknown, () => never[], boolean>;
    arrowControl: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    label: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, undefined, boolean>;
    tabindex: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown, 0, boolean>;
    validateEvent: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    unlinkPanels: BooleanConstructor;
    disabledHours: {
        readonly type: import("vue").PropType<import("./props/shared").GetDisabledHours>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    disabledMinutes: {
        readonly type: import("vue").PropType<import("./props/shared").GetDisabledMinutes>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    disabledSeconds: {
        readonly type: import("vue").PropType<import("./props/shared").GetDisabledSeconds>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    id: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("./common/props").SingleOrRange<string> & {}) | (() => import("./common/props").SingleOrRange<string>) | ((new (...args: any[]) => import("./common/props").SingleOrRange<string> & {}) | (() => import("./common/props").SingleOrRange<string>))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    name: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./common/props").SingleOrRange<string> & {}) | (() => import("./common/props").SingleOrRange<string>) | ((new (...args: any[]) => import("./common/props").SingleOrRange<string> & {}) | (() => import("./common/props").SingleOrRange<string>))[], unknown, unknown, "", boolean>;
    popperClass: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    format: StringConstructor;
    valueFormat: StringConstructor;
    type: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    clearable: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    clearIcon: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) | ((new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>))[], unknown, unknown, import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>, boolean>;
    editable: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    prefixIcon: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) | ((new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>))[], unknown, unknown, "", boolean>;
    size: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    placeholder: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    popperOptions: import("../../../utils").EpPropFinalized<(new (...args: any[]) => Partial<import("@popperjs/core").Options>) | (() => Partial<import("@popperjs/core").Options>) | ((new (...args: any[]) => Partial<import("@popperjs/core").Options>) | (() => Partial<import("@popperjs/core").Options>))[], unknown, unknown, () => {}, boolean>;
    modelValue: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./common/props").ModelValueType & {}) | (() => import("./common/props").ModelValueType) | ((new (...args: any[]) => import("./common/props").ModelValueType & {}) | (() => import("./common/props").ModelValueType))[], unknown, unknown, "", boolean>;
    rangeSeparator: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "-", boolean>;
    startPlaceholder: StringConstructor;
    endPlaceholder: StringConstructor;
    defaultValue: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Date | [Date, Date]) | (() => import("./common/props").SingleOrRange<Date>) | ((new (...args: any[]) => Date | [Date, Date]) | (() => import("./common/props").SingleOrRange<Date>))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    defaultTime: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Date | [Date, Date]) | (() => import("./common/props").SingleOrRange<Date>) | ((new (...args: any[]) => Date | [Date, Date]) | (() => import("./common/props").SingleOrRange<Date>))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "update:modelValue"[], "update:modelValue", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    /**
     * @description whether to pick a time range
     */
    isRange: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabledDate: {
        readonly type: import("vue").PropType<Function>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    cellClassName: {
        readonly type: import("vue").PropType<Function>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    shortcuts: import("../../../utils").EpPropFinalized<ArrayConstructor, unknown, unknown, () => never[], boolean>;
    arrowControl: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    label: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, undefined, boolean>;
    tabindex: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown, 0, boolean>;
    validateEvent: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    unlinkPanels: BooleanConstructor;
    disabledHours: {
        readonly type: import("vue").PropType<import("./props/shared").GetDisabledHours>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    disabledMinutes: {
        readonly type: import("vue").PropType<import("./props/shared").GetDisabledMinutes>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    disabledSeconds: {
        readonly type: import("vue").PropType<import("./props/shared").GetDisabledSeconds>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    id: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("./common/props").SingleOrRange<string> & {}) | (() => import("./common/props").SingleOrRange<string>) | ((new (...args: any[]) => import("./common/props").SingleOrRange<string> & {}) | (() => import("./common/props").SingleOrRange<string>))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    name: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./common/props").SingleOrRange<string> & {}) | (() => import("./common/props").SingleOrRange<string>) | ((new (...args: any[]) => import("./common/props").SingleOrRange<string> & {}) | (() => import("./common/props").SingleOrRange<string>))[], unknown, unknown, "", boolean>;
    popperClass: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    format: StringConstructor;
    valueFormat: StringConstructor;
    type: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    clearable: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    clearIcon: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) | ((new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>))[], unknown, unknown, import("vue").DefineComponent<{}, {}, {}, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>, boolean>;
    editable: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    prefixIcon: import("../../../utils").EpPropFinalized<(new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) | ((new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>))[], unknown, unknown, "", boolean>;
    size: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    placeholder: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    popperOptions: import("../../../utils").EpPropFinalized<(new (...args: any[]) => Partial<import("@popperjs/core").Options>) | (() => Partial<import("@popperjs/core").Options>) | ((new (...args: any[]) => Partial<import("@popperjs/core").Options>) | (() => Partial<import("@popperjs/core").Options>))[], unknown, unknown, () => {}, boolean>;
    modelValue: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./common/props").ModelValueType & {}) | (() => import("./common/props").ModelValueType) | ((new (...args: any[]) => import("./common/props").ModelValueType & {}) | (() => import("./common/props").ModelValueType))[], unknown, unknown, "", boolean>;
    rangeSeparator: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "-", boolean>;
    startPlaceholder: StringConstructor;
    endPlaceholder: StringConstructor;
    defaultValue: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Date | [Date, Date]) | (() => import("./common/props").SingleOrRange<Date>) | ((new (...args: any[]) => Date | [Date, Date]) | (() => import("./common/props").SingleOrRange<Date>))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    defaultTime: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => Date | [Date, Date]) | (() => import("./common/props").SingleOrRange<Date>) | ((new (...args: any[]) => Date | [Date, Date]) | (() => import("./common/props").SingleOrRange<Date>))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}>> & {
    "onUpdate:modelValue"?: ((...args: any[]) => any) | undefined;
}, {
    type: string;
    modelValue: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("./common/props").ModelValueType & {}) | (() => import("./common/props").ModelValueType) | ((new (...args: any[]) => import("./common/props").ModelValueType & {}) | (() => import("./common/props").ModelValueType))[], unknown, unknown>;
    popperClass: string;
    label: string;
    popperOptions: Partial<import("@popperjs/core").Options>;
    disabled: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    clearable: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    name: import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("./common/props").SingleOrRange<string> & {}) | (() => import("./common/props").SingleOrRange<string>) | ((new (...args: any[]) => import("./common/props").SingleOrRange<string> & {}) | (() => import("./common/props").SingleOrRange<string>))[], unknown, unknown>;
    placeholder: string;
    readonly: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    prefixIcon: import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) | ((new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>))[], unknown, unknown>;
    tabindex: import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>;
    validateEvent: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    clearIcon: import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) | ((new (...args: any[]) => (string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>) & {}) | (() => string | import("vue").Component<any, any, any, import("vue").ComputedOptions, import("vue").MethodOptions>))[], unknown, unknown>;
    editable: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    rangeSeparator: string;
    isRange: boolean;
    shortcuts: unknown[];
    arrowControl: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    unlinkPanels: boolean;
}>;
export default _default;
