import{d as Q,n as L,r as h,H as z,c as E,a as o,b as a,w as p,f as c,k as _,s as N,u as X,E as v,I as q,J as V,e as y,o as D,g as J,K,_ as j}from"./index-d8f61d92.js";import{a as G}from"./index-3556d18e.js";const W={class:"upload-container"},Y={class:"upload-content"},Z={class:"selected-type"},ee={class:"type-item"},te={class:"type-item"},oe={class:"type-item"},se={class:"upload-area"},ne={key:0,class:"file-info"},ie={key:1,class:"loading-info"},ae={class:"actions"},re=Q({__name:"ResumeUploadView",setup(le){const T=X(),t=L(),r=h(null),u=h(!1),m=h(120),F=z(()=>Math.min(100,Math.round((120-m.value)/120*100))),C=z(()=>{const n={hr:"HR面试",tech:"技术面试"},e={dev:"开发岗",support:"技术支持岗"},s={fresh:"应届生","1-3":"1-3年","3-5":"3-5年","5+":"5年以上"};return{stage:n[t.interviewType.stage]||"未选择",position:e[t.interviewType.position]||"未选择",experience:s[t.interviewType.experience]||"未选择"}});(!t.interviewType.stage||!t.interviewType.position||!t.interviewType.experience)&&T.replace("/selection");let g=null;const O=n=>`${m.value}秒`;let d;const S=n=>{r.value=n.raw},A=()=>{m.value=120,d&&clearInterval(d),d=window.setInterval(()=>{m.value>0?m.value--:clearInterval(d)},1e3)},b=()=>{d&&(clearInterval(d),d=void 0)},P=()=>{g&&(g.abort(),g=null),b(),u.value=!1,v.info("已取消简历分析")},R=()=>({hr:"HR面试",tech:"技术面试"})[t.interviewType.stage]||"未选择",k=()=>({dev:"开发岗",support:"技术支持岗"})[t.interviewType.position]||"未选择",B=()=>({fresh:"应届生","1-3":"1-3年","3-5":"3-5年","5+":"5年以上"})[t.interviewType.experience]||"未选择",U=async()=>{var n;if(!r.value){v.warning("请先上传简历文件");return}try{await q.confirm(`确认以下面试信息：
      - 面试环节：${R()}
      - 面试岗位：${k()}
      - 工作经验：${B()}
      
      系统将根据以上选择和您的简历生成针对性的面试问题。`,"确认面试信息",{confirmButtonText:"确认并开始分析",cancelButtonText:"返回修改",type:"info"}),u.value=!0,A(),console.log("【简历分析】开始处理简历分析请求"),console.log("【简历分析】当前axios默认超时设置:",V.defaults.timeout),g=new AbortController,t.setResume(r.value);const e={stage:{code:t.interviewType.stage,text:R()},position:{code:t.interviewType.position,text:k()},experience:{code:t.interviewType.experience,text:B()}};console.log("【简历分析】面试配置信息:",e);const s=new FormData;s.append("file",r.value),s.append("interviewStage",t.interviewType.stage),s.append("interviewPosition",t.interviewType.position),s.append("interviewExperience",t.interviewType.experience);const f=V.create({baseURL:"/api",timeout:12e4});f.defaults.timeout=12e4,console.log("【简历分析】请求前检查超时设置:",f.defaults.timeout),console.log("【简历分析】开始发送请求",{url:"/resume/analyze",timeout:12e4,fileSize:r.value.size,fileName:r.value.name,stage:t.interviewType.stage,position:t.interviewType.position,experience:t.interviewType.experience});const I=await f.post("/resume/analyze",s,{headers:{"Content-Type":"multipart/form-data"},timeout:12e4,signal:g.signal});console.log("【简历分析】请求成功返回");const l=I.data;if((l==null?void 0:l.code)!==0)throw new Error((l==null?void 0:l.message)||"简历分析失败");const i=l.data;if(!i||i.length===0)throw new Error("未能生成有效的面试问题");t.setQuestions(i),console.log("【创建面试】开始创建面试记录");try{let w="应聘者";if(i.length>0){const M=i[0].match(/^([^\s，,、。.]+)(?:先生|女士|同学|工程师)/);M&&M[1]&&(w=M[1],console.log("【创建面试】从问题中提取的候选人姓名:",w))}const H={type:"mock",candidateName:w,position:t.interviewType.position,stage:t.interviewType.stage,experience:t.interviewType.experience,questions:i},x=await G.createInterview(H);x.code!==0?console.error("创建面试记录失败:",x.message):(t.setInterviewId(x.data.interviewId),console.log("【创建面试】面试记录创建成功，ID:",x.data.interviewId))}catch(w){console.error("创建面试记录时出错:",w)}v.success("简历分析完成，即将进入面试环节！"),setTimeout(()=>{T.push("/interview")},1500)}catch(e){if(e==="cancel"||e.name==="AbortError"||e.name==="CanceledError")return;if(console.error("处理简历时出错:",e),e.code==="ECONNABORTED"||(n=e.message)!=null&&n.includes("timeout"))v.error("分析简历时间过长，请稍后重试。如果问题持续存在，可能需要简化简历内容");else if(e.response&&e.response.data){const s=e.response.data;v.error(s.message||"上传简历失败，请稍后重试")}else e.message?v.error(e.message):v.error("上传简历失败，请检查网络连接")}finally{u.value=!1,b(),g=null}},$=()=>{T.push("/selection")};return(n,e)=>{const s=y("el-tag"),f=y("el-icon"),I=y("el-upload"),l=y("el-progress"),i=y("el-button");return D(),E("div",W,[o("div",Y,[e[9]||(e[9]=o("h1",null,"上传您的简历",-1)),e[10]||(e[10]=o("p",{class:"description"}," 请上传您的简历，AI将分析您的经历并生成个性化面试问题。 支持PDF、DOC、DOCX格式文件。 ",-1)),o("div",Z,[o("div",ee,[e[0]||(e[0]=o("strong",null,"面试环节:",-1)),a(s,{size:"large",type:"primary"},{default:p(()=>[c(_(C.value.stage),1)]),_:1})]),o("div",te,[e[1]||(e[1]=o("strong",null,"面试岗位:",-1)),a(s,{size:"large",type:"success"},{default:p(()=>[c(_(C.value.position),1)]),_:1})]),o("div",oe,[e[2]||(e[2]=o("strong",null,"工作经验:",-1)),a(s,{size:"large",type:"warning"},{default:p(()=>[c(_(C.value.experience),1)]),_:1})])]),o("div",se,[a(I,{class:"resume-upload",drag:"",action:"#","auto-upload":!1,"on-change":S,limit:1,accept:".pdf,.doc,.docx"},{tip:p(()=>e[3]||(e[3]=[o("div",{class:"el-upload__tip"}," 仅限 PDF、DOC、DOCX 格式文件，且不超过 10MB ",-1)])),default:p(()=>[a(f,{class:"el-icon--upload"},{default:p(()=>[a(J(K))]),_:1}),e[4]||(e[4]=o("div",{class:"el-upload__text"},[c(" 拖拽文件到此处或 "),o("em",null,"点击上传")],-1))]),_:1,__:[4]}),r.value?(D(),E("div",ne,[o("p",null,[e[5]||(e[5]=o("strong",null,"已选择文件:",-1)),c(" "+_(r.value.name),1)])])):N("",!0),u.value?(D(),E("div",ie,[a(l,{percentage:F.value,format:O},null,8,["percentage"]),e[7]||(e[7]=o("p",{class:"timeout-hint"},"简历分析中，最长等待时间为120秒，请耐心等待...",-1)),a(i,{type:"warning",size:"small",onClick:P,class:"cancel-btn"},{default:p(()=>e[6]||(e[6]=[c(" 取消分析 ",-1)])),_:1,__:[6]})])):N("",!0),o("div",ae,[a(i,{onClick:$},{default:p(()=>e[8]||(e[8]=[c("返回",-1)])),_:1,__:[8]}),a(i,{type:"primary",onClick:U,disabled:!r.value||u.value,loading:u.value},{default:p(()=>[c(_(u.value?"处理中...":"开始分析"),1)]),_:1},8,["disabled","loading"])])])])])}}});const de=j(re,[["__scopeId","data-v-94a4d189"]]);export{de as default};
