package com.bimowu.resume.common.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.resume.entity.ResumeTalent;
import com.bimowu.resume.vo.ResumeTalentVo;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * <p>
 * 技能特长表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface ResumeTalentMapper extends BaseMapper<ResumeTalent> {

    List<ResumeTalentVo> selectByResumeId(@Param("resumeId") Long resumeId);
}
