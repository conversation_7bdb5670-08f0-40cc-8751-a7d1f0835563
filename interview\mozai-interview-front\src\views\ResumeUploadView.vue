<template>
  <div class="upload-container">
    <div class="upload-content">
      <h1>上传您的简历</h1>
      <p class="description">
        请上传您的简历，AI将分析您的经历并生成个性化面试问题。
        支持PDF、DOC、DOCX格式文件。
      </p>
      
      <!-- 展示选择的面试类型 -->
      <div class="selected-type">
        <div class="type-item">
          <strong>面试环节:</strong> 
          <el-tag size="large" type="primary">
            {{ interviewTypeText.stage }}
          </el-tag>
        </div>
        <div class="type-item">
          <strong>面试岗位:</strong> 
          <el-tag size="large" type="success">
            {{ interviewTypeText.position }}
          </el-tag>
        </div>
        <div class="type-item">
          <strong>工作经验:</strong> 
          <el-tag size="large" type="warning">
            {{ interviewTypeText.experience }}
          </el-tag>
        </div>
      </div>
      
      <div class="upload-area">
        <el-upload
          class="resume-upload"
          drag
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          :limit="1"
          accept=".pdf,.doc,.docx"
        >
          <el-icon class="el-icon--upload"><Upload /></el-icon>
          <div class="el-upload__text">
            拖拽文件到此处或 <em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              仅限 PDF、DOC、DOCX 格式文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
        
        <div v-if="selectedFile" class="file-info">
          <p><strong>已选择文件:</strong> {{ selectedFile.name }}</p>
        </div>
        
        <div class="loading-info" v-if="isLoading">
          <el-progress :percentage="progressPercentage" :format="progressFormat"></el-progress>
          <p class="timeout-hint">简历分析中，最长等待时间为120秒，请耐心等待...</p>
          <el-button type="warning" size="small" @click="cancelAnalysis" class="cancel-btn">
            取消分析
          </el-button>
        </div>
        
        <div class="actions">
          <el-button @click="goBack">返回</el-button>
          <el-button type="primary" @click="processResume" :disabled="!selectedFile || isLoading" :loading="isLoading">
            {{ isLoading ? '处理中...' : '开始分析' }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Upload } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useInterviewStore } from '@/store'
import api, { BaseResponse } from '@/api'
import axios from 'axios'
import http from '@/utils/http'

const router = useRouter()
const interviewStore = useInterviewStore()
const selectedFile = ref<File | null>(null)
const isLoading = ref(false)
const remainingTime = ref(120)
const progressPercentage = computed(() => {
  // 120秒倒计时对应的百分比
  return Math.min(100, Math.round((120 - remainingTime.value) / 120 * 100))
})

// 面试类型的映射文字
const interviewTypeText = computed(() => {
  const stageMap: Record<string, string> = {
    'hr': 'HR面试',
    'tech': '技术面试'
  }
  
  const positionMap: Record<string, string> = {
    'dev': '开发岗',
    'support': '技术支持岗'
  }
  
  const experienceMap: Record<string, string> = {
    'fresh': '应届生',
    '1-3': '1-3年',
    '3-5': '3-5年',
    '5+': '5年以上'
  }
  
  return {
    stage: stageMap[interviewStore.interviewType.stage] || '未选择',
    position: positionMap[interviewStore.interviewType.position] || '未选择',
    experience: experienceMap[interviewStore.interviewType.experience] || '未选择'
  }
})

// 如果未选择面试类型，则返回选择页
if (!interviewStore.interviewType.stage || !interviewStore.interviewType.position || !interviewStore.interviewType.experience) {
  router.replace('/selection')
}

// 请求取消控制器
let abortController: AbortController | null = null

// 格式化进度条显示
const progressFormat = (percentage: number) => {
  return `${remainingTime.value}秒`
}

let timer: number | undefined

// 处理文件选择
const handleFileChange = (file: any) => {
  selectedFile.value = file.raw
}

// 开始倒计时
const startTimer = () => {
  remainingTime.value = 120
  if (timer) clearInterval(timer)
  
  timer = window.setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--
    } else {
      clearInterval(timer)
    }
  }, 1000)
}

// 停止倒计时
const stopTimer = () => {
  if (timer) {
    clearInterval(timer)
    timer = undefined
  }
}

// 取消分析
const cancelAnalysis = () => {
  if (abortController) {
    abortController.abort()
    abortController = null
  }
  stopTimer()
  isLoading.value = false
  ElMessage.info('已取消简历分析')
}

// 添加辅助函数来获取文本
const getStageText = () => {
  const stageMap: Record<string, string> = {
    'hr': 'HR面试',
    'tech': '技术面试'
  }
  return stageMap[interviewStore.interviewType.stage] || '未选择'
}

const getPositionText = () => {
  const positionMap: Record<string, string> = {
    'dev': '开发岗',
    'support': '技术支持岗'
  }
  return positionMap[interviewStore.interviewType.position] || '未选择'
}

const getExperienceText = () => {
  const experienceMap: Record<string, string> = {
    'fresh': '应届生',
    '1-3': '1-3年',
    '3-5': '3-5年',
    '5+': '5年以上'
  }
  return experienceMap[interviewStore.interviewType.experience] || '未选择'
}

// 处理简历分析
const processResume = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先上传简历文件')
    return
  }
  
  try {
    // 显示确认对话框，确认用户选择的面试类型
    await ElMessageBox.confirm(
      `确认以下面试信息：
      - 面试环节：${getStageText()}
      - 面试岗位：${getPositionText()}
      - 工作经验：${getExperienceText()}
      
      系统将根据以上选择和您的简历生成针对性的面试问题。`,
      '确认面试信息',
      {
        confirmButtonText: '确认并开始分析',
        cancelButtonText: '返回修改',
        type: 'info'
      }
    )
    
    isLoading.value = true
    startTimer() // 开始倒计时
    
    console.log('【简历分析】开始处理简历分析请求')
    
    // 输出当前超时设置
    console.log('【简历分析】当前axios默认超时设置:', axios.defaults.timeout)
    
    // 创建新的 AbortController 以支持取消操作
    abortController = new AbortController()
    
    // 保存文件到 store
    interviewStore.setResume(selectedFile.value)
    
    // 构建详细的面试信息用于日志记录
    const interviewInfo = {
      stage: {
        code: interviewStore.interviewType.stage,
        text: getStageText()
      },
      position: {
        code: interviewStore.interviewType.position,
        text: getPositionText()
      },
      experience: {
        code: interviewStore.interviewType.experience,
        text: getExperienceText()
      }
    }
    
    // 记录详细的面试信息
    console.log('【简历分析】面试配置信息:', interviewInfo)
    
    // 直接使用axios发送请求，跳过所有中间层
    const formData = new FormData()
    formData.append('file', selectedFile.value)
    
    // 添加面试类型参数
    formData.append('interviewStage', interviewStore.interviewType.stage)
    formData.append('interviewPosition', interviewStore.interviewType.position)
    formData.append('interviewExperience', interviewStore.interviewType.experience)
    
    // 创建专用的axios实例，确保超时设置
    const axiosInstance = axios.create({
      baseURL: '/api',
      timeout: 120000, // 显式设置120秒
    })
    axiosInstance.defaults.timeout = 120000
    
    console.log('【简历分析】请求前检查超时设置:', axiosInstance.defaults.timeout)
    
    // 在控制台显示详细信息
    console.log('【简历分析】开始发送请求', {
      url: '/resume/analyze',
      timeout: 120000,
      fileSize: selectedFile.value.size,
      fileName: selectedFile.value.name,
      stage: interviewStore.interviewType.stage,
      position: interviewStore.interviewType.position,
      experience: interviewStore.interviewType.experience
    })
    
    // 直接发送请求
    const response = await axiosInstance.post('/resume/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 120000, // 再次确认超时设置
      signal: abortController.signal // 添加中断信号
    })
    
    console.log('【简历分析】请求成功返回')
    
    // 获取响应数据
    const result: BaseResponse<string[]> = response.data
    
    // 检查响应状态
    if (result?.code !== 0) {
      throw new Error(result?.message || '简历分析失败')
    }
    
    // 设置问题列表
    const questions = result.data
    if (!questions || questions.length === 0) {
      throw new Error('未能生成有效的面试问题')
    }
    
    // 存储问题到store
    interviewStore.setQuestions(questions)
    
    // 创建面试记录
    console.log('【创建面试】开始创建面试记录')
    try {
      // 从简历分析结果中提取姓名信息
      // 假设后端返回的第一个问题包含候选人姓名，例如："XXX先生/女士，请介绍一下你自己..."
      let candidateName = '应聘者'  // 默认值
      
      if (questions.length > 0) {
        const firstQuestion = questions[0]
        // 尝试提取姓名（假设格式为：XXX先生/女士，请介绍...）
        const nameMatch = firstQuestion.match(/^([^\s，,、。.]+)(?:先生|女士|同学|工程师)/);
        if (nameMatch && nameMatch[1]) {
          candidateName = nameMatch[1]
          console.log('【创建面试】从问题中提取的候选人姓名:', candidateName)
        }
      }
      
      // 构建面试数据
      const interviewData = {
        type: 'mock', // 添加面试类型为模拟面试
        candidateName,
        position: interviewStore.interviewType.position,
        stage: interviewStore.interviewType.stage,
        experience: interviewStore.interviewType.experience,
        questions
      }
      
      // 调用API创建面试
      const interviewResult = await api.createInterview(interviewData)
      
      if (interviewResult.code !== 0) {
        console.error('创建面试记录失败:', interviewResult.message)
        // 不阻止流程继续，仅记录错误
      } else {
        // 保存面试ID到store
        interviewStore.setInterviewId(interviewResult.data.interviewId)
        console.log('【创建面试】面试记录创建成功，ID:', interviewResult.data.interviewId)
      }
    } catch (interviewError) {
      console.error('创建面试记录时出错:', interviewError)
      // 不阻止流程继续，仅记录错误
    }
    
    // 显示成功消息
    ElMessage.success('简历分析完成，即将进入面试环节！')
    
    // 导航到面试页面
    setTimeout(() => {
      router.push('/interview')
    }, 1500) // 延迟1.5秒后跳转，让用户看到成功提示
  } catch (error: any) {
    // 如果是用户取消确认对话框，就不显示错误
    if (error === 'cancel' || error.name === 'AbortError' || error.name === 'CanceledError') {
      return
    }
    
    // 打印详细错误到控制台，方便调试
    console.error('处理简历时出错:', error)
    
    // 为用户显示友好的错误消息
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      // 处理请求超时错误
      ElMessage.error('分析简历时间过长，请稍后重试。如果问题持续存在，可能需要简化简历内容')
    } else if (error.response && error.response.data) {
      // 处理后端返回的错误
      const serverError = error.response.data
      ElMessage.error(serverError.message || '上传简历失败，请稍后重试')
    } else if (error.message) {
      // 处理本地捕获的错误
      ElMessage.error(error.message)
    } else {
      // 未知错误
      ElMessage.error('上传简历失败，请检查网络连接')
    }
  } finally {
    isLoading.value = false
    stopTimer() // 停止倒计时
    abortController = null
  }
}

const goBack = () => {
  router.push('/selection')
}
</script>

<style scoped>
.upload-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.upload-content {
  width: 100%;
  max-width: 800px;
  background-color: white;
  border-radius: 10px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

h1 {
  font-size: 2rem;
  margin-bottom: 20px;
  color: #409EFF;
  text-align: center;
}

.description {
  font-size: 1.1rem;
  color: #606266;
  margin-bottom: 30px;
  text-align: center;
  line-height: 1.6;
}

.upload-area {
  margin-top: 30px;
}

.resume-upload {
  width: 100%;
}

.file-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 5px;
}

.loading-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 5px;
}

.timeout-hint {
  margin-top: 10px;
  font-size: 0.9rem;
  color: #909399;
  text-align: center;
}

.cancel-btn {
  display: block;
  margin: 15px auto 0;
}

.actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

:deep(.el-upload-dragger) {
  width: 100%;
}

.selected-type {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  flex-wrap: wrap;
}

.type-item {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style> 