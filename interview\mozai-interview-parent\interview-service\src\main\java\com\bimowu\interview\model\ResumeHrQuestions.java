package com.bimowu.interview.model;


import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * HR问题表
 * @TableName resume_hr_questions
 */
@TableName(value ="resume_hr_questions")
@Data
public class ResumeHrQuestions implements Serializable {
    /**
     * 主键ID
     */
    @TableId
    private Long queId;

    /**
     * 问题类型
     */
    private Object questionType;

    /**
     * 题目内容
     */
    private String question;

    /**
     * 题目答案
     */
    private String answer;

    /**
     * 创建人
     */
    private String createAt;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}