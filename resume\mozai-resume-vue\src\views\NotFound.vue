<template>
  <MainLayout>
    <div class="not-found">
      <div class="container">
        <div class="not-found-content">
          <div class="error-code">404</div>
          <h1>页面未找到</h1>
          <p>抱歉，您访问的页面不存在或已被移除。</p>
          <router-link to="/" class="btn btn-primary">返回首页</router-link>
        </div>
      </div>
    </div>
  </MainLayout>
</template>

<script setup>
import MainLayout from '@/layouts/MainLayout.vue'
</script>

<style scoped>
.not-found {
  padding: 80px 0;
  text-align: center;
}

.not-found-content {
  max-width: 600px;
  margin: 0 auto;
}

.error-code {
  font-size: 120px;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
  margin-bottom: 20px;
}

h1 {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 15px;
}

p {
  font-size: 18px;
  color: var(--light-text);
  margin-bottom: 30px;
}

.btn {
  display: inline-block;
  padding: 12px 30px;
  font-size: 16px;
}
</style> 