import type { RadioProps } from './radio';
export declare const useRadio: (props: {
    label: RadioProps['label'];
    modelValue?: RadioProps['modelValue'];
}, emit?: (((event: "update:modelValue", val: string | number | boolean) => void) & ((event: "change", val: string | number | boolean) => void)) | undefined) => {
    radioRef: import("vue").Ref<HTMLInputElement | undefined>;
    isGroup: import("vue").ComputedRef<boolean>;
    radioGroup: import("./constants").RadioGroupContext | undefined;
    focus: import("vue").Ref<boolean>;
    size: import("vue").ComputedRef<"" | "default" | "small" | "large">;
    disabled: import("vue").ComputedRef<boolean>;
    tabIndex: import("vue").ComputedRef<0 | -1>;
    modelValue: import("vue").WritableComputedRef<import("../../../utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor, BooleanConstructor], unknown, unknown>>;
};
