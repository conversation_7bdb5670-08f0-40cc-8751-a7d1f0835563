import axios from 'axios'

const API_URL = 'https://ceping.bimowo.com/resume'

// 创建axios实例
const api = axios.create({
  baseURL: API_URL,
  timeout: 30000,
  withCredentials: true,  // 添加跨域请求时发送cookie
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
    config => {
      // 确保请求头中包含Referer和Origin
      if (!config.headers['Referer']) {
        config.headers['Referer'] = window.location.href;
      }
      if (!config.headers['Origin']) {
        config.headers['Origin'] = window.location.origin;
      }

      // 从本地存储中获取token
      const token = localStorage.getItem('token')

      // 如果存在token，则在请求头中携带
      if (token) {
        console.log('API请求头中添加token')
        config.headers['Authorization'] = `Bearer ${token}`
        // 也可以使用自定义头，取决于后端的实现
        config.headers['token'] = token
      }

      return config
    },
    error => {
      return Promise.reject(error)
    }
)

// 响应拦截器
api.interceptors.response.use(
    response => {
      console.log('API响应:', response)

      // 检查响应状态码
      if (response.status === 200) {
        // 检查业务状态码
        if (response.data && response.data.code) {
          // 处理未授权情况
          if (response.data.code === 401) {
            console.log('API检测到未授权响应，状态码401')

            // 检查是否有重定向URL
            if (response.data.data && response.data.data.redirectUrl) {
              const redirectUrl = response.data.data.redirectUrl
              console.log('API检测到重定向URL:', redirectUrl)

              // 执行重定向
              console.log('API正在重定向到:', redirectUrl)
              window.location.href = redirectUrl
              return Promise.reject(new Error('未授权，正在重定向到登录页面'))
            }
          }
        }
      }

      // 正常响应，返回数据
      return response.data
    },
    error => {
      console.error('API响应错误:', error)

      // 检查是否有响应
      if (error.response) {
        console.log('API错误响应状态码:', error.response.status)
        console.log('API错误响应数据:', error.response.data)

        // 处理401未授权错误
        if (error.response.status === 401) {
          console.log('API检测到未授权响应，状态码401')

          // 检查是否有重定向URL
          if (error.response.data && error.response.data.data && error.response.data.data.redirectUrl) {
            const redirectUrl = error.response.data.data.redirectUrl
            console.log('API检测到重定向URL:', redirectUrl)

            // 执行重定向
            console.log('API正在重定向到:', redirectUrl)
            window.location.href = redirectUrl
            return Promise.reject(new Error('未授权，正在重定向到登录页面'))
          }
        }
      }

      return Promise.reject(error)
    }
)

// 用户相关API
export const userApi = {
  login(data) {
    return api.post('/sso/login', data)
  },
  register(data) {
    return api.post('/user/register', data)
  },
  getUserInfo() {
    return api.get('/user/info')
  },
  updateUserInfo(data) {
    return api.put('/user/info', data)
  },
  // 头像上传接口
  uploadAvatar(formData) {
    return api.post('/resume/upload/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// 简历相关API
export const resumeApi = {
  getResumeList() {
    return api.get('/resume/mylist')
  },
  getResumeById(id) {
    return api.get(`/resume/detail/${id}`)
  },
  createResume(data) {
    return api.post('/resume', data)
  },
  updateResume(id, data) {
    return api.put(`/resume/${id}`, data)
  },
  getCategoryList() {
    return api.get('/category/list')
  },
  getProjectListByCategory(id) {
    return api.get(`/project/listByCategory?catId=${id}`)
  },
  getContextByProjectId(id) {
    return api.get(`/project/contentListByProject?proId=${id}`)
  },
  getSkillList() {
    return api.get('/skill/selectlist')
  },
  saveResume(data) {
    return api.post('/resume/save', data)
  },
  deleteResume(id) {
    console.log('删除简历API调用，ID:', id);
    return api.get(`/resume/delete/${id}`);
  }
}

// 模板相关API
export const templateApi = {
  getTemplateList() {
    return api.get('/template/list')
  },
  getTemplateById(id) {
    return api.get(`/template/${id}`)
  }
}

export default api 