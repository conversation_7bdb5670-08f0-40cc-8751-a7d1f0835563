// 分析音频噪音水平
export const analyzeNoiseLevel = (audioBuffer: AudioBuffer): {
  noiseLevel: number;
  qualityScore: number;
  quality: string;
} => {
  const channelData = audioBuffer.getChannelData(0) // 获取第一个声道数据
  
  // 计算音频信号的RMS（均方根）值，这是一种衡量信号强度的方法
  let sumSquares = 0
  for (let i = 0; i < channelData.length; i++) {
    sumSquares += channelData[i] * channelData[i]
  }
  const rms = Math.sqrt(sumSquares / channelData.length)
  
  // 计算信噪比（这里简化处理，实际应用中需要更复杂的算法）
  // 首先计算平均振幅
  let sum = 0
  for (let i = 0; i < channelData.length; i++) {
    sum += Math.abs(channelData[i])
  }
  const averageAmplitude = sum / channelData.length
  
  // 计算标准差作为噪音估计
  let sumDiffSquared = 0
  for (let i = 0; i < channelData.length; i++) {
    const diff = Math.abs(channelData[i]) - averageAmplitude
    sumDiffSquared += diff * diff
  }
  const standardDeviation = Math.sqrt(sumDiffSquared / channelData.length)
  
  // 噪音水平（标准差与平均振幅的比值）
  const noiseLevel = standardDeviation / (averageAmplitude + 0.0001) // 避免除以零
  
  // 计算质量分数（0-100）
  // 噪音水平越低，分数越高
  let qualityScore = 100 - Math.min(100, noiseLevel * 500)
  qualityScore = Math.max(0, qualityScore) // 确保分数不小于0
  
  // 确定质量等级
  let quality = 'excellent'
  if (qualityScore < 60) {
    quality = 'poor'
  } else if (qualityScore < 80) {
    quality = 'average'
  } else if (qualityScore < 90) {
    quality = 'good'
  }
  
  return {
    noiseLevel,
    qualityScore,
    quality
  }
}

// 从Blob创建AudioBuffer
export const createAudioBufferFromBlob = async (blob: Blob): Promise<AudioBuffer> => {
  const audioContext = new AudioContext()
  const arrayBuffer = await blob.arrayBuffer()
  return await audioContext.decodeAudioData(arrayBuffer)
}

// 检测音频中的静音段落
export const detectSilence = (
  audioBuffer: AudioBuffer,
  silenceThreshold = 0.01, // 静音阈值
  minSilenceDuration = 0.5 // 最小静音持续时间（秒）
): Array<{start: number, end: number, duration: number}> => {
  const channelData = audioBuffer.getChannelData(0)
  const sampleRate = audioBuffer.sampleRate
  const silenceSegments = []
  
  let isSilent = false
  let silenceStart = 0
  
  for (let i = 0; i < channelData.length; i++) {
    const amplitude = Math.abs(channelData[i])
    
    if (amplitude < silenceThreshold) {
      // 检测到静音
      if (!isSilent) {
        isSilent = true
        silenceStart = i
      }
    } else {
      // 检测到声音
      if (isSilent) {
        const silenceEnd = i
        const silenceDuration = (silenceEnd - silenceStart) / sampleRate
        
        // 只记录超过最小持续时间的静音段
        if (silenceDuration >= minSilenceDuration) {
          silenceSegments.push({
            start: silenceStart / sampleRate,
            end: silenceEnd / sampleRate,
            duration: silenceDuration
          })
        }
        
        isSilent = false
      }
    }
  }
  
  // 检查是否以静音结束
  if (isSilent) {
    const silenceEnd = channelData.length
    const silenceDuration = (silenceEnd - silenceStart) / sampleRate
    
    if (silenceDuration >= minSilenceDuration) {
      silenceSegments.push({
        start: silenceStart / sampleRate,
        end: silenceEnd / sampleRate,
        duration: silenceDuration
      })
    }
  }
  
  return silenceSegments
}

// 分析语速
export const analyzeSpeechRate = (
  audioDuration: number, // 音频总时长（秒）
  wordCount: number // 单词数量
): {
  wordsPerMinute: number;
  rateQuality: string;
} => {
  // 计算每分钟单词数
  const wordsPerMinute = (wordCount / audioDuration) * 60
  
  // 评估语速质量
  let rateQuality = 'good'
  
  if (wordsPerMinute < 120) {
    rateQuality = 'slow'
  } else if (wordsPerMinute > 180) {
    rateQuality = 'fast'
  }
  
  return {
    wordsPerMinute,
    rateQuality
  }
} 