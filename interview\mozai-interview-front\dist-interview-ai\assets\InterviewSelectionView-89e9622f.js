import{d as $,n as q,r as m,m as T,c as _,a as s,q as p,b as i,w as a,F as j,i as N,s as E,u as M,E as f,e as S,o as g,g as v,x as A,y as D,j as k,z as L,A as R,B as z,k as C,C as F,D as P,G,f as B,_ as H}from"./index-d8f61d92.js";import{a as x}from"./index-3556d18e.js";const U={class:"selection-container"},J={class:"selection-content"},K={class:"selection-area"},O={class:"selection-group"},W={class:"option-cards"},X={class:"selection-group"},Y={class:"option-cards"},Z=["onClick"],h={class:"selection-group"},ee={class:"option-cards"},se={class:"actions"},te={key:0,class:"loading-overlay"},ne={class:"loading-content"},oe=$({__name:"InterviewSelectionView",setup(le){const I=M(),r=q(),y=m(""),w=m(""),u=m(""),d=m(!1),Q=()=>{I.push("/")},V=async()=>{d.value=!0;try{let o=!1;r.setInterviewType({stage:y.value,position:w.value,experience:u.value});const e={type:"mock",candidateName:"面试者",position:r.interviewType.position,stage:r.interviewType.stage,experience:r.interviewType.experience,questions:[]},l=["请介绍一下你自己","你的优势是什么?","你为什么选择这个岗位?"];try{f.info("正在生成面试问题，请耐心等待...");const t=await x.createInterview(e);let n=null;if(t&&typeof t=="object"&&"data"in t&&t.data&&"interviewId"in t.data&&(n=t.data.interviewId),n){r.setInterviewId(n);try{const c=await x.getInterviewQuestions(n);c&&typeof c=="object"&&"questions"in c&&Array.isArray(c.questions)?(r.setQuestions(c.questions),f.success("面试问题生成成功")):(r.setQuestions(l),f.warning("无法获取面试问题，将使用默认问题"))}catch{r.setQuestions(l),f.warning("获取面试问题失败，将使用默认问题")}o=!0}else r.setQuestions(l)}catch(t){console.error("创建面试失败:",t);const n=(t==null?void 0:t.toString())||"";if(n.includes("resumeId")||n.includes("简历")||t&&typeof t=="object"&&"message"in t&&typeof t.message=="string"&&(t.message.includes("resumeId")||t.message.includes("简历"))){f.error("简历未创建，请先在简历系统创建对应简历"),d.value=!1;return}r.setQuestions(l),f.warning("创建面试失败，将使用默认问题")}o?I.push("/interview"):d.value=!1}catch(o){console.error("处理继续按钮点击事件失败:",o);const e=(o==null?void 0:o.toString())||"";if(e.includes("resumeId")||e.includes("简历")||o&&typeof o=="object"&&"message"in o&&typeof o.message=="string"&&(o.message.includes("resumeId")||o.message.includes("简历"))){f.error("简历未创建，请先在简历系统创建对应简历"),d.value=!1;return}I.push("/interview")}finally{d.value=!1}},b=m();return T(async()=>{try{const o=await x.getResumeCategoryList(),{code:e,message:l,data:t}=o;if(e!==0)throw new Error(l);b.value=t}catch(o){console.error("获取简历分类失败",o)}}),(o,e)=>{const l=S("el-icon"),t=S("el-button");return g(),_("div",U,[s("div",J,[e[22]||(e[22]=s("h1",null,"选择面试类型",-1)),e[23]||(e[23]=s("p",{class:"description"}," 请选择您要参加的面试类型和岗位，系统将根据您的选择生成相应的面试问题。 ",-1)),s("div",K,[s("div",O,[e[10]||(e[10]=s("h2",null,"面试环节",-1)),s("div",W,[s("div",{class:p(["option-card",{active:y.value==="hr"}]),onClick:e[0]||(e[0]=n=>y.value="hr")},[i(l,null,{default:a(()=>[i(v(A))]),_:1}),e[6]||(e[6]=s("h3",null,"HR面试",-1)),e[7]||(e[7]=s("p",null,"侧重考察个人软实力、职业规划及团队协作能力",-1))],2),s("div",{class:p(["option-card",{active:y.value==="tech"}]),onClick:e[1]||(e[1]=n=>y.value="tech")},[i(l,null,{default:a(()=>[i(v(D))]),_:1}),e[8]||(e[8]=s("h3",null,"技术面试",-1)),e[9]||(e[9]=s("p",null,"侧重考察专业技能、解决问题的能力及技术深度",-1))],2)])]),s("div",X,[e[11]||(e[11]=s("h2",null,"面试岗位",-1)),s("div",Y,[(g(!0),_(j,null,N(b.value,n=>(g(),_("div",{key:n.catId,class:p(["option-card",{active:w.value===n.catId}]),onClick:c=>w.value=n.catId},[n.catId===1?(g(),k(l,{key:0},{default:a(()=>[i(v(L))]),_:1})):n.catId===2?(g(),k(l,{key:1},{default:a(()=>[i(v(R))]),_:1})):n.catId===3?(g(),k(l,{key:2},{default:a(()=>[i(v(z))]),_:1})):E("",!0),s("h3",null,C(n.name),1),s("p",null,C(n.description),1)],10,Z))),128))])]),s("div",h,[e[20]||(e[20]=s("h2",null,"工作经验",-1)),s("div",ee,[s("div",{class:p(["option-card",{active:u.value==="fresh"}]),onClick:e[2]||(e[2]=n=>u.value="fresh")},[i(l,null,{default:a(()=>[i(v(F))]),_:1}),e[12]||(e[12]=s("h3",null,"应届生",-1)),e[13]||(e[13]=s("p",null,"刚毕业或即将毕业，无全职工作经验",-1))],2),s("div",{class:p(["option-card",{active:u.value==="1-3"}]),onClick:e[3]||(e[3]=n=>u.value="1-3")},[i(l,null,{default:a(()=>[i(v(R))]),_:1}),e[14]||(e[14]=s("h3",null,"1-3年",-1)),e[15]||(e[15]=s("p",null,"初级工程师，有1-3年工作经验",-1))],2),s("div",{class:p(["option-card",{active:u.value==="3-5"}]),onClick:e[4]||(e[4]=n=>u.value="3-5")},[i(l,null,{default:a(()=>[i(v(P))]),_:1}),e[16]||(e[16]=s("h3",null,"3-5年",-1)),e[17]||(e[17]=s("p",null,"中级工程师，有3-5年相关工作经验",-1))],2),s("div",{class:p(["option-card",{active:u.value==="5+"}]),onClick:e[5]||(e[5]=n=>u.value="5+")},[i(l,null,{default:a(()=>[i(v(G))]),_:1}),e[18]||(e[18]=s("h3",null,"5年以上",-1)),e[19]||(e[19]=s("p",null,"高级工程师，有5年以上丰富工作经验",-1))],2)])]),s("div",se,[i(t,{onClick:Q},{default:a(()=>e[21]||(e[21]=[B("返回",-1)])),_:1,__:[21]}),i(t,{type:"primary",onClick:V,disabled:!y.value||!w.value||!u.value||d.value,loading:d.value},{default:a(()=>[B(C(d.value?"生成面试问题中...":"继续"),1)]),_:1},8,["disabled","loading"])])])]),d.value?(g(),_("div",te,[s("div",ne,[i(l,{class:"loading-icon"},{default:a(()=>e[24]||(e[24]=[s("svg",{class:"circular",viewBox:"0 0 50 50"},[s("circle",{class:"path",cx:"25",cy:"25",r:"20",fill:"none"})],-1)])),_:1,__:[24]}),e[25]||(e[25]=s("p",null,"面试问题生成中，请耐心等待...",-1))])])):E("",!0)])}}});const re=H(oe,[["__scopeId","data-v-1f2e9b4d"]]);export{re as default};
