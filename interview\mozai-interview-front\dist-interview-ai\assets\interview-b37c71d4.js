import{J as f,S as h}from"./index-4df1abd4.js";var d=(t=>(t.MOCK="mock",t.FORMAL="formal",t))(d||{});function v(t){let e=t.position;e&&!isNaN(Number(e))&&(e=String(Number(e)));const o={id:t.id,position:e,interviewTime:t.interviewTime,status:t.status===0?"in_progress":t.status===1?"waiting_result":t.status===2?"completed":"pending",videoUrl:t.videoUrl,result:t.result};let r;return t.overallScore!==void 0&&t.feedback&&(r={score:t.overallScore,feedback:t.feedback,strengths:t.strengths||"",improvements:t.improvements||""}),t.type==="formal"?{...o,type:"formal",company:t.company||"未知公司",evaluation:r}:{...o,type:"mock",stage:t.stage||"未知阶段",evaluation:r}}const M=t=>t?t.includes("yyyy")||t.includes("Fr")||t.includes("MM")||t.includes("dd")?!0:!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(t):!1,g=t=>{if(!t)return"";if(M(t)){console.warn("检测到无效的日期格式:",t),console.warn("使用当前时间替代");const e=new Date,o=e.getFullYear(),r=String(e.getMonth()+1).padStart(2,"0"),n=String(e.getDate()).padStart(2,"0"),i=String(e.getHours()).padStart(2,"0"),s=String(e.getMinutes()).padStart(2,"0"),a=String(e.getSeconds()).padStart(2,"0");return`${o}-${r}-${n} ${i}:${s}:${a}`}try{const e=new Date(t);if(isNaN(e.getTime())){console.error("无效的日期字符串:",t);const c=new Date,S=c.getFullYear(),m=String(c.getMonth()+1).padStart(2,"0"),p=String(c.getDate()).padStart(2,"0"),y=String(c.getHours()).padStart(2,"0"),$=String(c.getMinutes()).padStart(2,"0"),w=String(c.getSeconds()).padStart(2,"0");return`${S}-${m}-${p} ${y}:${$}:${w}`}const o=e.getFullYear(),r=String(e.getMonth()+1).padStart(2,"0"),n=String(e.getDate()).padStart(2,"0"),i=String(e.getHours()).padStart(2,"0"),s=String(e.getMinutes()).padStart(2,"0"),a=String(e.getSeconds()).padStart(2,"0");return`${o}-${r}-${n} ${i}:${s}:${a}`}catch(e){console.error("格式化日期时出错:",e);const o=new Date,r=o.getFullYear(),n=String(o.getMonth()+1).padStart(2,"0"),i=String(o.getDate()).padStart(2,"0"),s=String(o.getHours()).padStart(2,"0"),a=String(o.getMinutes()).padStart(2,"0"),c=String(o.getSeconds()).padStart(2,"0");return`${r}-${n}-${i} ${s}:${a}:${c}`}},u="/interview/interviews",l=f.create({timeout:12e4});l.interceptors.request.use(t=>{const e=localStorage.getItem("token");return e&&(t.headers.token=e),t},t=>Promise.reject(t));const F=async t=>{try{const e=await l.get(`${u}/type/${t}`);return e.data.code===0&&e.data.data?e.data.data.map(o=>v(o)):[]}catch(e){return console.error(`获取${t}面试列表失败:`,e),[]}},D=async t=>{try{let e=t.interviewTime;e&&(e.includes("yyyy")||!e.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/))&&(e=g(new Date().toString()),console.log("修正日期格式:",e));const o={type:d.MOCK,position:t.position,interviewTime:e,status:0};console.log("提交模拟面试数据:",o);const r=await l.post(`${u}`,o);return r.data.code===0&&r.data.data?r.data.data.id:null}catch(e){return console.error("创建模拟面试失败:",e),null}},O=async t=>{try{let e=t.interviewTime;e&&(e.includes("yyyy")||!e.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/))&&(e=g(new Date().toString()),console.log("修正日期格式:",e));const o={type:d.FORMAL,company:t.company,position:t.position,interviewTime:e,status:t.status==="in_progress"?0:t.status==="waiting_result"?1:t.status==="completed"?2:0,videoUrl:t.videoUrl};console.log("提交正式面试数据:",o);const r=await l.post(`${u}`,o);return console.log("服务器响应:",r.data),r.data.code===0&&r.data.data&&r.data.data.interviewId?r.data.data.interviewId:(console.error("创建正式面试失败:",r.data.message||"未知错误"),null)}catch(e){return console.error("创建正式面试失败:",e),null}},T=async(t,e,o)=>{try{console.log("尝试使用OSS直传上传视频...");const{uploadToOss:r,loadOssModule:n}=await h(()=>import("./ossUploader-bfb7a53e.js"),["assets/ossUploader-bfb7a53e.js","assets/index-4df1abd4.js","assets/index-0ebec590.css"]);if(!await n())throw new Error("OSS模块不可用，无法使用直传功能");const s=await r(e,"interview/video",o);if(!s||!s.url)throw new Error("OSS上传失败，请稍后重试");const a=await l.post(`${u}/${t}/video-url`,{videoUrl:s.url});if(a.data.code!==0)throw new Error(`更新视频URL失败: ${a.data.message||"未知错误"}`);return!0}catch(r){throw console.error("OSS直传失败:",r),r}},k=async t=>{try{return t.type===d.MOCK?D(t):t.type===d.FORMAL?O(t):(console.error("未知的面试类型"),null)}catch(e){return console.error("创建面试失败:",e),null}};export{d as I,k as c,g as f,F as g,T as u};
