@echo off
echo 🔧 修复 Element Plus 构建错误...

REM 1. 清理环境
echo 📦 清理现有依赖...
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json
if exist dist-interview-ai rmdir /s /q dist-interview-ai
if exist .vite rmdir /s /q .vite

REM 2. 清理 npm 缓存
echo 🧹 清理 npm 缓存...
npm cache clean --force

REM 3. 重新安装依赖
echo 📥 重新安装依赖...
npm install

REM 4. 尝试简化构建
echo 🚀 使用简化配置构建...
npm run build:simple

REM 检查构建结果
if exist dist-interview-ai (
    echo ✅ 构建成功！
    echo 📁 输出目录: dist-interview-ai
    dir dist-interview-ai
) else (
    echo ❌ 简化构建失败，尝试标准构建...
    npm run build
    
    if exist dist-interview-ai (
        echo ✅ 标准构建成功！
    ) else (
        echo ❌ 构建失败，请检查错误信息
        exit /b 1
    )
)

echo 🎉 修复完成！
pause
