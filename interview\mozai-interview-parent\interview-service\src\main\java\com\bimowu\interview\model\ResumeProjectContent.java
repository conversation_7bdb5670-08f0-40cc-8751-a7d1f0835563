package com.bimowu.interview.model;


import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 项目内容表
 * @TableName resume_project_content
 */
@TableName(value ="resume_project_content")
@Data
public class ResumeProjectContent implements Serializable {
    /**
     * 
     */
    @TableId
    private Long conId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 内容文本
     */
    private String text;

    /**
     * 内容顺序
     */
    private Integer contentOrder;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}