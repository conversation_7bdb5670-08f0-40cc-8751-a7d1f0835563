import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useUserStore } from './user'
import { templateConfig } from '@/components/templates'

export const useResumeStore = defineStore('resume', () => {
  const userStore = useUserStore()
  const currentResume = ref(null)
  const templates = ref([
    {
      id: 1,
      name: '线条风格简历模板',
      thumbnail: 'https://bmw-pic.oss-cn-beijing.aliyuncs.com/muban1.png',
      colors: ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6'],
      supportLanguages: ['中文', '英文', '日文', '韩文', '法文', '德文', '西班牙文'],
      features: ['封面', '自荐信', '专业简洁']
    },
    {
      id: 2,
      name: '左右分栏蓝色模板',
      thumbnail: 'https://bmw-pic.oss-cn-beijing.aliyuncs.com/muban2.png',
      colors: ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6'],
      supportLanguages: ['中文', '英文', '日文', '韩文', '法文', '德文', '西班牙文'],
      features: ['封面', '自荐信', '现代风格']
    },
    {
      id: 3,
      name: '简洁浅色方格模板',
      thumbnail: 'https://bmw-pic.oss-cn-beijing.aliyuncs.com/muban3.png',
      colors: ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6'],
      supportLanguages: ['中文', '英文', '日文', '韩文', '法文', '德文', '西班牙文'],
      features: ['封面', '自荐信', '通用模板']
    },
    {
      id: 4,
      name: '专业商务简历模板',
      thumbnail: 'https://bmw-pic.oss-cn-beijing.aliyuncs.com/muban4.png',
      colors: ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6'],
      supportLanguages: ['中文', '英文', '日文', '韩文', '法文', '德文', '西班牙文'],
      features: ['商务风格', '标签式布局', '突出技能']
    },
    {
      id: 5,
      name: '蓝色顶部简历模板',
      thumbnail: 'https://bmw-pic.oss-cn-beijing.aliyuncs.com/muban5.png',
      colors: ['#4472c4', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6'],
      supportLanguages: ['中文', '英文', '日文', '韩文', '法文', '德文', '西班牙文'],
      features: ['蓝色主题', '时间轴布局', '简洁大方']
    }
  ])
  
  const resumeModules = ref([
    { id: 'basic', name: '基本信息', required: true },
    { id: 'education', name: '教育经历', required: false },
    { id: 'work', name: '工作经验', required: false },
    { id: 'internship', name: '实习经历', required: false },
    { id: 'projects', name: '项目经历', required: false },
    { id: 'skills', name: '技能特长', required: false },
    { id: 'certificates', name: '证书奖项', required: false },
    { id: 'campus', name: '校园经历', required: false },
    { id: 'interests', name: '兴趣爱好', required: false },
    { id: 'evaluation', name: '个人评价', required: false }
  ])
  
  // 判断用户是否已登录 - 始终返回true，不再校验登录状态
  const isUserLoggedIn = computed(() => {
    return true
  })
  
  function setCurrentResume(resume) {
    currentResume.value = resume
  }
  
  function createEmptyResume(templateId = 1) {
    return {
      id: Date.now(),
      templateId,
      name: '未命名简历',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      modules: {
        basic: {
          name: '',
          gender: '',
          birthday: '',
          birthDate: '',
          phone: '',
          email: '',
          address: '',
          hometown: '',
          photo: '',
          avatar: '',
          age: '',
          jobObjective: ''
        },
        education: [],
        work: [],
        internship: [],
        projects: [],
        skills: [],
        certificates: {
          id: null,
          resumeId: null,
          certificateName: ''
        },
        campus: {
          id: null,
          resumeId: null,
          description: ''
        },
        interests: {
          id: null,
          resumeId: null,
          description: ''
        },
        selfEvaluation: {
          id: null,
          resumeId: null,
          description: ''
        }
      }
    }
  }
  
  return {
    currentResume,
    templates,
    resumeModules,
    isUserLoggedIn,
    setCurrentResume,
    createEmptyResume
  }
}) 