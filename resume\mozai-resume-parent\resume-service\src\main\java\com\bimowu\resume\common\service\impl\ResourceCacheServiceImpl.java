package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.common.service.ResourceCacheService;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.concurrent.TimeUnit;

/**
 * 资源缓存服务实现
 */
@Slf4j
@Service
public class ResourceCacheServiceImpl implements ResourceCacheService {
    
    private Cache<String, byte[]> imageCache;
    private Cache<String, String> cssCache;
    private Cache<String, String> templateCache;
    private Cache<String, byte[]> fontCache;
    
    @PostConstruct
    public void initialize() {
        // 初始化各种缓存
        imageCache = Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .recordStats()
                .build();
        
        cssCache = Caffeine.newBuilder()
                .maximumSize(500)
                .expireAfterWrite(2, TimeUnit.HOURS)
                .recordStats()
                .build();
        
        templateCache = Caffeine.newBuilder()
                .maximumSize(200)
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .recordStats()
                .build();
        
        fontCache = Caffeine.newBuilder()
                .maximumSize(100)
                .expireAfterWrite(24, TimeUnit.HOURS)
                .recordStats()
                .build();
        
        log.info("资源缓存服务初始化完成");
    }
    
    @Override
    public void cacheImage(String imagePath, byte[] imageData) {
        if (imagePath != null && imageData != null) {
            imageCache.put(imagePath, imageData);
            log.debug("缓存图片资源: {}", imagePath);
        }
    }
    
    @Override
    public InputStream getCachedImage(String imagePath) {
        byte[] imageData = imageCache.getIfPresent(imagePath);
        if (imageData != null) {
            log.debug("从缓存获取图片资源: {}", imagePath);
            return new ByteArrayInputStream(imageData);
        }
        return null;
    }
    
    @Override
    public void cacheCSS(String cssPath, String cssContent) {
        if (cssPath != null && cssContent != null) {
            cssCache.put(cssPath, cssContent);
            log.debug("缓存CSS资源: {}", cssPath);
        }
    }
    
    @Override
    public String getCachedCSS(String cssPath) {
        String cssContent = cssCache.getIfPresent(cssPath);
        if (cssContent != null) {
            log.debug("从缓存获取CSS资源: {}", cssPath);
        }
        return cssContent;
    }
    
    @Override
    public void cacheTemplate(String templatePath, String templateContent) {
        if (templatePath != null && templateContent != null) {
            templateCache.put(templatePath, templateContent);
            log.debug("缓存模板资源: {}", templatePath);
        }
    }
    
    @Override
    public String getCachedTemplate(String templatePath) {
        String templateContent = templateCache.getIfPresent(templatePath);
        if (templateContent != null) {
            log.debug("从缓存获取模板资源: {}", templatePath);
        }
        return templateContent;
    }
    
    @Override
    public boolean isResourceCached(String resourcePath, ResourceType resourceType) {
        if (resourcePath == null || resourceType == null) {
            return false;
        }
        
        switch (resourceType) {
            case IMAGE:
                return imageCache.getIfPresent(resourcePath) != null;
            case CSS:
                return cssCache.getIfPresent(resourcePath) != null;
            case TEMPLATE:
                return templateCache.getIfPresent(resourcePath) != null;
            case FONT:
                return fontCache.getIfPresent(resourcePath) != null;
            default:
                return false;
        }
    }
    
    @Override
    public void clearCache(ResourceType resourceType) {
        switch (resourceType) {
            case IMAGE:
                imageCache.invalidateAll();
                log.info("已清除图片缓存");
                break;
            case CSS:
                cssCache.invalidateAll();
                log.info("已清除CSS缓存");
                break;
            case TEMPLATE:
                templateCache.invalidateAll();
                log.info("已清除模板缓存");
                break;
            case FONT:
                fontCache.invalidateAll();
                log.info("已清除字体缓存");
                break;
        }
    }
    
    @Override
    public void clearAllCache() {
        imageCache.invalidateAll();
        cssCache.invalidateAll();
        templateCache.invalidateAll();
        fontCache.invalidateAll();
        log.info("已清除所有资源缓存");
    }
    
    @Override
    public CacheStatistics getCacheStatistics() {
        CacheStatistics statistics = new CacheStatistics();
        
        // 获取各缓存的统计信息
        CacheStats imageStats = imageCache.stats();
        CacheStats cssStats = cssCache.stats();
        CacheStats templateStats = templateCache.stats();
        CacheStats fontStats = fontCache.stats();
        
        // 设置缓存数量
        statistics.setImageCount(imageCache.estimatedSize());
        statistics.setCssCount(cssCache.estimatedSize());
        statistics.setTemplateCount(templateCache.estimatedSize());
        statistics.setFontCount(fontCache.estimatedSize());
        
        // 计算总体命中率
        long totalRequests = imageStats.requestCount() + cssStats.requestCount() + 
                           templateStats.requestCount() + fontStats.requestCount();
        long totalHits = imageStats.hitCount() + cssStats.hitCount() + 
                        templateStats.hitCount() + fontStats.hitCount();
        
        double hitRate = totalRequests > 0 ? (double) totalHits / totalRequests : 0.0;
        statistics.setHitRate(hitRate);
        
        // 估算总大小（简化计算）
        long estimatedSize = statistics.getImageCount() * 50000 + // 假设每张图片50KB
                           statistics.getCssCount() * 5000 +     // 假设每个CSS文件5KB
                           statistics.getTemplateCount() * 10000 + // 假设每个模板10KB
                           statistics.getFontCount() * 2000000;   // 假设每个字体2MB
        statistics.setTotalSize(estimatedSize);
        
        return statistics;
    }
}