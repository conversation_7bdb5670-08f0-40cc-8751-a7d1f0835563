package com.bimowu.resume.common.service;

import java.io.InputStream;

/**
 * 资源缓存服务接口
 * 负责缓存PDF生成过程中使用的各种资源
 */
public interface ResourceCacheService {
    
    /**
     * 缓存图片资源
     * @param imagePath 图片路径
     * @param imageData 图片数据
     */
    void cacheImage(String imagePath, byte[] imageData);
    
    /**
     * 获取缓存的图片资源
     * @param imagePath 图片路径
     * @return 图片输入流
     */
    InputStream getCachedImage(String imagePath);
    
    /**
     * 缓存CSS资源
     * @param cssPath CSS路径
     * @param cssContent CSS内容
     */
    void cacheCSS(String cssPath, String cssContent);
    
    /**
     * 获取缓存的CSS资源
     * @param cssPath CSS路径
     * @return CSS内容
     */
    String getCachedCSS(String cssPath);
    
    /**
     * 缓存模板资源
     * @param templatePath 模板路径
     * @param templateContent 模板内容
     */
    void cacheTemplate(String templatePath, String templateContent);
    
    /**
     * 获取缓存的模板资源
     * @param templatePath 模板路径
     * @return 模板内容
     */
    String getCachedTemplate(String templatePath);
    
    /**
     * 检查资源是否已缓存
     * @param resourcePath 资源路径
     * @param resourceType 资源类型
     * @return 是否已缓存
     */
    boolean isResourceCached(String resourcePath, ResourceType resourceType);
    
    /**
     * 清除指定类型的缓存
     * @param resourceType 资源类型
     */
    void clearCache(ResourceType resourceType);
    
    /**
     * 清除所有缓存
     */
    void clearAllCache();
    
    /**
     * 获取缓存统计信息
     * @return 缓存统计
     */
    CacheStatistics getCacheStatistics();
    
    /**
     * 资源类型枚举
     */
    enum ResourceType {
        IMAGE, CSS, TEMPLATE, FONT
    }
    
    /**
     * 缓存统计信息
     */
    class CacheStatistics {
        private long imageCount;
        private long cssCount;
        private long templateCount;
        private long fontCount;
        private long totalSize;
        private double hitRate;
        
        // getters and setters
        public long getImageCount() { return imageCount; }
        public void setImageCount(long imageCount) { this.imageCount = imageCount; }
        
        public long getCssCount() { return cssCount; }
        public void setCssCount(long cssCount) { this.cssCount = cssCount; }
        
        public long getTemplateCount() { return templateCount; }
        public void setTemplateCount(long templateCount) { this.templateCount = templateCount; }
        
        public long getFontCount() { return fontCount; }
        public void setFontCount(long fontCount) { this.fontCount = fontCount; }
        
        public long getTotalSize() { return totalSize; }
        public void setTotalSize(long totalSize) { this.totalSize = totalSize; }
        
        public double getHitRate() { return hitRate; }
        public void setHitRate(double hitRate) { this.hitRate = hitRate; }
    }
}