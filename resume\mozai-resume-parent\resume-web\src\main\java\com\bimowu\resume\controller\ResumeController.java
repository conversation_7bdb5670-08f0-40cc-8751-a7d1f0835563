package com.bimowu.resume.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bimowu.resume.base.BaseResponse;
import com.bimowu.resume.base.Constant;
import com.bimowu.resume.common.service.*;
import com.bimowu.resume.dto.ResumeFullSaveDto;
import com.bimowu.resume.entity.*;
import com.bimowu.resume.common.service.impl.AiManager;
import com.bimowu.resume.utils.OssUtils;
import com.bimowu.resume.utils.RedisUtil;
import com.bimowu.resume.common.utils.ResumeExportUtil;
import com.bimowu.resume.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.bimowu.resume.exception.BaseException;

/**
 * <p>
 * 简历主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Api(tags = "简历管理接口")
@RestController
@RequestMapping("/resume")
@Slf4j
public class ResumeController {

    @Autowired
    private ResumeInformationService informationService;
    @Autowired
    private ResumeEducationalService educationalService;
    @Autowired
    private ResumeWorkService workService;
    @Autowired
    private ResumeProjectExperienceService resumeProjectExperienceService;
    @Autowired
    private ResumePracticeService practiceService;
    @Autowired
    private ResumeTalentService talentService;
    @Autowired
    private ResumeCertificateService certificateService;
    @Autowired
    private ResumeCampusService campusService;
    @Autowired
    private ResumeInterestService interestService;
    @Autowired
    private ResumeEvaluateService evaluateService;
    @Autowired
    private ResumeService resumeService;
    @Autowired
    private ResumeCategoryRelationService resumeCategoryRelationService;
    @Autowired
    private KsUserService ksUserService;
    @Autowired
    private OssUtils ossUtils;
    @Autowired
    private AiManager aiManager;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private SysTodoService sysTodoService;
    @Autowired
    private ResumeProgressService resumeProgressService;
    @Value("${resume.url}")
    private String resumeUrl;
    @PostMapping("/upload/avatar")
    public BaseResponse uploadAvatar(@RequestParam("file") MultipartFile file) {
        // 处理文件上传逻辑
        String avgUrl = null;
        try {
            //上传图片到阿里云
            avgUrl = ossUtils.uploadFile(file);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.ok(avgUrl);
    }

    /**
     * 新增登录用户的简历
     */
    @Transactional
    @PostMapping("/save")
    public BaseResponse saveFullResume(@RequestBody ResumeFullSaveDto dto,@RequestHeader Long userId) {
        log.info("新增/修改简历:{}", dto.toString());
        Long entityResumeId = null;
        Map map = (Map)redisUtil.get(String.format(Constant.REDIS_KEY, userId));
        log.info("新增简历，用户信息:{}", map);
        String nickName = (String) map.get("nickname");
        Long resumeCategoryRelationId=null;
        /*判断对应的类别的简历是否已存在*/
        List<ResumeCategoryRelation> resumeCategoryRelationList = resumeCategoryRelationService.list(new LambdaQueryWrapper<ResumeCategoryRelation>()
                .eq(ResumeCategoryRelation::getUserId, userId));
        if (CollectionUtils.isNotEmpty(resumeCategoryRelationList)) {

            for(ResumeCategoryRelation relation : resumeCategoryRelationList) {
                if(relation.getCatId().equals(dto.getResumeVo().getCategory())){//系统存在和当前编辑/新增简历职位类型一致的简历
                    //判断是否为同一份简历，如果为新增或非同一份简历，则拒绝新增或修改
                    if (dto.getResumeVo().getResumeId() == null || !relation.getResumeId().equals(dto.getResumeVo().getResumeId())) {
                        return BaseResponse.error(-9888, "此类别简历已存在!");
                    }
                }
                //如果为同一份id，则可以修改，同时记录当前的关联id，用于修改
                if(relation.getResumeId().equals(dto.getResumeVo().getResumeId())){
                    resumeCategoryRelationId = relation.getId();
                    log.info("<获取到简历与职位关系表id>:{}", resumeCategoryRelationId);
                }
            }


        }
        //保存简历
        if (dto.getResumeVo() != null) {
            Resume resume = new Resume();
            BeanUtils.copyProperties(dto.getResumeVo(), resume);
            resume.setCreateAt(nickName);
            resume.setUserId(userId);
            resume.setStatus(0);
            log.info("保存简历:{}", resume);
            resumeService.saveOrUpdate(resume);
            entityResumeId = resume.getResumeId(); // MyBatis-Plus会自动回填
        }

        // 新增或修改类型关联表
        if (dto.getResumeVo() != null) {
            ResumeCategoryRelation relation = new ResumeCategoryRelation();
            relation.setCatId(dto.getResumeVo().getCategory());
            relation.setResumeId(entityResumeId);
            relation.setUserId(userId);
            relation.setId(resumeCategoryRelationId!= null?resumeCategoryRelationId:null);
            resumeCategoryRelationService.saveOrUpdate(relation);
        }

        // 1. 保存基本信息
        if (dto.getInformation() != null) {
            ResumeInformation entity = new ResumeInformation();
            BeanUtils.copyProperties(dto.getInformation(), entity);
            entity.setCreateAt(nickName);
            entity.setResumeId(entityResumeId);
            informationService.saveOrUpdate(entity);
        }
        //统一删除旧数据
        // 先删除旧数据
        LambdaQueryWrapper<ResumeEducational> educationalWrapper = new LambdaQueryWrapper<ResumeEducational>()
                .eq(ResumeEducational::getResumeId, entityResumeId);
        educationalService.remove(educationalWrapper);
        // 先删除旧数据
        LambdaQueryWrapper<ResumeWork> workWrapper = new LambdaQueryWrapper<ResumeWork>()
                .eq(ResumeWork::getResumeId, entityResumeId);
        workService.remove(workWrapper);

        // 先删除旧数据
        LambdaQueryWrapper<ResumeProjectExperience> projectWrapper = new LambdaQueryWrapper<ResumeProjectExperience>()
                .eq(ResumeProjectExperience::getResumeId, entityResumeId);
        resumeProjectExperienceService.remove(projectWrapper);

        // 先删除旧数据
        LambdaQueryWrapper<ResumePractice> practiceWrapper = new LambdaQueryWrapper<ResumePractice>()
                .eq(ResumePractice::getResumeId, entityResumeId);
        practiceService.remove(practiceWrapper);

        // 先删除旧数据
        LambdaQueryWrapper<ResumeTalent> talentWrapper = new LambdaQueryWrapper<ResumeTalent>()
                .eq(ResumeTalent::getResumeId, entityResumeId);
        talentService.remove(talentWrapper);

        LambdaQueryWrapper<ResumeCertificate> certificateWrapper = new LambdaQueryWrapper<ResumeCertificate>()
                .eq(ResumeCertificate::getResumeId, entityResumeId);
        certificateService.remove(certificateWrapper);
        LambdaQueryWrapper<ResumeCampus> campusWrapper = new LambdaQueryWrapper<ResumeCampus>()
                .eq(ResumeCampus::getResumeId, entityResumeId);
        campusService.remove(campusWrapper);
        LambdaQueryWrapper<ResumeInterest> interestWrapper = new LambdaQueryWrapper<ResumeInterest>()
                .eq(ResumeInterest::getResumeId, entityResumeId);
        interestService.remove(interestWrapper);

        LambdaQueryWrapper<ResumeEvaluate> evaluateWrapper = new LambdaQueryWrapper<ResumeEvaluate>()
                .eq(ResumeEvaluate::getResumeId, entityResumeId);
        evaluateService.remove(evaluateWrapper);


        // 2. 批量保存教育经历
        if (dto.getEducationList() != null && dto.getEducationList().size() > 0) {
            // 添加新数据
            for (ResumeEducationalVo vo : dto.getEducationList()) {
                ResumeEducational entity = new ResumeEducational();
                BeanUtils.copyProperties(vo, entity);
                entity.setCreateAt(nickName);
                entity.setResumeId(entityResumeId);
                entity.setEduId(null);
                educationalService.save(entity); // 使用save而不是saveOrUpdate
            }
        }
        // 3. 批量保存工作经验
        if (dto.getWorkList() != null && dto.getWorkList().size() > 0) {
            // 添加新数据
            for (ResumeWorkVo vo : dto.getWorkList()) {
                ResumeWork entity = new ResumeWork();
                BeanUtils.copyProperties(vo, entity);
                entity.setCreateAt(nickName);
                entity.setResumeId(entityResumeId);
                entity.setResId(null);
                workService.save(entity); // 使用save而不是saveOrUpdate
            }
        }
        // 4. 批量保存项目经验
        if (dto.getProjectList() != null && dto.getProjectList().size() > 0) {
            // 添加新数据
            for (ResumeProjectExperienceVo vo : dto.getProjectList()) {
                ResumeProjectExperience entity = new ResumeProjectExperience();
                BeanUtils.copyProperties(vo, entity);
                // 设置proId到projectId
                if (vo.getProId() != null) {
                    entity.setProjectId(vo.getProId());
                }
                entity.setCreateAt(nickName);
                entity.setResumeId(entityResumeId);
                entity.setExpId(null);
                resumeProjectExperienceService.save(entity); // 使用save而不是saveOrUpdate
            }
        }
        // 5. 练手项目
        if (dto.getPracticeList() != null && dto.getPracticeList().size()>0) {

            // 添加新数据
            for (ResumePracticeVo vo : dto.getPracticeList()) {
                ResumePractice entity = new ResumePractice();
                BeanUtils.copyProperties(vo, entity);
                entity.setCreateAt(nickName);
                entity.setResumeId(entityResumeId);
                entity.setPraId(null);
                practiceService.save(entity); // 使用save而不是saveOrUpdate
            }
        }
        // 6. 技能特长
        if (dto.getTalentList() != null && dto.getTalentList().size()>0) {
            // 添加新数据
            for (ResumeTalentVo vo : dto.getTalentList()) {
                ResumeTalent entity = new ResumeTalent();
                BeanUtils.copyProperties(vo, entity);
                entity.setCreateAt(nickName);
                entity.setResumeId(entityResumeId);
                entity.setTalId(null);
                talentService.save(entity); // 使用save而不是saveOrUpdate
            }
        }
        // 7. 证书奖项
        if (dto.getCertificate() != null && StringUtils.isNotBlank(dto.getCertificate().getCertificateName()) ) {
            ResumeCertificate entity = new ResumeCertificate();
            BeanUtils.copyProperties(dto.getCertificate(), entity);
            entity.setCreateAt(nickName);
            entity.setResumeId(entityResumeId);
            entity.setCerId(null);
            certificateService.save(entity);
        }
        // 8. 校园经历
        if (dto.getCampus() != null && StringUtils.isNotBlank(dto.getCampus().getCampusExperience())) {
            ResumeCampus entity = new ResumeCampus();
            BeanUtils.copyProperties(dto.getCampus(), entity);
            entity.setCreateAt(nickName);
            entity.setResumeId(entityResumeId);
            entity.setCamId(null);
            campusService.save(entity);
        }
        // 9. 兴趣爱好
        if (dto.getInterest() != null  && StringUtils.isNotBlank(dto.getInterest().getInterest())) {
            ResumeInterest entity = new ResumeInterest();
            BeanUtils.copyProperties(dto.getInterest(), entity);
            entity.setCreateAt(nickName);
            entity.setResumeId(entityResumeId);
            entity.setIntId(null);
            interestService.save(entity);
        }

        // 10. 自我评价
        if (dto.getEvaluate() != null && StringUtils.isNotBlank(dto.getEvaluate().getSelfEvaluation())) {
            ResumeEvaluate entity = new ResumeEvaluate();
            BeanUtils.copyProperties(dto.getEvaluate(), entity);
            entity.setCreateAt(nickName);
            entity.setResumeId(entityResumeId);
            entity.setEvaId(null);
            evaluateService.save(entity);
        }

        // 更新用户待办事项状态，将类型为0（创建简历）的待办事项标记为已完成
        try {
            //如果修改简历，则把该简历的其他阶段的待办全部置为已完成，并且将该简历的进度改为0：创建简历
            if (dto.getResumeVo().getResumeId() != null) {
                try {
                    // 获取简历ID
                    Long resumeId = dto.getResumeVo().getResumeId();
                    log.info("修改简历，将所有阶段待办设为已完成，并重置进度为0，userId:{}, resumeId:{}", userId, resumeId);

                    // 将该简历所有阶段的待办标记为已完成
                    List<SysTodo> unfinishedTodos = sysTodoService.getUnfinishedTodosByUserIdAndResumeId(userId, resumeId);
                    if (unfinishedTodos != null && !unfinishedTodos.isEmpty()) {
                        for (SysTodo todo : unfinishedTodos) {
                            sysTodoService.completeTodo(todo.getId());
                            log.info("将待办事项标记为已完成，todoId:{}, title:{}, type:{}", todo.getId(), todo.getTitle(), todo.getTodoType());
                        }
                    }

                    // 查询是否存在进度记录
                    ResumeProgress progress = resumeProgressService.getResumeProgressByUserIdAndResumeId(userId.intValue(), resumeId);
                    if (progress != null) {
                        // 更新进度为0（创建简历）
                        progress.setCurrentStage("0");
                        progress.setUpdateTime(new Date());
                        resumeProgressService.updateResumeProgress(progress);
                        log.info("已重置简历进度为0：创建简历, progressId:{}", progress.getId());
                    } else {
                        // 如果不存在进度记录，创建一个新的进度记录
                        ResumeProgress newProgress = new ResumeProgress();
                        newProgress.setUserId(userId.intValue());
                        newProgress.setResumeId(resumeId);
                        newProgress.setCurrentStage("0");
                        newProgress.setCreateTime(new Date());
                        newProgress.setUpdateTime(new Date());
                        newProgress.setIsDeleted(0);
                        resumeProgressService.createResumeProgress(newProgress);
                        log.info("已创建简历进度记录，阶段为0：创建简历");
                    }
                } catch (Exception e) {
                    log.error("更新简历待办和进度失败", e);
                    // 捕获异常但不影响主流程
                }
            }else{
                log.info("更新用户待办事项状态，userId: {}", userId);
                // 使用新方法更新待办事项状态
                boolean result = sysTodoService.completeTodoByUserIdAndType(userId, 0);
                if (result) {
                    log.info("成功更新用户待办事项状态为已完成");
                } else {
                    log.info("没有找到需要更新的待办事项");
                }
                ResumeProgress progress = progress = new ResumeProgress();
                progress.setUserId(userId.intValue());
                progress.setResumeId(entityResumeId);
                progress.setCurrentStage("0"); // 设置初始阶段为"创建简历阶段"
                resumeProgressService.createResumeProgress(progress);
                log.info("已创建用户进度信息: {}", progress);
            }
        } catch (Exception e) {
            // 捕获异常但不影响主流程
            log.error("更新待办事项状态失败", e);
        }

        return BaseResponse.OK;
    }

    @GetMapping("/delete/{resumeId}")
    public BaseResponse deleteFullResume(@ApiParam(value = "简历ID", required = true) @PathVariable Long resumeId,@RequestHeader Long userId) {

        log.info("<删除简历：>userId:{}, resumeId:{}", userId, resumeId);
        Resume resume =  resumeService.getById(resumeId);
        log.info("<删除简历：简历所属用户：>{}", resume.getUserId());
//      判断删除简历是否属于该用户
        if(!userId.equals(resume.getUserId())){
            return BaseResponse.error(-9999, "无权限");
        }
        // 删除简历表中数据
        resumeService.removeById(resumeId);

        // 删除类型关联表中数据
        LambdaQueryWrapper<ResumeCategoryRelation> wrapperRelation = new LambdaQueryWrapper<ResumeCategoryRelation>()
                .eq(ResumeCategoryRelation::getResumeId, resumeId);
        resumeCategoryRelationService.remove(wrapperRelation);

        // 删除基本信息表中数据
        LambdaQueryWrapper<ResumeInformation> wrapperInformation = new LambdaQueryWrapper<ResumeInformation>()
                .eq(ResumeInformation::getResumeId, resumeId);
        informationService.remove(wrapperInformation);

        // 删除教育经历中数据
        LambdaQueryWrapper<ResumeEducational> wrapperEducational = new LambdaQueryWrapper<ResumeEducational>()
                .eq(ResumeEducational::getResumeId, resumeId);
        educationalService.remove(wrapperEducational);

        // 删除工作经历中数据
        LambdaQueryWrapper<ResumeWork> wrapperWork = new LambdaQueryWrapper<ResumeWork>()
                .eq(ResumeWork::getResumeId, resumeId);
        workService.remove(wrapperWork);

        // 删除项目经历中数据
        LambdaQueryWrapper<ResumeProjectExperience> wrapperProject = new LambdaQueryWrapper<ResumeProjectExperience>()
                .eq(ResumeProjectExperience::getResumeId, resumeId);
        resumeProjectExperienceService.remove(wrapperProject);

        // 删除练手项目中数据
        LambdaQueryWrapper<ResumePractice> wrapperPractice = new LambdaQueryWrapper<ResumePractice>()
                .eq(ResumePractice::getResumeId, resumeId);
        practiceService.remove(wrapperPractice);

        // 删除技能特长中数据
        LambdaQueryWrapper<ResumeTalent> wrapperTalent = new LambdaQueryWrapper<ResumeTalent>()
                .eq(ResumeTalent::getResumeId, resumeId);
        talentService.remove(wrapperTalent);

        // 删除证书奖项中数据
        LambdaQueryWrapper<ResumeCertificate> wrapperCertificate = new LambdaQueryWrapper<ResumeCertificate>()
                .eq(ResumeCertificate::getResumeId, resumeId);
        certificateService.remove(wrapperCertificate);

        // 删除校园经历
        LambdaQueryWrapper<ResumeCampus> wrapperCampus = new LambdaQueryWrapper<ResumeCampus>()
                .eq(ResumeCampus::getResumeId, resumeId);
        campusService.remove(wrapperCampus);

        // 删除兴趣爱好
        LambdaQueryWrapper<ResumeInterest> wrapperInterest = new LambdaQueryWrapper<ResumeInterest>()
                .eq(ResumeInterest::getResumeId, resumeId);
        interestService.remove(wrapperInterest);

        // 删除自我评价
        LambdaQueryWrapper<ResumeEvaluate> wrapperEvaluate = new LambdaQueryWrapper<ResumeEvaluate>()
                .eq(ResumeEvaluate::getResumeId, resumeId);
        evaluateService.remove(wrapperEvaluate);
        //清除简历的待办和进度
        try {
            // 1. 清除该简历的所有待办事项
            sysTodoService.deleteByUserIdAndResumeId(userId, resumeId);
            // 2. 清除该简历的进度
            ResumeProgress progress = resumeProgressService.getResumeProgressByUserIdAndResumeId(userId.intValue(), resumeId);
            if (progress != null) {
                resumeProgressService.deleteResumeProgress(progress.getId());
            }
        } catch (Exception e) {
            log.error("清除简历的待办和进度失败", e);
        }

        return BaseResponse.OK;
    }

    /**
     * 查询当前登录用户的所有完整简历
     */
    @GetMapping("/mylist")
    public BaseResponse myResumeList(@RequestHeader Long userId) {

        List<ResumeFullSaveDto> list = resumeService.getFullResumeListByUserId(userId);
        return BaseResponse.ok(list);
    }

    @ApiOperation("获取简历详情")
    @GetMapping("/detail/{resumeId}")
    public BaseResponse getResumeDetail(
            @ApiParam(value = "简历ID", required = true)
            @PathVariable Long resumeId,
            @RequestHeader Long userId) {
        ResumeFullSaveDto resumeDetail = resumeService.getResumeDetail(resumeId, userId);
        return BaseResponse.ok(resumeDetail);
    }

    /**
     * 导出PDF格式简历
     */
    @ApiOperation("导出PDF格式简历")
    @GetMapping("/export/pdf/{resumeId}")
    public ResponseEntity<byte[]> exportResumeToPdf(
            @ApiParam(value = "简历ID", required = true) @PathVariable Long resumeId,
            @RequestHeader(required = false) Long userId) {
        try {
            // 获取用户ID，优先使用header中的userId，如果没有则尝试从token解析
            Long effectiveUserId = userId;
            // 验证用户权限
            Resume resume = resumeService.getById(resumeId);
            if (resume == null) {
                return ResponseEntity.notFound().build();
            }

            if (!resume.getUserId().equals(effectiveUserId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body("您没有权限访问此简历".getBytes("UTF-8"));
            }

            // 检查简历状态，只有status=1的简历才能下载
            if (resume.getStatus() != 1) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body("只有已通过审核的简历才能下载".getBytes("UTF-8"));
            }

            // 获取完整简历数据
            ResumeFullSaveDto resumeData = resumeService.getResumeDetail(resumeId, effectiveUserId);
            if (resumeData == null) {
                return ResponseEntity.notFound().build();
            }

            // 记录简历模板ID
            log.info("导出PDF简历, ID: {}, 模板ID: {}, 标题: {}",
                    resumeId,
                    resumeData.getResumeVo().getTemplateId(),
                    resumeData.getResumeVo().getTitle());

            // 使用增强的ResumeExportUtil导出PDF
            log.info("开始导出PDF简历, ID: {}, 模板ID: {}, 标题: {}",
                    resumeId,
                    resumeData.getResumeVo().getTemplateId(),
                    resumeData.getResumeVo().getTitle());

            // 记录PDF生成开始时间
            long startTime = System.currentTimeMillis();

            byte[] pdfBytes = ResumeExportUtil.exportToPdf(resumeData);

            // 记录PDF生成耗时
            long duration = System.currentTimeMillis() - startTime;
            log.info("PDF生成完成, 耗时: {}ms, 大小: {}KB", duration, pdfBytes.length / 1024);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            String filename = resumeData.getResumeVo().getTitle() + ".pdf";
            // 解决中文文件名乱码问题
            filename = new String(filename.getBytes("UTF-8"), "ISO-8859-1");
            headers.setContentDispositionFormData("attachment", filename);
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            headers.setPragma("no-cache");
            headers.setExpires(0);

            return new ResponseEntity<>(pdfBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("导出PDF失败 - 简历ID: {}, 错误: {}", resumeId, e.getMessage(), e);

            // 根据错误类型返回不同的错误信息
            String errorMessage;
            HttpStatus status;

            if (e.getMessage().contains("模板")) {
                errorMessage = "PDF模板处理失败，请稍后重试";
                status = HttpStatus.INTERNAL_SERVER_ERROR;
            } else if (e.getMessage().contains("字体")) {
                errorMessage = "字体加载失败，PDF可能显示异常";
                status = HttpStatus.PARTIAL_CONTENT;
            } else if (e.getMessage().contains("内存")) {
                errorMessage = "服务器资源不足，请稍后重试";
                status = HttpStatus.SERVICE_UNAVAILABLE;
            } else {
                errorMessage = "PDF生成失败: " + e.getMessage();
                status = HttpStatus.INTERNAL_SERVER_ERROR;
            }

            try {
                return ResponseEntity.status(status)
                        .body(errorMessage.getBytes("UTF-8"));
            } catch (Exception ex) {
                log.error("返回错误信息失败", ex);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        }
    }

    /**
     * PDF预览接口（在线查看）
     */
    @ApiOperation("PDF预览")
    @GetMapping("/preview/pdf/{resumeId}")
    public ResponseEntity<byte[]> previewResumePdf(
            @ApiParam(value = "简历ID", required = true) @PathVariable Long resumeId,
            @RequestHeader(required = false) Long userId) {
        try {
            // 获取用户ID，优先使用header中的userId
            Long effectiveUserId = userId;

            // 验证用户权限
            Resume resume = resumeService.getById(resumeId);
            if (resume == null) {
                return ResponseEntity.notFound().build();
            }

            if (!resume.getUserId().equals(effectiveUserId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // 获取完整简历数据
            ResumeFullSaveDto resumeData = resumeService.getResumeDetail(resumeId, effectiveUserId);
            if (resumeData == null) {
                return ResponseEntity.notFound().build();
            }

            log.info("预览PDF简历, ID: {}, 模板ID: {}", resumeId, resumeData.getResumeVo().getTemplateId());

            // 生成PDF
            byte[] pdfBytes = ResumeExportUtil.exportToPdf(resumeData);

            // 设置响应头为在线预览
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setCacheControl("no-cache");
            // 使用inline而不是attachment，让浏览器在线显示
            headers.add("Content-Disposition", "inline; filename=\"preview.pdf\"");

            return new ResponseEntity<>(pdfBytes, headers, HttpStatus.OK);

        } catch (Exception e) {
            log.error("PDF预览失败 - 简历ID: {}", resumeId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 导出Word格式简历
     */
    @ApiOperation("导出Word格式简历")
    @GetMapping("/export/word/{resumeId}")
    public ResponseEntity<byte[]> exportResumeToWord(
            @ApiParam(value = "简历ID", required = true) @PathVariable Long resumeId,
            @RequestHeader Long userId) {
        try {
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }

            // 验证用户权限
            Resume resume = resumeService.getById(resumeId);
            if (resume == null) {
                return ResponseEntity.notFound().build();
            }

            if (!resume.getUserId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // 检查简历状态，只有status=1的简历才能下载
            if (resume.getStatus() != 1) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body("只有已通过审核的简历才能下载".getBytes());
            }

            // 获取完整简历数据
            ResumeFullSaveDto resumeData = resumeService.getResumeDetail(resumeId, userId);
            if (resumeData == null) {
                return ResponseEntity.notFound().build();
            }

            // 使用ResumeExportUtil导出Word
            byte[] wordBytes = ResumeExportUtil.exportToWord(resumeData);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.wordprocessingml.document"));
            String filename = resumeData.getResumeVo().getTitle() + ".docx";
            // 解决中文文件名乱码问题
            filename = new String(filename.getBytes("UTF-8"), "ISO-8859-1");
            headers.setContentDispositionFormData("attachment", filename);
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            headers.setPragma("no-cache");
            headers.setExpires(0);

            return new ResponseEntity<>(wordBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("导出Word失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * AI润色项目经验
     */
    @ApiOperation("AI润色项目经验")
    @PostMapping("/polish/project")
    public BaseResponse polishProjectExperience(@RequestBody Map<String, String> params, @RequestHeader Long userId) {
        try {
            String content = params.get("content");
            if (content == null || content.isEmpty()) {
                return BaseResponse.error(-1, "内容不能为空");
            }

            String polishedContent = aiManager.polishProjectExperience(userId, content);
            return BaseResponse.ok(polishedContent);
        } catch (BaseException e) {
            log.error("AI润色项目经验失败", e);
            return BaseResponse.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("AI润色项目经验失败", e);
            return BaseResponse.error(-1, "AI润色失败：" + e.getMessage());
        }
    }

    /**
     * AI润色技能特长
     */
    @ApiOperation("AI润色技能特长")
    @PostMapping("/polish/skill")
    public BaseResponse polishSkill(@RequestBody Map<String, String> params, @RequestHeader Long userId) {
        try {
            String content = params.get("content");
            if (content == null || content.isEmpty()) {
                return BaseResponse.error(-1, "内容不能为空");
            }

            String polishedContent = aiManager.polishSkill(userId, content);
            return BaseResponse.ok(polishedContent);
        } catch (BaseException e) {
            log.error("AI润色技能特长失败", e);
            return BaseResponse.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("AI润色技能特长失败", e);
            return BaseResponse.error(-1, "AI润色失败：" + e.getMessage());
        }
    }

    /**
     * AI润色证书奖项
     */
    @ApiOperation("AI润色证书奖项")
    @PostMapping("/polish/certificate")
    public BaseResponse polishCertificate(@RequestBody Map<String, String> params, @RequestHeader Long userId) {
        try {
            String content = params.get("content");
            if (content == null || content.isEmpty()) {
                return BaseResponse.error(-1, "内容不能为空");
            }

            String polishedContent = aiManager.polishCertificate(userId, content);
            return BaseResponse.ok(polishedContent);
        } catch (BaseException e) {
            log.error("AI润色证书奖项失败", e);
            return BaseResponse.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("AI润色证书奖项失败", e);
            return BaseResponse.error(-1, "AI润色失败：" + e.getMessage());
        }
    }

    /**
     * AI润色校园经历
     */
    @ApiOperation("AI润色校园经历")
    @PostMapping("/polish/campus")
    public BaseResponse polishCampus(@RequestBody Map<String, String> params, @RequestHeader Long userId) {
        log.info("AI润色校园经历:{}", params);
        try {
            String content = params.get("content");
            if (content == null || content.isEmpty()) {
                return BaseResponse.error(-1, "内容不能为空");
            }

            String polishedContent = aiManager.polishCampus(userId, content);
            return BaseResponse.ok(polishedContent);
        } catch (BaseException e) {
            log.error("AI润色校园经历失败", e);
            return BaseResponse.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("AI润色校园经历失败", e);
            return BaseResponse.error(-1, "AI润色失败：" + e.getMessage());
        }
    }

    /**
     * AI润色兴趣爱好
     */
    @ApiOperation("AI润色兴趣爱好")
    @PostMapping("/polish/interest")
    public BaseResponse polishInterest(@RequestBody Map<String, String> params, @RequestHeader Long userId) {
        try {
            String content = params.get("content");
            if (content == null || content.isEmpty()) {
                return BaseResponse.error(-1, "内容不能为空");
            }

            String polishedContent = aiManager.polishInterest(userId, content);
            return BaseResponse.ok(polishedContent);
        } catch (BaseException e) {
            log.error("AI润色兴趣爱好失败", e);
            return BaseResponse.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("AI润色兴趣爱好失败", e);
            return BaseResponse.error(-1, "AI润色失败：" + e.getMessage());
        }
    }

    /**
     * AI润色工作经验
     */
    @ApiOperation("AI润色工作经验")
    @PostMapping("/polish/work")
    public BaseResponse polishWork(@RequestBody Map<String, String> params, @RequestHeader Long userId) {
        try {
            String content = params.get("content");
            if (content == null || content.isEmpty()) {
                return BaseResponse.error(-1, "内容不能为空");
            }

            String polishedContent = aiManager.polishWork(userId, content);
            return BaseResponse.ok(polishedContent);
        } catch (BaseException e) {
            log.error("AI润色工作经验失败", e);
            return BaseResponse.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("AI润色工作经验失败", e);
            return BaseResponse.error(-1, "AI润色失败：" + e.getMessage());
        }
    }
}

