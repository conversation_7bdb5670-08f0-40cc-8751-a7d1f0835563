package com.bimowu.resume.common.service;

import com.bimowu.resume.dto.ExtractedStyles;
import com.bimowu.resume.entity.StyleSyncRecord;

import java.util.List;

/**
 * 样式同步服务接口
 * 负责前后端样式的同步和一致性管理
 */
public interface StyleSyncService {
    
    /**
     * 同步模板样式
     * @param templateId 模板ID
     * @param extractedStyles 提取的样式信息
     * @return 同步记录
     */
    StyleSyncRecord syncTemplateStyles(Integer templateId, ExtractedStyles extractedStyles);
    
    /**
     * 获取模板的最新样式
     * @param templateId 模板ID
     * @return 样式信息
     */
    ExtractedStyles getLatestStyles(Integer templateId);
    
    /**
     * 检查样式是否需要更新
     * @param templateId 模板ID
     * @param currentVersion 当前版本
     * @return 是否需要更新
     */
    boolean needsStyleUpdate(Integer templateId, String currentVersion);
    
    /**
     * 获取同步历史记录
     * @param templateId 模板ID
     * @param limit 记录数量限制
     * @return 同步记录列表
     */
    List<StyleSyncRecord> getSyncHistory(Integer templateId, int limit);
    
    /**
     * 强制全量同步
     * @param templateId 模板ID
     * @return 同步记录
     */
    StyleSyncRecord forceFullSync(Integer templateId);
    
    /**
     * 批量同步所有模板
     * @return 同步结果统计
     */
    SyncResult batchSyncAllTemplates();
    
    /**
     * 验证样式一致性
     * @param templateId 模板ID
     * @return 验证结果
     */
    StyleConsistencyResult validateStyleConsistency(Integer templateId);
    
    /**
     * 同步结果统计
     */
    class SyncResult {
        private int totalTemplates;
        private int successCount;
        private int failureCount;
        private List<String> errors;
        
        // getters and setters
        public int getTotalTemplates() { return totalTemplates; }
        public void setTotalTemplates(int totalTemplates) { this.totalTemplates = totalTemplates; }
        
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }
        
        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }
    }
    
    /**
     * 样式一致性验证结果
     */
    class StyleConsistencyResult {
        private boolean consistent;
        private List<String> differences;
        private double similarityScore;
        private String reportUrl;
        
        // getters and setters
        public boolean isConsistent() { return consistent; }
        public void setConsistent(boolean consistent) { this.consistent = consistent; }
        
        public List<String> getDifferences() { return differences; }
        public void setDifferences(List<String> differences) { this.differences = differences; }
        
        public double getSimilarityScore() { return similarityScore; }
        public void setSimilarityScore(double similarityScore) { this.similarityScore = similarityScore; }
        
        public String getReportUrl() { return reportUrl; }
        public void setReportUrl(String reportUrl) { this.reportUrl = reportUrl; }
    }
}