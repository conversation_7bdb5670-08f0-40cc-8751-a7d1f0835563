<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f640cd95-f3d7-478c-9a49-3c852073e38d" name="Changes" comment="ai apikey" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Pull.Settings">
    <option name="OPTIONS">
      <set>
        <option value="REBASE" />
      </set>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2yUWtPN05p2ft7aTVleY7MuKMwy" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.UserController.executor&quot;: &quot;Run&quot;,
    &quot;Maven.mozai-resume-api [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.mozai-resume-api [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.mozai-resume-parent [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.ResumeWebApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/IdeaProjects/sso/resume/mozai-resume-parent&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDKs&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;redis&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-common\src\main\resources\fonts" />
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-common\src\main\resources\templates\pdf" />
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-web\src\main\resources\mappers" />
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-service\src\main\java\com\bimowu\resume\common\service\impl" />
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-service\src\main\java\com\bimowu\resume\common\service" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-common\src\main\resources\templates\pdf" />
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-web\src\main\resources\mappers" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.bimowu.resume.common.dao" />
      <recent name="com.bimowu.resume.entity" />
      <recent name="com.bimowu.resume.config" />
      <recent name="com.bimowu.resume.utils" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.ResumeWebApplication">
    <configuration name="UserController" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.bimowu.resume.controller.UserController" />
      <module name="resume-web" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.bimowu.resume.controller.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="mozai-resume-parent" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="mozai-resume-parent" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="InterviewWebApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="resume-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bimowu.InterviewWebApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ResumeWebApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="resume-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bimowu.ResumeWebApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.bimowu.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.ResumeWebApplication" />
        <item itemvalue="Application.UserController" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="StructureViewState">
    <option name="selectedTab" value="Logical" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="f640cd95-f3d7-478c-9a49-3c852073e38d" name="Changes" comment="" />
      <created>1749889845876</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749889845876</updated>
      <workItem from="1749889847011" duration="2447000" />
      <workItem from="1750386817641" duration="3740000" />
      <workItem from="1750406478657" duration="3556000" />
      <workItem from="1750410497186" duration="3159000" />
      <workItem from="1750417603583" duration="18874000" />
      <workItem from="1750493193628" duration="188000" />
      <workItem from="1750493453385" duration="3342000" />
      <workItem from="1750643894031" duration="23722000" />
      <workItem from="1750728268645" duration="512000" />
      <workItem from="1750728994862" duration="2701000" />
      <workItem from="1750843860906" duration="6104000" />
      <workItem from="1750891153819" duration="2055000" />
      <workItem from="1751334793602" duration="8536000" />
      <workItem from="1751421947258" duration="8476000" />
      <workItem from="1751508625160" duration="5604000" />
      <workItem from="1751527525630" duration="2326000" />
      <workItem from="1751594198190" duration="598000" />
      <workItem from="1751612061908" duration="500000" />
      <workItem from="1751957941627" duration="44000" />
      <workItem from="1751958096039" duration="7621000" />
      <workItem from="1752026910192" duration="1672000" />
      <workItem from="1752042875032" duration="2134000" />
      <workItem from="1752281967488" duration="12628000" />
      <workItem from="1752457585229" duration="10441000" />
      <workItem from="1752477568948" duration="223000" />
      <workItem from="1752477928342" duration="16716000" />
      <workItem from="1752561871479" duration="14511000" />
      <workItem from="1752810827417" duration="4941000" />
      <workItem from="1752978727505" duration="4117000" />
      <workItem from="1753152028887" duration="2249000" />
      <workItem from="1753328907606" duration="18779000" />
      <workItem from="1753684132138" duration="13433000" />
      <workItem from="1753701491751" duration="70000" />
      <workItem from="1753701585126" duration="553000" />
      <workItem from="1753755340849" duration="18000" />
      <workItem from="1753768033581" duration="30247000" />
      <workItem from="1753869343694" duration="48290000" />
      <workItem from="1754273698434" duration="49839000" />
      <workItem from="1754449961509" duration="44055000" />
      <workItem from="1754897476933" duration="32973000" />
      <workItem from="1755136684623" duration="10740000" />
      <workItem from="1755483863040" duration="2479000" />
    </task>
    <task id="LOCAL-00304" summary="augment优化代码----模板4优化--工作经验">
      <option name="closed" value="true" />
      <created>1754902801481</created>
      <option name="number" value="00304" />
      <option name="presentableId" value="LOCAL-00304" />
      <option name="project" value="LOCAL" />
      <updated>1754902801481</updated>
    </task>
    <task id="LOCAL-00305" summary="augment优化代码----模板4优化--项目经验">
      <option name="closed" value="true" />
      <created>1754903370841</created>
      <option name="number" value="00305" />
      <option name="presentableId" value="LOCAL-00305" />
      <option name="project" value="LOCAL" />
      <updated>1754903370841</updated>
    </task>
    <task id="LOCAL-00306" summary="augment优化代码----模板4优化--项目经验">
      <option name="closed" value="true" />
      <created>1754903716947</created>
      <option name="number" value="00306" />
      <option name="presentableId" value="LOCAL-00306" />
      <option name="project" value="LOCAL" />
      <updated>1754903716947</updated>
    </task>
    <task id="LOCAL-00307" summary="augment优化代码----模板4优化--项目经验">
      <option name="closed" value="true" />
      <created>1754903994728</created>
      <option name="number" value="00307" />
      <option name="presentableId" value="LOCAL-00307" />
      <option name="project" value="LOCAL" />
      <updated>1754903994728</updated>
    </task>
    <task id="LOCAL-00308" summary="augment优化代码----模板4优化--项目经验">
      <option name="closed" value="true" />
      <created>1754909029004</created>
      <option name="number" value="00308" />
      <option name="presentableId" value="LOCAL-00308" />
      <option name="project" value="LOCAL" />
      <updated>1754909029004</updated>
    </task>
    <task id="LOCAL-00309" summary="augment优化代码----模板4优化--练手项目">
      <option name="closed" value="true" />
      <created>1754909337146</created>
      <option name="number" value="00309" />
      <option name="presentableId" value="LOCAL-00309" />
      <option name="project" value="LOCAL" />
      <updated>1754909337146</updated>
    </task>
    <task id="LOCAL-00310" summary="augment优化代码----模板4优化--技能">
      <option name="closed" value="true" />
      <created>1754909679548</created>
      <option name="number" value="00310" />
      <option name="presentableId" value="LOCAL-00310" />
      <option name="project" value="LOCAL" />
      <updated>1754909679548</updated>
    </task>
    <task id="LOCAL-00311" summary="augment优化代码----模板4优化--教育经历">
      <option name="closed" value="true" />
      <created>1754909938706</created>
      <option name="number" value="00311" />
      <option name="presentableId" value="LOCAL-00311" />
      <option name="project" value="LOCAL" />
      <updated>1754909938706</updated>
    </task>
    <task id="LOCAL-00312" summary="augment优化代码----模板4优化--校园经历、兴趣爱好">
      <option name="closed" value="true" />
      <created>1754910600409</created>
      <option name="number" value="00312" />
      <option name="presentableId" value="LOCAL-00312" />
      <option name="project" value="LOCAL" />
      <updated>1754910600409</updated>
    </task>
    <task id="LOCAL-00313" summary="augment优化代码----模板4优化--证书">
      <option name="closed" value="true" />
      <created>1754911219183</created>
      <option name="number" value="00313" />
      <option name="presentableId" value="LOCAL-00313" />
      <option name="project" value="LOCAL" />
      <updated>1754911219183</updated>
    </task>
    <task id="LOCAL-00314" summary="augment优化代码----模板4优化--个人评价">
      <option name="closed" value="true" />
      <created>1754965482754</created>
      <option name="number" value="00314" />
      <option name="presentableId" value="LOCAL-00314" />
      <option name="project" value="LOCAL" />
      <updated>1754965482754</updated>
    </task>
    <task id="LOCAL-00315" summary="augment优化代码----模板4优化--加粗处理">
      <option name="closed" value="true" />
      <created>1754967104320</created>
      <option name="number" value="00315" />
      <option name="presentableId" value="LOCAL-00315" />
      <option name="project" value="LOCAL" />
      <updated>1754967104320</updated>
    </task>
    <task id="LOCAL-00316" summary="augment优化代码----模板4优化--练手项目">
      <option name="closed" value="true" />
      <created>1754970383293</created>
      <option name="number" value="00316" />
      <option name="presentableId" value="LOCAL-00316" />
      <option name="project" value="LOCAL" />
      <updated>1754970383293</updated>
    </task>
    <task id="LOCAL-00317" summary="augment优化代码----模板5优化--">
      <option name="closed" value="true" />
      <created>1754975035355</created>
      <option name="number" value="00317" />
      <option name="presentableId" value="LOCAL-00317" />
      <option name="project" value="LOCAL" />
      <updated>1754975035355</updated>
    </task>
    <task id="LOCAL-00318" summary="augment优化代码----模板5优化--头部">
      <option name="closed" value="true" />
      <created>1754976707645</created>
      <option name="number" value="00318" />
      <option name="presentableId" value="LOCAL-00318" />
      <option name="project" value="LOCAL" />
      <updated>1754976707645</updated>
    </task>
    <task id="LOCAL-00319" summary="augment优化代码----模板5优化--头部">
      <option name="closed" value="true" />
      <created>1754977261915</created>
      <option name="number" value="00319" />
      <option name="presentableId" value="LOCAL-00319" />
      <option name="project" value="LOCAL" />
      <updated>1754977261915</updated>
    </task>
    <task id="LOCAL-00320" summary="augment优化代码----模板5优化--头部">
      <option name="closed" value="true" />
      <created>1754981655552</created>
      <option name="number" value="00320" />
      <option name="presentableId" value="LOCAL-00320" />
      <option name="project" value="LOCAL" />
      <updated>1754981655552</updated>
    </task>
    <task id="LOCAL-00321" summary="augment优化代码----模板5优化--头部">
      <option name="closed" value="true" />
      <created>1754982074006</created>
      <option name="number" value="00321" />
      <option name="presentableId" value="LOCAL-00321" />
      <option name="project" value="LOCAL" />
      <updated>1754982074006</updated>
    </task>
    <task id="LOCAL-00322" summary="augment优化代码----模板5优化--头部">
      <option name="closed" value="true" />
      <created>1754982080761</created>
      <option name="number" value="00322" />
      <option name="presentableId" value="LOCAL-00322" />
      <option name="project" value="LOCAL" />
      <updated>1754982080761</updated>
    </task>
    <task id="LOCAL-00323" summary="augment优化代码----模板5优化--头部">
      <option name="closed" value="true" />
      <created>1754982543502</created>
      <option name="number" value="00323" />
      <option name="presentableId" value="LOCAL-00323" />
      <option name="project" value="LOCAL" />
      <updated>1754982543502</updated>
    </task>
    <task id="LOCAL-00324" summary="augment优化代码----模板5优化--头部">
      <option name="closed" value="true" />
      <created>1754982918623</created>
      <option name="number" value="00324" />
      <option name="presentableId" value="LOCAL-00324" />
      <option name="project" value="LOCAL" />
      <updated>1754982918623</updated>
    </task>
    <task id="LOCAL-00325" summary="augment优化代码----模板5优化--教育经历">
      <option name="closed" value="true" />
      <created>1754983570014</created>
      <option name="number" value="00325" />
      <option name="presentableId" value="LOCAL-00325" />
      <option name="project" value="LOCAL" />
      <updated>1754983570014</updated>
    </task>
    <task id="LOCAL-00326" summary="augment优化代码----模板5优化--教育经历">
      <option name="closed" value="true" />
      <created>1754984327600</created>
      <option name="number" value="00326" />
      <option name="presentableId" value="LOCAL-00326" />
      <option name="project" value="LOCAL" />
      <updated>1754984327600</updated>
    </task>
    <task id="LOCAL-00327" summary="augment优化代码----模板5优化--教育经历">
      <option name="closed" value="true" />
      <created>1754985243741</created>
      <option name="number" value="00327" />
      <option name="presentableId" value="LOCAL-00327" />
      <option name="project" value="LOCAL" />
      <updated>1754985243741</updated>
    </task>
    <task id="LOCAL-00328" summary="augment优化代码----模板5优化--教育经历">
      <option name="closed" value="true" />
      <created>1754990966431</created>
      <option name="number" value="00328" />
      <option name="presentableId" value="LOCAL-00328" />
      <option name="project" value="LOCAL" />
      <updated>1754990966431</updated>
    </task>
    <task id="LOCAL-00329" summary="augment优化代码----模板5优化--教育经历">
      <option name="closed" value="true" />
      <created>1754991541462</created>
      <option name="number" value="00329" />
      <option name="presentableId" value="LOCAL-00329" />
      <option name="project" value="LOCAL" />
      <updated>1754991541462</updated>
    </task>
    <task id="LOCAL-00330" summary="augment优化代码----模板5优化--工作经验">
      <option name="closed" value="true" />
      <created>1754991981457</created>
      <option name="number" value="00330" />
      <option name="presentableId" value="LOCAL-00330" />
      <option name="project" value="LOCAL" />
      <updated>1754991981457</updated>
    </task>
    <task id="LOCAL-00331" summary="augment优化代码----模板5优化--工作经验">
      <option name="closed" value="true" />
      <created>1754992283866</created>
      <option name="number" value="00331" />
      <option name="presentableId" value="LOCAL-00331" />
      <option name="project" value="LOCAL" />
      <updated>1754992283866</updated>
    </task>
    <task id="LOCAL-00332" summary="augment优化代码----模板5优化--工作经验">
      <option name="closed" value="true" />
      <created>1754992784243</created>
      <option name="number" value="00332" />
      <option name="presentableId" value="LOCAL-00332" />
      <option name="project" value="LOCAL" />
      <updated>1754992784243</updated>
    </task>
    <task id="LOCAL-00333" summary="augment优化代码----模板优化--练手项目地址">
      <option name="closed" value="true" />
      <created>1755054166446</created>
      <option name="number" value="00333" />
      <option name="presentableId" value="LOCAL-00333" />
      <option name="project" value="LOCAL" />
      <updated>1755054166446</updated>
    </task>
    <task id="LOCAL-00334" summary="augment优化代码----模板5优化--字体加粗">
      <option name="closed" value="true" />
      <created>1755055288387</created>
      <option name="number" value="00334" />
      <option name="presentableId" value="LOCAL-00334" />
      <option name="project" value="LOCAL" />
      <updated>1755055288387</updated>
    </task>
    <task id="LOCAL-00335" summary="augment优化代码----模板5优化--回滚">
      <option name="closed" value="true" />
      <created>1755055776562</created>
      <option name="number" value="00335" />
      <option name="presentableId" value="LOCAL-00335" />
      <option name="project" value="LOCAL" />
      <updated>1755055776562</updated>
    </task>
    <task id="LOCAL-00336" summary="augment优化代码----模板5优化--字体加粗优化1">
      <option name="closed" value="true" />
      <created>1755058168308</created>
      <option name="number" value="00336" />
      <option name="presentableId" value="LOCAL-00336" />
      <option name="project" value="LOCAL" />
      <updated>1755058168308</updated>
    </task>
    <task id="LOCAL-00337" summary="augment优化代码----模板5优化--字体加粗优化">
      <option name="closed" value="true" />
      <created>1755058995292</created>
      <option name="number" value="00337" />
      <option name="presentableId" value="LOCAL-00337" />
      <option name="project" value="LOCAL" />
      <updated>1755058995293</updated>
    </task>
    <task id="LOCAL-00338" summary="augment优化代码----模板5优化--字体加粗优化">
      <option name="closed" value="true" />
      <created>1755062339784</created>
      <option name="number" value="00338" />
      <option name="presentableId" value="LOCAL-00338" />
      <option name="project" value="LOCAL" />
      <updated>1755062339784</updated>
    </task>
    <task id="LOCAL-00339" summary="augment优化代码----模板5优化--字体加粗优化">
      <option name="closed" value="true" />
      <created>1755062684059</created>
      <option name="number" value="00339" />
      <option name="presentableId" value="LOCAL-00339" />
      <option name="project" value="LOCAL" />
      <updated>1755062684059</updated>
    </task>
    <task id="LOCAL-00340" summary="augment优化代码----模板1优化--字体加粗优化">
      <option name="closed" value="true" />
      <created>1755064409544</created>
      <option name="number" value="00340" />
      <option name="presentableId" value="LOCAL-00340" />
      <option name="project" value="LOCAL" />
      <updated>1755064409544</updated>
    </task>
    <task id="LOCAL-00341" summary="augment优化代码----模板2优化--项目地址">
      <option name="closed" value="true" />
      <created>1755064859779</created>
      <option name="number" value="00341" />
      <option name="presentableId" value="LOCAL-00341" />
      <option name="project" value="LOCAL" />
      <updated>1755064859779</updated>
    </task>
    <task id="LOCAL-00342" summary="删除不需要的模板">
      <option name="closed" value="true" />
      <created>1755065971440</created>
      <option name="number" value="00342" />
      <option name="presentableId" value="LOCAL-00342" />
      <option name="project" value="LOCAL" />
      <updated>1755065971440</updated>
    </task>
    <task id="LOCAL-00343" summary="生产环境参数">
      <option name="closed" value="true" />
      <created>1755072556566</created>
      <option name="number" value="00343" />
      <option name="presentableId" value="LOCAL-00343" />
      <option name="project" value="LOCAL" />
      <updated>1755072556566</updated>
    </task>
    <task id="LOCAL-00344" summary="删除不必要的代码">
      <option name="closed" value="true" />
      <created>1755137607391</created>
      <option name="number" value="00344" />
      <option name="presentableId" value="LOCAL-00344" />
      <option name="project" value="LOCAL" />
      <updated>1755137607391</updated>
    </task>
    <task id="LOCAL-00345" summary="sql整合">
      <option name="closed" value="true" />
      <created>1755138808976</created>
      <option name="number" value="00345" />
      <option name="presentableId" value="LOCAL-00345" />
      <option name="project" value="LOCAL" />
      <updated>1755138808976</updated>
    </task>
    <task id="LOCAL-00346" summary="生产环境">
      <option name="closed" value="true" />
      <created>1755138872966</created>
      <option name="number" value="00346" />
      <option name="presentableId" value="LOCAL-00346" />
      <option name="project" value="LOCAL" />
      <updated>1755138872966</updated>
    </task>
    <task id="LOCAL-00347" summary="生产环境">
      <option name="closed" value="true" />
      <created>1755168329944</created>
      <option name="number" value="00347" />
      <option name="presentableId" value="LOCAL-00347" />
      <option name="project" value="LOCAL" />
      <updated>1755168329944</updated>
    </task>
    <task id="LOCAL-00348" summary="生产环境">
      <option name="closed" value="true" />
      <created>1755236176471</created>
      <option name="number" value="00348" />
      <option name="presentableId" value="LOCAL-00348" />
      <option name="project" value="LOCAL" />
      <updated>1755236176471</updated>
    </task>
    <task id="LOCAL-00349" summary="生产环境">
      <option name="closed" value="true" />
      <created>1755237044254</created>
      <option name="number" value="00349" />
      <option name="presentableId" value="LOCAL-00349" />
      <option name="project" value="LOCAL" />
      <updated>1755237044254</updated>
    </task>
    <task id="LOCAL-00350" summary="ai apikey">
      <option name="closed" value="true" />
      <created>1755240816122</created>
      <option name="number" value="00350" />
      <option name="presentableId" value="LOCAL-00350" />
      <option name="project" value="LOCAL" />
      <updated>1755240816122</updated>
    </task>
    <task id="LOCAL-00351" summary="ai apikey">
      <option name="closed" value="true" />
      <created>1755247326665</created>
      <option name="number" value="00351" />
      <option name="presentableId" value="LOCAL-00351" />
      <option name="project" value="LOCAL" />
      <updated>1755247326665</updated>
    </task>
    <task id="LOCAL-00352" summary="ai apikey">
      <option name="closed" value="true" />
      <created>1755483949262</created>
      <option name="number" value="00352" />
      <option name="presentableId" value="LOCAL-00352" />
      <option name="project" value="LOCAL" />
      <updated>1755483949263</updated>
    </task>
    <option name="localTasksCounter" value="353" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="508882c3-3b90-4a81-8d93-783580712658" value="TOOL_WINDOW" />
        <entry key="48117be4-ca99-4ef7-a8f1-bd7a3dd29436" value="TOOL_WINDOW" />
        <entry key="22a432c3-65a0-4c7d-a0ca-a5d2f5f122d5" value="TOOL_WINDOW" />
        <entry key="af16615c-559d-41ae-ae49-62cac2e25fc7" value="TOOL_WINDOW" />
        <entry key="af485b83-297d-49de-98fc-7d1f389cf7a5" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="22a432c3-65a0-4c7d-a0ca-a5d2f5f122d5">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="48117be4-ca99-4ef7-a8f1-bd7a3dd29436">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="508882c3-3b90-4a81-8d93-783580712658">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
        <entry key="af16615c-559d-41ae-ae49-62cac2e25fc7">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="af485b83-297d-49de-98fc-7d1f389cf7a5">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="augment优化代码----模板4优化--项目经验" />
    <MESSAGE value="augment优化代码----模板4优化--技能" />
    <MESSAGE value="augment优化代码----模板4优化--教育经历" />
    <MESSAGE value="augment优化代码----模板4优化--校园经历、兴趣爱好" />
    <MESSAGE value="augment优化代码----模板4优化--证书" />
    <MESSAGE value="augment优化代码----模板4优化--个人评价" />
    <MESSAGE value="augment优化代码----模板4优化--加粗处理" />
    <MESSAGE value="augment优化代码----模板4优化--练手项目" />
    <MESSAGE value="augment优化代码----模板5优化--" />
    <MESSAGE value="augment优化代码----模板5优化--头部" />
    <MESSAGE value="augment优化代码----模板5优化--教育经历" />
    <MESSAGE value="augment优化代码----模板5优化--工作经验" />
    <MESSAGE value="augment优化代码----模板优化--练手项目地址" />
    <MESSAGE value="augment优化代码----模板5优化--字体加粗" />
    <MESSAGE value="augment优化代码----模板5优化--回滚" />
    <MESSAGE value="augment优化代码----模板5优化--字体加粗优化1" />
    <MESSAGE value="augment优化代码----模板5优化--字体加粗优化" />
    <MESSAGE value="augment优化代码----模板1优化--字体加粗优化" />
    <MESSAGE value="augment优化代码----模板2优化--项目地址" />
    <MESSAGE value="删除不需要的模板" />
    <MESSAGE value="生产环境参数" />
    <MESSAGE value="删除不必要的代码" />
    <MESSAGE value="sql整合" />
    <MESSAGE value="生产环境" />
    <MESSAGE value="ai apikey" />
    <option name="LAST_COMMIT_MESSAGE" value="ai apikey" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/resume-web/src/main/java/com/bimowu/resume/controller/UserController.java</url>
          <line>237</line>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/resume-service/src/main/java/com/bimowu/resume/common/service/impl/FlyingSaucerPDFGeneratorImpl.java</url>
          <line>134</line>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/resume-service/src/main/java/com/bimowu/resume/common/service/impl/FlyingSaucerPDFGeneratorImpl.java</url>
          <line>74</line>
          <option name="timeStamp" value="16" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/resume-service/src/main/java/com/bimowu/resume/common/service/StyleSyncService.java</url>
          <line>19</line>
          <properties class="com.bimowu.resume.common.service.StyleSyncService" method="syncTemplateStyles">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="13" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="com.bimowu.resume.dto.ResumeFullSaveDto" memberName="practiceList" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
</project>