package com.bimowu.resume.common.service;

import com.bimowu.resume.dto.ExtractedStyles;

import java.util.List;

/**
 * 样式一致性验证器接口
 * 用于验证前后端样式的一致性
 */
public interface StyleConsistencyValidator {
    
    /**
     * 验证两个样式是否一致
     * @param frontendStyles 前端样式
     * @param backendStyles 后端样式
     * @return 是否一致
     */
    boolean validateConsistency(ExtractedStyles frontendStyles, ExtractedStyles backendStyles);
    
    /**
     * 查找样式差异
     * @param frontendStyles 前端样式
     * @param backendStyles 后端样式
     * @return 差异列表
     */
    List<String> findDifferences(ExtractedStyles frontendStyles, ExtractedStyles backendStyles);
    
    /**
     * 计算样式相似度
     * @param frontendStyles 前端样式
     * @param backendStyles 后端样式
     * @return 相似度分数 (0.0 - 1.0)
     */
    double calculateSimilarity(ExtractedStyles frontendStyles, ExtractedStyles backendStyles);
    
    /**
     * 验证字体一致性
     * @param frontendFonts 前端字体信息
     * @param backendFonts 后端字体信息
     * @return 是否一致
     */
    boolean validateFontConsistency(List<ExtractedStyles.FontInfo> frontendFonts, 
                                   List<ExtractedStyles.FontInfo> backendFonts);
    
    /**
     * 验证颜色一致性
     * @param frontendColors 前端颜色信息
     * @param backendColors 后端颜色信息
     * @return 是否一致
     */
    boolean validateColorConsistency(ExtractedStyles.ColorPalette frontendColors, 
                                    ExtractedStyles.ColorPalette backendColors);
    
    /**
     * 验证布局一致性
     * @param frontendLayout 前端布局信息
     * @param backendLayout 后端布局信息
     * @return 是否一致
     */
    boolean validateLayoutConsistency(ExtractedStyles.LayoutInfo frontendLayout, 
                                     ExtractedStyles.LayoutInfo backendLayout);
}