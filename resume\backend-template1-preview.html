<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>侯富文的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 14px;
            font-weight: normal;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 0;
            color: #333;
            line-height: 1.5;
            background: #fafafa;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', Aria<PERSON>, sans-serif !important;
            }
        }
        
        .resume-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 0;
            box-sizing: border-box;
        }
        
        .header {
            background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
            color: white;
            padding: 25px 30px;
            margin-bottom: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 80px;
        }

        .header h1 {
            margin: 0;
            font-size: 32px;
            font-weight: bold;
            letter-spacing: 2px;
        }

        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: flex-end;
        }

        .contact-row {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: white;
        }
        
        .section {
            margin-bottom: 25px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-left: 30px;
            margin-right: 30px;
        }

        .section-title {
            color: #4A90E2;
            border-bottom: 2px solid #4A90E2;
            padding-bottom: 8px;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }
        
        .item {
            margin-bottom: 0;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .item:last-child {
            border-bottom: none;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            align-items: center;
        }

        .item-title {
            font-weight: bold;
            color: #333;
            font-size: 16px;
            flex: 1;
            text-align: center;
        }

        .item-date {
            color: #666;
            font-weight: bold;
            font-size: 14px;
            min-width: 140px;
        }

        .item-subtitle {
            margin-bottom: 12px;
            font-weight: bold;
            color: #4A90E2;
            font-size: 14px;
            min-width: 120px;
            text-align: right;
        }

        .item-content {
            text-align: justify;
            font-size: 14px;
            line-height: 1.8;
            color: #555;
        }
        
        ul {
            margin-top: 5px;
            margin-bottom: 5px;
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 3px;
        }
        
        .skill-item {
            margin-bottom: 8px;
        }
        
        .skill-name {
            font-weight: bold;
        }
        
        .certificate-list,
        .campus-experience,
        .interests,
        .self-evaluation {
            text-align: justify;
            font-size: 14px;
            line-height: 1.8;
            color: #555;
        }

        /* 内容区域整体样式 */
        .content-area {
            padding: 25px 0;
            background: #fafafa;
        }

        /* 响应式和打印优化 */
        @media print {
            body {
                background: white;
            }
            .section {
                box-shadow: none;
                border: 1px solid #ddd;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <!-- 页眉部分 -->
        <div class="header">
            <div class="header-left">
                <h1>侯富文</h1>
            </div>
            <div class="contact-info">
                <div class="contact-row">
                    <div class="contact-item">
                        <span>👤 20岁</span>
                    </div>
                    <div class="contact-item">
                        <span>📞 13037044263</span>
                    </div>
                </div>
                <div class="contact-row">
                    <div class="contact-item">
                        <span>✉️ <EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <span>📍 陕西省/西安</span>
                    </div>
                </div>
                <div class="contact-row">
                    <div class="contact-item">
                        <span>🎯 软件工程师</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 教育经历 -->
            <div class="section">
                <div class="section-title">教育经历</div>
                <div class="item">
                    <div class="item-header">
                        <div class="item-date">2023-09 - 2027-07</div>
                        <div class="item-title">山西农业大学</div>
                        <div class="item-subtitle">软件工程（本科）</div>
                    </div>
                    <div class="item-content">
                        主修课程：Java程序设计、数据结构、算法分析与设计、计算机网络、软件工程
                    </div>
                </div>
            </div>

            <!-- 工作经历 -->
            <div class="section">
                <div class="section-title">工作经历</div>
                <div class="item">
                    <div class="item-header">
                        <div class="item-date">2024-09 - 2025-06</div>
                        <div class="item-title">农大实验室运维实习生</div>
                        <div class="item-subtitle">软件开发实习</div>
                    </div>
                    <div class="item-content">
                        1. 参与实验室实验室管理系统开发，后端采用SpringBoot框架，前端采用Vue；<br>
                        2. 负责实验数据库的维护与分析，优化sql查询语句，提升查询效率；<br>
                        3. 协助实验室日常bug，修复代码缺陷，优化代码架构和性能。
                    </div>
                </div>
            </div>

            <!-- 项目经验 -->
            <div class="section">
                <div class="section-title">项目经验</div>
                <div class="item">
                    <div class="item-header">
                        <div class="item-date">2025-02 - 2025-07</div>
                        <div class="item-title">人力资源信息管理系统</div>
                        <div class="item-subtitle">软件工程师</div>
                    </div>
                    <div class="item-content">
                        工作职责：需求分析与设计开发，系统架构设计，系统代码编写，系统测试与部署，维护和优化系统上线。<br>
                        技术栈：Spring Boot + Mybatis + MySQL + Redis + Linux + Vue + Nginx
                    </div>
                </div>
            </div>

            <!-- 技能特长 -->
            <div class="section">
                <div class="section-title">技能特长</div>
                <div class="skill-item">
                    <div class="item-content">
                        JAVA基础扎实：面向OO、多线程、集合等等，熟悉常用设计模式和数据结构；<br>
                        熟悉JVM虚拟机原理，对象生命周期，类加载机制，了解常见的JVM调优参数，掌握JVM性能调优工具；
                    </div>
                </div>
            </div>

            <!-- 个人评价 -->
            <div class="section">
                <div class="section-title">个人评价</div>
                <div class="self-evaluation">
                    有良好的沟通和协调能力，有良好的学习和适应能力；<br>
                    具备扎实的编程工作基础，有耐心、有责任心和执行能力；<br>
                    能够灵活使用Office办公软件，能够进行数据统计分析；<br>
                    富有团队协作精神和较强的集体荣誉感。
                </div>
            </div>
        </div>
    </div>
</body>
</html>
