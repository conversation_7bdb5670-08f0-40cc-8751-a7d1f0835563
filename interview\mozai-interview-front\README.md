# AI面试系统

基于Vue3和TypeScript开发的AI面试系统，用于模拟真实面试场景，提供AI驱动的面试体验。

## 功能特点

- 简历分析：上传简历，AI自动分析并生成针对性面试问题
- AI数字人：由AI驱动的数字人进行面试提问，带有问题字幕
- 语音合成：AI数字人通过语音朗读面试问题
- 全程录制：一键开始，自动录制整个面试过程的视频和音频
- 自动进行：系统检测静音5秒后自动进入下一题
- 面部表情分析：分析面试者的面部表情变化
- 环境评估：评估面试环境的光线和噪音情况
- 综合评分：对面试表现进行多维度评分
- 面试报告：生成详细的面试评估报告

## 技术栈

- Vue 3
- TypeScript
- Pinia (状态管理)
- Vue Router
- Element Plus (UI组件库)
- Face-api.js (人脸检测)
- RecordRTC (音视频录制)
- MediaPipe (人脸检测)
- Web Speech API (语音合成)

## 安装与运行

### 环境要求

- Node.js 16+
- npm 7+ 或 yarn 1.22+
- 支持Web Speech API的现代浏览器（如Chrome、Edge）

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发模式运行

```bash
npm run dev
# 或
yarn dev
```

### 构建生产版本

```bash
npm run build
# 或
yarn build
```

## 项目结构

```
quiz-ai-interview/
├── public/               # 静态资源
│   └── models/           # 人脸检测模型
├── src/
│   ├── api/              # API接口
│   ├── assets/           # 项目资源
│   ├── components/       # 公共组件
│   ├── router/           # 路由配置
│   ├── store/            # Pinia状态管理
│   ├── utils/            # 工具函数
│   ├── views/            # 页面视图
│   ├── App.vue           # 根组件
│   └── main.ts           # 入口文件
├── index.html            # HTML模板
├── package.json          # 项目依赖
├── tsconfig.json         # TypeScript配置
├── vite.config.ts        # Vite配置
└── README.md             # 项目说明
```

## 使用流程

1. 首页：了解系统功能，点击"开始面试"
2. 上传简历：上传您的简历，系统会分析并生成面试问题
3. 面试页面：点击"开始面试"按钮，系统会自动开启摄像头并开始录制
4. 面试进行：AI数字人会依次朗读问题，您只需自然回答，系统会在您回答完成后（检测到5秒静音）自动进入下一题
5. 结果页面：所有问题完成后，系统自动生成分析报告，可以查看详细评分和下载面试报告

## 必要资源配置

### 数字人图片

为了显示AI数字人，您需要：

1. 准备一张数字人图片（PNG格式，建议透明背景）
2. 将图片命名为 `digital-human.png`
3. 放置在 `src/assets/` 目录下

详细说明请参考 `src/assets/digital-human-info.md` 文件。

### 人脸检测模型

面部表情分析功能需要人脸检测模型：

1. 从 face-api.js 项目下载模型文件：https://github.com/justadudewhohacks/face-api.js/tree/master/weights
2. 将以下模型文件放入 `public/models/` 目录：
   - tiny_face_detector_model-weights_manifest.json
   - tiny_face_detector_model-shard1
   - face_landmark_68_model-weights_manifest.json
   - face_landmark_68_model-shard1
   - face_expression_model-weights_manifest.json
   - face_expression_model-shard1

## 注意事项

- 使用前请确保已授权浏览器访问摄像头和麦克风
- 建议在安静、光线充足的环境中进行面试
- 面试过程中请保持面部在摄像头范围内
- 首次运行时需要下载人脸检测模型，可能需要一些时间
- 语音合成功能需要浏览器支持Web Speech API
- 面试自动化流程：AI提问 → 您回答 → 静音5秒 → 自动下一题

## 浏览器兼容性

- Chrome 33+
- Edge 79+
- Safari 14.1+
- Firefox 49+
- Opera 20+

## 开发计划

- [ ] 增加更多数字人模型选择
- [ ] 支持更多简历格式解析
- [ ] 优化面部表情分析算法
- [ ] 增加语音情感分析功能
- [ ] 支持多语言面试

## 许可证

MIT 