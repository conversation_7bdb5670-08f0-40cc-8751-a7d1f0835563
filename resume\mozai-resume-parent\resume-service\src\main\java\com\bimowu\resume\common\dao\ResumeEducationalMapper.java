package com.bimowu.resume.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.resume.entity.ResumeEducational;
import com.bimowu.resume.vo.ResumeEducationalVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * 教育经历表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface ResumeEducationalMapper extends BaseMapper<ResumeEducational> {

    List<ResumeEducationalVo> selectByResumeId(@Param("resumeId") Long resumeId);
}
