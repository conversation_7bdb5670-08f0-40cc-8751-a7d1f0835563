<template>
  <div class="resume-template template-4">
    <div class="resume-container">
      <!-- 个人信息部分 -->
      <div class="header-section">
        <div class="personal-info">
          <div class="info-name">{{ resume.modules.basic?.name || '' }}</div>
          <div class="info-row">
            <div class="info-item"><span class="label">性别：</span>{{ resume.modules.basic?.gender || '未填写' }}</div>
            <div class="info-item"><span class="label">年龄：</span>{{ resume.modules.basic?.age || '未填写' }}</div>
          </div>
          <div class="info-row">
            <div class="info-item"><span class="label">电话：</span>{{ resume.modules.basic?.phone || '未填写' }}</div>
            <div class="info-item"><span class="label">邮箱：</span>{{ resume.modules.basic?.email || '未填写' }}</div>
          </div>
          <div class="info-row">
            <div class="info-item full-width"><span class="label">求职意向：</span>{{ resume.modules.basic?.jobObjective || '未填写' }}</div>
          </div>
        </div>
        <div class="avatar-container">
          <img v-if="resume.modules.basic?.avatar" :src="resume.modules.basic.avatar" alt="头像" class="avatar" />
          <img v-else src="/images/default-avatar.svg" alt="默认照片" class="avatar"/>
        </div>
      </div>

      <!-- 自我评价部分 -->
      <div v-if="hasEvaluation" class="section">
        <div class="section-header">
          <h2>自我评价</h2>
        </div>
        <div class="evaluation-content">
          <div class="evaluation-text" v-html="formatContent1(resume.modules.selfEvaluation)">
          </div>
        </div>
      </div>

      <!-- 工作经验部分 -->
      <div v-if="hasWork" class="section">
        <div class="section-header">
          <h2>工作经验</h2>
        </div>
        <div class="section-content">
          <div v-for="(work, index) in resume.modules.work" :key="index" class="experience-item">
            <div class="experience-header">
              <div class="experience-date">{{ formatDate(work.time[0]) }} - {{ formatDate(work.time[1]) || '至今' }}</div>
              <div style="display: flex" >
                <div class="experience-company">{{ work.company }}</div>
                <div class="experience-position">{{ work.position }}</div>
              </div>
            </div>
            <div class="experience-description" v-html="formatContent(work.description)"></div>
          </div>
        </div>
      </div>

      <!-- 项目经验部分 -->
      <div v-if="hasProjects" class="section">
        <div class="section-header">
          <h2>项目经验</h2>
        </div>
        <div class="section-content">
          <div v-for="(project, index) in resume.modules.projects" :key="index" class="project-item">
            <div class="project-header">
              <div class="project-title">{{ project.name }}</div>
              <div class="project-role">{{ project.role }}</div>
              <div class="project-date">{{ formatDate(project.time[0]) }} - {{ formatDate(project.time[1]) || '至今' }}</div>
            </div>

            <div class="project-description">
              <div v-html="formatContent(project.description)"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 练手项目 -->
      <div v-if="hasPractices" class="section">
        <div class="section-header">
          <h2>练手项目</h2>
        </div>
        <div class="section-content">
          <div v-for="(practice, index) in resume.modules.practices" :key="index" class="project-item">
            <div class="project-header">
              <div class="project-title">{{ practice.name }}</div>
              <div class="project-role">{{ practice.role }}</div>
              <div class="project-date">{{ formatDate(practice.time[0]) }} - {{ formatDate(practice.time[1]) || '至今' }}</div>
            </div>

            <div v-if="practice.url" class="project-url" style="line-height: 1.6; font-size: 14px; margin-bottom: 5px;">
              <span style="color: rgb(68, 84, 106);">项目地址：</span>
              <span style="color: #0066cc; text-decoration: underline; font-weight: normal;">{{ practice.url }}</span>
            </div>

            <div class="project-description">
              <div v-html="formatContent(practice.description)"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 相关技能部分 -->
      <div v-if="hasSkills" class="section">
        <div class="section-header">
          <h2>相关技能</h2>
        </div>
        <div class="section-content skills-content">
          <div class="skills-list">
            <div v-for="(skill, index) in resume.modules.skills" :key="index" class="skill-item">
              <div class="skill-description" v-html="formatContent(skill.description)">
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 教育背景部分 -->
      <div v-if="hasEducation" class="section">
        <div class="section-header">
          <h2>教育背景</h2>
        </div>
        <div class="section-content">
          <div v-for="(edu, index) in resume.modules.education" :key="index" class="education-item">
            <div class="education-header">
              <div class="education-school">{{ edu.school }}({{ edu.degree }})</div>
              <div class="education-major">{{ edu.major }}</div>
              <div class="education-date">{{ formatDate(edu.time[0]) }} - {{ formatDate(edu.time[1]) || '至今' }}</div>
            </div>
            <div class="education-details">

              <div v-if="edu.courses" class="education-courses" v-html="formatContent(edu.courses)"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 证书奖项部分 -->
      <div v-if="hasCertificates" class="section">
        <div class="section-header">
          <h2>证书奖项</h2>
        </div>
        <div class="certificate-content">
          <div v-html="formatContent(typeof resume.modules.certificates === 'string' ?
            resume.modules.certificates :
            (typeof resume.modules.certificates === 'object' ? resume.modules.certificates.certificateName : ''))"></div>
        </div>
      </div>

      <!-- 校园经历部分 -->
      <div v-if="hasCampus" class="section">
        <div class="section-header">
          <h2>校园经历</h2>
        </div>
        <div class="section-content">
          <div class="campus-content" v-html="formatContent(typeof resume.modules.campus === 'string' ?
            resume.modules.campus :
            (typeof resume.modules.campus === 'object' ? resume.modules.campus.description : ''))"></div>
        </div>
      </div>

      <!-- 兴趣爱好部分 -->
      <div v-if="hasInterests" class="section">
        <div class="section-header">
          <h2>兴趣爱好</h2>
        </div>
        <div class="section-content">
          <div class="interests-content" v-html="formatContent(typeof resume.modules.interests === 'string' ?
            resume.modules.interests :
            (typeof resume.modules.interests === 'object' ? resume.modules.interests.description : ''))"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed, toRefs} from 'vue';
import { MdPreview } from 'md-editor-v3';
import 'md-editor-v3/lib/preview.css';
import {Discount} from "@element-plus/icons-vue";

const props = defineProps({
  resume: {
    type: Object,
    required: true
  }
});
const {resume} = toRefs(props);

// 判断各模块是否有内容
const hasEducation = computed(() => props.resume.modules && props.resume.modules.education && props.resume.modules.education.length > 0);
const hasWork = computed(() => props.resume.modules && props.resume.modules.work && props.resume.modules.work.length > 0);
const hasProjects = computed(() => props.resume.modules && props.resume.modules.projects && props.resume.modules.projects.length > 0);
const hasSkills = computed(() => props.resume.modules && props.resume.modules.skills && props.resume.modules.skills.length > 0);
const hasPractices = computed(() => props.resume.modules && props.resume.modules.practices && props.resume.modules.practices.length > 0);
const hasCertificates = computed(() => {
  if (!props.resume.modules || !props.resume.modules.certificates) return false;
  const cert = props.resume.modules.certificates;
  return typeof cert === 'string' ? cert.trim() !== '' :
      (typeof cert === 'object' && cert.certificateName && cert.certificateName.trim() !== '');
});
const hasCampus = computed(() => {
  if (!props.resume.modules || !props.resume.modules.campus) return false;
  const campus = props.resume.modules.campus;
  return typeof campus === 'string' ? campus.trim() !== '' :
      (typeof campus === 'object' && campus.description && campus.description.trim() !== '');
});
const hasInterests = computed(() => {
  if (!props.resume.modules || !props.resume.modules.interests) return false;
  const interests = props.resume.modules.interests;
  return typeof interests === 'string' ? interests.trim() !== '' :
      (typeof interests === 'object' && interests.description && interests.description.trim() !== '');
});
const hasEvaluation = computed(() => {
  if (!props.resume.modules || !props.resume.modules.selfEvaluation) return false;
  const evaluation = props.resume.modules.selfEvaluation;
  return typeof evaluation === 'string' ? evaluation.trim() !== '' :
      (typeof evaluation === 'object' && evaluation.description && evaluation.description.trim() !== '');
});

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';

  try {
    const dateObj = new Date(date);

    // Check if the date is valid
    if (isNaN(dateObj.getTime())) {
      return date; // Return original string if parsing fails
    }

    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');

    return `${year}-${month}`;
  } catch (e) {
    console.error('Error formatting date:', e);
    return date; // Return original string in case of error
  }
};

// 格式化文本内容，支持Markdown格式
const formatContent = (content) => {
  if (!content) return '';

  // 如果是对象类型，提取description字段
  if (typeof content === 'object') {
    content = content.description || '';
    if (!content) return '';
  }

  // 如果内容已经包含HTML标签，说明是从富文本编辑器来的，直接返回
  if (content.includes('<') && content.includes('>')) {
    return content;
  }

  // 简单的Markdown转HTML处理
  return content
      // 处理标题
      .replace(/^### (.*?)$/gm, '<h3>$1</h3>')
      .replace(/^## (.*?)$/gm, '<h2>$1</h2>')
      .replace(/^# (.*?)$/gm, '<h1>$1</h1>')
      // 处理加粗和斜体
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // 处理列表
      .replace(/^\- (.*?)$/gm, '<li>$1</li>')
      .replace(/(<\/li>\n<li>)/g, '</li><li>')
      .replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>')
      // 处理段落
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^(.+)$/, '<p>$1</p>');
};

const formatContent1 = (content) => {
  if (!content) return '';

  // 如果是对象类型，提取description字段
  if (typeof content === 'object') {
    content = content.description || '';
    if (!content) return '';
  }

  // 如果内容已经包含HTML标签，说明是从富文本编辑器来的，直接返回
  if (content.includes('<') && content.includes('>')) {
    return content;
  }

  // 处理列表（保持原有逻辑）
  let formatted = content
      .replace(/^\- (.*?)$/gm, '<li>$1</li>')
      .replace(/(<\/li>\n<li>)/g, '</li><li>')
      .replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>');

  // 处理标题（保持原有逻辑）
  formatted = formatted
      .replace(/^### (.*?)$/gm, '<h3>$1</h3>')
      .replace(/^## (.*?)$/gm, '<h2>$1</h2>')
      .replace(/^# (.*?)$/gm, '<h1>$1</h1>');

  // 处理加粗和斜体（修改加粗的正则表达式，使用非贪婪模式）
  formatted = formatted
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>');

  // 处理下划线
  formatted = formatted
      .replace(/\*\_(.*?)\_\*/g, '<u>$1</u>');

  // 处理段落
  formatted = formatted
      .replace(/\n\n/g, '<p></p>')
      .replace(/\n/g, '<br>')
      .replace(/^(.+)$/gm, '<p>$1</p>');

  return formatted;
};

// 格式化项目描述
const formatProjectDescription = (description) => {
  if (!description) return '';

  // 处理带有标记的文本
  if (description.includes('【') && description.includes('】')) {
    return description.replace(/【(.*?)】(.*?)(?=【|$)/g, '<div class="description-tag"><span class="tag-title">【$1】</span>$2</div>');
  }

  // 普通文本处理
  return description.split('\n').filter(line => line.trim()).map(line => {
    if (line.startsWith('•') || line.startsWith('·')) {
      return `<div class="description-bullet">${line}</div>`;
    }
    return `<div class="description-paragraph">${line}</div>`;
  }).join('');
};

// 获取技能简要描述
const getSkillSummary = (description) => {
  if (!description) return '';
  // 提取第一句话或者前50个字符
  const firstSentence = description.split('。')[0];
  if (firstSentence.length > 50) {
    return firstSentence.substring(0, 50) + '...';
  }
  return firstSentence;
};

// 自我评价标签
const evaluationTags = computed(() => {
  if (!props.resume.modules || !props.resume.modules.evaluation) return [];

  const tags = [];
  const evaluation = props.resume.modules.evaluation;

  // 提取【】中的内容作为标签
  const tagMatches = evaluation.match(/【(.*?)】(.*?)(?=【|$)/g);
  if (tagMatches) {
    tagMatches.forEach(match => {
      const titleMatch = match.match(/【(.*?)】/);
      const contentMatch = match.match(/】(.*?)$/);
      if (titleMatch && contentMatch) {
        tags.push({
          title: titleMatch[1],
          content: contentMatch[1].trim()
        });
      }
    });
  }

  return tags;
});

// 格式化自我评价
const formattedEvaluation = computed(() => {
  if (!props.resume.modules || !props.resume.modules.evaluation) return '';

  const evaluation = props.resume.modules.evaluation;
  // 移除标签部分，只保留纯文本
  return evaluation.replace(/【.*?】/g, '');
});
</script>

<style scoped>
.resume-template {
  width: 100%;
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  background-color: #fff;
  line-height: 1.7;
  color: rgb(68, 84, 106);
  padding: 0 !important;
}

.resume-container {
  max-width: 210mm;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
  border: 15px solid rgb(68, 84, 106); /* 添加RGB颜色的边框，粗细为2px */
}

.info-name {
  font-weight: bold;
  font-size: 30px;
  padding-bottom: 20px;
}

/* 头部个人信息样式 */
.header-section {
  display: flex;
  justify-content: space-between;
  padding-bottom: 15px;
}

.personal-info {
  flex: 1;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
}

.info-item {
  flex: 1;
  min-width: 0;
}

.info-item.full-width {
  flex: 2;
}

.label {
  font-weight: bold;
}

.avatar-container {
  width: 120px;
  height: 150px;
  margin: 0 auto;
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.3);
}


.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: 1px solid #ccc;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
}

/* 各部分通用样式 */
.section {
  margin-bottom: 5px;
  padding: 0 !important;
}

.section-header {
  margin-bottom: 5px;
  position: relative;
  display: inline-block;
  padding: 0 5px;
}

.section-header::before,
.section-header::after {
  content: '';
  position: absolute;
  height: 1px;
  background-color: #333;
  width: calc(100% + 10px); /* 比文字宽10px（左右各5px） */
  left: -5px; /* 向左偏移5px */
}

.section-header::before {
  bottom: -2px;
}

.section-header::after {
  bottom: -5px;
}

.section-header h2 {
  display: inline-block;
  margin: 0;
  padding: 5px 15px;
  font-size: 25px;
  font-weight: bold;
  background-color: #f0f0f0;
  border-radius: 5px 5px 0 0;
}

.section-content {
  padding: 5px 0;
}

/* 自我评价样式 */
.evaluation-content {
  padding: 5px;
}

.evaluation-tags {
  margin-bottom: 10px;
}

.tag {
  margin-bottom: 5px;
}

.evaluation-text {
  line-height: 1.7;
}

/* 工作经验样式 */
.experience-item {
  margin-bottom: 20px; /* 增加底部间距 */
}

.experience-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.education-date{
  flex: 1;
  font-weight: bold;
  font-size: 17px;
  margin-bottom: 10px;
  text-align: right;
}

.experience-date {
  margin-right: 40px;
  font-weight: bold;
  color: black;
  font-size: 17px;
  flex: 1;
}
.experience-company {
  margin-right: 15px;
  font-weight: bold;
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  &::after {
    content: " | ";
    color: inherit;
  }
  font-size: 18px;
}
.experience-position {
  white-space: nowrap;
  font-size: 18px;
}

.experience-position {
  color: #666;
  white-space: nowrap;
}

.experience-description {
  line-height: 1.7; /* 增加行高 */
}

/* 项目经验样式 */
.project-item {
  margin-bottom: 12px; /* 与experience-item一致，原8px */
}

.project-header {
  display: flex;
  align-items: center;
  margin-bottom: 6px; /* 与experience-header一致，原10px */
}

.project-title {
  font-weight: bold;
  font-size: 17px;
  flex: 1;
}

.project-date {
  font-weight: bold;
  color: black;
  font-size: 17px;
  flex: 1;
  text-align: right;
}

.project-role {
  margin-right: 15px;
  font-size: 18px;
  font-weight: bold;
}

.project-description {
  padding-left: 10px;
  line-height: 1.7; /* 与experience-description一致 */
}

/* 描述内容样式 */
.description-content {
  line-height: 1.7;
}

.description-tag {
  margin-bottom: 5px;
}

.tag-title {
  font-weight: bold;
}

.description-bullet {
  padding-left: 20px;
  position: relative;
  margin-bottom: 5px;
}

.description-bullet::before {
  content: "•";
  position: absolute;
  left: 5px;
}

.description-paragraph {
  margin-bottom: 5px;
  text-indent: 2em;
}

/* 技能部分样式 */
.skills-content {
  padding: 10px;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
}

.skill-item {
  width: 100%;
  margin-bottom: 10px;
  align-items: center; /* 垂直居中对齐 */
}

.skill-name {
  font-weight: bold;
  margin-right: 10px;
  white-space: nowrap;
}

.skill-description {
  flex: 1;
  line-height: 1.7;
}

/* 教育背景样式 */
.education-item {
  margin-bottom: 5px;
}

.education-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.education-school {
  width: 200px;
  flex: 1;
  font-weight: bold;
  margin-right: 20px;
  margin-bottom: 10px;
  font-size: 18px;
}

.education-major {
  font-weight: bold;
  margin-bottom: 10px;
  font-size: 18px;
}

.education-details {
  margin-top: 5px;
}

.education-courses {
  line-height: 1.7; /* 增加行高 */
}

.education-courses p,
.education-courses li {
  margin-bottom: 5px; /* 增加段落和列表项的底部间距 */
}

/* 添加 MdPreview 相关样式 */
:deep(.md-preview) {
  background: none;
  padding: 0;
}

:deep(.md-preview-html) {
  padding: 0;
}

:deep(.md-preview-html p) {
  margin: 0;
}

:deep(.md-preview-html ul) {
  margin: 0;
  padding-left: 20px;
}

:deep(.md-preview-html li) {
  margin: 0;
}

:deep(.md-preview-html strong) {
  font-weight: bold;
}

:deep(.md-preview-html em) {
  font-style: italic;
}

:deep(.md-preview-html h1) {
  font-size: 1.5em;
  margin: 0.5em 0;
}

:deep(.md-preview-html h2) {
  font-size: 1.3em;
  margin: 0.5em 0;
}

:deep(.md-preview-html h3) {
  font-size: 1.1em;
  margin: 0.5em 0;
}
</style> 