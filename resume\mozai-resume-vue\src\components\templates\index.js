import Template1 from './Template1.vue';
import Template2 from './Template2.vue';
import Template3 from './Template3.vue';
import Template4 from './Template4.vue';
import Template5 from './Template5.vue';
import TemplateSelector from './TemplateSelector.vue';
import ResumePreview from './ResumePreview.vue';

// 模板配置信息
export const templateConfig = [
  {
    id: 1,
    name: '模板1',
    thumbnail: 'https://bmw-pic.oss-cn-beijing.aliyuncs.com/muban1.png',
    component: Template1,
    features: ['专业简洁', '渐变标题', '适合程序员']
  },
  {
    id: 2,
    name: '模板2',
    thumbnail: 'https://bmw-pic.oss-cn-beijing.aliyuncs.com/muban2.png',
    component: Template2,
    features: ['单列布局', '红色标题', '适合设计师']
  },
  {
    id: 3,
    name: '模板3',
    thumbnail: 'https://bmw-pic.oss-cn-beijing.aliyuncs.com/muban3.png',
    component: Template3,
    features: ['清新简约', '灰色配色', '通用模板']
  },
  {
    id: 4,
    name: '模板4',
    thumbnail: 'https://bmw-pic.oss-cn-beijing.aliyuncs.com/muban4.png',
    component: Template4,
    features: ['商务风格', '标签式布局', '突出技能']
  },
  {
    id: 5,
    name: '模板5',
    thumbnail: 'https://bmw-pic.oss-cn-beijing.aliyuncs.com/muban5.png',
    component: Template5,
    features: ['蓝色主题', '时间轴布局', '简洁大方']
  },
];

// 获取模板组件函数
export const getTemplateComponent = (templateId) => {
  const template = templateConfig.find(t => t.id === Number(templateId));
  return template ? template.component : Template1;
};

export {
  Template1,
  Template2,
  Template3,
  Template4,
  Template5,
  TemplateSelector,
  ResumePreview
};