# OSS直传配置指南

本文档介绍如何配置阿里云RAM角色，以支持前端直传文件到OSS。

## 配置步骤

### 1. 创建RAM角色

1. 登录阿里云控制台，进入RAM访问控制服务
2. 在左侧菜单选择"RAM角色管理"，点击"创建RAM角色"
3. 选择"阿里云账号"作为可信实体类型
4. 选择"当前云账号"作为受信云账号
5. 输入角色名称，如"aliyunosstokengeneratorrole"
6. 点击"确定"创建角色

### 2. 为角色授权OSS权限

1. 在RAM角色列表中，找到刚创建的角色，点击"添加权限"
2. 在权限策略名称中搜索"AliyunOSSFullAccess"，选择该策略
3. 点击"确定"完成授权

### 3. 获取角色ARN

1. 在RAM角色列表中，点击刚创建的角色名称
2. 在角色详情页面，找到"ARN"字段，复制该值，格式如：`acs:ram::1234567890123456:role/aliyunosstokengeneratorrole`

### 4. 配置应用

1. 打开`application.yml`文件
2. 在`aliyun.oss`配置部分，添加或修改`roleArn`配置项，填入上一步复制的ARN值：

```yaml
aliyun:
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    accessKeyId: LTAI5tEm5vMpTW7AB2BjHtYD
    accessKeySecret: ******************************
    bucketName: bmw-pic
    roleArn: acs:ram::1234567890123456:role/aliyunosstokengeneratorrole  # 替换为实际的ARN
```

## 注意事项

1. 确保使用的AccessKey有足够权限调用STS服务的AssumeRole接口
2. 为了安全考虑，建议为前端直传创建单独的RAM角色，并仅授予必要的OSS权限
3. 可以通过RAM角色的权限策略限制上传路径、文件大小等，增强安全性

## 测试验证

配置完成后，可以通过以下方式验证：

1. 启动后端服务
2. 访问`/interview/oss/sts`接口，应返回包含临时凭证的JSON响应
3. 使用前端OSS上传组件测试文件上传功能 