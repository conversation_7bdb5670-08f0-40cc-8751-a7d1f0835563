package com.bimowu.resume.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 样式同步响应DTO
 */
@Data
public class StyleSyncResponse {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 模板ID
     */
    private Integer templateId;
    
    /**
     * 批量处理结果
     */
    private BatchSyncResult batchResult;
    
    /**
     * 同步前版本
     */
    private String beforeVersion;
    
    /**
     * 同步后版本
     */
    private String afterVersion;
    
    /**
     * 同步耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 变更文件数量
     */
    private Integer changedFiles;
    
    /**
     * 错误详情
     */
    private String errorDetail;
    
    /**
     * 同步记录ID
     */
    private Long syncRecordId;
    
    /**
     * 批量同步结果
     */
    @Data
    public static class BatchSyncResult {
        /**
         * 总数
         */
        private int total;
        
        /**
         * 成功数
         */
        private int success;
        
        /**
         * 失败数
         */
        private int failed;
        
        /**
         * 成功的模板ID列表
         */
        private List<Integer> successTemplateIds;
        
        /**
         * 失败的模板ID列表
         */
        private List<Integer> failedTemplateIds;
        
        /**
         * 失败详情
         */
        private Map<Integer, String> failureDetails;
        
        /**
         * 总耗时（毫秒）
         */
        private Long totalDuration;
    }
}