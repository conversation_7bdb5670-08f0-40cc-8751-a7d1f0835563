package com.bimowu.resume.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * PDF生成配置类
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "pdf.generation")
public class PDFGenerationConfig {
    
    /**
     * PDF生成引擎 (flying-saucer, itext-xmlworker)
     */
    private String engine = "flying-saucer";
    
    /**
     * 生成超时时间（秒）
     */
    private int timeoutSeconds = 30;
    
    /**
     * 最大并发生成数
     */
    private int maxConcurrent = 10;
    
    /**
     * 页面配置
     */
    private PageConfig page = new PageConfig();
    
    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();
    
    /**
     * 性能配置
     */
    private PerformanceConfig performance = new PerformanceConfig();
    
    /**
     * 模板配置
     */
    private TemplateConfig template = new TemplateConfig();
    
    /**
     * 页面配置
     */
    @Data
    public static class PageConfig {
        /**
         * 页面大小 (A4, A3, LETTER等)
         */
        private String size = "A4";
        
        /**
         * 页面方向 (portrait, landscape)
         */
        private String orientation = "portrait";
        
        /**
         * DPI设置
         */
        private float dpi = 300f;
        
        /**
         * 页边距 (单位: mm)
         */
        private MarginConfig margin = new MarginConfig();
        
        @Data
        public static class MarginConfig {
            private float top = 20f;
            private float bottom = 20f;
            private float left = 20f;
            private float right = 20f;
        }
    }
    
    /**
     * 缓存配置
     */
    @Data
    public static class CacheConfig {
        /**
         * 是否启用缓存
         */
        private boolean enabled = true;
        
        /**
         * 缓存TTL（秒）
         */
        private long ttl = 3600;
        
        /**
         * 最大缓存大小
         */
        private int maxSize = 1000;
        
        /**
         * 缓存类型 (memory, redis)
         */
        private String type = "memory";
    }
    
    /**
     * 性能配置
     */
    @Data
    public static class PerformanceConfig {
        /**
         * 对象池核心大小
         */
        private int poolCoreSize = 5;
        
        /**
         * 对象池最大大小
         */
        private int poolMaxSize = 10;
        
        /**
         * 对象池空闲超时（秒）
         */
        private int poolIdleTimeout = 60;
        
        /**
         * 是否启用异步生成
         */
        private boolean asyncEnabled = false;
        
        /**
         * 内存限制（MB）
         */
        private int memoryLimitMB = 512;
    }
    
    /**
     * 模板配置
     */
    @Data
    public static class TemplateConfig {
        /**
         * 模板根路径
         */
        private String basePath = "templates/pdf";
        

        /**
         * 是否启用样式验证
         */
        private boolean validationEnabled = true;
        
        /**
         * 自定义CSS
         */
        private Map<String, String> customCSS = new HashMap<>();
    }
}