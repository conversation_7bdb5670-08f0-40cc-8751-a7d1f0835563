export declare const isScroll: (el: HTMLElement, isVertical?: boolean | undefined) => boolean;
export declare const getScrollContainer: (el: HTMLElement, isVertical?: boolean | undefined) => Window | HTMLElement | undefined;
export declare const getScrollBarWidth: (namespace: string) => number;
/**
 * Scroll with in the container element, positioning the **selected** element at the top
 * of the container
 */
export declare function scrollIntoView(container: HTMLElement, selected: HTMLElement): void;
