<template>
  <div class="resume-editor">
    <!-- 左侧导航栏 -->
    <div class="side-nav">
      <div class="nav-item" :class="{ active: currentNav === 'content' }" @click="currentNav = 'content'">
        <el-icon>
          <document />
        </el-icon>
        <span class="nav-text">简历内容</span>
      </div>
      <div class="nav-item" :class="{ active: currentNav === 'template' }" @click="currentNav = 'template'">
        <el-icon>
          <picture />
        </el-icon>
        <span class="nav-text">风格模板</span>
      </div>
    </div>

    <!-- 顶部导航栏 -->
    <div class="editor-header">
      <div class="container editor-header-container">
        <div class="editor-logo">
          <router-link to="/">
            <h1>墨崽简历</h1>
          </router-link>
        </div>

        <div class="editor-actions">
          <el-input v-model="resumeName" placeholder="未命名" class="resume-name-input" style="width: 300px;"
                    @change="handleResumeNameChange" />
          <el-select v-model="resumeData.category" placeholder="请选择简历类型" @change="updateResumeDataObject">
            <el-option v-for="option in categoryOptions" :key="option.value" :label="option.label"
                       :value="option.value" />
          </el-select>
          <el-button @click="handleSaveResume">
            保存
          </el-button>
        </div>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton style="width: 100%" :rows="20" animated />
    </div>

    <div v-else-if="resumeData" class="editor-content">
      <!-- 左侧编辑区域 -->
      <div class="edit-panel">
        <!-- 进度条 -->
        <div class="progress-section">
          <div class="progress-info">
            <span>简历完成度</span>
            <span>{{ completionPercentage }}%</span>
          </div>
          <el-progress :percentage="completionPercentage" color="#3498db"></el-progress>
        </div>

        <!-- 编辑表单内容区域 -->
        <div class="editing-area" v-if="currentNav === 'content'">
          <!-- 基本信息 -->
          <div class="module-card" id="module-basic">
            <div class="module-card-header">
              <div class="module-title">
                <el-icon>
                  <user />
                </el-icon>
                <span>基本信息</span>
              </div>
              <div class="module-controls">
                <el-switch v-model="moduleVisibility.basic" active-color="#67C23A"
                           @change="toggleModuleVisibility('basic')" />
              </div>
            </div>

            <div class="form-content">
              <div class="form-row">
                <div class="form-item">
                  <div class="avatar-upload">
                    <el-avatar :size="70" :src="resumeData.modules.basic?.avatar || ''" />
                    <input
                        type="file"
                        ref="avatarInput"
                        class="avatar-input"
                        accept="image/*"
                        @change="handleAvatarUpload"
                    />
                    <div class="upload-btn" @click="triggerAvatarUpload">
                      <el-icon>
                        <plus />
                      </el-icon>
                    </div>
                  </div>
                </div>
              </div>

              <div class="form-row">
                <div class="form-item">
                  <div class="form-label">姓名</div>
                  <el-input v-model="resumeData.modules.basic.name" placeholder="请输入姓名"
                            @input="updateResumeDataObject" />
                </div>

                <div class="form-item">
                  <div class="form-label">性别</div>
                  <el-select v-model="resumeData.modules.basic.gender" placeholder="请选择性别"
                             @change="updateResumeDataObject">
                    <el-option label="男" value="男" />
                    <el-option label="女" value="女" />
                  </el-select>
                </div>
              </div>

              <div class="form-row">
                <div class="form-item">
                  <div class="form-label">出生日期</div>
                  <el-date-picker v-model="resumeData.modules.basic.birthDate" type="date" placeholder="选择出生日期"
                                  format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="updateResumeDataObject" />
                </div>

                <div class="form-item">
                  <div class="form-label">联系电话</div>
                  <el-input v-model="resumeData.modules.basic.phone" placeholder="联系电话"
                            @input="updateResumeDataObject" />
                </div>
              </div>

              <div class="form-row">
                <div class="form-item">
                  <div class="form-label">联系邮箱</div>
                  <el-input v-model="resumeData.modules.basic.email" placeholder="联系邮箱"
                            @input="updateResumeDataObject" />
                </div>

                <div class="form-item">
                  <div class="form-label">籍贯</div>
                  <el-input v-model="resumeData.modules.basic.hometown" placeholder="籍贯"
                            @input="updateResumeDataObject" />
                </div>
              </div>

              <div class="form-row">
                <div class="form-item">
                  <div class="form-label">年龄</div>
                  <el-input-number v-model="resumeData.modules.basic.age" :min="0" :max="100" placeholder="年龄"
                                   @change="updateResumeDataObject" />
                </div>

                <div class="form-item">
                  <div class="form-label">求职意向</div>
                  <el-input v-model="resumeData.modules.basic.jobObjective" placeholder="求职意向"
                            @input="updateResumeDataObject" />
                </div>
              </div>

              <div class="form-row">
                <div class="form-item">
                  <div class="form-label">民族</div>
                  <el-select v-model="resumeData.modules.basic.nationality" placeholder="请选择民族"
                             @change="updateResumeDataObject">
                    <el-option label="汉族" value="汉族" />
                    <el-option label="蒙古族" value="蒙古族" />
                    <el-option label="回族" value="回族" />
                    <el-option label="藏族" value="藏族" />
                    <el-option label="维吾尔族" value="维吾尔族" />
                    <el-option label="苗族" value="苗族" />
                    <el-option label="彝族" value="彝族" />
                    <el-option label="壮族" value="壮族" />
                    <el-option label="布依族" value="布依族" />
                    <el-option label="朝鲜族" value="朝鲜族" />
                    <el-option label="满族" value="满族" />
                    <el-option label="侗族" value="侗族" />
                    <el-option label="瑶族" value="瑶族" />
                    <el-option label="白族" value="白族" />
                    <el-option label="土家族" value="土家族" />
                    <el-option label="哈尼族" value="哈尼族" />
                    <el-option label="哈萨克族" value="哈萨克族" />
                    <el-option label="傣族" value="傣族" />
                    <el-option label="其他" value="其他" />
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="form-label">政治面貌</div>
                  <el-select v-model="resumeData.modules.basic.politicalStatus" placeholder="请选择政治面貌"
                             @change="updateResumeDataObject">
                    <el-option label="中共党员" value="中共党员" />
                    <el-option label="中共预备党员" value="中共预备党员" />
                    <el-option label="共青团员" value="共青团员" />
                    <el-option label="民主党派" value="民主党派" />
                    <el-option label="群众" value="群众" />
                  </el-select>
                </div>
              </div>
            </div>
          </div>

          <!-- 教育经历 -->
          <div class="module-card" id="module-education">
            <div class="module-card-header">
              <div class="module-title">
                <el-icon>
                  <reading />
                </el-icon>
                <span>教育经历</span>
              </div>
              <div class="module-controls">
                <el-switch v-model="moduleVisibility.education" active-color="#67C23A"
                           @change="toggleModuleVisibility('education')" />
                <el-button type="primary" size="small" class="add-btn" @click="addEducation">添加</el-button>
              </div>
            </div>

            <div class="form-content" v-if="moduleVisibility.education">
              <div v-for="(edu, index) in resumeData.modules.education" :key="'edu-' + index" class="education-item-edit">
                <div class="form-row">
                  <div class="form-item">
                    <div class="form-label">学校</div>
                    <el-input v-model="edu.school" placeholder="学校名称" @input="updateResumeDataObject" />
                  </div>

                  <div class="form-item">
                    <div class="form-label">专业</div>
                    <el-input v-model="edu.major" placeholder="所学专业" @input="updateResumeDataObject" />
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-item">
                    <div class="form-label">学历</div>
                    <el-select v-model="edu.degree" placeholder="选择学历" @change="updateResumeDataObject">
                      <el-option label="高中" value="高中" />
                      <el-option label="大专" value="大专" />
                      <el-option label="本科" value="本科" />
                      <el-option label="硕士" value="硕士" />
                      <el-option label="博士" value="博士" />
                    </el-select>
                  </div>
                </div>
                <div class="form-row full-width">
                  <div class="form-item">
                    <div class="form-label">时间段</div>
                    <el-date-picker v-model="edu.time" type="monthrange" range-separator="至" start-placeholder="开始月份"
                                    end-placeholder="结束月份" value-format="YYYY-MM"
                                    @change="handleDateChangeEducation(edu, $event); updateResumeDataObject()" style="width: 100%;" />
                  </div>
                </div>

                <div class="form-row full-width">
                  <div class="form-item">
                    <div class="form-label">主修课程</div>
                    <el-input v-model="edu.courses" type="textarea" :rows="2" placeholder="请输入主修课程，多个课程用逗号分隔"
                              @input="updateResumeDataObject" />
                  </div>
                </div>

                <div class="item-actions">
                  <el-button type="danger" size="small" plain circle @click="removeEducation(index)">
                    <el-icon>
                      <delete />
                    </el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 工作经验 -->
          <div class="module-card" id="module-work">
            <div class="module-card-header">
              <div class="module-title">
                <el-icon>
                  <office-building />
                </el-icon>
                <span>工作经验</span>
              </div>
              <div class="module-controls">
                <el-switch v-model="moduleVisibility.work" active-color="#67C23A"
                           @change="toggleModuleVisibility('work')" />
                <el-button type="primary" size="small" class="add-btn" @click="addWork">添加</el-button>
              </div>
            </div>

            <div class="form-content" v-if="moduleVisibility.work">
              <div v-for="(work, index) in resumeData.modules.work" :key="'work-' + index" class="work-item-edit">
                <div class="form-row">
                  <div class="form-item">
                    <div class="form-label">公司</div>
                    <el-input v-model="work.company" placeholder="公司名称" @input="updateResumeDataObject" />
                  </div>

                  <div class="form-item">
                    <div class="form-label">职位</div>
                    <el-input v-model="work.position" placeholder="担任职位" @input="updateResumeDataObject" />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-item">
                    <div class="form-label">工作时间</div>
                    <el-date-picker
                        v-model="work.time"
                        type="monthrange"
                        range-separator="至"
                        start-placeholder="开始月份"
                        end-placeholder="结束月份"
                        value-format="YYYY-MM"
                        @change="handleDateChangeWork(work, $event); updateResumeDataObject()"
                    />
                  </div>
                </div>

                <div class="form-row full-width">
                  <div class="form-item">
                    <div class="form-label">工作描述</div>
                    <work-description-form :ref="el => { if (el) workFormRefs[index] = el }" :data="work.description"
                                           :company="work.company" :position="work.position"
                                           @update="(val) => updateWorkDescription(index, val)"
                                           @polish-content="aiPolishWorkDescription(index)" :show-editor="true" />
                  </div>
                </div>

                <div class="item-actions">
                  <el-button type="danger" size="small" plain circle @click="removeWork(index)">
                    <el-icon>
                      <delete />
                    </el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 项目经验 -->
          <div class="module-card" id="module-projects">
            <div class="module-card-header">
              <div class="module-title">
                <el-icon>
                  <folder />
                </el-icon>
                <span>项目经验</span>
              </div>
              <div class="module-controls">
                <el-switch v-model="moduleVisibility.projects" active-color="#67C23A"
                           @change="toggleModuleVisibility('projects')" />
                <el-button type="primary" size="small" class="add-btn" @click="addProject">添加</el-button>
              </div>
            </div>

            <div class="form-content" v-if="moduleVisibility.projects">
              <div v-for="(project, index) in resumeData.modules.projects" :key="'project-' + index"
                   class="project-item-edit">
                <div class="form-row">
                  <div class="form-item">
                    <div class="form-label">职位类别</div>
                    <el-select v-model="project.category" placeholder="选择职位类别"
                               @change="handleProjectCategoryChange($event, index); updateResumeDataObject()">
                      <el-option v-for="option in categoryOptions" :key="option.value" :label="option.label"
                                 :value="option.value" />
                    </el-select>
                  </div>

                  <div class="form-item">
                    <div class="form-label">项目名称</div>
                    <el-select v-model="project.name" filterable allow-create default-first-option placeholder="选择或创建项目"
                               @change="handleProjectSelect($event, index); updateResumeDataObject()" style="width: 100%;">
                      <el-option v-for="item in projectOptions" :key="item.value" :label="item.name"
                                 :value="item.value" />
                    </el-select>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-item">
                    <div class="form-label">担任角色</div>
                    <el-input v-model="project.role" placeholder="项目职责/角色" @input="updateResumeDataObject" />
                  </div>
                </div>
                <div class="form-row full-width">
                  <div class="form-item">
                    <div class="form-label">时间段</div>
                    <el-date-picker v-model="project.time" type="monthrange" range-separator="至"
                                    start-placeholder="开始月份" end-placeholder="结束月份" value-format="YYYY-MM"
                                    @change="handleDateChangeProject(project, $event); updateResumeDataObject()" style="width: 100%;" />
                  </div>
                </div>

                <div class="form-row full-width">
                  <div class="form-item">
                    <div class="form-label">项目描述</div>
                    <project-form :ref="el => { if (el) projectFormRefs[index] = el }" :data="project.description"
                                  :project-name="project.name" :project-category="project.category"
                                  @update="(val) => updateProjectDescription(index, val)"
                                  @select-content-items="selectProjectContentItems(index)"
                                  @polish-content="aiPolishDescription(index)" :show-editor="true" />
                  </div>
                </div>

                <div class="item-actions">
                  <el-button type="danger" size="small" plain circle @click="removeProject(index)">
                    <el-icon>
                      <delete />
                    </el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 练手项目 -->
          <div class="module-card" id="module-practices">
            <div class="module-card-header">
              <div class="module-title">
                <el-icon>
                  <folder />
                </el-icon>
                <span>练手项目</span>
              </div>
              <div class="module-controls">
                <el-switch v-model="moduleVisibility.practices" active-color="#67C23A"
                           @change="toggleModuleVisibility('practices')" />
                <el-button type="primary" size="small" class="add-btn" @click="addPractice">添加</el-button>
              </div>
            </div>

            <div class="form-content" v-if="moduleVisibility.practices">
              <div v-for="(practice, index) in resumeData.modules.practices" :key="'practice-' + index"
                   class="practice-item-edit">
                <div class="form-row">
                  <div class="form-item">
                    <div class="form-label">项目名称</div>
                    <el-input v-model="practice.name" placeholder="项目名称" @input="updateResumeDataObject" />
                  </div>

                  <div class="form-item">
                    <div class="form-label">担任角色</div>
                    <el-input v-model="practice.role" placeholder="担任角色" @input="updateResumeDataObject" />
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-item">
                    <div class="form-label">项目地址</div>
                    <el-input v-model="practice.url" placeholder="项目地址（如GitHub链接）" @input="updateResumeDataObject" />
                  </div>
                </div>
                <div class="form-row full-width">
                  <div class="form-item">
                    <div class="form-label">时间段</div>
                    <el-date-picker v-model="practice.time" type="monthrange" range-separator="至"
                                    start-placeholder="开始月份" end-placeholder="结束月份" value-format="YYYY-MM"
                                    @change="handleDateChangePractice(practice, $event); updateResumeDataObject()" style="width: 100%;" />
                  </div>
                </div>
                <div class="form-row full-width">
                  <div class="form-item">
                    <div class="form-label">项目描述</div>
                    <practice-form :ref="el => { if (el) practiceFormRefs[index] = el }" :data="practice.description"
                                   :project-name="practice.name" :show-editor="true"
                                   @update="(val) => updatePracticeDescription(index, val)" />
                  </div>
                </div>

                <div class="item-actions">
                  <el-button type="danger" size="small" plain circle @click="removePractice(index)">
                    <el-icon>
                      <delete />
                    </el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 技能特长 -->
          <div class="module-card" id="module-skills">
            <div class="module-card-header">
              <div class="module-title">
                <el-icon>
                  <star />
                </el-icon>
                <span>技能特长</span>
              </div>
              <div class="module-controls">
                <el-switch v-model="moduleVisibility.skills" active-color="#67C23A"
                           @change="toggleModuleVisibility('skills')" />
                <el-button type="primary" size="small" class="add-btn" @click="addSkill">添加</el-button>
              </div>
            </div>

            <div class="form-content" v-if="moduleVisibility.skills">
              <div v-for="(skill, index) in resumeData.modules.skills" :key="'skill-' + index" class="skill-item-edit">
                <div class="form-row">
                  <div class="form-item">
                    <div class="form-label">技能名称</div>
                    <div class="skill-name-wrapper">
                      <el-select v-model="skill.name" filterable allow-create default-first-option placeholder="选择或创建技能"
                                 @change="handleSkillSelect($event, index)" class="skill-name-select">
                        <el-option v-for="skill in skillList" :key="skill.skiId" :label="skill.name"
                                   :value="skill.skiId" />
                      </el-select>
                      <el-button type="danger" size="small" plain circle @click="removeSkill(index)"
                                 class="skill-delete-btn">
                        <el-icon>
                          <delete />
                        </el-icon>
                      </el-button>
                    </div>
                  </div>
                  <div class="form-item">
                    <div class="form-label">熟练度</div>
                    <el-select v-model="skill.level" placeholder="选择熟练程度" @change="generateSkillDescription(index)">
                      <el-option :label="'一般'" :value="1" />
                      <el-option :label="'良好'" :value="2" />
                      <el-option :label="'熟练'" :value="3" />
                      <el-option :label="'擅长'" :value="4" />
                      <el-option :label="'精通'" :value="5" />
                    </el-select>
                  </div>
                </div>

                <div class="form-row full-width">
                  <div class="form-item">
                    <div class="form-label">技能描述</div>
                    <skill-form :ref="el => { if (el) skillFormRefs[index] = el }" :data="skill.description"
                                :skill-name="String(skill.skiId || skill.id)" :skill-level="skill.level"
                                @update="(val) => updateSkillDescription(index, val)" :show-editor="true" />
                  </div>
                </div>

                <div class="skill-level-visual">
                  <div class="skill-level-bar">
                    <div class="skill-level-fill" :style="{ width: `${skill.level * 20}%` }"></div>
                  </div>
                  <div class="skill-level-label">{{ getSkillLevelText(skill.level) }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 证书奖项 -->
          <div class="module-card" id="module-certificates">
            <div class="module-card-header">
              <div class="module-title">
                <el-icon>
                  <medal />
                </el-icon>
                <span>证书奖项</span>
              </div>
              <div class="module-controls">
                <el-switch v-model="moduleVisibility.certificates" active-color="#67C23A"
                           @change="toggleModuleVisibility('certificates')" />
                <el-button type="primary" size="small" class="add-btn" @click="addCertificate">添加</el-button>
              </div>
            </div>
            <div class="form-content" v-if="moduleVisibility.certificates">
              <certificates-form ref="certificateFormRef" :data="resumeData.modules.certificates" @update="updateCertificates" />
            </div>
          </div>

          <!-- 校园经历 -->
          <div class="module-card" id="module-campus">
            <div class="module-card-header">
              <div class="module-title">
                <el-icon>
                  <school />
                </el-icon>
                <span>校园经历</span>
              </div>
              <div class="module-controls">
                <el-switch v-model="moduleVisibility.campus" active-color="#67C23A"
                           @change="toggleModuleVisibility('campus')" />
                <el-button type="primary" size="small" class="add-btn" @click="showCampusInput">添加</el-button>
              </div>
            </div>
            <div class="form-content" v-if="moduleVisibility.campus">
              <campus-form ref="campusFormRef" :data="resumeData.modules.campus" @update="updateCampus" />
            </div>
          </div>

          <!-- 兴趣爱好 -->
          <div class="module-card" id="module-interests">
            <div class="module-card-header">
              <div class="module-title">
                <el-icon>
                  <brush />
                </el-icon>
                <span>兴趣爱好</span>
              </div>
              <div class="module-controls">
                <el-switch v-model="moduleVisibility.interests" active-color="#67C23A"
                           @change="toggleModuleVisibility('interests')" />
                <el-button type="primary" size="small" class="add-btn" @click="showInterestInput">添加</el-button>
              </div>
            </div>
            <div class="form-content" v-if="moduleVisibility.interests">
              <interest-form ref="interestFormRef" :data="resumeData.modules.interests" @update="updateInterests" />
            </div>
          </div>

          <!-- 个人评价 -->
          <div class="module-card" id="module-evaluation">
            <div class="module-card-header">
              <div class="module-title">
                <el-icon>
                  <reading />
                </el-icon>
                <span>个人评价</span>
              </div>
              <div class="module-controls">
                <el-switch v-model="moduleVisibility.evaluation" active-color="#67C23A"
                           @change="toggleModuleVisibility('evaluation')" />
                <el-button type="primary" size="small" class="add-btn" @click="showEvaluationInput">添加</el-button>
              </div>
            </div>
            <div class="form-content" v-if="moduleVisibility.evaluation">
              <!-- 直接编辑自我评价，不使用组件 -->
              <el-form-item label="个人评价">
                <el-input
                    type="textarea"
                    v-model="resumeData.modules.selfEvaluation.description"
                    placeholder="请输入您的自我评价，建议包含个人优势、性格特点、工作风格、职业规划等内容"
                    :rows="5"
                    @input="updateResumeDataObject"
                ></el-input>
              </el-form-item>
              <!-- 原组件方式保留但不渲染 -->
              <div style="display: none;">
                <self-evaluation-form ref="evaluationFormRef" :data="resumeData.modules.selfEvaluation"
                                      @update="updateEvaluation" />
              </div>
            </div>
          </div>
        </div>

        <!-- 风格模板选择区域 -->
        <div class="editing-area template-area" v-if="currentNav === 'template'">
          <div class="module-card">
            <div class="module-card-header">
              <div class="module-title">
                <el-icon>
                  <picture />
                </el-icon>
                <span>选择模板风格</span>
              </div>
            </div>

            <div class="form-content">
              <p class="section-description">选择一个喜欢的模板风格，您的简历内容会自动适配所选模板</p>
              <div class="templates-grid">
                <div v-for="template in templates" :key="template.id" class="template-item"
                     :class="{ 'active': currentTemplateId === template.id }" @click="handleTemplateChange(template.id)">
                  <div class="template-preview">
                    <img :src="template.thumbnail" :alt="template.name" class="template-image" />
                  </div>
                  <div class="template-info">
                    <div class="template-name">{{ template.name }}</div>
                    <div class="template-badges">
                      <span class="template-badge" v-for="(feature, index) in template.features" :key="index">
                        {{ feature }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI助手区域 -->
        <div class="editing-area ai-area" v-if="currentNav === 'ai'">
          <div class="module-card">
            <div class="module-card-header">
              <div class="module-title">
                <el-icon>
                  <chat-dot-round />
                </el-icon>
                <span>AI 简历助手</span>
              </div>
            </div>

            <div class="form-content">
              <p class="section-description">AI助手可以帮助您完善简历内容，提供专业的修改建议</p>
              <div class="ai-chat-container">
                <div class="ai-messages">
                  <div class="ai-message">
                    <el-icon>
                      <chat-round />
                    </el-icon>
                    <div class="message-content">
                      您好！我是AI简历助手，有什么可以帮助您改进简历的吗？
                    </div>
                  </div>
                </div>
                <div class="ai-input">
                  <el-input type="textarea" :rows="2" placeholder="输入您的问题，例如：如何写好个人评价？"></el-input>
                  <el-button type="primary">发送</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧预览区域 -->
      <div class="preview-panel">
        <div class="preview-toolbar">
          <div class="toolbar-right">
            <el-button size="small" @click="handleZoom">缩放</el-button>
            <el-button size="small" type="danger" plain @click="handleDeleteResume">删除</el-button>
          </div>
        </div>

        <div class="resizer" @mousedown="startResize">
          <div class="resizer-tip">可左右拖动改变左右侧的宽度</div>
        </div>

        <div class="resume-preview-container">
          <div class="scale-container" :style="{ transform: `scale(${previewScale})` }">
            <!-- u6839u636eu5f53u524du6a21u677fIDu76f4u63a5u6e32u67d3u5bf9u5e94u6a21u677f -->
            <component :is="getCurrentTemplate" :resume="{ modules: resumeData.modules }"
                       v-if="resumeData && resumeData.modules" />
          </div>
        </div>
      </div>
    </div>

    <div v-if="!loading && !resumeData" class="error-container">
      <el-empty description="无法加载简历数据">
        <el-button type="primary" @click="router.push('/templates')">返回模板页</el-button>
      </el-empty>
    </div>

    <!-- 项目内容条款选择对话框 -->
    <el-dialog v-model="projectDialogVisible" title="项目内容生成" width="600px"
               :before-close="() => projectDialogVisible = false">
      <h3>选择项目内容条款</h3>
      <p>请选择适合您项目经验的内容条款：</p>

      <el-checkbox-group v-model="checkedContentItems" class="project-content-items">
        <div v-if="dialogContentItems && dialogContentItems.length > 0">
          <div v-for="item in dialogContentItems" :key="item.value" class="project-content-item">
            <el-checkbox :label="item.value">{{ item.description }}</el-checkbox>
          </div>
        </div>
      </el-checkbox-group>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="projectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmProjectContent">生成描述</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- AI润色对话框 -->
    <el-dialog v-model="aiPolishDialogVisible" title="AI润色工作描述" width="600px"
               :before-close="() => aiPolishDialogVisible = false"
               :close-on-click-modal="false">
      <div v-if="isPolishing" class="loading-container">
        <el-progress type="circle" :percentage="polishProgress"></el-progress>
        <div class="polish-tip">正在润色中，请稍候...</div>
      </div>
      <div v-else class="ai-polish-container">
        <div class="polish-result">
          <div class="original-content">
            <h4>原始内容</h4>
            <div class="content-box">{{ currentPolishingText }}</div>
          </div>
          <div class="polished-content">
            <h4>润色后内容</h4>
            <div class="content-box">{{ polishingResult }}</div>
          </div>
        </div>
        <div class="polish-options">
          <h4>请选择要使用的版本</h4>
          <el-radio-group v-model="selectedVersion">
            <el-radio :label="0">使用原始内容</el-radio>
            <el-radio :label="1">使用润色后内容</el-radio>
          </el-radio-group>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="aiPolishDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyPolishedText" :disabled="isPolishing">
            应用所选内容
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { useResumeStore } from '@/stores/resume'
import { useUserStore } from '@/stores/user'
import { resumeApi } from '@/api'
import { polishWork, polishProjectExperience } from '@/api/resume'
import { getTemplateComponent, templateConfig, TemplateSelector } from '@/components/templates'
import Template1 from '@/components/templates/Template1.vue'
import Template2 from '@/components/templates/Template2.vue'
import Template3 from '@/components/templates/Template3.vue'
import {
  ArrowDown,
  User,
  Reading,
  OfficeBuilding,
  Star,
  Medal,
  School,
  Brush,
  ChatRound,
  Document,
  Check,
  Edit,
  Picture,
  Setting,
  ChatDotRound,
  ChatSquare,
  Plus,
  Delete,
  EditPen,
  Folder, MagicStick
} from '@element-plus/icons-vue'
import SelfEvaluationForm from '@/components/self-evaluation-form.vue'
import InterestForm from '@/components/interest-form.vue'
import CampusForm from '@/components/campus-form.vue'
import CertificateForm from '@/components/certificate-form.vue'
import SkillForm from '@/components/skill-form.vue'
import ProjectForm from '@/components/project-form.vue'
import WorkDescriptionForm from '@/components/work-description-form.vue'
import PracticeForm from "@/components/practice-form.vue";
import CertificatesForm from '@/components/certificates-form.vue'

import { userApi } from '@/api/index'

// 个人评价组件引用
const evaluationFormRef = ref(null)
// 兴趣爱好组件引用
const interestFormRef = ref(null)
// 校园经历组件引用
const campusFormRef = ref(null)
// 证书奖项组件引用
const certificateFormRef = ref(null)
// 技能特长组件引用（多个）
const skillFormRefs = ref([])
// 项目经验组件引用（多个）
const projectFormRefs = ref([])
// 工作经验组件引用（多个）
const workFormRefs = ref([])
// 练手项目组件引用（多个）
const practiceFormRefs = ref([])

const route = useRoute()
const router = useRouter()
const resumeStore = useResumeStore()
const userStore = useUserStore()

// 导航状态
const currentNav = ref('content')

// 简历数据
const resumeId = ref(route.params.id)
const currentTemplateId = ref(1)

const resumeData = ref(resumeStore.currentResume || {
  templateId: '',
  modules: {
    basic: {
      name: '',
      gender: '',
      birthDate: '',
      phone: '',
      email: '',
      age: '',
      hometown: '',
      jobObjective: '',
      avatar: ''
    },
    education: [],
    work: [],
    projects: [],
    practices: [],
    skills: [],
    certificates: [],
    campus: '',
    interests: '',
    evaluation: ''
  }
})

const resumeName = ref('未命名简历')
const loading = ref(false)

const previewScale = ref(0.7) // 预览缩放比例

// 模块设置
const resumeModules = ref(resumeStore.resumeModules)
const activeModule = ref('basic')
const visibleModules = ref([])

// 模板选项
const templates = ref(templateConfig)

// 添加布局模式
const layoutMode = ref('line')

// 面板宽度
const leftPanelWidth = ref(35) // 初始宽度百分比

// 拖动调整宽度相关变量
const isResizing = ref(false)
const startX = ref(0)
const startWidth = ref(0)

// 模块可见性控制
const moduleVisibility = reactive({
  basic: true,
  education: true,
  work: true,
  internship: true,
  projects: true,
  practices: true,
  skills: true,
  certificates: true,
  campus: true,
  interests: true,
  evaluation: true
})

// 开始调整宽度
const startResize = (e) => {
  isResizing.value = true
  startX.value = e.clientX
  startWidth.value = leftPanelWidth.value

  // 添加全局事件监听
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)

  // 添加禁止选择的样式
  document.body.style.userSelect = 'none'
}

// 调整宽度进行中
const handleResize = (e) => {
  if (!isResizing.value) return

  // 计算鼠标移动的距离
  const deltaX = e.clientX - startX.value

  // 计算面板宽度的变化百分比
  const containerWidth = document.querySelector('.editor-content').offsetWidth
  const deltaPercent = (deltaX / containerWidth) * 100

  // 更新左侧面板宽度，限制在20%-80%之间
  leftPanelWidth.value = Math.min(80, Math.max(20, startWidth.value + deltaPercent))
}

// 停止调整宽度
const stopResize = () => {
  isResizing.value = false

  // 移除全局事件监听
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)

  // 移除禁止选择的样式
  document.body.style.userSelect = ''
}

// 计算简历完成度
const completionPercentage = computed(() => {
  if (!resumeData.value) return 0;

  let completedModules = 0;
  let totalModules = 0;

  // 为每个模块计算完成状态
  resumeModules.value.forEach(module => {
    totalModules++;
    if (isModuleComplete(module.id)) {
      completedModules++;
    }
  });

  return Math.round((completedModules / totalModules) * 100);
});

// 检查模块是否已完成
const isModuleComplete = (moduleId) => {
  if (!resumeData.value || !resumeData.value.modules) return false;

  const moduleData = resumeData.value.modules[moduleId];

  if (!moduleData) return false;

  switch (moduleId) {
    case 'basic':
      return moduleData.name && moduleData.phone && moduleData.email;
    case 'education':
      return moduleData && moduleData.length > 0;
    case 'work':
      return moduleData && moduleData.length > 0;
    case 'skills':
      return moduleData && moduleData.length > 0;
    case 'certificates':
      return moduleData && moduleData.length > 0;
    case 'campus':
      return moduleData && moduleData.length > 0;
    case 'interests':
      return moduleData && moduleData.length > 0;
    default:
      return false;
  }
};

// 初始化可见模块
const initVisibleModules = () => {
  visibleModules.value = resumeModules.value.map(module => ({
    id: module.id,
    name: module.name,
    visible: true
  }))
}

// 检查模块是否可见
const isModuleVisible = (moduleId) => {
  // 如果resumeData还没有初始化，返回false
  if (!resumeData.value) return false;

  // 基本信息模块总是显示
  if (moduleId === 'basic') return true;

  const module = visibleModules.value.find(m => m.id === moduleId)
  return module ? module.visible : true
}

// 获取模块对应的编辑组件
const getModuleComponent = (moduleId) => {
  // 根据模块ID返回对应的组件
  switch (moduleId) {
    case 'basic':
      return 'basic-form'
    case 'job':
      return 'job-form'
    case 'education':
      return 'education-form'
    case 'work':
      return 'work-form'
    case 'internship':
      return 'internship-form'
    case 'projects':
      return 'projects-form'
    case 'skills':
      return 'skills-form'
    case 'certificates':
      return 'certificates-form'
    case 'campus':
      return 'campus-form'
    case 'interests':
      return 'interests-form'
    default:
      return null
  }
}

// 获取模块数据
const getModuleData = (moduleId) => {
  if (!resumeData.value || !resumeData.value.modules) return {};
  return resumeData.value.modules[moduleId] || {}
}

// 更新模块数据
const updateModuleData = (moduleId, data) => {
  if (!resumeData.value || !resumeData.value.modules) return;
  resumeData.value.modules[moduleId] = data
}

// 处理简历名称变更
const handleResumeNameChange = () => {
  if (!resumeData.value) return;
  resumeData.value.name = resumeName.value
}

// 格式化项目日期
const formatProjectDates = () => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.projects)) return;

  resumeData.value.modules.projects.forEach(project => {
    if (project.time && Array.isArray(project.time) && project.time.length === 2) {
      // 确保处理的是日期对象
      const startDate = new Date(project.time[0]);
      const endDate = new Date(project.time[1]);

      if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
        project.startDate = formatDate(startDate);
        project.endDate = formatDate(endDate);
      }
    } else if (!project.startDate) {
      project.startDate = '2022年1月1日';
      project.endDate = '2022年6月30日';
    }
  });
}

const formatPracticeDates = () => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.practices)) return;

  resumeData.value.modules.practices.forEach(project => {
    if (project.time && Array.isArray(project.time) && project.time.length === 2) {
      // 确保处理的是日期对象
      const startDate = new Date(project.time[0]);
      const endDate = new Date(project.time[1]);

      if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
        project.startDate = formatDate(startDate);
        project.endDate = formatDate(endDate);
      }
    } else if (!project.startDate) {
      project.startDate = '2022年1月1日';
      project.endDate = '2022年6月30日';
    }
  });
}

// 格式化教育经历日期
const formatEducationDates = () => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.education)) return;

  resumeData.value.modules.education.forEach(education => {
    if (education.time && Array.isArray(education.time) && education.time.length === 2) {
      // 确保处理的是日期对象
      const startDate = new Date(education.time[0]);
      const endDate = new Date(education.time[1]);

      if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
        education.startDate = formatDate(startDate);
        education.endDate = formatDate(endDate);
      }
    } else if (!education.startDate) {
      education.startDate = '2018年9月1日';
      education.endDate = '2022年6月30日';
    }
  });
}

// 格式化工作经验日期
const formatWorkDates = () => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.work)) return;

  resumeData.value.modules.work.forEach(work => {
    if (work.time && Array.isArray(work.time) && work.time.length === 2) {
      // 确保处理的是日期对象
      const startDate = new Date(work.time[0]);
      const endDate = new Date(work.time[1]);

      if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
        work.startDate = formatDate(startDate);
        work.endDate = formatDate(endDate);
      }
    } else if (!work.startDate) {
      work.startDate = '2022年7月1日';
      work.endDate = '2023年6月30日';
    }
  });
}

// 格式化实习经验日期
const formatInternshipDates = () => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.internship)) return;

  resumeData.value.modules.internship.forEach(internship => {
    if (internship.time && Array.isArray(internship.time) && internship.time.length === 2) {
      // 确保处理的是日期对象
      const startDate = new Date(internship.time[0]);
      const endDate = new Date(internship.time[1]);

      if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
        internship.startDate = formatDate(startDate);
        internship.endDate = formatDate(endDate);
      }
    } else if (!internship.startDate) {
      internship.startDate = '2021年7月1日';
      internship.endDate = '2021年9月30日';
    }
  });
}

// 格式化所有日期
const formatAllDates = () => {
  if (!resumeData.value) return;

  try {
    // 确保各个模块存在后再进行格式化
    if (Array.isArray(resumeData.value.modules.education)) {
      formatEducationDates();
    }

    if (Array.isArray(resumeData.value.modules.work)) {
      formatWorkDates();
    }

    if (Array.isArray(resumeData.value.modules.internship)) {
      formatInternshipDates();
    }

    if (Array.isArray(resumeData.value.modules.projects)) {
      formatProjectDates();
    }

    if (Array.isArray(resumeData.value.modules.practices)) {
      formatPracticeDates();
    }
  } catch (error) {
    console.error('格式化日期时出错:', error);
  }
}

// 格式化日期为中文年月日格式
const formatDate = (date) => {
  if (!date) return '';

  let d;

  // 处理不同类型的日期输入
  if (typeof date === 'string') {
    // 处理ISO格式的日期字符串
    d = new Date(date);
    if (isNaN(d.getTime())) {
      // 如果无法解析，直接返回原字符串
      return date;
    }
  } else if (date instanceof Date) {
    d = date;
  } else {
    // 无法处理的类型，返回空字符串
    return '';
  }

  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();

  return `${year}年${month}月${day}日`;
}

// 处理模板变更
const handleTemplateChange = (templateId) => {
  currentTemplateId.value = Number(templateId)

  if (resumeData.value) {
    resumeData.value.templateId = Number(templateId)

    // 强制更新简历数据，使模板组件重新渲染
    updateResumeDataObject()

    ElMessage.success('已更换模板，简历风格已更新')
  }
}



const saveResumeData = ref({})

// 保存简历
const handleSaveResume = async () => {
  if (!resumeData.value.category || resumeData.value.category < 1) {
    ElMessage.warning('请先选择简历类型')
    return
  }

  // 在保存前格式化所有日期
  formatAllDates();

  try {
    // 确保自我评价数据正确
    if (resumeData.value.modules.selfEvaluation) {
      // 确保ID字段为数字类型或null
      if (typeof resumeData.value.modules.selfEvaluation.id === 'string' &&
          resumeData.value.modules.selfEvaluation.id.trim() !== '') {
        resumeData.value.modules.selfEvaluation.id = Number(resumeData.value.modules.selfEvaluation.id);
      } else if (resumeData.value.modules.selfEvaluation.id === '') {
        resumeData.value.modules.selfEvaluation.id = null;
      }

      if (typeof resumeData.value.modules.selfEvaluation.resumeId === 'string' &&
          resumeData.value.modules.selfEvaluation.resumeId.trim() !== '') {
        resumeData.value.modules.selfEvaluation.resumeId = Number(resumeData.value.modules.selfEvaluation.resumeId);
      } else if (!resumeData.value.modules.selfEvaluation.resumeId && resumeData.value.resumeId) {
        resumeData.value.modules.selfEvaluation.resumeId = Number(resumeData.value.resumeId);
      } else if (resumeData.value.modules.selfEvaluation.resumeId === '') {
        resumeData.value.modules.selfEvaluation.resumeId = null;
      }

      console.log('保存前自我评价数据(修正后):', resumeData.value.modules.selfEvaluation);
    } else {
      // 如果没有自我评价数据，初始化一个空对象
      resumeData.value.modules.selfEvaluation = {
        id: null,
        resumeId: resumeData.value.resumeId ? Number(resumeData.value.resumeId) : null,
        description: ''
      };
    }

    // 转换并保存数据
    const dataToSave = convertResumeData(resumeData.value);
    console.log('最终提交的数据:', dataToSave);
    console.log('最终提交的自我评价数据:', dataToSave.evaluate);

    const response = await resumeApi.saveResume(dataToSave);

    if (response.code === 0) {
      // 保存成功
      ElMessage.success('保存成功');

      // 如果是新建简历，保存后更新resumeId
      if (!resumeData.value.resumeId && response.data) {
        resumeData.value.resumeId = response.data;
        // 同时更新自我评价的resumeId
        if (resumeData.value.modules.selfEvaluation) {
          resumeData.value.modules.selfEvaluation.resumeId = Number(response.data);
        }
      }
    } else {
      // 保存失败，显示后端返回的具体错误信息
      ElMessage.error(response.message || '保存失败');
    }
  } catch (error) {
    console.error('保存简历失败:', error);
    // 显示具体的错误信息
    if (error.response && error.response.data) {
      ElMessage.error(error.response.data.message || error.message || '保存简历失败');
    } else {
      ElMessage.error(error.message || '保存简历失败，请检查网络连接');
    }
  }
};





// 预览简历
const handlePreview = () => {
  if (!resumeData.value) {
    ElMessage.warning('没有可以预览的简历数据');
    return;
  }

  // 在预览前格式化所有日期
  formatAllDates();

  resumeStore.setCurrentResume({ ...resumeData.value })
  router.push(`/preview/${resumeId.value || 'temp'}`)
}

// 处理缩放
const handleZoom = () => {
  // 在0.5和1之间循环
  previewScale.value = previewScale.value >= 0.9 ? 0.5 : previewScale.value + 0.2;
}

// 切换模块的可见性
const toggleModuleVisibility = (moduleId) => {
  if (!resumeData.value) return;

  // 基本信息模块不能隐藏
  if (moduleId === 'basic') {
    moduleVisibility.basic = true;
    return;
  }

  // 更新模块的可见性
  const isVisible = moduleVisibility[moduleId];

  // 根据可见性，在简历预览中显示或隐藏模块
  if (moduleId === 'internship') {
    scrollToElement('module-internship');

    // 收起或显示编辑框
    if (!isVisible) {
      // 如果需要隐藏模块，先备份数据再清空
      resumeData.value.modules[`_hidden${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)}`] =
          JSON.parse(JSON.stringify(resumeData.value.modules[moduleId]));

      // 清空显示内容
      if (Array.isArray(resumeData.value.modules[moduleId])) {
        resumeData.value.modules[moduleId] = [];
      } else if (typeof resumeData.value.modules[moduleId] === 'string') {
        resumeData.value.modules[moduleId] = '';
      } else {
        resumeData.value.modules[moduleId] = null;
      }
    } else {
      // 恢复之前备份的数据
      const backupKey = `_hidden${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)}`;
      if (resumeData.value.modules[backupKey]) {
        resumeData.value.modules[moduleId] = resumeData.value.modules[backupKey];
        resumeData.value.modules[backupKey] = null;
      }
    }
  } else if (moduleId === 'projects') {
    scrollToElement('module-project');

    // 收起或显示编辑框
    if (!isVisible) {
      // 如果需要隐藏模块，先备份数据再清空
      resumeData.value.modules[`_hidden${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)}`] =
          JSON.parse(JSON.stringify(resumeData.value.modules[moduleId]));

      // 清空显示内容
      if (Array.isArray(resumeData.value.modules[moduleId])) {
        resumeData.value.modules[moduleId] = [];
      } else if (typeof resumeData.value.modules[moduleId] === 'string') {
        resumeData.value.modules[moduleId] = '';
      } else {
        resumeData.value.modules[moduleId] = null;
      }
    } else {
      // 恢复之前备份的数据
      const backupKey = `_hidden${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)}`;
      if (resumeData.value.modules[backupKey]) {
        resumeData.value.modules[moduleId] = resumeData.value.modules[backupKey];
        resumeData.value.modules[backupKey] = null;
      }
    }
  } else if (moduleId === 'evaluation') {
    // 个人评价模块特殊处理
    if (!isVisible) {
      // 如果需要隐藏模块，先备份数据再清空
      resumeData.value.modules._hiddenEvaluation = { ...resumeData.value.modules.selfEvaluation };
      console.log('隐藏自我评价，备份数据:', resumeData.value.modules._hiddenEvaluation);

      // 清空显示内容，但保留ID信息
      resumeData.value.modules.selfEvaluation = {
        id: resumeData.value.modules.selfEvaluation?.id || null,
        resumeId: resumeData.value.modules.selfEvaluation?.resumeId ||
            (resumeData.value.resumeId ? Number(resumeData.value.resumeId) : null),
        description: ''
      };

      // 清空相关输入框状态
      clearModuleInputs('evaluation');
    } else {
      // 恢复之前备份的数据
      if (resumeData.value.modules._hiddenEvaluation) {
        resumeData.value.modules.selfEvaluation = { ...resumeData.value.modules._hiddenEvaluation };
        resumeData.value.modules._hiddenEvaluation = null;
        console.log('恢复自我评价数据:', resumeData.value.modules.selfEvaluation);
      } else if (!resumeData.value.modules.selfEvaluation) {
        // 如果没有自我评价数据，初始化一个空对象
        resumeData.value.modules.selfEvaluation = {
          id: null,
          resumeId: resumeData.value.resumeId ? Number(resumeData.value.resumeId) : null,
          description: ''
        };
        console.log('初始化自我评价数据:', resumeData.value.modules.selfEvaluation);
      }

      // 显示编辑器
      nextTick(() => {
        if (evaluationFormRef.value) {
          evaluationFormRef.value.showEditor = true;
          console.log('切换显示自我评价编辑器，当前数据:', resumeData.value.modules.selfEvaluation);
        }
      });
    }
  } else if (moduleId === 'interests') {
    // 兴趣爱好模块特殊处理，与个人评价保持一致
    if (!isVisible) {
      // 如果需要隐藏模块，先备份数据再清空
      resumeData.value.modules._hiddenInterests = {...resumeData.value.modules.interests};
      // 清空内容但保留ID字段
      resumeData.value.modules.interests = {
        id: resumeData.value.modules.interests?.id || null,
        resumeId: resumeData.value.modules.interests?.resumeId || null,
        description: ''
      };

      // 清空相关输入框状态
      clearModuleInputs('interests');
    } else {
      // 恢复之前备份的数据
      if (resumeData.value.modules._hiddenInterests) {
        resumeData.value.modules.interests = resumeData.value.modules._hiddenInterests;
        resumeData.value.modules._hiddenInterests = null;
      }

      // 恢复相关输入框状态
      restoreModuleInputs('interests');

      // 显示编辑器
      nextTick(() => {
        if (interestFormRef.value) {
          interestFormRef.value.showEditor = true;
        }
      });
    }
  } else if (moduleId === 'campus') {
    // 校园经历模块特殊处理，与兴趣爱好保持一致
    if (!isVisible) {
      // 如果需要隐藏模块，先备份数据再清空
      resumeData.value.modules._hiddenCampus = {...resumeData.value.modules.campus};
      // 清空内容但保留ID字段
      resumeData.value.modules.campus = {
        id: resumeData.value.modules.campus?.id || null,
        resumeId: resumeData.value.modules.campus?.resumeId || null,
        description: ''
      };

      // 清空相关输入框状态
      clearModuleInputs('campus');
    } else {
      // 恢复之前备份的数据
      if (resumeData.value.modules._hiddenCampus) {
        resumeData.value.modules.campus = resumeData.value.modules._hiddenCampus;
        resumeData.value.modules._hiddenCampus = null;
      }

      // 恢复相关输入框状态
      restoreModuleInputs('campus');

      // 显示编辑器
      nextTick(() => {
        if (campusFormRef.value) {
          campusFormRef.value.showEditor = true;
        }
      });
    }
  } else if (moduleId === 'certificates') {
    // 证书奖项模块特殊处理，与校园经历保持一致
    if (!isVisible) {
      // 如果需要隐藏模块，先备份数据再清空
      resumeData.value.modules._hiddenCertificates = {...resumeData.value.modules.certificates};
      // 清空内容但保留ID字段
      resumeData.value.modules.certificates = {
        id: resumeData.value.modules.certificates?.id || null,
        resumeId: resumeData.value.modules.certificates?.resumeId || null,
        certificateName: ''
      };
    } else {
      // 恢复之前备份的数据
      if (resumeData.value.modules._hiddenCertificates) {
        resumeData.value.modules.certificates = resumeData.value.modules._hiddenCertificates;
        resumeData.value.modules._hiddenCertificates = null;
      }
    }
  } else {
    // 其他模块的通用处理
    if (!isVisible) {
      // 如果需要隐藏模块，先备份数据再清空
      resumeData.value.modules[`_hidden${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)}`] =
          JSON.parse(JSON.stringify(resumeData.value.modules[moduleId]));

      // 清空显示内容
      if (Array.isArray(resumeData.value.modules[moduleId])) {
        resumeData.value.modules[moduleId] = [];
      } else if (typeof resumeData.value.modules[moduleId] === 'string') {
        resumeData.value.modules[moduleId] = '';
      } else if (resumeData.value.modules[moduleId] && typeof resumeData.value.modules[moduleId] === 'object') {
        // 对于对象类型，保留ID字段但清空其他内容
        const originalData = resumeData.value.modules[moduleId];
        resumeData.value.modules[moduleId] = {
          id: originalData.id || null,
          resumeId: originalData.resumeId || null,
          description: ''
        };
      } else {
        resumeData.value.modules[moduleId] = null;
      }

      // 清空相关的输入框状态
      clearModuleInputs(moduleId);
    } else {
      // 恢复之前备份的数据
      const backupKey = `_hidden${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)}`;
      if (resumeData.value.modules[backupKey]) {
        resumeData.value.modules[moduleId] = resumeData.value.modules[backupKey];
        resumeData.value.modules[backupKey] = null;
      }

      // 恢复相关的输入框状态
      restoreModuleInputs(moduleId);
    }
  }

  // 通过创建一个新对象，强制Vue刷新视图
  resumeData.value = { ...resumeData.value };

  ElMessage.success(isVisible ? `已显示${getModuleName(moduleId)}模块` : `已隐藏${getModuleName(moduleId)}模块`);
}

// 清空模块相关的输入框状态
const clearModuleInputs = (moduleId) => {
  switch (moduleId) {
    case 'education':
      // 清空教育经历相关的输入框
      // 教育经历是数组，已在通用处理中清空
      break;
    case 'work':
      // 清空工作经验相关的输入框
      // 工作经验是数组，已在通用处理中清空
      break;
    case 'projects':
      // 清空项目经验相关的输入框
      // 项目经验是数组，已在通用处理中清空
      break;
    case 'practices':
      // 清空练手项目相关的输入框
      // 练手项目是数组，已在通用处理中清空
      break;
    case 'skills':
      // 清空技能特长相关的输入框
      // 技能特长是数组，已在通用处理中清空
      break;
    case 'certificates':
      // 清空证书奖项相关的输入框
      newCertificate.value = '';
      showCertificateForm.value = false;
      certificateItems.value = [];
      if (certificateFormRef.value) {
        certificateFormRef.value.showEditor = false;
      }
      break;
    case 'campus':
      // 清空校园经历相关的输入框
      newCampus.value = '';
      showCampusForm.value = false;
      campusItems.value = [];
      if (campusFormRef.value) {
        campusFormRef.value.showEditor = false;
      }
      break;
    case 'interests':
      // 清空兴趣爱好相关的输入框
      newInterest.value = '';
      showInterestForm.value = false;
      interestItems.value = [];
      if (interestFormRef.value) {
        interestFormRef.value.showEditor = false;
      }
      break;
    case 'evaluation':
      // 清空自我评价相关的输入框
      if (evaluationFormRef.value) {
        evaluationFormRef.value.showEditor = false;
      }
      break;
    default:
      break;
  }
};

// 恢复模块相关的输入框状态
const restoreModuleInputs = (moduleId) => {
  switch (moduleId) {
    case 'education':
      // 教育经历恢复后无需特殊处理
      break;
    case 'work':
      // 工作经验恢复后无需特殊处理
      break;
    case 'projects':
      // 项目经验恢复后无需特殊处理
      break;
    case 'practices':
      // 练手项目恢复后无需特殊处理
      break;
    case 'skills':
      // 技能特长恢复后无需特殊处理
      break;
    case 'certificates':
      // 恢复证书奖项的显示状态
      updateCertificatesFromResumeData();
      break;
    case 'campus':
      // 恢复校园经历的显示状态
      updateCampusFromResumeData();
      break;
    case 'interests':
      // 恢复兴趣爱好的显示状态
      updateInterestsFromResumeData();
      break;
    case 'evaluation':
      // 自我评价恢复后无需特殊处理
      break;
    default:
      break;
  }
};

// 从简历数据更新证书奖项状态
const updateCertificatesFromResumeData = () => {
  if (resumeData.value && resumeData.value.modules && resumeData.value.modules.certificates) {
    const certificates = resumeData.value.modules.certificates;
    if (typeof certificates === 'string' && certificates.trim()) {
      // 如果是字符串格式，按换行符分割
      certificateItems.value = certificates.split('\n').filter(item => item.trim());
    } else if (Array.isArray(certificates)) {
      // 如果是数组格式
      certificateItems.value = [...certificates];
    }
  }
};

// 从简历数据更新校园经历状态
const updateCampusFromResumeData = () => {
  if (resumeData.value && resumeData.value.modules && resumeData.value.modules.campus) {
    const campus = resumeData.value.modules.campus;
    if (campus.description && campus.description.trim()) {
      campusItems.value = campus.description.split('\n').filter(item => item.trim());
    }
  }
};

// 从简历数据更新兴趣爱好状态
const updateInterestsFromResumeData = () => {
  if (resumeData.value && resumeData.value.modules && resumeData.value.modules.interests) {
    const interests = resumeData.value.modules.interests;
    if (interests.description && interests.description.trim()) {
      interestItems.value = interests.description.split('\n').filter(item => item.trim());
    }
  }
};

// 获取模块名称
const getModuleName = (moduleId) => {
  const moduleMap = {
    basic: '基本信息',
    education: '教育经历',
    work: '工作经验',
    internship: '实习经历',
    projects: '项目经验',
    practices: '练手项目',
    skills: '技能特长',
    certificates: '证书奖项',
    campus: '校园经历',
    interests: '兴趣爱好',
    evaluation: '个人评价'
  };

  return moduleMap[moduleId] || moduleId;
}

// 格式化文本内容，将换行符转换为<br>，保留段落格式
const formatContent = (content) => {
  if (!content) return '';

  // 如果content是对象类型，返回空字符串
  if (typeof content === 'object') return '';

  // 替换换行符为HTML换行，并保留段落格式
  return content
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^(.+)$/, '<p>$1</p>');
}

// 监视resumeData深层变化，确保实时更新简历预览
// 使用回调方式避免无限循环
const updatePreview = () => {
  // 格式化各类日期
  if (resumeData.value && resumeData.value.modules) {
    formatAllDates();
  }
};

// 监听基本信息的变化
watch(() => resumeData.value?.modules?.basic, () => {
  updatePreview();
}, { deep: true });

// 监听其他模块的变化
watch(() => resumeData.value?.modules?.education, () => {
  updatePreview();
}, { deep: true });

watch(() => resumeData.value?.modules?.work, () => {
  updatePreview();
}, { deep: true });

watch(() => resumeData.value?.modules?.projects, () => {
  updatePreview();
}, { deep: true });

watch(() => resumeData.value?.modules?.internship, () => {
  updatePreview();
}, { deep: true });

watch(() => resumeData.value?.modules?.practices, () => {
  updatePreview();
}, { deep: true });


// 监听模块可见性变化
watch(visibleModules, () => {
  updatePreview();
}, { deep: true });

// 监听校园经历模块可见性变化
watch(() => moduleVisibility.campus, (isVisible) => {
  if (isVisible && resumeData.value && resumeData.value.modules) {
    nextTick(() => {
      if (campusFormRef.value) {
        campusFormRef.value.showEditor = true;
      }
    });
  }
});

// 监听兴趣爱好模块可见性变化
watch(() => moduleVisibility.interests, (isVisible) => {
  if (isVisible && resumeData.value && resumeData.value.modules) {
    nextTick(() => {
      if (interestFormRef.value) {
        interestFormRef.value.showEditor = true;
      }
    });
  }
});

// 处理模块选项卡点击
const handleModuleTabClick = (moduleId) => {
  // 如果点击当前激活的模块，不做任何操作
  if (activeModule.value === moduleId) return;

  // 否则切换到对应模块
  activeModule.value = moduleId;
}

// 添加新的教育经历
const addEducation = () => {
  if (!resumeData.value || !resumeData.value.modules) return;

  if (!Array.isArray(resumeData.value.modules.education)) {
    resumeData.value.modules.education = [];
  }

  // 设置默认的教育经历时间：4年本科
  const endDate = new Date();
  const startDate = new Date();
  // 设置为4年前，通常是本科入学时间
  startDate.setFullYear(endDate.getFullYear() - 4);
  // 将月份设置为9月（开学）和6月（毕业）
  startDate.setMonth(8); // 9月
  endDate.setMonth(5);   // 6月

  resumeData.value.modules.education.push({
    school: '',
    major: '',
    degree: '',
    time: [startDate, endDate],
    startDate: formatDate(startDate),
    endDate: formatDate(endDate),
    courses: ''
  });

  updateResumeDataObject();
}

// 删除教育经历
const removeEducation = (index) => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.education)) return;

  resumeData.value.modules.education.splice(index, 1);
  updateResumeDataObject();
}

// 添加新的工作经验
const addWork = () => {
  if (!resumeData.value || !resumeData.value.modules) return;

  if (!Array.isArray(resumeData.value.modules.work)) {
    resumeData.value.modules.work = [];
  }
  function formatToYYYYMM(date) {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
  }

  // 设置为YYYY-MM格式的日期
  const endDate = formatToYYYYMM(new Date(new Date().getFullYear(), 8, 30)); // 9月30日
  const startDate = formatToYYYYMM(new Date(new Date().getFullYear(), 6, 1));
  // 设置默认工作时间：一年经验


  resumeData.value.modules.work.push({
    company: '',
    position: '',
    time: [startDate, endDate],
    startDate: formatDate(startDate),
    endDate: formatDate(endDate),
    description: ''
  });

  updateResumeDataObject();
}

// 删除工作经验
const removeWork = (index) => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.work)) return;

  resumeData.value.modules.work.splice(index, 1);
  updateResumeDataObject();
}

// 添加新的实习经历
const addInternship = () => {
  if (!resumeData.value || !resumeData.value.modules) return;

  if (!Array.isArray(resumeData.value.modules.internship)) {
    resumeData.value.modules.internship = [];
  }

  // 设置默认实习时间：三个月
  const endDate = new Date(new Date().getFullYear(), 8, 30); // 9月30日结束
  const startDate = new Date(new Date().getFullYear(), 6, 1); // 7月1日开始

  resumeData.value.modules.internship.push({
    company: '',
    position: '',
    time: [startDate, endDate],
    startDate: formatDate(startDate),
    endDate: formatDate(endDate),
    description: ''
  });

  updateResumeDataObject();
}

// 删除实习经历
const removeInternship = (index) => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.internship)) return;

  resumeData.value.modules.internship.splice(index, 1);
  updateResumeDataObject();
}

// 添加新的项目经历
const addProject = () => {
  if (!resumeData.value || !resumeData.value.modules) return;

  if (!Array.isArray(resumeData.value.modules.projects)) {
    resumeData.value.modules.projects = [];
  }
  function formatToYYYYMM(date) {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
  }

  // 设置为YYYY-MM格式的日期
  const endDate = formatToYYYYMM(new Date(new Date().getFullYear(), 8, 30)); // 9月30日
  const startDate = formatToYYYYMM(new Date(new Date().getFullYear(), 6, 1));

  resumeData.value.modules.projects.push({
    category: '',
    name: '',
    role: '',
    time: [startDate, endDate],
    startDate: formatDate(startDate),
    endDate: formatDate(endDate),
    description: '',
    isEditingDescription: false
    // 注意：不设置projectId，因为这是手动添加的项目
  });

  updateResumeDataObject();
}

function generateProjectTime(monthsAgo = 3) {
  // 获取当前日期
  const endDate = new Date();

  // 计算开始日期（当前日期减去指定月数）
  const startDate = new Date(endDate);
  startDate.setMonth(endDate.getMonth() - monthsAgo);

  // 格式化日期为 YYYY-MM 格式
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  };

  return [formatDate(startDate), formatDate(endDate)];
}
// 删除项目经历
const removeProject = (index) => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.projects)) return;

  resumeData.value.modules.projects.splice(index, 1);
  updateResumeDataObject();
}

// 处理项目职位类别变更
const handleProjectCategoryChange = (category, index) => {
  if (!resumeData.value || !resumeData.value.modules.projects[index]) return;

  // 清空项目名称，因为类别已经改变
  resumeData.value.modules.projects[index].name = '';
  resumeData.value.modules.projects[index].description = '';
  fetchProjectById(category)
}

// 项目列表类型选项
const projectOptions = ref([])

const fetchProjectById = async (id) => {
  try {
    const res = await resumeApi.getProjectListByCategory(id)
    projectOptions.value = res.data.map(item => ({
      label: item.name,
      value: item.proId,
      name: item.name,
      description: item.content
    }))
  } catch (error) {
    console.error('获取项目列表失败：', error)
    ElMessage.error('获取项目列表失败')
  }
}

// 处理项目选择变更
const handleProjectSelect = async (projectName, index) => {
  if (!resumeData.value || !resumeData.value.modules.projects[index]) return;

  // 从 projectOptions.value 中找到对应的项目
  const selectedProject = projectOptions.value.find(item => item.value === projectName);
  if (selectedProject) {
    // 更新项目名称
    resumeData.value.modules.projects[index].name = selectedProject.name;
    // 保存项目ID，用于上传时的判断
    resumeData.value.modules.projects[index].projectId = selectedProject.value;
  } else {
    // 如果是手动输入的项目名称，则清除项目ID
    delete resumeData.value.modules.projects[index].projectId;
  }

  await fetchProjectContent(projectName)


  const project = resumeData.value.modules.projects[index];

  // 如果是自定义项目名称，不自动填充描述
  // if (!getProjectsByCategory(project.category).some(item => item.value === projectName)) {
  //   // 这是一个自定义项目名称，不做特殊处理
  //   return;
  // }
  // 设置默认时间范围，如果尚未设置
  // if (!project.time) {
  //   const now = new Date();
  //   const sixMonthsAgo = new Date();
  //   sixMonthsAgo.setMonth(now.getMonth() - 6);
  //
  //   project.time = [sixMonthsAgo, now];
  //   project.startDate = formatDate(sixMonthsAgo);
  //   project.endDate = formatDate(now);
  // }

  // 在这里可以根据项目类别和名称预设一些描述内容
  const category = project.category;
  if (!category) return;

  // 让用户选择项目内容条款
  selectProjectContentItems(index);
}

// 选择项目内容条款
const selectProjectContentItems = (index) => {
  if (!resumeData.value || !resumeData.value.modules.projects[index]) return;
  const project = resumeData.value.modules.projects[index];
  const category = project.category;
  const projectName = project.name;
  if (!category || !projectName) {
    ElMessage.warning('请先选择职位类别和项目名称');
    return;
  }
  // 根据职位类别和项目名称获取可选的内容条款
  // getProjectContentItems(category, projectName);
  // 清空已选项目
  dialogContentItems.value = projectContentItems.value;
  projectDialogVisible.value = true;
  checkedContentItems.value = [];
  currentEditingProjectIndex.value = index;
}

// 存储对话框状态
const projectDialogVisible = ref(false);
const dialogContentItems = ref([]);
const checkedContentItems = ref([]);
const currentEditingProjectIndex = ref(null);

// 确认选择并生成项目描述
const confirmProjectContent = () => {
  if (currentEditingProjectIndex.value === null || checkedContentItems.value.length === 0) {
    ElMessage.warning('请至少选择一项内容');
    return;
  }
  // console.log(currentEditingProjectIndex.value)
  // 生成项目描述
  generateProjectDescription(currentEditingProjectIndex.value);

  // 关闭对话框
  projectDialogVisible.value = false;
}

// 获取项目内容条款
const projectContentItems = ref([]);

const fetchProjectContent = async (id) => {
  try {
    const res = await resumeApi.getContextByProjectId(id)
    projectContentItems.value = res.data.map(item => ({
      label: item.conId,
      value: item.conId,
      description: item.text
    }))
  } catch (error) {
    console.error('获取项目描述失败：', error)
    ElMessage.error('获取项目描述失败')
  }
}


// 根据选择的条款生成项目描述
const generateProjectDescription = (index) => {
  if (!resumeData.value || !resumeData.value.modules.projects[index] || checkedContentItems.value.length === 0) return;

  const project = resumeData.value.modules.projects[index];
  const category = project.category;
  const projectName = project.name;

  // 获取所有可选的内容条款
  const allContentItems = dialogContentItems.value;

  // 筛选出用户选择的条款
  const selectedItems = allContentItems.filter(item => checkedContentItems.value.includes(item.value));


  // 生成描述文本
  // let description = `项目描述：${projectName}是一个${getCategoryDesc(category)}项目。\n\n主要职责：\n`;
  let description = ''
  selectedItems.forEach((item, i) => {
    description += `${i + 1}. ${item.description}。\n`;
  });
  // 添加项目成果
  // description += `\n项目成果：成功完成项目开发并上线，${getProjectOutcome(category)}`;

  // 更新项目描述
  resumeData.value.modules.projects[index].description = description;

  // 清空选择
  checkedContentItems.value = [];

  // 使用updateResumeDataObject方法强制刷新简历数据
  updateResumeDataObject();

  ElMessage.success('项目描述已生成，您可以继续编辑完善');
}

// 获取职位类别描述
const getCategoryDesc = (category) => {
  const categoryDescMap = {
    frontend: '前端开发',
    backend: '后端开发',
    fullstack: '全栈开发',
    mobile: '移动应用开发',
    data: '数据分析',
    product: '产品设计',
    design: 'UI/UX设计',
    testing: '测试/QA',
    devops: '运维/DevOps',
    management: '项目管理',
    other: '创新'
  };

  return categoryDescMap[category] || '技术';
}

// 获取项目成果描述
const getProjectOutcome = (category) => {
  const outcomeMap = {
    frontend: '用户界面响应速度提升30%，用户体验显著改善。',
    backend: '系统性能提升50%，服务稳定性大幅提高。',
    fullstack: '产品功能完善，用户满意度提升35%。',
    mobile: 'App下载量增长40%，用户活跃度显著提升。',
    data: '数据分析准确度提高25%，为业务决策提供有力支持。',
    product: '产品市场接受度高，获得用户好评。',
    design: '设计方案获得团队认可，提升产品品牌形象。',
    testing: '发现并修复95%的潜在问题，确保产品质量。',
    devops: '部署时间缩短60%，发布流程更加高效。',
    management: '项目按时交付，团队协作效率提升40%。',
    other: '项目获得良好反响，达成预期目标。'
  };

  return outcomeMap[category] || '项目顺利完成，达到预期目标。';
}

// 删除简历
const handleDeleteResume = async () => {
  try {
    // 获取resumeId，优先使用resumeData中的，如果不存在则从路由参数中获取
    let resumeId = null;
    if (resumeData.value && resumeData.value.resumeId) {
      resumeId = resumeData.value.resumeId;
    } else if (route.params.id && route.params.id !== 'new') {
      resumeId = route.params.id;
    }

    // 检查resumeId是否存在
    if (!resumeId) {
      ElMessage.error('简历ID不存在，无法删除');
      console.error('删除失败：简历ID不存在', { resumeData: resumeData.value, routeParams: route.params });
      return;
    }

    // 显示确认对话框
    await ElMessageBox.confirm('确定要删除当前简历吗？此操作不可恢复。', '删除简历', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      center: true,
      closeOnClickModal: false,
      closeOnPressEscape: false,
    });

    // 显示加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在删除...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.1)',
    });

    try {
      console.log('准备删除简历，ID:', resumeId);
      // 调用API删除简历
      const response = await resumeApi.deleteResume(resumeId);
      console.log('删除简历响应:', response);

      if (response.code === 0) {
        // 删除成功
        ElMessage({
          message: '简历已删除',
          type: 'success',
          duration: 2000,
        });
        // 删除后跳转回用户中心
        router.push('/user-center');
      } else {
        // 业务错误
        throw new Error(response.message || '删除失败，请重试');
      }
    } catch (error) {
      // 处理异常
      console.error('删除简历失败:', error);
      ElMessage({
        message: error.message || '删除失败，服务器异常',
        type: 'error',
        duration: 3000,
      });
    } finally {
      // 隐藏加载状态
      loadingInstance.close();
    }
  } catch (e) {
    // 用户取消删除，无需提示
    console.log('用户取消删除简历');
  }
}
function convertCertificates() {
  if (!resumeData.value || !resumeData.value.modules || !resumeData.value.modules.certificates) {
    return ''; // 如果没有证书数据，返回空字符串
  }

  const certificates = resumeData.value.modules.certificates;

  // 如果已经是字符串，直接返回
  if (typeof certificates === 'string') {
    return certificates;
  }

  // 如果是数组，转换为字符串
  if (Array.isArray(certificates)) {
    return certificates.map(cert => {
      if (typeof cert === 'string') return cert;
      if (typeof cert === 'object' && cert.certificateName) return cert.certificateName;
      return '';
    }).filter(Boolean).join('\n');
  }

  // 如果是对象，尝试提取证书名称
  if (typeof certificates === 'object') {
    return Object.values(certificates)
        .map(cert => typeof cert === 'string' ? cert :
            (typeof cert === 'object' && cert.certificateName) ? cert.certificateName : '')
        .filter(Boolean)
        .join('\n');
  }

  return '';
}


function convertSelfEvaluations() {
  if (!resumeData.value || !resumeData.value.modules || !resumeData.value.modules.selfEvaluation) {
    return ''; // 如果没有证书数据，返回空字符串
  }

  const educations = resumeData.value.modules.selfEvaluation;

  // 如果已经是字符串，直接返回
  if (typeof educations === 'string') {
    return educations;
  }

  // 如果是数组，转换为字符串
  if (Array.isArray(educations)) {
    return educations.map(cert => {
      if (typeof cert === 'string') return cert;
      if (typeof cert === 'object' && cert.description) return cert.description;
      return '';
    }).filter(Boolean).join('\n');
  }

  // 如果是对象，尝试提取证书名称
  if (typeof educations === 'object') {
    return Object.values(educations)
        .map(cert => typeof cert === 'string' ? cert :
            (typeof cert === 'object' && cert.description) ? cert.description : '')
        .filter(Boolean)
        .join('\n');
  }

  return '';
}

function convertInterests() {
  if (!resumeData.value || !resumeData.value.modules || !resumeData.value.modules.interests) {
    return ''; // 如果没有证书数据，返回空字符串
  }

  const interests = resumeData.value.modules.interests;

  // 如果已经是字符串，直接返回
  if (typeof interests === 'string') {
    return interests;
  }

  // 如果是数组，转换为字符串
  if (Array.isArray(interests)) {
    return interests.map(cert => {
      if (typeof cert === 'string') return cert;
      if (typeof cert === 'object' && cert.description) return cert.description;
      return '';
    }).filter(Boolean).join('\n');
  }

  // 如果是对象，尝试提取证书名称
  if (typeof interests === 'object') {
    return Object.values(interests)
        .map(cert => typeof cert === 'string' ? cert :
            (typeof cert === 'object' && cert.description) ? cert.description : '')
        .filter(Boolean)
        .join('\n');
  }

  return '';
}

function convertCampus() {
  if (!resumeData.value || !resumeData.value.modules || !resumeData.value.modules.campus) {
    return ''; // 如果没有证书数据，返回空字符串
  }

  const campus = resumeData.value.modules.campus;

  // 如果已经是字符串，直接返回
  if (typeof campus === 'string') {
    return campus;
  }

  // 如果是数组，转换为字符串
  if (Array.isArray(campus)) {
    return campus.map(cert => {
      if (typeof cert === 'string') return cert;
      if (typeof cert === 'object' && cert.description) return cert.description;
      return '';
    }).filter(Boolean).join('\n');
  }

  // 如果是对象，尝试提取证书名称
  if (typeof campus === 'object') {
    return Object.values(campus)
        .map(cert => typeof cert === 'string' ? cert :
            (typeof cert === 'object' && cert.description) ? cert.description : '')
        .filter(Boolean)
        .join('\n');
  }

  return '';
}


function convertResumeData(resumeData) {
  if (!resumeData || !resumeData.modules) {
    console.error('无效的简历数据格式');
    return null;
  }

  const modules = resumeData.modules;

  // 辅助函数：合并时间范围数组
  function formatTimePeriod(timeArray) {
    if (!timeArray || timeArray.length < 2) return '';
    return `${timeArray[0]}至${timeArray[1]}`;
  }

  // 辅助函数：拆分证书字符串为数组项
  function splitCertificates(certString) {
    if (!certString || certString.trim() === '') return [];
    return certString.split('\n').filter(item => item.trim() !== '');
  }

  function processEvaluates(selfEvaluation) {
    if (!selfEvaluation || !Array.isArray(selfEvaluation)) return [];
    return selfEvaluation.map((cert, index) => ({
      evaId: cert.id,
      resumeId: cert.resumeId,
      selfEvaluation: cert.description
    }));
  }
  function processInterests(interests) {
    if (!interests || !Array.isArray(interests)) return [];
    return interests.map((cert, index) => ({
      intId: cert.id,
      resumeId: cert.resumeId,
      interest: cert.description
    }));
  }

  // 辅助函数：拆分校园经历字符串为数组项
  function processCampus(campus) {
    if (!campus || !Array.isArray(campus)) return [];
    return campus.map((cert, index) => ({
      camId: cert.id,
      resumeId: cert.resumeId,
      campusExperience: cert.description
    }));
  }

  function processCertificates(certificates) {
    if (!certificates || !Array.isArray(certificates)) return [];
    return certificates.map((cert, index) => ({
      cerId: cert.id,
      resumeId: cert.resumeId,
      certificateName: cert.certificateName
    }));
  }

  // 辅助函数：处理技能列表
  function processSkills(skills) {
    if (!skills || !Array.isArray(skills)) return [];
    return skills.map((skill, index) => ({
      talId: skill.id,
      resumeId: skill.resumeId,
      skillName: skill.name || '',
      // 后端ResumeTalentVo中没有skillId字段，所以不应该传递此字段
      proficiency: getSkillLevelText(skill.level) || '',  // 将数字转换为文本描述
      skillDescription: skill.description || ''
    }));
  }
  // 构建sevaData对象
  const saveData = {
    resumeVo: {
      resumeId: resumeData.resumeId || null,
      title: resumeData.name || '未命名简历',
      templateId: resumeData.templateId || 0,
      status: resumeData.status || 0,
      category: resumeData.category || 0
    },
    information: {
      inforId: modules.basic?.id || null,
      resumeId: modules.basic?.resumeId || null,
      avatar: modules.basic?.avatar || '',
      name: modules.basic?.name || '',
      gender: modules.basic?.gender || '',
      birthDate: modules.basic?.birthday || '',
      phone: modules.basic?.phone || '',
      email: modules.basic?.email || '',
      hometown: modules.basic?.address || '',
      jobObjective: modules.basic?.jobObjective || '',
      age: modules.basic?.age || '',
      nationality: modules.basic?.nationality || ''
    },
    educationList: moduleVisibility.education ? (modules.education || []).map((edu, index) => ({
      eduId: edu.id || null,
      resumeId: edu.resumeId || null,
      school: edu.school || '',
      major: edu.major || '',
      education: edu.degree || '',
      timePeriod: formatTimePeriod(edu.time),
      mainCourses: edu.courses || ''
    })) : [],
    workList: moduleVisibility.work ? (modules.work || []).map((work, index) => ({
      resId: work.id || null,
      resumeId: work.resumeId || null,
      company: work.company || '',
      position: work.position || '',
      timePeriod: formatTimePeriod(work.time),
      workDescription: work.description || ''
    })) : [],
    projectList: moduleVisibility.projects ? (modules.projects || []).map((project, index) => ({
      expId: project.id || null,
      resumeId: project.resumeId || null,
      timePeriod: formatTimePeriod(project.time),
      // positionType: '后端开发', // 默认值，可根据实际项目类型调整
      projectName: project.name || '',
      role: project.role || '',
      projectDescription: project.description || '',
      ...(project.projectId ? { proId: project.projectId } : {}) // 只有当有项目ID时才添加该字段
    })) : [],
    practiceList: moduleVisibility.practices ? (modules.practices || []).map((practice, index) => ({
      praId: practice.id || null,
      resumeId: practice.resumeId || null,
      timePeriod: formatTimePeriod(practice.time),
      projectName: practice.name || '',
      role: practice.role || '',
      projectDescription: practice.description || '',
      projectUrl: practice.url || '',
    })) : [],
    talentList: moduleVisibility.skills ? processSkills(modules.skills || '') : [],
    certificate: {
      cerId: modules.certificates?.id || null,
      resumeId: modules.certificates?.resumeId || resumeData.resumeId || null,
      certificateName: moduleVisibility.certificates ? (modules.certificates?.certificateName || '') : ''
    },
    campus: {
      camId: modules.campus?.id || null,
      resumeId: modules.campus?.resumeId || resumeData.resumeId || null,
      campusExperience: moduleVisibility.campus ? (modules.campus?.description || '') : ''
    },
    interest: {
      intId: modules.interests?.id || null,
      resumeId: modules.interests?.resumeId || resumeData.resumeId || null,
      interest: moduleVisibility.interests ? (modules.interests?.description || '') : ''
    },
    evaluate: modules.selfEvaluation ? {
      evaId: (modules.selfEvaluation.id && modules.selfEvaluation.id !== '') ? Number(modules.selfEvaluation.id) : null,
      resumeId: (modules.selfEvaluation.resumeId && modules.selfEvaluation.resumeId !== '') ? Number(modules.selfEvaluation.resumeId || resumeData.resumeId) : Number(resumeData.resumeId || null),
      selfEvaluation: moduleVisibility.evaluation ? (modules.selfEvaluation.description || '') : ''
    } : {
      evaId: null,
      resumeId: Number(resumeData.resumeId || null),
      selfEvaluation: ''
    },
  };
  console.log('保存简历数据:', saveData);
  console.log('自我评价数据:', saveData.evaluate);
  return saveData;
}


const fetchResumeData = async () => {
  try {
    const response = await resumeApi.getResumeById(route.params.id)
    resumeName.value = response.data.resumeVo.title
    if (response && response.data) {
      const apiData = response.data
      resumeData.value = {
        resumeId: apiData.resumeVo?.resumeId || '',
        templateId: apiData.resumeVo?.templateId || 1,
        category: apiData.resumeVo?.category || 0,
        status: apiData.resumeVo?.status || 0,
        name: apiData.resumeVo?.title,
        modules: {
          basic: {
            id: apiData.information?.inforId || '',
            resumeId: apiData.information?.resumeId || '',
            name: apiData.information?.name || '',
            gender: apiData.information?.gender || '',
            birthday: apiData.information?.birthDate || '',
            phone: apiData.information?.phone || '',
            email: apiData.information?.email || '',
            address: apiData.information?.hometown || '',
            avatar: apiData.information?.avatar || '',
            age: apiData.information?.age || '',
            jobObjective: apiData.information?.jobObjective || ''
          },
          education: apiData.educationList?.map(edu => ({
            id: edu.eduId || '',
            resumeId: edu.resumeId || '',
            school: edu.school || '',
            major: edu.major || '',
            degree: edu.education || '',
            time: (edu.timePeriod || '').split('至').map(part => part.trim()).filter(Boolean) || [],
            startDate: edu.timePeriod?.split('至')[0] || '',
            endDate: edu.timePeriod?.split('至')[1] || '',
            courses: edu.mainCourses || ''
          })) || [],
          work: apiData.workList?.map(work => ({
            id: work.resId || '',
            resumeId: work.resumeId || '',
            company: work.company || '',
            position: work.position || '',
            startDate: work.timePeriod?.split('-')[0] || '',
            endDate: work.timePeriod?.split('-')[1] || '',
            time: (work.timePeriod || '').split('至').map(part => part.trim()).filter(Boolean) || [],
            description: work.workDescription || ''
          })) || [],
          projects: apiData.projectList?.map(project => ({
            id: project.expId || '',
            resumeId: project.resumeId || '',
            name: project.projectName || '',
            role: project.role || '',
            startDate: project.timePeriod?.split('-')[0] || '',
            endDate: project.timePeriod?.split('-')[1] || '',
            time: (project.timePeriod || '').split('至').map(part => part.trim()).filter(Boolean) || [],
            description: project.projectDescription || '',
            projectId: project.proId || null // 保存原始项目ID，用于区分是选择还是手动输入
          })) || [],
          practices: apiData.practiceList?.map(practice => ({
            id: practice.praId || '',
            resumeId: practice.resumeId || '',
            name: practice.projectName || '',
            startDate: practice.timePeriod?.split('-')[0] || '',
            endDate: practice.timePeriod?.split('-')[1] || '',
            time: (practice.timePeriod || '').split('至').map(part => part.trim()).filter(Boolean) || [],
            url: practice.projectUrl || '',
            role: practice.role || '',
            description: practice.projectDescription || ''
          })) || [],
          skills: apiData.talentList?.map(skill => ({
            id: skill.talId || '',
            resumeId: skill.resumeId || '',
            name: skill.skillName || '',
            level: skill.proficiency || 1,
            description: skill.skillDescription || ''
          })) || [],
          // certificates: apiData.certificateList?.map(cert => cert.certificateName).join('\n') || '',
          certificates: {
            id: apiData?.certificate?.cerId || '',
            resumeId: apiData?.certificate?.resumeId || '',
            certificateName: apiData?.certificate?.certificateName || ''
          },
          // campus: apiData.campusList?.map(campus => campus.campusExperience).join('\n') || '',
          campus: {
            id: apiData.campus?.camId || '',
            resumeId: apiData.campus?.resumeId || '',
            description: apiData.campus?.campusExperience || ''
          },
          //interests: apiData.interestList?.map(interest => interest.interestName).join('\n') || '',
          interests: {
            id: apiData?.interest?.intId || '',
            resumeId: apiData?.interest?.resumeId || '',
            description: apiData?.interest?.interest || ''
          },
          //selfEvaluation: apiData.evaluateList?.map(evaluation => evaluation.evaluateContent).join('\n') || ''
          selfEvaluation: {
            id: apiData?.evaluate?.evaId || '',
            resumeId: apiData?.evaluate?.resumeId || '',
            description: apiData?.evaluate?.selfEvaluation || ''
          },
        }
      }
      console.log('1', resumeData.value)
      handleTemplateChange(resumeData.value.templateId)
      return
    }
  } catch (error) {
    // console.error('从API获取数据失败:', error)
  }
}
// 初始化数据
onMounted(async () => {
  try {
    loading.value = true
    const id = route.params.id

    // 如果是新建简历
    if (id === 'new') {
      resumeData.value = resumeStore.createEmptyResume()
      resumeName.value = '未命名简历'
      currentTemplateId.value = 1
      await nextTick()
      initVisibleModules()
      return
    }
    await fetchResumeData()
  } catch (error) {
    console.error('初始化简历编辑器失败:', error)
    ElMessage.error('初始化简历编辑器失败')
    router.push('/user')
  } finally {
    loading.value = false

    // 确保在加载完成后显示校园经历和兴趣爱好的编辑器
    nextTick(() => {
      // 如果校园经历模块可见，显示其编辑器
      if (moduleVisibility.campus && campusFormRef.value) {
        campusFormRef.value.showEditor = true;
      }

      // 如果兴趣爱好模块可见，显示其编辑器
      if (moduleVisibility.interests && interestFormRef.value) {
        interestFormRef.value.showEditor = true;
      }
    });
  }
})


// 用于强制更新简历对象，触发Vue的响应式更新
const updateResumeDataObject = () => {
  if (!resumeData.value) return;

  // 首先格式化所有日期
  formatAllDates();

  // 然后强制重新渲染
  resumeData.value = JSON.parse(JSON.stringify(resumeData.value));
};

// 获取当前模板组件
const getCurrentTemplate = computed(() => {
  return getTemplateComponent(currentTemplateId.value);
});

// 通用日期处理函数
const handleDateChange = (item, event) => {
  if (event && Array.isArray(event) && event.length === 2) {
    item.time = event;
    item.startDate = formatDate(event[0]);
    item.endDate = formatDate(event[1]);
  }
};

// 教育经历部分的时间段选择器
const handleDateChangeEducation = (edu, event) => {
  console.log(resumeData.value.modules.education)
  handleDateChange(edu, event);
  updateResumeDataObject();
};

// 工作经验部分的时间段选择器
const handleDateChangeWork = (work, event) => {
  handleDateChange(work, event);
  updateResumeDataObject();
};

// 实习经历部分的时间段选择器
const handleDateChangeInternship = (internship, event) => {
  handleDateChange(internship, event);
  updateResumeDataObject();
};

// 项目经验部分的时间段选择器
const handleDateChangeProject = (project, event) => {
  handleDateChange(project, event);
  updateResumeDataObject();
};

// 练手项目部分的时间段选择器
const handleDateChangePractice = (practice, event) => {
  handleDateChange(practice, event);
  updateResumeDataObject();
};

// AI润色对话框
const aiPolishDialogVisible = ref(false);
const currentPolishingText = ref('');
const polishingResult = ref('');
const isPolishing = ref(false);
const polishProgress = ref(0);
const selectedVersion = ref(1);

// 当前正在编辑的项目索引
const currentPolishingIndex = ref(-1);

// AI润色描述
const aiPolishDescription = (index) => {
  if (!resumeData.value || !resumeData.value.modules.projects[index]) return;

  const project = resumeData.value.modules.projects[index];

  // 如果没有描述，添加一个默认描述
  if (!project.description || project.description.trim() === '') {
    project.description = `项目描述：${project.name || '项目'}是一个${getCategoryDesc(project.category || '技术')}项目。\n\n`;
  }

  // 设置当前正在编辑的项目
  currentPolishingIndex.value = index;
  currentPolishingText.value = project.description;
  polishingResult.value = '';

  // 显示对话框
  aiPolishDialogVisible.value = true;

  // 模拟AI润色过程
  isPolishing.value = true;
  polishProgress.value = 0;

  // 模拟进度
  const interval = setInterval(() => {
    polishProgress.value += 10;
    if (polishProgress.value >= 100) {
      clearInterval(interval);
      isPolishing.value = false;

      // 生成润色结果
      generatePolishedText(project);
    }
  }, 300);
};

// 生成润色后的文本
const generatePolishedText = (project) => {
  const originalText = currentPolishingText.value;

  // 这里是简单的模拟AI润色，实际项目中可以调用AI API
  let polished = originalText;

  // 基本润色逻辑
  polished = polished.replace(/项目描述[:：]/g, '【项目背景】');
  polished = polished.replace(/主要职责[:：]/g, '【我的职责】');
  polished = polished.replace(/项目成果[:：]/g, '【项目成就】');

  // 添加专业术语和表达
  const category = project.category || '';

  // 根据项目类别添加不同的润色内容
  if (category.includes('front') || category === 'frontend') {
    polished += '\n\n【技术亮点】\n1. 采用组件化、模块化的开发方式，提高了代码复用率和可维护性\n2. 运用虚拟DOM和高效的渲染算法，大幅提升了用户界面响应速度\n3. 通过合理的状态管理策略，确保了复杂应用的数据流清晰可控';
  } else if (category.includes('back') || category === 'backend') {
    polished += '\n\n【技术亮点】\n1. 基于微服务架构设计，实现了系统的高可用性和可扩展性\n2. 通过合理的数据库设计和索引优化，使查询效率提升了60%\n3. 实现了完善的异常处理和日志监控机制，确保系统稳定运行';
  } else if (category.includes('data') || category === 'data') {
    polished += '\n\n【技术亮点】\n1. 应用先进的数据处理算法，使数据处理效率提升了40%\n2. 设计了直观的数据可视化界面，使业务决策更加高效准确\n3. 通过数据清洗和预处理流程，显著提高了数据质量';
  } else {
    polished += '\n\n【技术亮点】\n1. 采用业内先进的技术架构和开发理念，确保系统高效稳定\n2. 通过严格的代码审查和测试流程，保证了产品的质量\n3. 积极应用新技术解决实际问题，推动了技术创新';
  }

  polishingResult.value = polished;
};

// 应用润色后的文本
const applyPolishedText = () => {
  if (currentPolishingIndex.value >= 0 && resumeData.value) {
    // 判断当前正在润色的是项目描述还是工作描述
    if (aiPolishDialogVisible.value && currentPolishingText.value) {
      // 如果当前润色的文本与项目描述匹配
      const matchedProject = resumeData.value.modules.projects.find(
          (p, idx) => idx === currentPolishingIndex.value && p.description === currentPolishingText.value
      );

      if (matchedProject) {
        // 应用润色结果到项目描述
        if (selectedVersion.value === 1) {
          resumeData.value.modules.projects[currentPolishingIndex.value].description = polishingResult.value;
        }
        ElMessage.success('已应用所选内容');
      } else {
        // 应用润色结果到工作描述
        const matchedWork = resumeData.value.modules.work.find(
            (w, idx) => idx === currentPolishingIndex.value
        );

        if (matchedWork) {
          if (selectedVersion.value === 1) {
            resumeData.value.modules.work[currentPolishingIndex.value].description = polishingResult.value;
          }
          ElMessage.success('已应用所选内容');
        }
      }
    }

    // 关闭对话框
    aiPolishDialogVisible.value = false;
  }
}

// 添加新的技能
const addSkill = () => {
  if (!resumeData.value || !resumeData.value.modules) return;

  if (!Array.isArray(resumeData.value.modules.skills)) {
    resumeData.value.modules.skills = [];
  }

  resumeData.value.modules.skills.push({
    name: '',
    skiId: null, // 添加技能ID字段
    level: 1,
    description: ''
  });

  updateResumeDataObject();
}

// 删除技能
const removeSkill = (index) => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.skills)) return;

  resumeData.value.modules.skills.splice(index, 1);
  updateResumeDataObject();
}

const getSkillLevelValue = (text) => {
  const levelMap = {
    '一般': 1,
    '良好': 2,
    '熟练': 3,
    '擅长': 4,
    '精通': 5
  };

  // 支持大小写不敏感匹配（可选）
  const normalizedText = text?.trim()?.toLowerCase() || '';

  // 查找匹配的等级值
  return Object.keys(levelMap).find(
      (key) => key.toLowerCase() === normalizedText
  )
      ? levelMap[text]
      : null; // 返回 null 或默认值（如 3）
};

// 处理技能选择变更
const handleSkillSelect = (skiId, index) => {
  if (!resumeData.value || !resumeData.value.modules.skills[index]) return;

  // 获取选中的技能
  const selectedSkill = skillList.value.find(skill => Number(skill.skiId) === Number(skiId));
  if (selectedSkill) {
    // 设置技能名称
    resumeData.value.modules.skills[index].name = selectedSkill.name;
    // 设置技能ID
    resumeData.value.modules.skills[index].skiId = selectedSkill.skiId;
    // 设置熟练度
    resumeData.value.modules.skills[index].level = getSkillLevelValue(selectedSkill.proficiency) || 1;

    // 选择技能后，自动生成对应的描述
    generateSkillDescription(skiId, index);
  } else {
    ElMessage.warning('未找到选择的技能');
  }
};

// 选择技能描述条款
// 函数已不需要，可以删除
// 存储对话框状态也不再需要

// 修复技能描述生成函数
const generateSkillDescription = (skiId, index) => {
  const skill = getSkillById(skiId)
  if (skill) {
    resumeData.value.modules.skills[index].description = skill.description || ''
    updateResumeDataObject()

  }
}


const getSkillById = (skiId) => {
  const skill = skillList.value.find(item => item.skiId === skiId)
  if (skill) {
    // 如果有 segments，直接修改 skill 的 description
    if (skill.segments && skill.segments.length > 0) {
      skill.description = skill.segments.map(segment => segment.text).join('\n\n')
    }
    return skill
  }
  return null
}

// 判断技能所属分类
const getSkillCategory = (skillName) => {
  // 编程语言
  const programmingLanguages = ['JavaScript', 'TypeScript', 'Java', 'Python', 'C++', 'C#', 'Go', 'PHP', 'Ruby', 'Swift', 'Kotlin'];

  // 前端技术
  const frontendTech = ['React', 'Vue.js', 'Angular', 'HTML5', 'CSS3', 'SCSS/SASS', 'WebAssembly', 'Webpack', 'Redux', 'Vuex', 'Next.js', 'Nuxt.js'];

  // 后端技术
  const backendTech = ['Node.js', 'Express', 'Spring Boot', 'Django', 'Flask', 'Laravel', 'Ruby on Rails', '.NET Core', 'NestJS', 'GraphQL', 'RESTful API'];

  // 数据库
  const databaseTech = ['MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'Elasticsearch', 'SQLite', 'Oracle', 'SQL Server', 'Firebase'];

  // 移动开发
  const mobileTech = ['Android原生', 'iOS原生', 'Flutter', 'React Native', '微信小程序', 'UniApp', 'Ionic'];

  // DevOps/云服务
  const devopsTech = ['Docker', 'Kubernetes', 'Jenkins', 'Git', 'AWS', 'Azure', 'Google Cloud', 'Nginx', 'Linux', 'CI/CD'];

  // 数据科学/AI
  const dataScienceTech = ['TensorFlow', 'PyTorch', '机器学习', '深度学习', '数据分析', 'Pandas', 'NumPy', 'Scikit-learn', '自然语言处理', '计算机视觉'];

  if (programmingLanguages.includes(skillName)) return 'programming';
  if (frontendTech.includes(skillName)) return 'frontend';
  if (backendTech.includes(skillName)) return 'backend';
  if (databaseTech.includes(skillName)) return 'database';
  if (mobileTech.includes(skillName)) return 'mobile';
  if (devopsTech.includes(skillName)) return 'devops';
  if (dataScienceTech.includes(skillName)) return 'datascience';

  return 'other';
};

// 获取技能水平描述
const getSkillLevelText = (level) => {
  const levelTextMap = {
    1: '一般',
    2: '良好',
    3: '熟练',
    4: '擅长',
    5: '精通'
  };

  return levelTextMap[level] || '未知';
}

// 技能列表数据
const skillList = ref([])

// 获取技能列表
const fetchSkillList = async () => {
  try {
    const res = await resumeApi.getSkillList()
    // 直接保存原始数据
    skillList.value = res.data
  } catch (error) {
    console.error('获取技能列表失败:', error)
    ElMessage.error('获取技能列表失败')
  }
}

// 获取分类标签
const getCategoryLabel = (category) => {
  const categoryMap = {
    'programming': '编程语言',
    'frontend': '前端技术',
    'backend': '后端技术',
    'database': '数据库',
    'mobile': '移动开发',
    'devops': 'DevOps/云服务',
    'datascience': '数据科学/AI',
    'other': '其他技能'
  }
  return categoryMap[category] || '其他技能'
}

// 在组件挂载时获取技能列表
onMounted(() => {
  initResumeData()
  fetchCategoryList()
  fetchSkillList()
})

// 证书奖项模块
const showCertificateForm = ref(false);
const newCertificate = ref('');
const certificateItems = ref([]);
const hasCertificateItems = computed(() => certificateItems.value.length > 0);

// 显示证书输入框
const showCertificateInput = () => {
  // 确保已启用该模块
  if (!moduleVisibility.certificates) {
    moduleVisibility.certificates = true;

    // 恢复之前备份的数据（如果有）
    const backupKey = '_hiddenCertificates';
    if (resumeData.value.modules[backupKey]) {
      resumeData.value.modules.certificates = resumeData.value.modules[backupKey];
      resumeData.value.modules[backupKey] = null;

      // 强制刷新视图
      resumeData.value = { ...resumeData.value };
    }
  }

  // 显示编辑器
  nextTick(() => {
    if (certificateFormRef.value) {
      certificateFormRef.value.showEditor = true;
    }
  });
};

// 添加新的证书/奖项
const addCertificate = () => {
  if (newCertificate.value.trim() === '') {
    ElMessage.warning('请输入证书/奖项名称');
    return;
  }
  // 添加到列表
  certificateItems.value.push(newCertificate.value);

  // 添加后清空输入框但不隐藏
  newCertificate.value = '';

  // 将焦点保持在输入框
  setTimeout(() => {
    document.querySelector('#module-certificates textarea')?.focus();
  }, 100);

  // 更新resumeData中的certificates字段
  updateCertificatesToResumeData();
};

// 取消添加证书/奖项
const cancelCertificate = () => {
  newCertificate.value = '';
  showCertificateForm.value = false;
};

// 删除证书/奖项
const removeCertificate = (index) => {
  certificateItems.value.splice(index, 1);

  // 更新resumeData中的certificates字段
  updateCertificatesToResumeData();
};

// 更新证书奖项数据到resumeData
const updateCertificatesToResumeData = () => {
  if (!resumeData.value || !resumeData.value.modules) return;

  // 检查输入框是否有内容
  if (newCertificate.value.trim() !== '') {
    // 如果输入框有内容，添加新的证书对象
    if (!Array.isArray(resumeData.value.modules.certificates)) {
      resumeData.value.modules.certificates = [];
    }
    resumeData.value.modules.certificates.push({
      id: '',
      resumeId: resumeData.value.resumeId || '',
      certificateName: newCertificate.value.trim()
    });
  } else if (certificateItems.value.length > 0) {
    // 如果列表不为空，使用列表中的证书对象
    resumeData.value.modules.certificates = certificateItems.value;
  } else {
    // 如果输入框为空且列表为空，设置为空数组
    resumeData.value.modules.certificates = [];
  }

  // 更新整个resumeData对象
  updateResumeDataObject();
};

// 初始化证书数据
const initCertificateItems = () => {
  if (!resumeData.value || !resumeData.value.modules || !resumeData.value.modules.certificates) return;

  // 如果certificates是字符串，按行拆分为数组
  if (typeof resumeData.value.modules.certificates === 'string' && resumeData.value.modules.certificates.trim() !== '') {
    certificateItems.value = resumeData.value.modules.certificates.split('\n').filter(item => item.trim() !== '');
  }
};

// 在resumeData初始化后，初始化证书数据
watch(() => resumeData.value, () => {
  initCertificateItems();
}, { immediate: true });

// 证书输入框输入事件
const onCertificateInput = (value) => {
  newCertificate.value = value;

  // 实时更新预览 - 只显示当前输入框的内容
  if (resumeData.value && resumeData.value.modules) {
    resumeData.value.modules.certificates = value;

    // 触发响应式更新
    updateResumeDataObject();
  }
};

// 证书保存事件
const saveCertificate = () => {
  // 失焦时什么都不做，保持输入框状态
};

// 校园经历模块
const showCampusForm = ref(false);
const newCampus = ref('');
const campusItems = ref([]);
const hasCampusItems = computed(() => campusItems.value.length > 0);

// 显示校园经历输入框
const showCampusInput = () => {
  // 确保已启用该模块
  if (!moduleVisibility.campus) {
    moduleVisibility.campus = true;

    // 恢复之前备份的数据（如果有）
    const backupKey = '_hiddenCampus';
    if (resumeData.value.modules[backupKey]) {
      resumeData.value.modules.campus = resumeData.value.modules[backupKey];
      resumeData.value.modules[backupKey] = null;

      // 强制刷新视图
      resumeData.value = { ...resumeData.value };
    }
  }

  // 显示编辑器
  nextTick(() => {
    if (campusFormRef.value) {
      campusFormRef.value.showEditor = true;
    }
  });
}

// 添加新的校园经历
const addCampus = () => {
  if (newCampus.value.trim() === '') {
    ElMessage.warning('请输入校园活动或社团经历');
    return;
  }
  campusItems.value.push(newCampus.value);
  newCampus.value = '';

  // 更新resumeData中的campus字段
  updateCampusesToResumeData();
};

// 取消添加校园经历
const cancelCampus = () => {
  newCampus.value = '';
  showCampusForm.value = false;
};

// 删除校园经历
const removeCampus = (index) => {
  campusItems.value.splice(index, 1);

  // 更新resumeData中的campus字段
  updateCampusesToResumeData();
};

// 更新校园经历数据到resumeData - 只在手动添加时调用
const updateCampusesToResumeData = () => {
  if (!resumeData.value || !resumeData.value.modules) return;

  // 如果campus是对象
  if (typeof resumeData.value.modules.campus === 'object' && resumeData.value.modules.campus !== null) {
    resumeData.value.modules.campus = {
      ...resumeData.value.modules.campus,
      description: campusItems.value.join('\n')
    };
  } else {
    // 将校园经历数组转换为字符串，每项一行
    resumeData.value.modules.campus = campusItems.value.join('\n');
  }

  // 更新整个resumeData对象
  updateResumeDataObject();
};

// 初始化校园经历数据
const initCampusItems = () => {
  if (!resumeData.value || !resumeData.value.modules || !resumeData.value.modules.campus) return;

  // 如果campus是对象且有description属性
  if (typeof resumeData.value.modules.campus === 'object' && resumeData.value.modules.campus !== null) {
    const description = resumeData.value.modules.campus.description || '';
    if (description.trim() !== '') {
      campusItems.value = description.split('\n').filter(item => item.trim() !== '');
    }
  }
  // 如果campus是字符串，按行拆分为数组
  else if (typeof resumeData.value.modules.campus === 'string' && resumeData.value.modules.campus.trim() !== '') {
    campusItems.value = resumeData.value.modules.campus.split('\n').filter(item => item.trim() !== '');
  }
};

// 在resumeData初始化后，初始化校园经历数据并显示编辑器
watch(() => resumeData.value, () => {
  initCampusItems();

  // 如果有校园经历数据，自动显示编辑器
  if (resumeData.value &&
      resumeData.value.modules &&
      resumeData.value.modules.campus &&
      moduleVisibility.campus) {
    nextTick(() => {
      if (campusFormRef.value) {
        campusFormRef.value.showEditor = true;
      }
    });
  }
}, { immediate: true });

// 兴趣爱好模块
const showInterestForm = ref(false);
const newInterest = ref('');
const interestItems = ref([]);
const hasInterestItems = computed(() => interestItems.value.length > 0);

// 显示兴趣爱好输入框
const showInterestInput = () => {
  // 确保已启用该模块
  if (!moduleVisibility.interests) {
    moduleVisibility.interests = true;

    // 恢复之前备份的数据（如果有）
    const backupKey = '_hiddenInterests';
    if (resumeData.value.modules[backupKey]) {
      resumeData.value.modules.interests = resumeData.value.modules[backupKey];
      resumeData.value.modules[backupKey] = null;

      // 强制刷新视图
      resumeData.value = { ...resumeData.value };
    }
  }

  // 显示编辑器
  nextTick(() => {
    if (interestFormRef.value) {
      interestFormRef.value.showEditor = true;
    }
  });
}

// 添加新的兴趣爱好
const addInterest = () => {
  if (newInterest.value.trim() === '') {
    ElMessage.warning('请输入兴趣爱好及相关描述');
    return;
  }
  interestItems.value.push(newInterest.value);
  newInterest.value = '';

  // 更新resumeData中的interests字段
  updateInterestsToResumeData();
};

// 取消添加兴趣爱好
const cancelInterest = () => {
  newInterest.value = '';
  showInterestForm.value = false;
};

// 删除兴趣爱好
const removeInterest = (index) => {
  interestItems.value.splice(index, 1);

  // 更新resumeData中的interests字段
  updateInterestsToResumeData();
};

// 更新兴趣爱好数据到resumeData - 只在手动添加时调用
const updateInterestsToResumeData = () => {
  if (!resumeData.value || !resumeData.value.modules) return;

  // 如果interests是对象
  if (typeof resumeData.value.modules.interests === 'object' && resumeData.value.modules.interests !== null) {
    resumeData.value.modules.interests = {
      ...resumeData.value.modules.interests,
      description: interestItems.value.join('\n')
    };
  } else {
    // 将兴趣爱好数组转换为字符串，每项一行
    resumeData.value.modules.interests = interestItems.value.join('\n');
  }

  // 更新整个resumeData对象
  updateResumeDataObject();
};

// 初始化兴趣爱好数据
const initInterestItems = () => {
  if (!resumeData.value || !resumeData.value.modules || !resumeData.value.modules.interests) return;

  // 如果interests是对象且有description属性
  if (typeof resumeData.value.modules.interests === 'object' && resumeData.value.modules.interests !== null) {
    const description = resumeData.value.modules.interests.description || '';
    if (description.trim() !== '') {
      interestItems.value = description.split('\n').filter(item => item.trim() !== '');
    }
  }
  // 如果interests是字符串，按行拆分为数组
  else if (typeof resumeData.value.modules.interests === 'string' && resumeData.value.modules.interests.trim() !== '') {
    interestItems.value = resumeData.value.modules.interests.split('\n').filter(item => item.trim() !== '');
  }
};

// 在resumeData初始化后，初始化兴趣爱好数据并显示编辑器
watch(() => resumeData.value, () => {
  initInterestItems();

  // 如果有兴趣爱好数据，自动显示编辑器
  if (resumeData.value &&
      resumeData.value.modules &&
      resumeData.value.modules.interests &&
      moduleVisibility.interests) {
    nextTick(() => {
      if (interestFormRef.value) {
        interestFormRef.value.showEditor = true;
      }
    });
  }
}, { immediate: true });

// 校园经历输入框输入事件
const onCampusInput = (value) => {
  newCampus.value = value;

  // 实时更新预览
  if (resumeData.value && resumeData.value.modules) {
    // 如果campus是对象
    if (typeof resumeData.value.modules.campus === 'object' && resumeData.value.modules.campus !== null) {
      resumeData.value.modules.campus = {
        ...resumeData.value.modules.campus,
        description: newCampus.value
      };
    } else {
      // 仅显示输入框内容，不与已有列表合并
      resumeData.value.modules.campus = newCampus.value;
    }

    // 触发响应式更新
    updateResumeDataObject();
  }
};

// 校园经历保存事件 - 失焦时不做任何动作
const saveCampus = () => {
  // 失焦时什么都不做，保持输入框状态
};

// 修改兴趣爱好输入框输入事件
const onInterestInput = (value) => {
  newInterest.value = value;

  // 实时更新预览
  if (resumeData.value && resumeData.value.modules) {
    // 如果interests是对象
    if (typeof resumeData.value.modules.interests === 'object' && resumeData.value.modules.interests !== null) {
      resumeData.value.modules.interests = {
        ...resumeData.value.modules.interests,
        description: newInterest.value
      };
    } else {
      // 仅显示输入框内容，不与已有列表合并
      resumeData.value.modules.interests = newInterest.value;
    }

    // 触发响应式更新
    updateResumeDataObject();
  }
};

// 兴趣爱好保存事件 - 失焦时不做任何动作
const saveInterest = () => {
  // 失焦时什么都不做，保持输入框状态
};

// 个人评价模块
const updateEvaluation = (newEvaluation) => {
  if (!resumeData.value || !resumeData.value.modules) return;
  console.log('更新自我评价:', newEvaluation);

  // 处理ID字段，确保为数字类型或null
  const evaId = newEvaluation.evaId ?
      (typeof newEvaluation.evaId === 'string' && newEvaluation.evaId.trim() !== '' ? Number(newEvaluation.evaId) : newEvaluation.evaId) :
      (resumeData.value.modules.selfEvaluation?.id || null);

  const resumeId = newEvaluation.resumeId ?
      (typeof newEvaluation.resumeId === 'string' && newEvaluation.resumeId.trim() !== '' ? Number(newEvaluation.resumeId) : newEvaluation.resumeId) :
      (resumeData.value.modules.selfEvaluation?.resumeId ||
          (resumeData.value.resumeId ? Number(resumeData.value.resumeId) : null));

  // 确保保留evaId和resumeId字段，以及selfEvaluation内容
  resumeData.value.modules.selfEvaluation = {
    id: evaId,
    resumeId: resumeId,
    description: newEvaluation.selfEvaluation || ''
  };

  console.log('更新后的自我评价数据:', resumeData.value.modules.selfEvaluation);
  updateResumeDataObject();
};

// 显示个人评价输入框
const showEvaluationInput = () => {
  // 确保已启用该模块
  if (!moduleVisibility.evaluation) {
    moduleVisibility.evaluation = true;

    // 恢复之前备份的数据（如果有）
    const backupKey = '_hiddenEvaluation';
    if (resumeData.value.modules[backupKey]) {
      resumeData.value.modules.selfEvaluation = resumeData.value.modules[backupKey];
      resumeData.value.modules[backupKey] = null;
    } else if (!resumeData.value.modules.selfEvaluation) {
      // 如果没有自我评价数据，初始化一个空对象
      resumeData.value.modules.selfEvaluation = {
        id: null, // 对应后端的evaId
        resumeId: resumeData.value.resumeId ? Number(resumeData.value.resumeId) : null,
        description: '' // 对应后端的selfEvaluation
      };
    }

    // 强制刷新视图
    resumeData.value = { ...resumeData.value };
  }

  console.log('当前自我评价数据:', resumeData.value.modules.selfEvaluation);

  // 直接显示编辑器
  nextTick(() => {
    if (evaluationFormRef.value) {
      evaluationFormRef.value.showEditor = true;
      console.log('显示自我评价编辑器，当前数据:', resumeData.value.modules.selfEvaluation);
    }
  });
};

// 练手项目模块
const updatePractices = (newPractices) => {
  if (!resumeData.value || !resumeData.value.modules) return;
  resumeData.value.modules.practices = newPractices;
  updateResumeDataObject();
}

// 兴趣爱好模块
const updateInterests = (newInterests) => {
  if (!resumeData.value || !resumeData.value.modules) return;

  // 如果传入的是对象，直接使用
  if (typeof newInterests === 'object' && newInterests !== null) {
    resumeData.value.modules.interests = newInterests;
  } else {
    // 如果传入的是字符串，创建或更新对象
    resumeData.value.modules.interests = {
      ...resumeData.value.modules.interests,
      description: newInterests
    };
  }

  updateResumeDataObject();
};

// 校园经历模块
const updateCampus = (newCampus) => {
  if (!resumeData.value || !resumeData.value.modules) return;

  // 如果传入的是对象，直接使用
  if (typeof newCampus === 'object' && newCampus !== null) {
    resumeData.value.modules.campus = newCampus;
  } else {
    // 如果传入的是字符串，创建或更新对象
    resumeData.value.modules.campus = {
      ...resumeData.value.modules.campus,
      description: newCampus
    };
  }

  updateResumeDataObject();
};

// 证书奖项模块
const updateCertificates = (newCertificates) => {
  if (!resumeData.value || !resumeData.value.modules) return;

  // 如果传入的是对象，直接使用
  if (typeof newCertificates === 'object') {
    resumeData.value.modules.certificates = newCertificates;
  } else {
    // 如果传入的是字符串，创建或更新对象
    resumeData.value.modules.certificates = {
      ...resumeData.value.modules.certificates,
      certificateName: newCertificates
    };
  }

  updateResumeDataObject();
};

// 添加updateSkillDescription函数
const updateSkillDescription = (index, newDescription) => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.skills)) return;

  resumeData.value.modules.skills[index].description = newDescription;
  updateResumeDataObject();
};

// 添加updateProjectDescription函数
const updateProjectDescription = (index, newDescription) => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.projects)) return;
  resumeData.value.modules.projects[index].description = newDescription;
  updateResumeDataObject();
};

const updatePracticeDescription = (index, newDescription) => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.practices)) return;
  resumeData.value.modules.practices[index].description = newDescription;
  updateResumeDataObject();
};


// 添加updateWorkDescription函数
const updateWorkDescription = (index, newDescription) => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.work)) return;

  resumeData.value.modules.work[index].description = newDescription;
  updateResumeDataObject();
};

// 添加AI润色工作描述函数
const aiPolishWorkDescription = (index) => {
  if (!resumeData.value || !resumeData.value.modules.work[index]) return;

  const work = resumeData.value.modules.work[index];

  // 显示AI润色对话框
  aiPolishDialogVisible.value = true;
  currentPolishingText.value = work.description;
  polishingResult.value = work.description; // 初始化为原始内容
  isPolishing.value = true;
  polishProgress.value = 0;
  selectedVersion.value = 1; // 默认选择润色后的内容

  // 记录当前正在编辑的工作索引
  currentPolishingIndex.value = index;

  // 模拟AI润色过程
  simulateAIPolishing();
};

// 模拟AI润色过程
const simulateAIPolishing = () => {
  isPolishing.value = true;
  polishProgress.value = 0;

  // 模拟进度
  const interval = setInterval(() => {
    polishProgress.value += 10;
    if (polishProgress.value >= 100) {
      clearInterval(interval);
      isPolishing.value = false;

      // 调用后端AI润色API
      const workExperience = resumeData.value.modules.work[currentPolishingIndex.value];
      const fullDescription = `公司：${workExperience.company}\n职位：${workExperience.position}\n工作描述：\n${currentPolishingText.value}`;

      polishWork(fullDescription).then(res => {
        if (res.code === 0 || res.code === 200) {
          // 提取润色后的工作描述部分
          const polishedText = res.data;
          const match = polishedText.match(/工作描述：\s*\n*([\s\S]*)/i);
          polishingResult.value = match && match[1] ? match[1].trim() : polishedText;
        } else {
          // 显示后端返回的具体错误消息
          const errorMsg = res.message || res.msg || '润色失败';
          ElMessage.error(errorMsg);
          polishingResult.value = currentPolishingText.value;
        }
      }).catch(error => {
        console.error('润色请求失败', error);
        // 显示具体的错误消息
        ElMessage.error(error.message || '润色请求失败，请稍后重试');
        polishingResult.value = currentPolishingText.value;
      });
    }
  }, 300);
};

// 职位类型选项
const categoryOptions = ref([])

// 获取职位类型列表
const fetchCategoryList = async () => {
  try {
    const res = await resumeApi.getCategoryList()
    categoryOptions.value = res.data.map(item => ({
      label: item.name,
      value: item.catId
    }))
  } catch (error) {
    console.error('获取职位类型列表失败:', error)
    ElMessage.error('获取职位类型列表失败')
  }
}

// 初始化简历数据
const initResumeData = () => {
  const storedData = localStorage.getItem('resumeData')
  if (storedData) {
    const parsedData = JSON.parse(storedData)
    resumeData.value = parsedData
    // 清除localStorage中的数据
    localStorage.removeItem('resumeData')
  }
}

// 添加练手项目
const addPractice = () => {
  if (!resumeData.value || !resumeData.value.modules) return;

  if (!Array.isArray(resumeData.value.modules.practices)) {
    resumeData.value.modules.practices = [];
  }
  function formatToYYYYMM(date) {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
  }

  // 设置为YYYY-MM格式的日期
  const endDate = formatToYYYYMM(new Date(new Date().getFullYear(), 8, 30)); // 9月30日
  const startDate = formatToYYYYMM(new Date(new Date().getFullYear(), 6, 1));

  resumeData.value.modules.practices.push({
    name: '',
    role: '',
    url: '',
    description: '',
    time: [startDate, endDate],
    startDate: formatDate(startDate),
    endDate: formatDate(endDate)
  });

  updateResumeDataObject();
}

// 删除练手项目
const removePractice = (index) => {
  if (!resumeData.value || !resumeData.value.modules || !Array.isArray(resumeData.value.modules.practices)) return;

  resumeData.value.modules.practices.splice(index, 1);
  updateResumeDataObject();
}

const avatarInput = ref(null)

const triggerAvatarUpload = () => {
  avatarInput.value.click()
}

const handleAvatarUpload = async (event) => {
  const file = event.target.files[0]
  if (file) {
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      ElMessage.error('请上传图片文件')
      return
    }
    // 检查文件大小（限制为2MB）
    if (file.size > 2 * 1024 * 1024) {
      ElMessage.error('图片大小不能超过2MB')
      return
    }
    // 上传到服务器
    const formData = new FormData()
    formData.append('file', file)
    try {
      const res = await userApi.uploadAvatar(formData)
      if (res && res.data) {
        resumeData.value.modules.basic.avatar = res.data // 后端返回图片url
        updateResumeDataObject()
        ElMessage.success('头像上传成功')
      } else {
        ElMessage.error('头像上传失败')
      }
    } catch (e) {
      ElMessage.error('头像上传失败')
    }
  }
}

// 各模块ID字段映射
const moduleIdMap = {
  education: 'eduId',
  work: 'resId',
  projects: 'expId',
  practices: 'praId',
  skills: 'talId',
  certificates: 'cerId',
  campus: 'camId',
  interests: 'intId',
  evaluation: 'evaId',
  basic: 'inforId'
}

</script>

<style scoped>
.resume-editor {
  display: flex;
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 左侧导航栏样式 */
.side-nav {
  width: 70px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 101;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.nav-item {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 14px 0;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  color: #606266;
}

.nav-item:hover {
  color: #3498db;
  background-color: rgba(52, 152, 219, 0.1);
}

.nav-item.active {
  color: #3498db;
  background-color: rgba(52, 152, 219, 0.15);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #3498db;
}

.nav-item .el-icon {
  font-size: 22px;
  margin-bottom: 5px;
}

.nav-text {
  font-size: 12px;
}

.editor-header {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 10px 0;
  position: fixed;
  top: 0;
  left: 70px;
  /* 调整为导航栏宽度 */
  width: calc(100% - 70px);
  z-index: 100;
}

.editor-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

.editor-logo h1 {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.editor-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.resume-name-input {
  width: 180px;
}

.editor-content {
  display: flex;
  margin-top: 60px;
  margin-left: 70px;
  /* 为左侧导航栏留出空间 */
  height: calc(100vh - 60px);
  width: calc(100% - 70px);
  overflow: hidden;
}

/* 左侧编辑区域 */
.edit-panel {
  width: v-bind('`${leftPanelWidth}%`');
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  border-right: none;
  position: relative;
  overflow-y: auto;
  height: 100%;
}

.progress-section {
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.module-selector {
  overflow-y: auto;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.module-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  margin-bottom: 6px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
}

.module-item:hover {
  background-color: #f5f7fa;
}

.module-item.active {
  background-color: #ecf5ff;
  color: #3498db;
}

.module-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  width: 20px;
}

.module-name {
  flex: 1;
  font-size: 14px;
}

.module-status {
  color: #67c23a;
}

.editing-area {
  padding: 16px;
}

/* 右侧预览区域 */
.preview-panel {
  width: v-bind('`${100 - leftPanelWidth}%`');
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
}

.preview-toolbar {
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.layout-options {
  display: flex;
  align-items: center;
  gap: 10px;
}



.preview-actions {
  display: flex;
  gap: 10px;
}

.resizer {
  width: 6px;
  height: 100%;
  cursor: col-resize;
  position: absolute;
  left: -3px;
  top: 0;
  bottom: 0;
  background-color: #ccc;
  opacity: 0.3;
  z-index: 10;
  transition: opacity 0.3s;
}

.resizer:hover,
.resizer:active {
  opacity: 0.6;
}

.resizer-tip {
  position: absolute;
  white-space: nowrap;
  right: 10px;
  top: 50%;
  transform: translateY(-50%) translateX(100%);
  color: #606266;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  pointer-events: none;
}

.resizer:hover .resizer-tip {
  opacity: 1;
}

.resume-preview-container {
  flex: 1;
  padding: 30px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  overflow-y: auto;
  /* 添加垂直滚动 */
  height: calc(100% - 51px);
  /* 减去工具栏高度 */
}

.scale-container {
  width: 210mm;
  background-color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  transform-origin: top center;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 60px);
  margin-top: 60px;
  padding: 20px;
}

.loading-container {
  width: 100%;
  max-width: 800px;
  margin: 60px auto 0;
  padding: 30px;
}

.error-container {
  text-align: center;
}

@media (max-width: 1024px) {
  .editor-content {
    flex-direction: column;
  }

  .edit-panel,
  .preview-panel {
    width: 100%;
  }

  .edit-panel {
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }
}

.module-edit-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding: 0;
  position: relative;
}

.module-edit-header {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.module-edit-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.module-edit-title .el-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF;
}

.module-actions,
.section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.edit-button,
.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #606266;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.edit-button:hover,
.add-button:hover {
  color: #409EFF;
}

.module-toggle {
  display: flex;
  align-items: center;
}

.edit-icon {
  cursor: pointer;
  font-size: 16px;
  color: #606266;
}

.edit-icon:hover {
  color: #409EFF;
}

.form-content {
  padding: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-row.full-width .form-item {
  width: 100%;
  max-width: none;
}

.form-item {
  flex: 1 1 0;
  min-width: 0;
  max-width: 240px;
  box-sizing: border-box;
}

.form-item .el-date-picker,
.form-item .el-input {
  width: 100%;
  max-width: 220px;
  box-sizing: border-box;
}

.form-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.form-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.avatar-upload {
  position: relative;
  width: 70px;
  height: 70px;
  cursor: pointer;
}

.avatar-input {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
}

.upload-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 24px;
  height: 24px;
  background-color: #409EFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
}

.upload-btn:hover {
  background-color: #66b1ff;
}

.internship-item-edit,
.project-item-edit,
.education-item-edit,
.work-item-edit {
  position: relative;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fafafa;
}

.internship-item-edit:last-child,
.project-item-edit:last-child,
.education-item-edit:last-child,
.work-item-edit:last-child {
  margin-bottom: 0;
}

.item-actions {
  position: absolute;
  top: 10px;
  right: 10px;
}

.photo-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  text-align: center;
}

.module-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.module-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.module-title .el-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF;
}

.module-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.add-btn,
.edit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #606266;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.add-btn:hover,
.edit-btn:hover {
  color: #409EFF;
}

.module-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding: 0;
  position: relative;
}

.add-btn {
  min-width: 70px;
  border-radius: 4px;
}

.project-tag-select {
  margin-bottom: 10px;
}

.project-content-dialog .el-checkbox {
  display: flex;
  margin-left: 0;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.project-content-dialog .el-checkbox:hover {
  background-color: #f5f7fa;
}

.project-content-dialog .el-message-box__content {
  max-height: 400px;
  overflow-y: auto;
}

.project-content-items {
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.project-content-item {
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: background-color 0.3s;
  cursor: pointer;
  border: 1px solid #f0f0f0;
}

.project-content-item:hover {
  background-color: #f5f7fa;
}

.project-content-item .el-checkbox {
  width: 100%;
  display: flex;
  align-items: flex-start;
  margin-right: 0;
}

.project-content-item .el-checkbox__label {
  white-space: normal;
  line-height: 1.5;
  color: #606266;
}

.el-dialog__header {
  padding: 20px;
  margin-right: 0;
  border-bottom: 1px solid #f0f0f0;
}

.el-dialog__body {
  padding: 20px;
}

.el-dialog__footer {
  padding: 10px 20px 20px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
}

.ai-polish-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 10px 0;
}

.polish-original,
.polish-result {
  width: 100%;
}

.polish-original h4,
.polish-result h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #333;
}

.original-content,
.result-content {
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
  min-height: 100px;
  white-space: pre-wrap;
  font-size: 14px;
  line-height: 1.6;
}

.result-content {
  background-color: #f0f7ff;
  border-color: #c2e0ff;
}

.polish-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}

.polish-tip {
  margin-top: 15px;
  color: #909399;
  font-size: 14px;
}

.ai-polish-container {
  padding: 10px;
}

.polish-result {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.original-content, .polished-content {
  flex: 1;
}

.content-box {
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  min-height: 200px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  font-size: 14px;
  line-height: 1.5;
}

.polish-options {
  margin-top: 20px;
}

.skill-level-visual {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.skill-level-bar {
  width: 100px;
  height: 10px;
  background-color: #f0f0f0;
  border-radius: 5px;
  overflow: hidden;
}

.skill-level-fill {
  height: 100%;
  background-color: #3498db;
}

.skill-level-label {
  margin-left: 10px;
  font-size: 12px;
  color: #606266;
}

.certificate-list {
  margin-top: 10px;
}

.certificate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
}

.certificate-content {
  flex: 1;
}

.empty-hint {
  text-align: center;
  color: #909399;
  margin-top: 10px;
}

.certificate-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.campus-form-dialog .el-checkbox {
  display: flex;
  margin-left: 0;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.campus-form-dialog .el-checkbox:hover {
  background-color: #f5f7fa;
}

.campus-form-dialog .el-message-box__content {
  max-height: 400px;
  overflow-y: auto;
}

.campus-form-items {
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.campus-form-item {
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: background-color 0.3s;
  cursor: pointer;
  border: 1px solid #f0f0f0;
}

.campus-form-item:hover {
  background-color: #f5f7fa;
}

.campus-form-item .el-checkbox {
  width: 100%;
  display: flex;
  align-items: flex-start;
  margin-right: 0;
}

.campus-form-item .el-checkbox__label {
  white-space: normal;
  line-height: 1.5;
  color: #606266;
}

.campus-form-dialog .el-dialog__header {
  padding: 20px;
  margin-right: 0;
  border-bottom: 1px solid #f0f0f0;
}

.campus-form-dialog .el-dialog__body {
  padding: 20px;
}

.campus-form-dialog .el-dialog__footer {
  padding: 10px 20px 20px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
}

.campus-form-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.interest-form-dialog .el-checkbox {
  display: flex;
  margin-left: 0;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.interest-form-dialog .el-checkbox:hover {
  background-color: #f5f7fa;
}

.interest-form-dialog .el-message-box__content {
  max-height: 400px;
  overflow-y: auto;
}

.interest-form-items {
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.interest-form-item {
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: background-color 0.3s;
  cursor: pointer;
  border: 1px solid #f0f0f0;
}

.interest-form-item:hover {
  background-color: #f5f7fa;
}

.interest-form-item .el-checkbox {
  width: 100%;
  display: flex;
  align-items: flex-start;
  margin-right: 0;
}

.interest-form-item .el-checkbox__label {
  white-space: normal;
  line-height: 1.5;
  color: #606266;
}

.interest-form-dialog .el-dialog__header {
  padding: 20px;
  margin-right: 0;
  border-bottom: 1px solid #f0f0f0;
}

.interest-form-dialog .el-dialog__body {
  padding: 20px;
}

.interest-form-dialog .el-dialog__footer {
  padding: 10px 20px 20px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
}

.interest-form-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

/* 技能特长模块样式 */
.skill-name-wrapper {
  display: flex;
  align-items: center;
}

.skill-name-select {
  flex: 1;
}

.skill-delete-btn {
  margin-left: 10px;
}

/* 模板选择区域样式 */
.section-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 20px;
}

.templates-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.template-item {
  display: flex;
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #fff;
}

.template-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-item.active {
  border-color: #3498db;
  background-color: rgba(52, 152, 219, 0.05);
}

.template-preview {
  width: 100px;
  min-width: 100px;
  height: 140px;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid #eaeaea;
  background-color: #f9f9f9;
}

.template-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.template-info {
  flex: 1;
  padding: 0 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.template-name {
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 10px;
  color: #333;
}

.template-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.template-badge {
  font-size: 12px;
  padding: 2px 8px;
  background-color: #f0f7ff;
  border-radius: 10px;
  color: #3498db;
}

/* AI助手区域样式 */
.ai-chat-container {
  display: flex;
  flex-direction: column;
  height: 500px;
  border: 1px solid #eaeaea;
  border-radius: 6px;
  overflow: hidden;
}

.ai-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.ai-message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  background-color: #fff;
  padding: 12px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ai-message .el-icon {
  margin-right: 10px;
  font-size: 20px;
  color: #3498db;
  margin-top: 2px;
}

.message-content {
  flex: 1;
  line-height: 1.5;
}

.ai-input {
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #eaeaea;
  display: flex;
  gap: 10px;
}

.practice-item-edit {
  position: relative;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fafafa;
}

.practice-item-edit:last-child {
  margin-bottom: 0;
}
</style>