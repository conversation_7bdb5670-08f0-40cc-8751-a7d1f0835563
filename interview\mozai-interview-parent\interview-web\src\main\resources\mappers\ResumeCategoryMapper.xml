<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.interview.dao.ResumeCategoryMapper">

    <resultMap id="BaseResultMap" type="com.bimowu.interview.model.ResumeCategory">
            <id property="catId" column="cat_id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createAt" column="create_at" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        cat_id,name,code,
        description,sort_order,status,
        create_time,update_time,create_at,
        is_delete
    </sql>
</mapper>
