package com.bimowu.resume.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户AI使用次数记录表 resume_ai_usage
 */
@Data
@TableName("resume_ai_usage")
public class ResumeAiUsage {
    /** 主键 */
    @TableId
    private Long id;

    /** 用户ID */
    private Long userId;

    /** 剩余可用次数 */
    private Integer remainingCount;

    /** 已使用总次数 */
    private Integer totalUsed;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 是否删除(0-未删除,1-已删除) */
    private Integer isDelete;
} 