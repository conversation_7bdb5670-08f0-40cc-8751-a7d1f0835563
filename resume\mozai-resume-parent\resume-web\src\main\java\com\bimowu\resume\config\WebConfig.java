package com.bimowu.resume.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类，处理跨域问题
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    private static final Logger logger = LoggerFactory.getLogger(WebConfig.class);

    /**
     * 配置CORS过滤器
     */
    @Bean
    public CorsFilter corsFilter() {
        logger.info("初始化CORS过滤器");
        
        CorsConfiguration config = new CorsConfiguration();
        // 明确指定允许的来源
//        config.addAllowedOrigin("http://localhost:3000");
//        config.addAllowedOrigin("http://127.0.0.1:3000");
//        config.addAllowedOrigin("http://localhost:5173");
//        config.addAllowedOrigin("http://127.0.0.1:5173");
//        config.addAllowedOrigin("https://tt.bimowo.com");
//        config.addAllowedOrigin("http://tt.bimowo.com");

        // 允许所有来源，但不能与具体的来源同时使用
         config.addAllowedOrigin("*");

        // 允许跨域发送cookie
        config.setAllowCredentials(true);

        // 允许所有请求头
        config.addAllowedHeader("*");
        // 明确指定允许的请求头
        config.addAllowedHeader("Origin");
        config.addAllowedHeader("Referer");
        config.addAllowedHeader("Content-Type");
        config.addAllowedHeader("Accept");
        config.addAllowedHeader("Authorization");
        config.addAllowedHeader("token");

        // 允许所有请求方法
        config.addAllowedMethod("*");

        // 预检请求的有效期，单位为秒
        config.setMaxAge(3600L);

        // 添加暴露的头信息
        config.addExposedHeader("Authorization");
        config.addExposedHeader("token");
        config.addExposedHeader("Access-Control-Allow-Origin");
        config.addExposedHeader("Access-Control-Allow-Credentials");

        logger.info("CORS配置完成: {}", config.toString());

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);

        return new CorsFilter(source);
    }

    /**
     * 添加CORS映射
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        logger.info("添加CORS映射");

        registry.addMapping("/**")
                .allowedOrigins(
                    "http://localhost:3000",
                    "http://127.0.0.1:3000",
                    "http://localhost:5173",
                    "http://127.0.0.1:5173",
                    "https://tt.bimowo.com",
                    "http://tt.bimowo.com"
                )
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("Origin", "Referer", "Content-Type", "Accept", "Authorization", "token")
                .exposedHeaders("Authorization", "token", "Access-Control-Allow-Origin", "Access-Control-Allow-Credentials")
                .allowCredentials(true)
                .maxAge(3600);
                
        logger.info("CORS映射添加完成");
    }
} 