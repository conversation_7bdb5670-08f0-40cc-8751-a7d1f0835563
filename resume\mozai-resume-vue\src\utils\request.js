import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '../router'

const service = axios.create({
  baseURL: 'https://ceping.bimowo.com/resume',  // 直接指向后端服务地址
  timeout: 10000,
  withCredentials: true  // 添加跨域请求时发送cookie
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    console.log('Request URL:', config.url)
    console.log('Full URL:', config.baseURL + config.url)
    console.log('Request Method:', config.method)
    console.log('Request Data:', config.data)
    console.log('Request Params:', config.params)
    
    // 添加更多请求头
    config.headers['X-Requested-With'] = 'XMLHttpRequest';
    config.headers['Accept'] = 'application/json';
    config.headers['Content-Type'] = 'application/json';
    
    // 从本地存储中获取token
    const token = localStorage.getItem('token')
    
    // 如果存在token，则在请求头中携带
    if (token) {
      console.log('在请求头中添加token')
      config.headers['Authorization'] = `Bearer ${token}`
      // 也可以使用自定义头，取决于后端的实现
      config.headers['token'] = token
    }
    
    return config
  },
  error => {
    console.error('请求错误：', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    console.log('Response received:', response)
    console.log('Response data:', response.data)
    
    // 检查响应状态码
    if (response.status === 200) {
      // 检查业务状态码
      if (response.data && response.data.code !== undefined) {
        // 处理未授权情况
        if (response.data.code === 401) {
          console.log('检测到未授权响应，状态码401')
          
          // 检查是否有重定向URL
          if (response.data.data && response.data.data.redirectUrl) {
            const redirectUrl = response.data.data.redirectUrl
            console.log('检测到重定向URL:', redirectUrl)
            
            // 执行重定向
            console.log('正在重定向到:', redirectUrl)
            window.location.href = redirectUrl
            return Promise.reject(new Error('未授权，正在重定向到登录页面'))
          }
        } else if (response.data.code !== 0 && response.data.code !== 200) {
          // 处理其他业务错误
          const errorMsg = response.data.message || response.data.msg || '请求失败'
          console.error('业务错误:', errorMsg)
          
          // 不在这里显示错误消息，而是将错误传递给调用者处理
          // ElMessage.error(errorMsg)
          
          // 返回原始响应，让调用者处理错误
          return response.data
        }
      }
    }
    
    // 正常响应，返回数据
    return response.data
  },
  error => {
    console.error('响应错误:', error)
    
    // 添加更详细的错误日志
    if (error.config) {
      console.error('请求配置:', {
        url: error.config.url,
        method: error.config.method,
        data: error.config.data,
        headers: error.config.headers
      })
    }
    
    // 检查是否有响应
    if (error.response) {
      console.log('错误响应状态码:', error.response.status)
      console.log('错误响应数据:', error.response.data)
      
      // 处理401未授权错误
      if (error.response.status === 401) {
        console.log('检测到未授权响应，状态码401')
        
        // 检查是否有重定向URL
        if (error.response.data && error.response.data.data && error.response.data.data.redirectUrl) {
          const redirectUrl = error.response.data.data.redirectUrl
          console.log('检测到重定向URL:', redirectUrl)
          
          // 执行重定向
          console.log('正在重定向到:', redirectUrl)
          window.location.href = redirectUrl
          return Promise.reject(new Error('未授权，正在重定向到登录页面'))
        } else {
          // 没有重定向URL，直接跳转到登录页
          router.push('/login')
          return Promise.reject(new Error('未授权，请重新登录'))
        }
      } else if (error.response.status === 403) {
        // 处理403禁止访问错误
        console.log('检测到禁止访问响应，状态码403')
        ElMessage.error('没有权限访问该资源')
        return Promise.reject(new Error('没有权限访问该资源'))
      } else if (error.response.status === 500) {
        // 处理500服务器错误
        console.log('检测到服务器错误，状态码500')
        
        // 如果有错误消息，使用它
        if (error.response.data && (error.response.data.message || error.response.data.msg)) {
          const errorMsg = error.response.data.message || error.response.data.msg
          return Promise.reject(new Error(errorMsg))
        } else {
          ElMessage.error('服务器错误，请稍后重试')
          return Promise.reject(new Error('服务器错误，请稍后重试'))
        }
      }
    } else if (error.request) {
      // 请求已经发出，但没有收到响应
      console.log('没有收到响应:', error.request)
      ElMessage.error('网络错误，请检查您的网络连接')
      return Promise.reject(new Error('网络错误，请检查您的网络连接'))
    } else {
      // 发送请求时出了点问题
      console.log('请求配置错误:', error.message)
      ElMessage.error('请求错误，请稍后重试')
      return Promise.reject(new Error('请求错误，请稍后重试'))
    }
    
    // 默认错误处理
    ElMessage.error(error.message || '请求失败，请稍后重试')
    return Promise.reject(error)
  }
)

export default service 