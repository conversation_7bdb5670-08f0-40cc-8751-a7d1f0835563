<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 14px;
            font-weight: normal;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 0;
            color: rgb(68, 84, 106);
            line-height: 1.7;
        }

        .resume-template {
            width: 100%;
            font-family: inherit !important;
            background-color: #fff;
            line-height: 1.7;
            color: rgb(68, 84, 106);
            padding: 0 !important;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', Aria<PERSON>, sans-serif !important;
            }
        }
        
        .resume-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
            border: 15px solid rgb(68, 84, 106);
        }
        
        /* 头部内容样式 */
        .header-content {
            position: relative;
            min-height: 140px;
            margin-bottom: 20px;
            padding-bottom: 15px;
        }

        .resume-title {
            font-size: 28px;
            font-weight: bold;
            color: rgb(68, 84, 106);
            margin-bottom: 15px;
        }

        /* 个人信息和头像区域 */
        .info-photo-section {
            position: relative;
            width: 100%;
        }

        .basic-info-section {
            width: 100%;
            display: block;
        }

        .info-item {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            line-height: 1.5;
        }

        .info-label {
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: rgb(68, 84, 106);
            margin-right: 5px;
        }

        .info-value {
            color: rgb(68, 84, 106);
        }
        
        .photo-container {
            position: absolute;
            top: 0;
            right: 0;
            width: 120px;
            height: 150px;
            overflow: hidden;
            border: 1px solid #ccc;
        }
        
        .avatar {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            border: 1px solid #ccc;
        }
        
        .avatar-placeholder {
            width: 100%;
            height: 100%;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
        }
        
        /* 各部分通用样式 */
        .section {
            margin-bottom: 5px;
            padding: 0 !important;
        }
        
        .section-header {
            margin-bottom: 5px;
            position: relative;
            display: inline-block;
            padding: 0 5px;
        }
        
        .section-header::before,
        .section-header::after {
            content: '';
            position: absolute;
            height: 1px;
            background-color: #333;
            width: calc(100% + 10px);
            left: -5px;
        }
        
        .section-header::before {
            bottom: -2px;
        }
        
        .section-header::after {
            bottom: -5px;
        }
        
        .section-header h2 {
            display: inline-block;
            margin: 0;
            padding: 5px 15px;
            font-size: 25px;
            font-weight: bold;
            background-color: #f0f0f0;
            border-radius: 5px 5px 0 0;
        }
        
        .section-content {
            padding: 5px 0;
        }
        
        /* 自我评价样式 */
        .evaluation-content {
            padding: 5px;
        }
        
        .evaluation-tags {
            margin-bottom: 10px;
        }
        
        .tag {
            margin-bottom: 5px;
        }
        
        .evaluation-text {
            line-height: 1.7;
        }
        
        /* 工作经验样式 */
        .experience-item {
            margin-bottom: 20px;
        }
        
        .experience-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .education-date {
            flex: 1;
            font-weight: bold;
            font-size: 17px;
            margin-bottom: 10px;
            text-align: right;
        }
        
        .experience-date {
            margin-right: 40px;
            font-weight: bold;
            color: black;
            font-size: 17px;
            flex: 1;
        }
        
        .experience-company {
            margin-right: 15px;
            font-weight: bold;
            flex-grow: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 18px;
        }
        
        .experience-company::after {
            content: " | ";
            color: inherit;
        }
        
        .experience-position {
            white-space: nowrap;
            font-size: 18px;
        }
        
        .experience-description {
            line-height: 1.7;
        }
        
        /* 项目经验样式 */
        .project-item {
            margin-bottom: 12px;
        }
        
        .project-header {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        
        .project-title {
            font-weight: bold;
            font-size: 17px;
            flex: 1;
        }
        
        .project-date {
            font-weight: bold;
            color: black;
            font-size: 17px;
            flex: 1;
            text-align: right;
        }
        
        .project-role {
            margin-right: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .project-description {
            padding-left: 10px;
            line-height: 1.7;
        }
        
        /* 描述内容样式 */
        .description-content {
            line-height: 1.7;
        }
        
        .description-tag {
            margin-bottom: 5px;
        }
        
        .tag-title {
            font-weight: bold;
        }
        
        .description-bullet {
            padding-left: 20px;
            position: relative;
            margin-bottom: 5px;
        }
        
        .description-bullet::before {
            content: "•";
            position: absolute;
            left: 5px;
        }
        
        .description-paragraph {
            margin-bottom: 5px;
            text-indent: 2em;
        }
        
        /* 技能部分样式 */
        .skills-content {
            padding: 10px;
        }
        
        .skills-list {
            display: flex;
            flex-wrap: wrap;
        }
        
        .skill-item {
            width: 100%;
            margin-bottom: 10px;
            align-items: center;
        }
        
        .skill-name {
            font-weight: bold;
            margin-right: 10px;
            white-space: nowrap;
        }
        
        .skill-description {
            flex: 1;
            line-height: 1.7;
        }
        
        /* 教育背景样式 */
        .education-item {
            margin-bottom: 5px;
        }
        
        .education-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .education-school {
            width: 200px;
            flex: 1;
            font-weight: bold;
            margin-right: 20px;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .education-major {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .education-details {
            margin-top: 5px;
        }
        
        .education-courses {
            line-height: 1.7;
        }
        
        .education-courses p,
        .education-courses li {
            margin-bottom: 5px;
        }
        
        .certificate-content {
            padding: 5px;
        }
    </style>
</head>
<body>
    <div class="resume-template template-4">
        <div class="resume-container">
            <!-- 简历头部 -->
            <div class="header-content">
                <!-- 头像容器，绝对定位右上角 -->
                <div class="photo-container">
                    ${avatar}
                </div>
                <!-- 姓名单独一行 -->
                <div class="resume-title">
                    ${name}
                </div>
                <!-- 个人信息区域 -->
                <div class="info-photo-section">
                    <div class="basic-info-section">
                        <div class="info-item">
                            <span class="info-label">性别：</span>
                            <span class="info-value">${gender}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">年龄：</span>
                            <span class="info-value">${age}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">电话：</span>
                            <span class="info-value">${phone}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">邮箱：</span>
                            <span class="info-value">${email}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">求职意向：</span>
                            <span class="info-value">${jobObjective}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 自我评价部分 -->
            ${selfEvaluation}

            <!-- 工作经验部分 -->
            ${work}

            <!-- 项目经验部分 -->
            ${projects}

            <!-- 练手项目 -->
            ${practices}

            <!-- 技能特长 -->
            ${skills}

            <!-- 教育背景部分 -->
            ${education}

            <!-- 证书奖项部分 -->
            ${certificates}
            
            <!-- 校园经历 -->
            ${campus}
            
            <!-- 兴趣爱好 -->
            ${interests}
        </div>
    </div>
</body>
</html> 