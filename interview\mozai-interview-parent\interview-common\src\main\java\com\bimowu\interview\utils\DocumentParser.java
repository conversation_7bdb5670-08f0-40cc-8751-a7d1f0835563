package com.bimowu.interview.utils;

import org.apache.commons.io.FileUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.util.PDFTextStripper;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;

/**
 * 文档解析工具类
 * 用于解析PDF、DOC、DOCX等文档格式
 */
@Component
public class DocumentParser {
    
    /**
     * 解析文档提取文本内容
     * @param file 文档文件
     * @param fileType 文件类型（pdf, doc, docx）
     * @return 提取的文本内容
     * @throws Exception 解析过程中可能的异常
     */
    public String parseDocument(File file, String fileType) throws Exception {
        switch (fileType.toLowerCase()) {
            case "pdf":
                return parsePDF(file);
            case "doc":
                return parseDoc(file);
            case "docx":
                return parseDocx(file);
            default:
                throw new IllegalArgumentException("不支持的文件类型: " + fileType);
        }
    }
    
    /**
     * 解析PDF文件
     * @param file PDF文件
     * @return 提取的文本内容
     * @throws Exception 解析过程中可能的异常
     */
    private String parsePDF(File file) throws Exception {
        PDDocument document = null;
        try {
            document = PDDocument.load(file);
            PDFTextStripper stripper = new PDFTextStripper();
            return stripper.getText(document);
        } finally {
            if (document != null) {
                document.close();
            }
        }
    }
    
    /**
     * 解析DOC文件
     * @param file DOC文件
     * @return 提取的文本内容
     * @throws Exception 解析过程中可能的异常
     */
    private String parseDoc(File file) throws Exception {
        try (FileInputStream fis = new FileInputStream(file);
             HWPFDocument document = new HWPFDocument(fis)) {
            WordExtractor extractor = new WordExtractor(document);
            return extractor.getText();
        }
    }
    
    /**
     * 解析DOCX文件
     * @param file DOCX文件
     * @return 提取的文本内容
     * @throws Exception 解析过程中可能的异常
     */
    private String parseDocx(File file) throws Exception {
        try (FileInputStream fis = new FileInputStream(file);
             XWPFDocument document = new XWPFDocument(fis)) {
            XWPFWordExtractor extractor = new XWPFWordExtractor(document);
            return extractor.getText();
        }
    }
} 