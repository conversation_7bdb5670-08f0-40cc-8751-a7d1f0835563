#!/bin/bash

echo "🔧 修复 Element Plus 构建错误..."

# 1. 清理环境
echo "📦 清理现有依赖..."
rm -rf node_modules package-lock.json dist-interview-ai .vite

# 2. 清理 npm 缓存
echo "🧹 清理 npm 缓存..."
npm cache clean --force

# 3. 重新安装依赖
echo "📥 重新安装依赖..."
npm install

# 4. 尝试简化构建
echo "🚀 使用简化配置构建..."
npm run build:simple

# 检查构建结果
if [ -d "dist-interview-ai" ]; then
    echo "✅ 构建成功！"
    echo "📁 输出目录: dist-interview-ai"
    ls -la dist-interview-ai/
else
    echo "❌ 简化构建失败，尝试标准构建..."
    npm run build
    
    if [ -d "dist-interview-ai" ]; then
        echo "✅ 标准构建成功！"
    else
        echo "❌ 构建失败，请检查错误信息"
        exit 1
    fi
fi

echo "🎉 修复完成！"
