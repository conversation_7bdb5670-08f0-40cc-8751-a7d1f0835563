package com.bimowu.resume.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bimowu.resume.entity.ResumeKnowledge;

import java.util.List;
import java.util.Map;

/**
 * 简历知识管理服务接口
 */
public interface ResumeKnowledgeService extends IService<ResumeKnowledge> {
    
    /**
     * 创建知识信息
     * 
     * @param resumeKnowledge 知识信息
     * @return 是否成功
     */
    boolean createResumeKnowledge(ResumeKnowledge resumeKnowledge);
    
    /**
     * 更新知识信息
     * 
     * @param resumeKnowledge 知识信息
     * @return 是否成功
     */
    boolean updateResumeKnowledge(ResumeKnowledge resumeKnowledge);
    
    /**
     * 获取知识信息
     * 
     * @param id 知识ID
     * @return 知识信息
     */
    ResumeKnowledge getResumeKnowledgeById(Long id);
    
    /**
     * 根据职位类型获取知识列表
     * 
     * @param positionType 职位类型
     * @return 知识列表
     */
    List<ResumeKnowledge> getResumeKnowledgeByPositionType(Integer positionType);
    
    /**
     * 根据知识目录获取知识信息
     * 
     * @param knowledgeCatalog 知识目录
     * @return 知识信息
     */
    ResumeKnowledge getResumeKnowledgeByKnowledgeCatalog(String knowledgeCatalog);
    
    /**
     * 根据条件查询知识列表
     * 
     * @param params 查询条件
     * @return 知识列表
     */
    List<ResumeKnowledge> getResumeKnowledgeByCondition(Map<String, Object> params);
    
    /**
     * 删除知识信息
     * 
     * @param id 知识ID
     * @return 是否成功
     */
    boolean deleteResumeKnowledge(Long id);
} 