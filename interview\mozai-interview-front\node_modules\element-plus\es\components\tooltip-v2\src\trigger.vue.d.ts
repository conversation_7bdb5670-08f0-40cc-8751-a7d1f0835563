declare const _default: import("vue").DefineComponent<{
    onBlur: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    onClick: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    onFocus: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    onMouseDown: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    onMouseEnter: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    onMouseLeave: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    nowrap: BooleanConstructor;
}, {
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        onBlur: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onClick: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onFocus: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onMouseDown: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onMouseEnter: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        onMouseLeave: {
            readonly type: import("vue").PropType<(e: Event) => boolean | void>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        nowrap: BooleanConstructor;
    }>> & {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
    }>>;
    onClose: () => void;
    onOpen: () => void;
    onDelayOpen: () => void;
    triggerRef: import("vue").Ref<HTMLElement | null>;
    contentId: import("vue").Ref<string>;
    isMousedown: boolean;
    setTriggerRef: (el: HTMLElement | null) => void;
    onMouseup: () => void;
    onMouseenter: (event: Event) => void;
    onMouseleave: (event: Event) => void;
    onMousedown: (event: Event) => void;
    onFocus: (event: Event) => void;
    onBlur: (event: Event) => void;
    onClick: (event: Event) => void;
    events: {
        blur: (event: Event) => void;
        click: (event: Event) => void;
        focus: (event: Event) => void;
        mousedown: (event: Event) => void;
        mouseenter: (event: Event) => void;
        mouseleave: (event: Event) => void;
    };
    setEvents: <T extends (e: Event) => void>(el: HTMLElement | null | undefined, events: Record<string, T>, type: 'addEventListener' | 'removeEventListener') => void;
    ForwardRef: import("vue").DefineComponent<{
        readonly setRef: {
            readonly type: import("vue").PropType<import("./forward-ref").RefSetter>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly onlyChild: BooleanConstructor;
    }, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly setRef: {
            readonly type: import("vue").PropType<import("./forward-ref").RefSetter>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly onlyChild: BooleanConstructor;
    }>>, {
        readonly onlyChild: boolean;
    }>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    onBlur: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    onClick: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    onFocus: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    onMouseDown: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    onMouseEnter: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    onMouseLeave: {
        readonly type: import("vue").PropType<(e: Event) => boolean | void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    nowrap: BooleanConstructor;
}>>, {
    nowrap: boolean;
}>;
export default _default;
