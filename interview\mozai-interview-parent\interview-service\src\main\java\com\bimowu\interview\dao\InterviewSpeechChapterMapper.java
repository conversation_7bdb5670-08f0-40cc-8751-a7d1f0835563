package com.bimowu.interview.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.interview.model.InterviewSpeechChapter;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 面试语音转文字章节段落记录Mapper接口
 */
@Mapper
public interface InterviewSpeechChapterMapper extends BaseMapper<InterviewSpeechChapter> {

    /**
     * 插入章节记录
     * 
     * @param chapter 章节记录
     * @return 影响的行数
     */
    int insert(InterviewSpeechChapter chapter);
    
    /**
     * 更新章节记录
     * 
     * @param chapter 章节记录
     * @return 影响的行数
     */
    int update(InterviewSpeechChapter chapter);
    
    /**
     * 根据ID查询章节记录
     * 
     * @param id 记录ID
     * @return 章节记录
     */
    InterviewSpeechChapter selectById(@Param("id") Long id);
    
    /**
     * 根据面试ID查询章节记录列表
     * 
     * @param interviewId 面试ID
     * @return 章节记录列表
     */
    List<InterviewSpeechChapter> selectByInterviewId(@Param("interviewId") String interviewId);
    
    /**
     * 根据面试ID和时间范围查询章节记录
     * 
     * @param interviewId 面试ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 章节记录列表
     */
    List<InterviewSpeechChapter> selectByTimeRange(
            @Param("interviewId") String interviewId,
            @Param("startTime") Integer startTime,
            @Param("endTime") Integer endTime);
    
    /**
     * 根据条件查询章节记录列表
     * 
     * @param params 查询条件
     * @return 章节记录列表
     */
    List<InterviewSpeechChapter> selectByCondition(Map<String, Object> params);
    
    /**
     * 根据ID删除章节记录
     * 
     * @param id 记录ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据面试ID删除所有章节记录
     * 
     * @param interviewId 面试ID
     * @return 影响的行数
     */
    int deleteByInterviewId(@Param("interviewId") String interviewId);
} 