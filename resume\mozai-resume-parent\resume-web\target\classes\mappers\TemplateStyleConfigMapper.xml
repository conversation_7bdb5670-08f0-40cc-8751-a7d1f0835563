<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.resume.common.dao.TemplateStyleConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.bimowu.resume.entity.TemplateStyleConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="template_id" property="templateId" jdbcType="INTEGER"/>
        <result column="css_content" property="cssContent" jdbcType="LONGVARCHAR"/>
        <result column="font_config" property="fontConfig" jdbcType="VARCHAR"/>
        <result column="color_palette" property="colorPalette" jdbcType="VARCHAR"/>
        <result column="layout_config" property="layoutConfig" jdbcType="VARCHAR"/>
        <result column="responsive_rules" property="responsiveRules" jdbcType="VARCHAR"/>
        <result column="last_sync_time" property="lastSyncTime" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>
        <result column="enabled" property="enabled" jdbcType="BOOLEAN"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, template_id, css_content, font_config, color_palette, layout_config, 
        responsive_rules, last_sync_time, version, enabled, create_time, update_time, 
        create_by, update_by, remark
    </sql>

    <!-- 根据模板ID查询样式配置 -->
    <select id="selectByTemplateId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM resume_template_style_config
        WHERE template_id = #{templateId,jdbcType=INTEGER}
        AND enabled = 1
    </select>

    <!-- 查询所有启用的样式配置 -->
    <select id="selectEnabledConfigs" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM resume_template_style_config
        WHERE enabled = 1
        ORDER BY template_id ASC
    </select>

    <!-- 根据版本查询样式配置 -->
    <select id="selectByVersion" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM resume_template_style_config
        WHERE version = #{version,jdbcType=VARCHAR}
        AND enabled = 1
        ORDER BY template_id ASC
    </select>

    <!-- 批量更新同步时间 -->
    <update id="batchUpdateSyncTime">
        UPDATE resume_template_style_config 
        SET last_sync_time = NOW(), update_time = NOW()
        WHERE template_id IN
        <foreach collection="templateIds" item="templateId" open="(" separator="," close=")">
            #{templateId}
        </foreach>
    </update>



    <!-- 根据模板ID删除配置 -->
    <delete id="deleteByTemplateId" parameterType="java.lang.Integer">
        DELETE FROM resume_template_style_config
        WHERE template_id = #{templateId,jdbcType=INTEGER}
    </delete>

    <!-- 插入样式配置 -->
    <insert id="insert" parameterType="com.bimowu.resume.entity.TemplateStyleConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO resume_template_style_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateId != null">template_id,</if>
            <if test="cssContent != null">css_content,</if>
            <if test="fontConfig != null">font_config,</if>
            <if test="colorPalette != null">color_palette,</if>
            <if test="layoutConfig != null">layout_config,</if>
            <if test="responsiveRules != null">responsive_rules,</if>
            <if test="lastSyncTime != null">last_sync_time,</if>
            <if test="version != null">version,</if>
            <if test="enabled != null">enabled,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="templateId != null">#{templateId,jdbcType=INTEGER},</if>
            <if test="cssContent != null">#{cssContent,jdbcType=LONGVARCHAR},</if>
            <if test="fontConfig != null">#{fontConfig,jdbcType=VARCHAR},</if>
            <if test="colorPalette != null">#{colorPalette,jdbcType=VARCHAR},</if>
            <if test="layoutConfig != null">#{layoutConfig,jdbcType=VARCHAR},</if>
            <if test="responsiveRules != null">#{responsiveRules,jdbcType=VARCHAR},</if>
            <if test="lastSyncTime != null">#{lastSyncTime,jdbcType=TIMESTAMP},</if>
            <if test="version != null">#{version,jdbcType=VARCHAR},</if>
            <if test="enabled != null">#{enabled,jdbcType=BOOLEAN},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <!-- 更新样式配置 -->
    <update id="updateById" parameterType="com.bimowu.resume.entity.TemplateStyleConfig">
        UPDATE resume_template_style_config
        <set>
            <if test="templateId != null">template_id = #{templateId,jdbcType=INTEGER},</if>
            <if test="cssContent != null">css_content = #{cssContent,jdbcType=LONGVARCHAR},</if>
            <if test="fontConfig != null">font_config = #{fontConfig,jdbcType=VARCHAR},</if>
            <if test="colorPalette != null">color_palette = #{colorPalette,jdbcType=VARCHAR},</if>
            <if test="layoutConfig != null">layout_config = #{layoutConfig,jdbcType=VARCHAR},</if>
            <if test="responsiveRules != null">responsive_rules = #{responsiveRules,jdbcType=VARCHAR},</if>
            <if test="lastSyncTime != null">last_sync_time = #{lastSyncTime,jdbcType=TIMESTAMP},</if>
            <if test="version != null">version = #{version,jdbcType=VARCHAR},</if>
            <if test="enabled != null">enabled = #{enabled,jdbcType=BOOLEAN},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null">update_by = #{updateBy,jdbcType=VARCHAR},</if>
            <if test="remark != null">remark = #{remark,jdbcType=VARCHAR},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

</mapper>