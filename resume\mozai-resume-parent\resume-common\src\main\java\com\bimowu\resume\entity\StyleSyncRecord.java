package com.bimowu.resume.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 样式同步记录实体
 */
@Data
@TableName("resume_style_sync_record")
public class StyleSyncRecord {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 模板ID
     */
    private Integer templateId;
    
    /**
     * 同步类型（FULL-全量同步, INCREMENTAL-增量同步）
     */
    private String syncType;
    
    /**
     * 同步状态（SUCCESS-成功, FAILED-失败, PENDING-进行中）
     */
    private String status;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 同步时间
     */
    private Date syncTime;
    
    /**
     * 变更日志
     */
    private String changeLog;
    
    /**
     * 同步前版本
     */
    private String beforeVersion;
    
    /**
     * 同步后版本
     */
    private String afterVersion;
    
    /**
     * 同步耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 变更文件数量
     */
    private Integer changedFiles;
    
    /**
     * 同步触发方式（AUTO-自动, MANUAL-手动）
     */
    private String triggerType;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 备注
     */
    private String remark;
}