<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bimowu.interview.dao.InterviewMapper">
    
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.bimowu.interview.model.Interview">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="candidate_name" property="candidateName" jdbcType="VARCHAR"/>
        <result column="company" property="company" jdbcType="VARCHAR"/>
        <result column="position" property="position" jdbcType="VARCHAR"/>
        <result column="stage" property="stage" jdbcType="VARCHAR"/>
        <result column="experience" property="experience" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="result" property="result" jdbcType="INTEGER"/>
        <result column="overall_score" property="overallScore" jdbcType="INTEGER"/>
        <result column="feedback" property="feedback" jdbcType="VARCHAR"/>
        <result column="strengths" property="strengths" jdbcType="VARCHAR"/>
        <result column="improvements" property="improvements" jdbcType="VARCHAR"/>
        <result column="video_url" property="videoUrl" jdbcType="VARCHAR"/>
        <result column="interview_time" property="interviewTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 所有列 -->
    <sql id="Base_Column_List">
        id, type, candidate_name, company, position, stage, experience, status, result, overall_score, 
        feedback, strengths, improvements, video_url, interview_time, create_time, update_time
    </sql>
    
    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.bimowu.interview.model.Interview">
        INSERT INTO interview (
            id, user_id, type, candidate_name, company, position, stage, experience, status, result,
            overall_score, feedback, strengths, improvements, video_url, interview_time, create_time, update_time
        ) VALUES (
            #{id,jdbcType=VARCHAR},
            #{userId,javaType=INTEGER},
            #{type,jdbcType=VARCHAR},
            #{candidateName,jdbcType=VARCHAR},
            #{company,jdbcType=VARCHAR},
            #{position,jdbcType=VARCHAR},
            #{stage,jdbcType=VARCHAR},
            #{experience,jdbcType=VARCHAR},
            #{status,jdbcType=INTEGER},
            #{result,jdbcType=INTEGER},
            #{overallScore,jdbcType=INTEGER},
            #{feedback,jdbcType=VARCHAR},
            #{strengths,jdbcType=VARCHAR},
            #{improvements,jdbcType=VARCHAR},
            #{videoUrl,jdbcType=VARCHAR},
            #{interviewTime,jdbcType=TIMESTAMP},
            NOW(),
            NOW()
        )
    </insert>
    
    <!-- 更新记录 -->
    <update id="update" parameterType="com.bimowu.interview.model.Interview">
        UPDATE interview
        <set>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="candidateName != null">
                candidate_name = #{candidateName,jdbcType=VARCHAR},
            </if>
            <if test="company != null">
                company = #{company,jdbcType=VARCHAR},
            </if>
            <if test="position != null">
                position = #{position,jdbcType=VARCHAR},
            </if>
            <if test="stage != null">
                stage = #{stage,jdbcType=VARCHAR},
            </if>
            <if test="experience != null">
                experience = #{experience,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="result != null">
                result = #{result,jdbcType=INTEGER},
            </if>
            <if test="overallScore != null">
                overall_score = #{overallScore,jdbcType=INTEGER},
            </if>
            <if test="feedback != null">
                feedback = #{feedback,jdbcType=VARCHAR},
            </if>
            <if test="strengths != null">
                strengths = #{strengths,jdbcType=VARCHAR},
            </if>
            <if test="improvements != null">
                improvements = #{improvements,jdbcType=VARCHAR},
            </if>
            <if test="videoUrl != null">
                video_url = #{videoUrl,jdbcType=VARCHAR},
            </if>
            <if test="interviewTime != null">
                interview_time = #{interviewTime,jdbcType=TIMESTAMP},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>
    
    <!-- 更新面试状态 -->
    <update id="updateStatus">
        UPDATE interview
        SET status = #{status,jdbcType=INTEGER},
            update_time = NOW()
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>
    
    <!-- 更新面试结果 -->
    <update id="updateResult">
        UPDATE interview
        SET overall_score = #{overallScore,jdbcType=INTEGER},
            feedback = #{feedback,jdbcType=VARCHAR},
            strengths = #{strengths,jdbcType=VARCHAR},
            improvements = #{improvements,jdbcType=VARCHAR},
            status = 1, <!-- 已完成 -->
            update_time = NOW()
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>
    
    <!-- 更新面试通过/未通过状态 -->
    <update id="updateInterviewResult">
        UPDATE interview
        SET result = #{result,jdbcType=INTEGER},
            status = 2, <!-- 已完成 -->
            update_time = NOW()
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>
    
    <!-- 更新视频URL -->
    <update id="updateVideoUrl">
        UPDATE interview
        SET video_url = #{videoUrl,jdbcType=VARCHAR},
            update_time = NOW()
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>
    
    <!-- 根据ID查询记录 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview
        WHERE id = #{id,jdbcType=VARCHAR}
    </select>
    
    <!-- 根据条件查询记录 -->
    <select id="selectByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview
        <where>
            <if test="type != null">
                AND type = #{type,jdbcType=VARCHAR}
            </if>
            <if test="candidateName != null">
                AND candidate_name LIKE CONCAT('%', #{candidateName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="position != null">
                AND position = #{position,jdbcType=VARCHAR}
            </if>
            <if test="stage != null">
                AND stage = #{stage,jdbcType=VARCHAR}
            </if>
            <if test="experience != null">
                AND experience = #{experience,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                AND status = #{status,jdbcType=INTEGER}
            </if>
            <if test="result != null">
                AND result = #{result,jdbcType=INTEGER}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据类型查询面试信息列表 -->
    <select id="selectByType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview
        WHERE type = #{type,jdbcType=VARCHAR}
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据ID删除记录 -->
    <delete id="deleteById">
        DELETE FROM interview
        WHERE id = #{id,jdbcType=VARCHAR}
    </delete>
</mapper> 