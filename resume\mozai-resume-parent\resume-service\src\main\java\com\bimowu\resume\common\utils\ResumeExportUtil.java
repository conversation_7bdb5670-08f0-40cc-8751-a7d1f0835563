package com.bimowu.resume.common.utils;

import com.bimowu.resume.common.service.AdvancedPDFGenerator;
import com.bimowu.resume.common.service.impl.FlyingSaucerPDFGenerator;
import com.bimowu.resume.dto.ResumeFullSaveDto;
import com.bimowu.resume.exception.PDFGenerationException;
import com.bimowu.resume.utils.FontUtil;
import com.bimowu.resume.utils.HtmlTemplateUtil;
import com.bimowu.resume.utils.PDFGenerationLogger;
import com.bimowu.resume.utils.ChineseFontFixer;
import com.bimowu.resume.utils.MemoryManager;
import com.bimowu.resume.utils.OptimizedFontLoader;
import com.bimowu.resume.config.MemoryOptimizedPDFConfig;
import com.bimowu.resume.common.service.impl.Template3RenderService;
import com.bimowu.resume.common.service.impl.PDFMetricsService;
import com.bimowu.resume.vo.*;
import com.itextpdf.text.*;
import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPageMar;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.math.BigInteger;

@Slf4j
@Component
public class ResumeExportUtil {

    private static AdvancedPDFGenerator pdfGenerator;
    private static FlyingSaucerPDFGenerator flyingSaucerGenerator;
    private static Template3RenderService template3RenderService;
    private static PDFGenerationLogger pdfLogger;
    private static PDFMetricsService metricsService;
    private static MemoryOptimizedPDFConfig memoryConfig;

    @Autowired
    public void setPdfGenerator(@Qualifier("flyingSaucerPDFGenerator") AdvancedPDFGenerator pdfGenerator) {
        ResumeExportUtil.pdfGenerator = pdfGenerator;
    }

    @Autowired
    public void setFlyingSaucerGenerator(FlyingSaucerPDFGenerator flyingSaucerGenerator) {
        ResumeExportUtil.flyingSaucerGenerator = flyingSaucerGenerator;
    }

    @Autowired
    public void setTemplate3RenderService(Template3RenderService template3RenderService) {
        ResumeExportUtil.template3RenderService = template3RenderService;
    }

    @Autowired
    public void setPdfLogger(PDFGenerationLogger pdfLogger) {
        ResumeExportUtil.pdfLogger = pdfLogger;
    }

    @Autowired
    public void setMetricsService(PDFMetricsService metricsService) {
        ResumeExportUtil.metricsService = metricsService;
    }

    @Autowired
    public void setMemoryConfig(MemoryOptimizedPDFConfig memoryConfig) {
        ResumeExportUtil.memoryConfig = memoryConfig;
    }

    /**
     * 导出PDF格式简历（使用增强的PDF生成器）  -----在用
     */
    public static byte[] exportToPdf(ResumeFullSaveDto resume) throws Exception {
        String resumeId = "unknown";
        Long templateId = 1L; // 默认模板ID为1
        long startTime = System.currentTimeMillis();

        try {
            // 获取简历基本信息
            if (resume.getResumeVo() != null) {
                resumeId = String.valueOf(resume.getResumeVo().getResumeId());
                if (resume.getResumeVo().getTemplateId() != null) {
                    templateId = resume.getResumeVo().getTemplateId();
                }

                log.info("开始生成PDF简历，简历ID: {}, 模板ID: {}, 标题: {}",
                        resumeId, templateId, resume.getResumeVo().getTitle());
            } else {
                log.warn("resumeVo为空，将使用默认模板");
            }

            // 开始监控
            if (metricsService != null) {
                metricsService.startGeneration(String.valueOf(templateId));
            }

            // 验证输入数据
            validateResumeData(resume, resumeId, templateId);

            // 检查内存使用情况（使用优化配置）
            if (memoryConfig != null && !memoryConfig.canGeneratePDF()) {
                throw new PDFGenerationException("INSUFFICIENT_MEMORY",
                        "内存不足，无法生成PDF", String.valueOf(templateId), resumeId);
            } else if (memoryConfig == null && !MemoryManager.checkMemoryBeforePDFGeneration()) {
                throw new PDFGenerationException("INSUFFICIENT_MEMORY",
                        "内存不足，无法生成PDF", String.valueOf(templateId), resumeId);
            }


            // 根据模板ID选择不同的渲染方式
            String filledHtml;
            if (templateId == 3 && template3RenderService != null) {
                log.info("使用Template3RenderService渲染模板3");
                // 验证数据
                template3RenderService.validateResumeData(resume);
                // 使用专门的模板3渲染服务
                filledHtml = template3RenderService.renderTemplate3(resume);
            } else {
                log.info("使用HtmlTemplateUtil渲染模板{}", templateId);
                // 使用通用的模板渲染方式
                String templateHtml = HtmlTemplateUtil.readTemplate(templateId);
                log.info("模板HTML读取完成，长度: {}, 是否包含蓝色渐变: {}",
                        templateHtml.length(),
                        templateHtml.contains("linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)"));

                // 输出简历基本信息用于调试
                if (resume.getInformation() != null) {
                    log.info("简历基本信息 - 姓名: {}, 年龄: {}, 电话: {}",
                            resume.getInformation().getName(),
                            resume.getInformation().getAge(),
                            resume.getInformation().getPhone());
                } else {
                    log.warn("简历基本信息为空!");
                }

                filledHtml = HtmlTemplateUtil.fillTemplate(templateHtml, resume, templateId);
                log.info("模板填充完成，填充后长度: {}", filledHtml.length());
            }

            // 记录PDF生成前的内存状态
            MemoryManager.logMemoryUsage("PDF生成前");

            // 生成PDF（使用增强的错误处理）
            byte[] pdfBytes = generatePdfFromHtmlWithEnhancedErrorHandling(filledHtml, templateId, resumeId);

            // 记录PDF生成后的内存状态
            MemoryManager.logMemoryUsage("PDF生成后");

            // 记录成功日志
            long duration = System.currentTimeMillis() - startTime;
            if (pdfLogger != null) {
                pdfLogger.logPDFGenerationSuccess(resumeId, String.valueOf(templateId),
                        duration, pdfBytes.length);
            }

            // 结束监控（成功）
            if (metricsService != null) {
                metricsService.endGeneration(String.valueOf(templateId), true, duration, null);
            }

            log.info("PDF生成成功 - 简历ID: {}, 模板ID: {}, 耗时: {}ms, 大小: {} bytes",
                    resumeId, templateId, duration, pdfBytes.length);

            return pdfBytes;

        } catch (PDFGenerationException e) {
            // 记录失败日志
            long duration = System.currentTimeMillis() - startTime;
            if (pdfLogger != null) {
                pdfLogger.logPDFGenerationFailure(resumeId, String.valueOf(templateId),
                        e.getErrorCode(), e.getMessage(), e);
            }

            // 结束监控（失败）
            if (metricsService != null) {
                metricsService.endGeneration(String.valueOf(templateId), false, duration, e.getErrorCode());
            }

            throw e; // 重新抛出已包装的异常
        } catch (Exception e) {
            // 记录失败日志
            long duration = System.currentTimeMillis() - startTime;
            if (pdfLogger != null) {
                pdfLogger.logPDFGenerationFailure(resumeId, String.valueOf(templateId),
                        "UNKNOWN_ERROR", e.getMessage(), e);
            }

            // 结束监控（失败）
            if (metricsService != null) {
                metricsService.endGeneration(String.valueOf(templateId), false, duration, "UNKNOWN_ERROR");
            }

            log.error("PDF生成失败，尝试降级方案 - 简历ID: {}, 模板ID: {}", resumeId, templateId, e);

            // 错误恢复：使用简化的HTML模板
            try {
                String simplifiedHtml = generateSimplifiedHtml(resume);
                byte[] fallbackPdf = generatePdfFromHtmlLegacy(simplifiedHtml);

                log.warn("使用降级方案生成PDF成功 - 简历ID: {}", resumeId);
                return fallbackPdf;
            } catch (Exception fallbackException) {
                log.error("降级方案也失败 - 简历ID: {}", resumeId, fallbackException);
                throw new PDFGenerationException("COMPLETE_FAILURE",
                        "PDF生成完全失败，包括降级方案", fallbackException,
                        String.valueOf(templateId), resumeId);
            }
        }
    }

    /**
     * 从HTML生成PDF----在用
     */
    private static byte[] generatePdfFromHtml(String html, Long templateId, String resumeId) throws Exception {
        // 使用新的Flying Saucer PDF生成器
        if (flyingSaucerGenerator != null) {
            log.debug("使用Flying Saucer生成器生成PDF - 简历ID: {}", resumeId);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            boolean success = flyingSaucerGenerator.generatePDFFromHTML(html, baos);
            if (success) {
                return baos.toByteArray();
            } else {
                throw new PDFGenerationException("FLYING_SAUCER_ERROR",
                        "Flying Saucer PDF生成失败", String.valueOf(templateId), resumeId);
            }
        } else if (pdfGenerator != null) {
            log.debug("使用默认PDF生成器生成PDF - 简历ID: {}", resumeId);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            boolean success = pdfGenerator.generatePDFFromHTML(html, outputStream);
            if (success) {
                return outputStream.toByteArray();
            } else {
                throw new PDFGenerationException("PDF_GENERATOR_ERROR",
                        "PDF生成器生成失败", String.valueOf(templateId), resumeId);
            }
        } else {
            // 降级到原有的XMLWorker方案
            log.warn("PDF生成器未初始化，降级使用XMLWorker - 简历ID: {}", resumeId);
            return generatePdfFromHtmlLegacy(html);
        }
    }

    /**
     * 使用iText的XMLWorker从HTML生成PDF（降级方案）
     */
    private static byte[] generatePdfFromHtmlLegacy(String html) throws Exception {
        Document document = new Document(PageSize.A4);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PdfWriter writer = PdfWriter.getInstance(document, baos);
        document.open();

        // 使用XMLWorker解析HTML并写入PDF
        XMLWorkerHelper.getInstance().parseXHtml(
                writer,
                document,
                new ByteArrayInputStream(html.getBytes(StandardCharsets.UTF_8)),
                StandardCharsets.UTF_8
        );

        document.close();
        return baos.toByteArray();
    }

    /**
     * 生成简化的HTML内容（错误恢复用）
     */
    private static String generateSimplifiedHtml(ResumeFullSaveDto resume) {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html><html><head><meta charset=\"UTF-8\"/>");
        html.append("<style>body{font-family:SimSun,serif;margin:20px;line-height:1.5;}</style>");
        html.append("</head><body>");

        // 基本信息
        if (resume.getInformation() != null) {
            html.append("<h1>").append(resume.getInformation().getName() != null ?
                    resume.getInformation().getName() : "简历").append("</h1>");

            if (resume.getInformation().getPhone() != null) {
                html.append("<p>电话: ").append(resume.getInformation().getPhone()).append("</p>");
            }
            if (resume.getInformation().getEmail() != null) {
                html.append("<p>邮箱: ").append(resume.getInformation().getEmail()).append("</p>");
            }
        }

        // 教育经历
        if (resume.getEducationList() != null && !resume.getEducationList().isEmpty()) {
            html.append("<h2>教育经历</h2>");
            for (ResumeEducationalVo edu : resume.getEducationList()) {
                html.append("<p><strong>").append(edu.getSchool()).append("</strong> - ")
                        .append(edu.getMajor()).append(" (").append(edu.getEducation()).append(")</p>");
            }
        }

        // 工作经验
        if (resume.getWorkList() != null && !resume.getWorkList().isEmpty()) {
            html.append("<h2>工作经验</h2>");
            for (ResumeWorkVo work : resume.getWorkList()) {
                html.append("<p><strong>").append(work.getCompany()).append("</strong> - ")
                        .append(work.getPosition()).append("</p>");
            }
        }

        html.append("</body></html>");
        return html.toString();
    }

    /**
     * 导出Word格式简历
     */
    public static byte[] exportToWord(ResumeFullSaveDto resume) throws Exception {
        XWPFDocument document = new XWPFDocument();

        // 设置页面边距
        CTSectPr sectPr = document.getDocument().getBody().addNewSectPr();
        CTPageMar pageMar = sectPr.addNewPgMar();
        pageMar.setLeft(BigInteger.valueOf(1134L)); // 1 inch in twips
        pageMar.setRight(BigInteger.valueOf(1134L));
        pageMar.setTop(BigInteger.valueOf(1134L));
        pageMar.setBottom(BigInteger.valueOf(1134L));

        // 标题样式
        XWPFParagraph titlePara = document.createParagraph();
        titlePara.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText(resume.getResumeVo().getTitle());
        titleRun.setBold(true);
        titleRun.setFontSize(16);

        // 基本信息
        if (resume.getInformation() != null) {
            XWPFParagraph infoPara = document.createParagraph();
            XWPFRun infoRun = infoPara.createRun();
            infoRun.setText("基本信息");
            infoRun.setBold(true);
            infoRun.setFontSize(14);

            XWPFTable infoTable = document.createTable(5, 2);
            infoTable.setCellMargins(100, 100, 100, 100);
            infoTable.getRow(0).getCell(0).setText("姓名");
            infoTable.getRow(0).getCell(1).setText(resume.getInformation().getName());
            infoTable.getRow(1).getCell(0).setText("电话");
            infoTable.getRow(1).getCell(1).setText(resume.getInformation().getPhone());
            infoTable.getRow(2).getCell(0).setText("邮箱");
            infoTable.getRow(2).getCell(1).setText(resume.getInformation().getEmail());
            infoTable.getRow(3).getCell(0).setText("籍贯");
            infoTable.getRow(3).getCell(1).setText(resume.getInformation().getHometown());
            infoTable.getRow(4).getCell(0).setText("求职意向");
            infoTable.getRow(4).getCell(1).setText(resume.getInformation().getJobObjective());
        }

        // 教育经历
        if (resume.getEducationList() != null && !resume.getEducationList().isEmpty()) {
            document.createParagraph(); // 空行
            XWPFParagraph eduPara = document.createParagraph();
            XWPFRun eduRun = eduPara.createRun();
            eduRun.setText("教育经历");
            eduRun.setBold(true);
            eduRun.setFontSize(14);

            for (ResumeEducationalVo edu : resume.getEducationList()) {
                XWPFParagraph schoolPara = document.createParagraph();
                XWPFRun schoolRun = schoolPara.createRun();
                schoolRun.setText(edu.getSchool() + " - " + edu.getMajor());
                schoolRun.setBold(true);

                XWPFParagraph timePara = document.createParagraph();
                XWPFRun timeRun = timePara.createRun();
                timeRun.setText("学历：" + edu.getEducation() + " | 时间：" + edu.getTimePeriod());

                if (edu.getMainCourses() != null && !edu.getMainCourses().isEmpty()) {
                    XWPFParagraph coursesPara = document.createParagraph();
                    XWPFRun coursesRun = coursesPara.createRun();
                    coursesRun.setText("主修课程：" + edu.getMainCourses());
                }

                document.createParagraph(); // 空行
            }
        }

        // 工作经验
        if (resume.getWorkList() != null && !resume.getWorkList().isEmpty()) {
            XWPFParagraph workPara = document.createParagraph();
            XWPFRun workRun = workPara.createRun();
            workRun.setText("工作经验");
            workRun.setBold(true);
            workRun.setFontSize(14);

            for (ResumeWorkVo work : resume.getWorkList()) {
                XWPFParagraph companyPara = document.createParagraph();
                XWPFRun companyRun = companyPara.createRun();
                companyRun.setText(work.getCompany() + " - " + work.getPosition());
                companyRun.setBold(true);

                XWPFParagraph timePara = document.createParagraph();
                XWPFRun timeRun = timePara.createRun();
                timeRun.setText("时间：" + work.getTimePeriod());

                if (work.getWorkDescription() != null && !work.getWorkDescription().isEmpty()) {
                    XWPFParagraph descPara = document.createParagraph();
                    XWPFRun descRun = descPara.createRun();
                    descRun.setText("工作描述：" + work.getWorkDescription());
                }

                document.createParagraph(); // 空行
            }
        }

        // 项目经验
        if (resume.getProjectList() != null && !resume.getProjectList().isEmpty()) {
            XWPFParagraph projPara = document.createParagraph();
            XWPFRun projRun = projPara.createRun();
            projRun.setText("项目经验");
            projRun.setBold(true);
            projRun.setFontSize(14);

            for (ResumeProjectExperienceVo project : resume.getProjectList()) {
                XWPFParagraph namePara = document.createParagraph();
                XWPFRun nameRun = namePara.createRun();
                nameRun.setText(project.getProjectName() + " - " + project.getRole());
                nameRun.setBold(true);

                XWPFParagraph timePara = document.createParagraph();
                XWPFRun timeRun = timePara.createRun();
                timeRun.setText("时间：" + project.getTimePeriod());

                if (project.getProjectDescription() != null && !project.getProjectDescription().isEmpty()) {
                    XWPFParagraph descPara = document.createParagraph();
                    XWPFRun descRun = descPara.createRun();
                    descRun.setText("项目描述：" + project.getProjectDescription());
                }

                document.createParagraph(); // 空行
            }
        }

        // 技能特长
        if (resume.getTalentList() != null && !resume.getTalentList().isEmpty()) {
            XWPFParagraph skillsPara = document.createParagraph();
            XWPFRun skillsRun = skillsPara.createRun();
            skillsRun.setText("技能特长");
            skillsRun.setBold(true);
            skillsRun.setFontSize(14);

            for (ResumeTalentVo skill : resume.getTalentList()) {
                XWPFParagraph namePara = document.createParagraph();
                XWPFRun nameRun = namePara.createRun();
                nameRun.setText(skill.getSkillName() + "（熟练度：" + skill.getProficiency() + "）");
                nameRun.setBold(true);

                if (skill.getSkillDescription() != null && !skill.getSkillDescription().isEmpty()) {
                    XWPFParagraph descPara = document.createParagraph();
                    XWPFRun descRun = descPara.createRun();
                    descRun.setText(skill.getSkillDescription());
                }

                document.createParagraph(); // 空行
            }
        }

        // 证书奖项
        if (resume.getCertificate() != null && resume.getCertificate().getCertificateName() != null && !resume.getCertificate().getCertificateName().isEmpty()) {
            XWPFParagraph certPara = document.createParagraph();
            XWPFRun certRun = certPara.createRun();
            certRun.setText("证书奖项");
            certRun.setBold(true);
            certRun.setFontSize(14);

            XWPFParagraph contentPara = document.createParagraph();
            XWPFRun contentRun = contentPara.createRun();
            contentRun.setText(resume.getCertificate().getCertificateName());

            document.createParagraph(); // 空行
        }

        // 校园经历
        if (resume.getCampus() != null && resume.getCampus().getCampusExperience() != null && !resume.getCampus().getCampusExperience().isEmpty()) {
            XWPFParagraph campusPara = document.createParagraph();
            XWPFRun campusRun = campusPara.createRun();
            campusRun.setText("校园经历");
            campusRun.setBold(true);
            campusRun.setFontSize(14);

            XWPFParagraph contentPara = document.createParagraph();
            XWPFRun contentRun = contentPara.createRun();
            contentRun.setText(resume.getCampus().getCampusExperience());

            document.createParagraph(); // 空行
        }

        // 兴趣爱好
        if (resume.getInterest() != null && resume.getInterest().getInterest() != null && !resume.getInterest().getInterest().isEmpty()) {
            XWPFParagraph interestPara = document.createParagraph();
            XWPFRun interestRun = interestPara.createRun();
            interestRun.setText("兴趣爱好");
            interestRun.setBold(true);
            interestRun.setFontSize(14);

            XWPFParagraph contentPara = document.createParagraph();
            XWPFRun contentRun = contentPara.createRun();
            contentRun.setText(resume.getInterest().getInterest());

            document.createParagraph(); // 空行
        }

        // 自我评价
        if (resume.getEvaluate() != null && resume.getEvaluate().getSelfEvaluation() != null && !resume.getEvaluate().getSelfEvaluation().isEmpty()) {
            XWPFParagraph evalPara = document.createParagraph();
            XWPFRun evalRun = evalPara.createRun();
            evalRun.setText("自我评价");
            evalRun.setBold(true);
            evalRun.setFontSize(14);

            XWPFParagraph contentPara = document.createParagraph();
            XWPFRun contentRun = contentPara.createRun();
            contentRun.setText(resume.getEvaluate().getSelfEvaluation());
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        document.write(out);
        document.close();
        return out.toByteArray();
    }

    /**
     * 验证简历数据完整性
     */
    private static void validateResumeData(ResumeFullSaveDto resume, String resumeId, Long templateId) {
        if (resume == null) {
            throw new PDFGenerationException("INVALID_INPUT",
                    "简历数据不能为空", String.valueOf(templateId), resumeId);
        }

        // 记录详细的调试日志
        if (pdfLogger != null && pdfLogger.isDebugEnabled()) {
            pdfLogger.logDataRetrievalPhase(resumeId, "数据验证开始", resume);

            StringBuilder validationDetails = new StringBuilder();
            validationDetails.append("数据验证详情: ");

            // 验证基本信息
            boolean hasBasicInfo = resume.getInformation() != null;
            validationDetails.append("基本信息:").append(hasBasicInfo ? "✓" : "✗").append(" ");

            if (hasBasicInfo) {
                ResumeInformationVo info = resume.getInformation();
                validationDetails.append("姓名:").append(info.getName() != null ? "✓" : "✗").append(" ");
                validationDetails.append("电话:").append(info.getPhone() != null ? "✓" : "✗").append(" ");
                validationDetails.append("邮箱:").append(info.getEmail() != null ? "✓" : "✗").append(" ");
            }

            // 验证各模块数据
            int educationCount = resume.getEducationList() != null ? resume.getEducationList().size() : 0;
            int workCount = resume.getWorkList() != null ? resume.getWorkList().size() : 0;
            int projectCount = resume.getProjectList() != null ? resume.getProjectList().size() : 0;
            int practiceCount = resume.getPracticeList() != null ? resume.getPracticeList().size() : 0;
            int skillCount = resume.getTalentList() != null ? resume.getTalentList().size() : 0;

            validationDetails.append("教育:").append(educationCount).append("条 ");
            validationDetails.append("工作:").append(workCount).append("条 ");
            validationDetails.append("项目:").append(projectCount).append("条 ");
            validationDetails.append("练手:").append(practiceCount).append("条 ");
            validationDetails.append("技能:").append(skillCount).append("条 ");

            boolean hasCertificate = resume.getCertificate() != null &&
                    resume.getCertificate().getCertificateName() != null;
            boolean hasCampus = resume.getCampus() != null &&
                    resume.getCampus().getCampusExperience() != null;
            boolean hasInterest = resume.getInterest() != null &&
                    resume.getInterest().getInterest() != null;
            boolean hasEvaluation = resume.getEvaluate() != null &&
                    resume.getEvaluate().getSelfEvaluation() != null;

            validationDetails.append("证书:").append(hasCertificate ? "✓" : "✗").append(" ");
            validationDetails.append("校园:").append(hasCampus ? "✓" : "✗").append(" ");
            validationDetails.append("兴趣:").append(hasInterest ? "✓" : "✗").append(" ");
            validationDetails.append("评价:").append(hasEvaluation ? "✓" : "✗");

            pdfLogger.logDataValidation(resumeId, validationDetails.toString());

            // 检查是否有足够的数据生成简历
            if (!hasBasicInfo && educationCount == 0 && workCount == 0 && projectCount == 0) {
                pdfLogger.logErrorHandling(resumeId, "数据验证", "INSUFFICIENT_DATA",
                        "简历数据不足，至少需要基本信息或教育/工作/项目经验之一", null);
                throw new PDFGenerationException("INSUFFICIENT_DATA",
                        "简历数据不足，至少需要基本信息或教育/工作/项目经验之一",
                        String.valueOf(templateId), resumeId);
            }

            log.info("简历数据验证通过 - resumeId: {}, 模板ID: {}", resumeId, templateId);
        }
    }

    /**
     * 输出详细的调试日志
     */
    private static void logDetailedDebugInfo(String resumeId, Long templateId, String html) {
        if (pdfLogger != null && pdfLogger.isDetailedDebugEnabled()) {
            // 记录HTML内容的详细信息
            pdfLogger.logHtmlContentGeneration(resumeId, "最终HTML", html);

            // 分析HTML内容
            int htmlLength = html.length();
            int imageCount = countOccurrences(html, "<img");
            int tableCount = countOccurrences(html, "<table");
            int divCount = countOccurrences(html, "<div");
            int cssRuleCount = countOccurrences(html, "style=");

            String analysisResult = String.format(
                    "HTML分析: 长度=%d, 图片=%d, 表格=%d, DIV=%d, 内联样式=%d",
                    htmlLength, imageCount, tableCount, divCount, cssRuleCount);

            pdfLogger.logPerformanceStats("HTML内容分析", 0, analysisResult);

            // 检查可能的问题
            if (htmlLength < 500) {
                log.warn("HTML内容过短，可能存在数据丢失 - resumeId: {}, 长度: {}", resumeId, htmlLength);
            }

            if (html.contains("${")) {
                int unreplacedCount = countOccurrences(html, "${");
                log.warn("发现未替换的占位符 - resumeId: {}, 数量: {}", resumeId, unreplacedCount);
            }
        }
    }

    /**
     * 计算字符串中子字符串的出现次数
     */
    private static int countOccurrences(String text, String substring) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(substring, index)) != -1) {
            count++;
            index += substring.length();
        }
        return count;
    }

    /**
     * 优化PDF转换的错误处理----在用
     */
    private static byte[] generatePdfFromHtmlWithEnhancedErrorHandling(String html, Long templateId, String resumeId) throws Exception {
        try {
            // 记录详细调试信息
            logDetailedDebugInfo(resumeId, templateId, html);

            // 预处理HTML内容
            String processedHtml = preprocessHtmlForPdf(html, resumeId);

            return generatePdfFromHtml(processedHtml, templateId, resumeId);

        } catch (Exception e) {
            if (pdfLogger != null) {
                pdfLogger.logErrorHandling(resumeId, "PDF生成", "PDF_CONVERSION_ERROR",
                        "PDF转换失败: " + e.getMessage(), e);
            }

            // 尝试修复HTML并重新生成
            try {
                String repairedHtml = repairHtmlForPdf(html, resumeId);
                log.warn("使用修复后的HTML重新生成PDF - resumeId: {}", resumeId);
                return generatePdfFromHtml(repairedHtml, templateId, resumeId);
            } catch (Exception repairException) {
                log.error("HTML修复也失败 - resumeId: {}", resumeId, repairException);
                throw e; // 抛出原始异常
            }
        }
    }

    /**
     * 预处理HTML内容以优化PDF生成
     */
    private static String preprocessHtmlForPdf(String html, String resumeId) {
        if (pdfLogger != null && pdfLogger.isDebugEnabled()) {
            pdfLogger.logContentFormatting(resumeId, "HTML预处理", html, null);
        }

        // 确保HTML有完整的文档结构
        if (!html.trim().toLowerCase().startsWith("<!doctype") &&
                !html.trim().toLowerCase().startsWith("<html")) {
            html = "<!DOCTYPE html><html><head><meta charset=\"UTF-8\"/></head><body>" +
                    html + "</body></html>";
        }

        // 修复常见的HTML问题
        html = html.replace("&nbsp;", " "); // 替换不间断空格
        html = html.replaceAll("<br\\s*/?>", "<br/>"); // 统一br标签格式
        html = html.replaceAll("<img([^>]*?)(?<!/)>", "<img$1/>"); // 确保img标签自闭合

        // 测试字体加载（仅在调试模式下）
        if (pdfLogger != null && pdfLogger.isDebugEnabled()) {
            FontUtil.testFontLoading();

            // 检查是否包含中文字符
            if (ChineseFontFixer.containsChineseCharacters(html)) {
                String chineseChars = ChineseFontFixer.extractChineseCharacters(html);
                log.info("检测到中文字符，简历ID: {}, 中文字符数量: {}", resumeId, chineseChars.length());
                log.debug("中文字符示例: {}", chineseChars.length() > 50 ? chineseChars.substring(0, 50) + "..." : chineseChars);
            } else {
                log.warn("未检测到中文字符，简历ID: {}", resumeId);
            }
        }

        // 使用中文字体修复工具
        html = ChineseFontFixer.fixChineseFontInHtml(html);

        // 记录修复后的HTML长度
        if (pdfLogger != null && pdfLogger.isDebugEnabled()) {
            pdfLogger.logContentFormatting(resumeId, "中文字体修复完成", null, html);
        }

        return html;
    }

    /**
     * 修复HTML以适应PDF生成
     */
    private static String repairHtmlForPdf(String html, String resumeId) {
        if (pdfLogger != null && pdfLogger.isDebugEnabled()) {
            pdfLogger.logContentFormatting(resumeId, "HTML修复", html, null);
        }

        // 移除可能导致问题的CSS属性
        html = html.replaceAll("position\\s*:\\s*[^;]+;", "");
        html = html.replaceAll("float\\s*:\\s*[^;]+;", "");
        html = html.replaceAll("transform\\s*:\\s*[^;]+;", "");

        // 简化复杂的CSS选择器
        html = html.replaceAll("@media[^{]*\\{[^}]*\\}", "");

        // 确保所有标签正确闭合
        html = html.replaceAll("<br(?!/)([^>]*)>", "<br$1/>");
        html = html.replaceAll("<hr(?!/)([^>]*)>", "<hr$1/>");
        html = html.replaceAll("<img(?!/)([^>]*)>", "<img$1/>");

        // 再次应用中文字体修复
        html = ChineseFontFixer.fixChineseFontInHtml(html);

        if (pdfLogger != null && pdfLogger.isDebugEnabled()) {
            pdfLogger.logContentFormatting(resumeId, "HTML修复完成", null, html);
        }

        return html;
    }

    /**
     * 图片转换为字节数组
     */
    public static byte[] imageToBytes(MultipartFile imageFile) throws IOException {
        return imageFile.getBytes();
    }
} 