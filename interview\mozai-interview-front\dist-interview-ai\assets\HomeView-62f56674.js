import{d as x,r as C,c as m,a as t,b as s,w as o,u as B,e as n,o as p,f as r,g as d,h as z,v as H,p as N,t as F,F as S,i as U,j,k as u,_ as q}from"./index-4df1abd4.js";const D="/interview-ai/assets/interview-illustration-f25b8736.svg",E={class:"home-container"},L={class:"hero-section"},M={class:"hero-content"},R={class:"hero-buttons"},T={class:"features-section"},G={class:"features"},J={class:"feature-card"},K={class:"feature-card"},O={class:"feature-card"},P={class:"feature-card"},Q={class:"process-section"},W={class:"action-button"},X={class:"testimonials-section"},Y={class:"testimonial-card"},Z={class:"user-info"},$={class:"testimonial-content"},tt={class:"testimonial-rating"},et=x({__name:"HomeView",setup(st){const v=B(),f=()=>{v.push("/selection")},g=()=>{const c=document.querySelector(".features-section");c&&c.scrollIntoView({behavior:"smooth"})},I=C([{name:"李明",position:"软件工程师",avatar:"",content:"通过墨崽AI面试系统的训练，我成功通过了三家知名科技公司的面试。系统提供的反馈非常专业，帮助我发现了很多自己没注意到的问题。",rating:5},{name:"张婷",position:"人力资源经理",avatar:"",content:"作为招聘负责人，我推荐候选人使用墨崽AI面试系统进行练习。它能很好地模拟我们公司的面试流程，让候选人更有准备。",rating:4.5},{name:"王伟",position:"应届毕业生",avatar:"",content:"没有面试经验的我通过墨崽AI面试系统进行了多次模拟训练，大大提升了我的自信心。最终我成功获得了心仪公司的offer！",rating:5},{name:"刘芳",position:"产品经理",avatar:"",content:"系统的分析非常全面，不仅评价了我的回答内容，还给出了关于肢体语言和表情的建议，这在其他平台上很少见。",rating:4.7}]);return(c,e)=>{const _=n("el-button"),i=n("el-icon"),a=n("el-step"),A=n("el-steps"),V=n("el-avatar"),w=n("el-rate"),y=n("el-carousel-item"),b=n("el-carousel");return p(),m("div",E,[t("section",L,[t("div",M,[e[2]||(e[2]=t("h1",null,"墨崽AI智能面试系统",-1)),e[3]||(e[3]=t("p",{class:"description"}," 通过AI技术打造的智能面试平台，为您提供专业的面试评估和训练 ",-1)),t("div",R,[s(_,{type:"primary",size:"large",onClick:f},{default:o(()=>e[0]||(e[0]=[r("开始面试",-1)])),_:1,__:[0]}),s(_,{size:"large",onClick:g},{default:o(()=>e[1]||(e[1]=[r("了解更多",-1)])),_:1,__:[1]})])]),e[4]||(e[4]=t("div",{class:"hero-image"},[t("img",{src:D,alt:"面试插图"})],-1))]),t("section",T,[e[13]||(e[13]=t("div",{class:"section-title"},[t("h2",null,"功能特点"),t("p",null,"我们提供全方位的面试解决方案")],-1)),t("div",G,[t("div",J,[s(i,{class:"feature-icon"},{default:o(()=>[s(d(z))]),_:1}),e[5]||(e[5]=t("h3",null,"简历分析",-1)),e[6]||(e[6]=t("p",null,"上传您的简历，AI会根据您的经历生成针对性问题，帮助您准备更符合自身情况的面试",-1))]),t("div",K,[s(i,{class:"feature-icon"},{default:o(()=>[s(d(H))]),_:1}),e[7]||(e[7]=t("h3",null,"AI数字人面试",-1)),e[8]||(e[8]=t("p",null,"由逼真的AI数字人进行面试，提供真实面试体验，帮助您克服面试紧张感",-1))]),t("div",O,[s(i,{class:"feature-icon"},{default:o(()=>[s(d(N))]),_:1}),e[9]||(e[9]=t("h3",null,"全面评估",-1)),e[10]||(e[10]=t("p",null,"对回答内容、表情、环境等多维度评分，帮助您了解自己的优势和不足",-1))]),t("div",P,[s(i,{class:"feature-icon"},{default:o(()=>[s(d(F))]),_:1}),e[11]||(e[11]=t("h3",null,"进度追踪",-1)),e[12]||(e[12]=t("p",null,"记录每次面试结果，分析您的进步情况，提供针对性的改进建议",-1))])])]),t("section",Q,[e[15]||(e[15]=t("div",{class:"section-title"},[t("h2",null,"使用流程"),t("p",null,"简单四步，轻松完成面试训练")],-1)),s(A,{active:1,"finish-status":"success",simple:""},{default:o(()=>[s(a,{title:"选择面试类型",description:"选择您想要进行的面试类型"}),s(a,{title:"上传简历",description:"上传您的简历以获取个性化面试问题"}),s(a,{title:"AI面试",description:"由AI数字人进行面试互动"}),s(a,{title:"获取评估报告",description:"获取详细的面试表现评估报告"})]),_:1}),t("div",W,[s(_,{type:"primary",onClick:f},{default:o(()=>e[14]||(e[14]=[r("立即开始",-1)])),_:1,__:[14]})])]),t("section",X,[e[16]||(e[16]=t("div",{class:"section-title"},[t("h2",null,"用户评价"),t("p",null,"听听其他用户怎么说")],-1)),s(b,{interval:4e3,type:"card",height:"280px"},{default:o(()=>[(p(!0),m(S,null,U(I.value,(l,h)=>(p(),j(y,{key:h},{default:o(()=>[t("div",Y,[t("div",Z,[s(V,{size:60,src:l.avatar},{default:o(()=>[r(u(l.name.charAt(0)),1)]),_:2},1032,["src"]),t("div",null,[t("h4",null,u(l.name),1),t("p",null,u(l.position),1)])]),t("div",$,[t("p",null,'"'+u(l.content)+'"',1)]),t("div",tt,[s(w,{modelValue:l.rating,"onUpdate:modelValue":k=>l.rating=k,disabled:"","show-score":"","text-color":"#ff9900"},null,8,["modelValue","onUpdate:modelValue"])])])]),_:2},1024))),128))]),_:1})]),e[17]||(e[17]=t("footer",{class:"site-footer"},[t("div",{class:"footer-bottom"},[t("p",null,"© 2023 墨崽AI面试系统. 保留所有权利.")])],-1))])}}});const nt=q(et,[["__scopeId","data-v-fec52d74"]]);export{nt as default};
